#!/usr/bin/env tsx

import { seedDatabase } from '../src/lib/db/seed'

async function main() {
  try {
    console.log('🌱 开始执行数据库种子数据插入...')

    const result = await seedDatabase()

    console.log('\n✅ 种子数据插入成功!')
    console.log('📊 插入统计:')
    console.log(`   - 角色: ${result.roles} 个`)
    console.log(`   - 权限: ${result.permissions} 个`)
    console.log(`   - 字典项: ${result.dictionaries} 个`)
    console.log(`   - 系统配置: ${result.systemConfigs} 个`)
    console.log(`   - 费用项目: ${result.feeItems} 个`)
    console.log(`   - 房间: ${result.rooms} 个`)
    console.log(`   - 测试用户: ${result.users} 个`)
    console.log(`   - 示例老人: ${result.elders} 个`)

    console.log('\n🎉 数据库初始化完成!')
  } catch (error) {
    console.error('❌ 种子数据插入失败:', error)
    process.exit(1)
  }
}

main()
