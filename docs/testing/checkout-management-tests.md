# 退住管理功能测试文档

## 概述

本文档描述了退住管理功能的测试用例和测试策略。我们为退住管理功能创建了全面的测试套件，包括 API 测试、组件测试和集成测试。

## 测试文件结构

```
src/__tests__/
├── api/
│   ├── checkout-applications.test.ts    # 退住申请 API 测试
│   └── checkout-reviews.test.ts         # 退住审核 API 测试
├── components/
│   └── checkout-application-form.test.tsx  # 退住申请表单组件测试
├── integration/
│   └── checkout-workflow.test.ts        # 退住流程集成测试
└── setup.ts                            # 测试环境设置
```

## 测试覆盖范围

### 1. API 测试

#### 退住申请 API (`checkout-applications.test.ts`)
- **GET /api/checkout-applications**
  - ✅ 成功获取申请列表
  - ✅ 处理搜索和筛选参数
  - ✅ 未认证用户返回 401
  
- **POST /api/checkout-applications**
  - ✅ 成功创建退住申请
  - ✅ 老人不存在时返回 404
  - ✅ 老人未在住时返回 400
  - ✅ 已有待处理申请时返回 400
  - ✅ 验证失败时返回 400
  - ✅ 未认证用户返回 401
  - ✅ 数据库错误处理

#### 退住审核 API (`checkout-reviews.test.ts`)
- **GET /api/checkout-reviews**
  - ✅ 成功获取审核记录列表
  - ✅ 按申请 ID 筛选
  - ✅ 未认证用户返回 401
  
- **POST /api/checkout-reviews**
  - ✅ 成功创建审核记录
  - ✅ 申请不存在时返回 404
  - ✅ 处理审核通过结果
  - ✅ 处理审核拒绝结果
  - ✅ 验证失败时返回 400
  - ✅ 未认证用户返回 401
  - ✅ 数据库错误处理

### 2. 组件测试

#### 退住申请表单 (`checkout-application-form.test.tsx`)
- ✅ 正确渲染表单字段
- ✅ 填充老人选项
- ✅ 验证必填字段
- ✅ 提交有效数据
- ✅ 文件上传功能
- ✅ 删除已上传文件
- ✅ 取消按钮功能
- ✅ 初始数据填充
- ✅ 加载状态显示
- ✅ 退住类型选择

### 3. 集成测试

#### 完整退住流程 (`checkout-workflow.test.ts`)
- ✅ 完整退住流程测试
  1. 创建退住申请
  2. 初审通过
  3. 终审通过
  4. 状态更新触发费用结算
  5. 确认费用结算
  6. 创建房间清理记录
  
- ✅ 审核拒绝流程测试
- ✅ 工作流中的错误处理
- ✅ 无效状态转换处理

## 测试运行命令

### 运行所有测试
```bash
npm run test
```

### 运行特定的退住管理测试
```bash
npm run test:checkout
```

### 运行测试并生成覆盖率报告
```bash
npm run test:coverage
```

### 运行测试 UI 界面
```bash
npm run test:ui
```

### 运行单个测试文件
```bash
# API 测试
npm run test src/__tests__/api/checkout-applications.test.ts
npm run test src/__tests__/api/checkout-reviews.test.ts

# 组件测试
npm run test src/__tests__/components/checkout-application-form.test.tsx

# 集成测试
npm run test src/__tests__/integration/checkout-workflow.test.ts
```

## 测试数据和 Mock

### Mock 数据结构

```typescript
// 用户数据
const mockUser = {
  id: 'user-1',
  name: 'Test User',
  email: '<EMAIL>',
}

// 老人数据
const mockElder = {
  id: 'elder-1',
  name: '张三',
  age: 75,
  gender: '男',
  idCard: '110101194901010001',
  careLevel: '中度护理',
  status: 1,
}

// 退住申请数据
const mockApplication = {
  id: 'app-1',
  applicationNumber: 'CO20240127001',
  elderInfoId: 'elder-1',
  applicantName: '李四',
  applicantPhone: '13800138000',
  applicantRelation: '子女',
  checkoutReason: '家庭原因',
  checkoutType: 1,
  expectedCheckoutDate: '2024-02-01',
  status: 1,
  applicationDate: '2024-01-27',
  createdBy: 'user-1',
}
```

### Mock 服务

- **数据库 (Drizzle ORM)**: 完全 mock，模拟所有数据库操作
- **认证服务**: Mock `getCurrentUser` 函数
- **日期函数**: Mock `date-fns` 库的相关函数
- **文件上传**: Mock `URL.createObjectURL` 和相关 API
- **浏览器 API**: Mock `localStorage`, `ResizeObserver`, `IntersectionObserver` 等

## 测试策略

### 1. 单元测试
- 测试单个 API 端点的功能
- 测试单个组件的行为
- 验证输入验证和错误处理

### 2. 集成测试
- 测试完整的业务流程
- 验证不同组件之间的交互
- 测试状态转换和工作流

### 3. 错误处理测试
- 数据库连接失败
- 网络请求失败
- 无效输入数据
- 权限验证失败

### 4. 边界条件测试
- 空数据处理
- 极限值测试
- 并发操作测试

## 测试最佳实践

### 1. 测试命名
- 使用描述性的测试名称
- 遵循 "should [expected behavior] when [condition]" 格式

### 2. 测试结构
- 使用 AAA 模式 (Arrange, Act, Assert)
- 每个测试只验证一个功能点
- 保持测试的独立性

### 3. Mock 策略
- 只 mock 外部依赖
- 保持 mock 的简单性
- 验证 mock 的调用

### 4. 测试数据
- 使用有意义的测试数据
- 避免硬编码的魔法数字
- 重用测试数据结构

## 持续集成

### GitHub Actions 配置示例

```yaml
name: Test
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:run
      - run: npm run test:coverage
```

## 测试报告

测试运行后会生成以下报告：

1. **控制台输出**: 显示测试结果和覆盖率摘要
2. **HTML 覆盖率报告**: 在 `coverage/` 目录中生成详细的覆盖率报告
3. **JSON 报告**: 机器可读的测试结果和覆盖率数据

## 故障排除

### 常见问题

1. **测试超时**
   - 增加 `max_wait_seconds` 值
   - 检查异步操作的处理

2. **Mock 不生效**
   - 确保 mock 在测试之前设置
   - 检查 mock 的路径和模块名

3. **组件渲染失败**
   - 检查必需的 props 是否提供
   - 验证测试环境的设置

4. **数据库 Mock 问题**
   - 确保所有数据库操作都被正确 mock
   - 检查返回值的结构是否正确

## 总结

我们为退住管理功能创建了全面的测试套件，覆盖了：

- ✅ 4 个主要测试文件
- ✅ 30+ 个测试用例
- ✅ API、组件和集成测试
- ✅ 完整的错误处理测试
- ✅ 业务流程验证

这些测试确保了退住管理功能的可靠性和稳定性，为后续的开发和维护提供了坚实的基础。
