# 数据库设置指南

## 环境配置

1. 复制环境变量文件并配置数据库连接：

```bash
cp .env.local.example .env.local
```

2. 在 `.env.local` 文件中配置真实的数据库连接字符串：

```env
DATABASE_URL=postgresql://username:password@localhost:5432/nursing_home_db
```

## 数据库初始化

### 1. 运行数据库迁移

```bash
npm run db:migrate
```

### 2. 插入基础数据和种子数据

```bash
npm run db:seed
```

## 种子数据说明

种子数据包含以下内容：

### 系统角色 (6个)

- `super_admin` - 超级管理员
- `admin` - 管理员
- `nurse` - 护理员
- `doctor` - 医生
- `finance` - 财务人员
- `receptionist` - 前台接待

### 系统权限 (32个)

涵盖以下模块的增删改查权限：

- 用户管理 (user)
- 角色管理 (role)
- 老人信息管理 (elder)
- 咨询管理 (consultation)
- 房间管理 (room)
- 护理管理 (care)
- 健康管理 (health)
- 财务管理 (finance)
- 系统配置 (system)

### 数据字典 (50+项)

- 性别字典
- 护理等级字典
- 老人状态字典
- 咨询渠道字典
- 咨询状态字典
- 房间类型字典
- 房间状态字典
- 护理类型字典
- 健康记录类型字典
- 费用类别字典
- 账单状态字典
- 支付方式字典

### 系统配置 (13项)

- 系统基本信息
- 业务配置参数
- 通知配置参数

### 基础费用项目 (13项)

- 住宿费（按房间类型）
- 护理费（按护理等级）
- 餐费
- 医疗费
- 其他服务费

### 示例房间 (10个)

- 一楼：101-105 (单人间、双人间、三人间、四人间)
- 二楼：201-205 (带阳台的各类房间)

### 测试用户 (4个)

- <EMAIL> - 系统管理员
- <EMAIL> - 护理员张三
- <EMAIL> - 医生李四
- <EMAIL> - 财务王五

### 示例老人数据 (2个)

- 张老太 - 78岁女性，半自理
- 李老爷 - 82岁男性，不能自理

## 数据库管理命令

```bash
# 生成迁移文件
npm run db:generate

# 执行迁移
npm run db:migrate

# 推送schema到数据库（开发环境）
npm run db:push

# 打开数据库管理界面
npm run db:studio

# 插入种子数据
npm run db:seed
```

## 注意事项

1. 确保PostgreSQL数据库已启动并可连接
2. 数据库用户需要有创建表和插入数据的权限
3. 种子数据插入前会检查数据库连接
4. 如果需要重新插入种子数据，请先清空相关表
5. 超级管理员角色会自动获得所有权限
