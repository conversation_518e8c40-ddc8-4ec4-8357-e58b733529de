'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Plus, 
  Search, 
  Filter,
  FileText,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  MoreHorizontal
} from 'lucide-react'
import { AdmissionSteps, SimpleAdmissionSteps, getStatusDescription } from '@/components/admission/admission-steps'
import { AdmissionApplicationForm } from '@/components/admission/admission-application-form'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { toast } from 'sonner'
import { format } from 'date-fns'

// 申请状态映射
const STATUS_MAP = {
  1: { label: '待评估', color: 'bg-yellow-100 text-yellow-800' },
  2: { label: '评估中', color: 'bg-blue-100 text-blue-800' },
  3: { label: '待审核', color: 'bg-orange-100 text-orange-800' },
  4: { label: '审核中', color: 'bg-purple-100 text-purple-800' },
  5: { label: '已通过', color: 'bg-green-100 text-green-800' },
  6: { label: '已拒绝', color: 'bg-red-100 text-red-800' },
  7: { label: '已入住', color: 'bg-emerald-100 text-emerald-800' },
}

// 模拟数据
const mockApplications = [
  {
    id: '1',
    applicationNumber: 'APP20240101001',
    elderName: '张三',
    elderAge: 75,
    applicantName: '张小明',
    applicantPhone: '13800138001',
    status: 3,
    expectedAdmissionDate: '2024-02-01',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-16T14:30:00Z',
  },
  {
    id: '2',
    applicationNumber: 'APP20240101002',
    elderName: '李四',
    elderAge: 82,
    applicantName: '李小红',
    applicantPhone: '13800138002',
    status: 5,
    expectedAdmissionDate: '2024-02-15',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-18T16:00:00Z',
  },
  {
    id: '3',
    applicationNumber: 'APP20240101003',
    elderName: '王五',
    elderAge: 78,
    applicantName: '王小华',
    applicantPhone: '13800138003',
    status: 2,
    expectedAdmissionDate: '2024-03-01',
    createdAt: '2024-01-20T11:00:00Z',
    updatedAt: '2024-01-21T10:00:00Z',
  },
]

export default function AdmissionPage() {
  const [applications, setApplications] = useState(mockApplications)
  const [filteredApplications, setFilteredApplications] = useState(mockApplications)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [isNewApplicationOpen, setIsNewApplicationOpen] = useState(false)
  const [selectedApplication, setSelectedApplication] = useState<any>(null)
  const [isDetailOpen, setIsDetailOpen] = useState(false)

  // 搜索和筛选
  useEffect(() => {
    let filtered = applications

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(app => 
        app.elderName.includes(searchTerm) ||
        app.applicantName.includes(searchTerm) ||
        app.applicationNumber.includes(searchTerm) ||
        app.applicantPhone.includes(searchTerm)
      )
    }

    // 状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(app => app.status === parseInt(statusFilter))
    }

    setFilteredApplications(filtered)
  }, [applications, searchTerm, statusFilter])

  // 统计数据
  const stats = {
    total: applications.length,
    pending: applications.filter(app => [1, 2, 3, 4].includes(app.status)).length,
    approved: applications.filter(app => app.status === 5).length,
    rejected: applications.filter(app => app.status === 6).length,
    checkedIn: applications.filter(app => app.status === 7).length,
  }

  // 提交新申请
  const handleNewApplication = async (data: any) => {
    try {
      // 这里应该调用 API
      const newApplication = {
        id: Date.now().toString(),
        applicationNumber: `APP${format(new Date(), 'yyyyMMdd')}${String(applications.length + 1).padStart(3, '0')}`,
        elderName: data.elderName,
        elderAge: data.elderAge,
        applicantName: data.applicantName,
        applicantPhone: data.applicantPhone,
        status: 1,
        expectedAdmissionDate: format(data.expectedAdmissionDate, 'yyyy-MM-dd'),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      setApplications(prev => [newApplication, ...prev])
      setIsNewApplicationOpen(false)
      toast.success('申请提交成功')
    } catch (error) {
      toast.error('提交失败，请重试')
    }
  }

  // 查看详情
  const handleViewDetail = (application: any) => {
    setSelectedApplication(application)
    setIsDetailOpen(true)
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">入住流程管理</h1>
          <p className="text-muted-foreground">管理入住申请、评估、审核、合同签订等完整流程</p>
        </div>
        <Dialog open={isNewApplicationOpen} onOpenChange={setIsNewApplicationOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              新建申请
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>新建入住申请</DialogTitle>
            </DialogHeader>
            <AdmissionApplicationForm onSubmit={handleNewApplication} />
          </DialogContent>
        </Dialog>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">总申请</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">处理中</p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">已通过</p>
                <p className="text-2xl font-bold">{stats.approved}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">已拒绝</p>
                <p className="text-2xl font-bold">{stats.rejected}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-emerald-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">已入住</p>
                <p className="text-2xl font-bold">{stats.checkedIn}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="搜索申请人、老人姓名、申请编号或电话..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="状态筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="1">待评估</SelectItem>
                <SelectItem value="2">评估中</SelectItem>
                <SelectItem value="3">待审核</SelectItem>
                <SelectItem value="4">审核中</SelectItem>
                <SelectItem value="5">已通过</SelectItem>
                <SelectItem value="6">已拒绝</SelectItem>
                <SelectItem value="7">已入住</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 申请列表 */}
      <Card>
        <CardHeader>
          <CardTitle>申请列表</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredApplications.map((application) => (
              <div
                key={application.id}
                className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center space-x-4">
                      <h3 className="font-medium">{application.applicationNumber}</h3>
                      <Badge className={STATUS_MAP[application.status as keyof typeof STATUS_MAP].color}>
                        {STATUS_MAP[application.status as keyof typeof STATUS_MAP].label}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
                      <div>
                        <span className="font-medium">老人：</span>
                        {application.elderName} ({application.elderAge}岁)
                      </div>
                      <div>
                        <span className="font-medium">申请人：</span>
                        {application.applicantName}
                      </div>
                      <div>
                        <span className="font-medium">联系电话：</span>
                        {application.applicantPhone}
                      </div>
                      <div>
                        <span className="font-medium">预期入住：</span>
                        {application.expectedAdmissionDate}
                      </div>
                    </div>

                    {/* 流程步骤 */}
                    <div className="mt-3">
                      <SimpleAdmissionSteps currentStatus={application.status} />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewDetail(application)}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      查看
                    </Button>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <FileText className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            ))}

            {filteredApplications.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                暂无申请记录
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 详情对话框 */}
      <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>申请详情 - {selectedApplication?.applicationNumber}</DialogTitle>
          </DialogHeader>
          {selectedApplication && (
            <div className="space-y-6">
              {/* 流程步骤 */}
              <Card>
                <CardHeader>
                  <CardTitle>流程进度</CardTitle>
                </CardHeader>
                <CardContent>
                  <AdmissionSteps currentStatus={selectedApplication.status} />
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      当前状态：{getStatusDescription(selectedApplication.status)}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* 基本信息 */}
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">申请编号：</span>
                      {selectedApplication.applicationNumber}
                    </div>
                    <div>
                      <span className="font-medium">申请时间：</span>
                      {format(new Date(selectedApplication.createdAt), 'yyyy-MM-dd HH:mm')}
                    </div>
                    <div>
                      <span className="font-medium">老人姓名：</span>
                      {selectedApplication.elderName}
                    </div>
                    <div>
                      <span className="font-medium">年龄：</span>
                      {selectedApplication.elderAge}岁
                    </div>
                    <div>
                      <span className="font-medium">申请人：</span>
                      {selectedApplication.applicantName}
                    </div>
                    <div>
                      <span className="font-medium">联系电话：</span>
                      {selectedApplication.applicantPhone}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
