'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { CheckoutApplicationForm } from '@/components/checkout/checkout-application-form'
import { CheckoutApplicationList } from '@/components/checkout/checkout-application-list'
import { CheckoutReviewForm } from '@/components/checkout/checkout-review-form'
import { Plus, FileText, DollarSign, Trash2, BarChart3 } from 'lucide-react'
import { toast } from 'sonner'

interface CheckoutApplication {
  id: string
  applicationNumber: string
  applicantName: string
  applicantPhone: string
  applicantRelation: string
  checkoutReason: string
  checkoutType: number
  expectedCheckoutDate: string
  actualCheckoutDate?: string
  status: number
  applicationDate: string
  notes?: string
  elder: {
    id: string
    name: string
    age: number
    gender: string
    idCard: string
    careLevel: string
  }
  room?: {
    id: string
    roomNumber: string
    roomType: string
  }
  createdBy: {
    id: string
    name: string
  }
}

interface ElderOption {
  id: string
  name: string
  idCard: string
  roomNumber?: string
}

interface ReviewerOption {
  id: string
  name: string
  email: string
}

export default function CheckoutManagementPage() {
  const [applications, setApplications] = useState<CheckoutApplication[]>([])
  const [elderOptions, setElderOptions] = useState<ElderOption[]>([])
  const [reviewerOptions, setReviewerOptions] = useState<ReviewerOption[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showApplicationForm, setShowApplicationForm] = useState(false)
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [selectedApplication, setSelectedApplication] =
    useState<CheckoutApplication | null>(null)
  const [editingApplication, setEditingApplication] =
    useState<CheckoutApplication | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  })
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    checkoutType: '',
  })

  // 获取退住申请列表
  const fetchApplications = async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...filters,
      })

      const response = await fetch(`/api/checkout-applications?${params}`)
      const data = await response.json()

      if (data.success) {
        setApplications(data.data.list)
        setPagination(data.data.pagination)
      } else {
        toast.error(data.message || '获取申请列表失败')
      }
    } catch (error) {
      toast.error('获取申请列表失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 获取老人选项
  const fetchElderOptions = async () => {
    try {
      const response = await fetch('/api/elder-info?status=1') // 在住老人
      const data = await response.json()

      if (data.success) {
        setElderOptions(
          data.data.list.map((elder: any) => ({
            id: elder.id,
            name: elder.name,
            idCard: elder.idCard,
            roomNumber: elder.room?.roomNumber,
          }))
        )
      }
    } catch (error) {
      console.error('获取老人列表失败:', error)
    }
  }

  // 获取审核人选项
  const fetchReviewerOptions = async () => {
    try {
      const response = await fetch('/api/users?role=reviewer')
      const data = await response.json()

      if (data.success) {
        setReviewerOptions(data.data.list)
      }
    } catch (error) {
      console.error('获取审核人列表失败:', error)
    }
  }

  useEffect(() => {
    fetchApplications()
  }, [pagination.page, filters])

  useEffect(() => {
    fetchElderOptions()
    fetchReviewerOptions()
  }, [])

  // 创建申请
  const handleCreateApplication = async (data: any) => {
    try {
      const response = await fetch('/api/checkout-applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('退住申请创建成功')
        setShowApplicationForm(false)
        fetchApplications()
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      throw error
    }
  }

  // 更新申请
  const handleUpdateApplication = async (data: any) => {
    if (!editingApplication) return

    try {
      const response = await fetch(
        `/api/checkout-applications/${editingApplication.id}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        }
      )

      const result = await response.json()

      if (result.success) {
        toast.success('退住申请更新成功')
        setShowApplicationForm(false)
        setEditingApplication(null)
        fetchApplications()
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      throw error
    }
  }

  // 删除申请
  const handleDeleteApplication = async (applicationId: string) => {
    if (!confirm('确定要删除这个退住申请吗？')) return

    try {
      const response = await fetch(
        `/api/checkout-applications/${applicationId}`,
        {
          method: 'DELETE',
        }
      )

      const result = await response.json()

      if (result.success) {
        toast.success('退住申请删除成功')
        fetchApplications()
      } else {
        toast.error(result.message || '删除失败')
      }
    } catch (error) {
      toast.error('删除失败')
    }
  }

  // 提交审核
  const handleSubmitReview = async (data: any) => {
    if (!selectedApplication) return

    try {
      const response = await fetch('/api/checkout-reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          applicationId: selectedApplication.id,
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('审核提交成功')
        setShowReviewForm(false)
        setSelectedApplication(null)
        fetchApplications()
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      throw error
    }
  }

  // 查看申请详情
  const handleViewApplication = (application: CheckoutApplication) => {
    // 这里可以打开详情对话框或跳转到详情页面
    console.log('查看申请详情:', application)
  }

  // 编辑申请
  const handleEditApplication = (application: CheckoutApplication) => {
    setEditingApplication(application)
    setShowApplicationForm(true)
  }

  // 审核申请
  const handleReviewApplication = (applicationId: string) => {
    const application = applications.find(app => app.id === applicationId)
    if (application) {
      setSelectedApplication(application)
      setShowReviewForm(true)
    }
  }

  // 搜索
  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  // 筛选
  const handleFilter = (newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  // 分页
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }))
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">退住管理</h1>
          <p className="text-muted-foreground">
            管理老人退住申请、审核流程、费用结算和房间清理
          </p>
        </div>
        <Button onClick={() => setShowApplicationForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          新建退住申请
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待审核申请</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {applications.filter(app => app.status === 1).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">审核中申请</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {applications.filter(app => app.status === 2).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">费用结算中</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {applications.filter(app => app.status === 5).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">房间清理中</CardTitle>
            <Trash2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {applications.filter(app => app.status === 6).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容 */}
      <Tabs defaultValue="applications" className="space-y-4">
        <TabsList>
          <TabsTrigger value="applications">退住申请</TabsTrigger>
          <TabsTrigger value="settlements">费用结算</TabsTrigger>
          <TabsTrigger value="cleaning">房间清理</TabsTrigger>
          <TabsTrigger value="statistics">统计报表</TabsTrigger>
        </TabsList>

        <TabsContent value="applications">
          <CheckoutApplicationList
            applications={applications}
            onView={handleViewApplication}
            onEdit={handleEditApplication}
            onDelete={handleDeleteApplication}
            onReview={handleReviewApplication}
            isLoading={isLoading}
            pagination={pagination}
            onPageChange={handlePageChange}
            onSearch={handleSearch}
            onFilter={handleFilter}
          />
        </TabsContent>

        <TabsContent value="settlements">
          <Card>
            <CardHeader>
              <CardTitle>费用结算管理</CardTitle>
              <CardDescription>
                管理退住费用结算，包括费用计算、押金退还等
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                费用结算功能开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cleaning">
          <Card>
            <CardHeader>
              <CardTitle>房间清理管理</CardTitle>
              <CardDescription>
                管理房间清理任务，包括物品清点、设施检查等
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                房间清理功能开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <Card>
            <CardHeader>
              <CardTitle>统计报表</CardTitle>
              <CardDescription>
                查看退住管理相关的统计数据和报表
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                统计报表功能开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 申请表单对话框 */}
      <Dialog open={showApplicationForm} onOpenChange={setShowApplicationForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingApplication ? '编辑退住申请' : '新建退住申请'}
            </DialogTitle>
            <DialogDescription>请填写完整的退住申请信息</DialogDescription>
          </DialogHeader>
          <CheckoutApplicationForm
            elderOptions={elderOptions}
            onSubmit={
              editingApplication
                ? handleUpdateApplication
                : handleCreateApplication
            }
            onCancel={() => {
              setShowApplicationForm(false)
              setEditingApplication(null)
            }}
            initialData={
              editingApplication
                ? {
                    elderInfoId: editingApplication.elder.id,
                    applicantName: editingApplication.applicantName,
                    applicantPhone: editingApplication.applicantPhone,
                    applicantRelation: editingApplication.applicantRelation,
                    checkoutReason: editingApplication.checkoutReason,
                    checkoutType: editingApplication.checkoutType,
                    expectedCheckoutDate: new Date(
                      editingApplication.expectedCheckoutDate
                    ),
                    notes: editingApplication.notes,
                  }
                : undefined
            }
            isLoading={isLoading}
          />
        </DialogContent>
      </Dialog>

      {/* 审核表单对话框 */}
      <Dialog open={showReviewForm} onOpenChange={setShowReviewForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>退住申请审核</DialogTitle>
            <DialogDescription>
              请仔细审核申请信息并填写审核意见
            </DialogDescription>
          </DialogHeader>
          {selectedApplication && (
            <CheckoutReviewForm
              application={selectedApplication}
              reviewerOptions={reviewerOptions}
              onSubmit={handleSubmitReview}
              onCancel={() => {
                setShowReviewForm(false)
                setSelectedApplication(null)
              }}
              isLoading={isLoading}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
