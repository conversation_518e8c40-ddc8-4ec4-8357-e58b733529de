'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Plus, 
  Search, 
  Filter,
  Eye,
  Users,
  ArrowLeft,
  Calendar,
  Clock
} from 'lucide-react'
import { useRouter } from 'next/navigation'

interface VisitRegistration {
  id: string
  registrationNumber: string
  elderName: string
  elderAge: number
  elderGender: number
  genderLabel: string
  elderRoomNumber: string
  elderBedNumber: string
  visitorName: string
  visitorPhone: string
  relationToElder: string
  visitorCount: number
  visitDate: string
  plannedStartTime: string
  plannedEndTime: string
  actualStartTime?: string
  actualEndTime?: string
  visitPurpose: string
  visitType: number
  visitTypeLabel: string
  visitLocation: string
  isOutsideVisit: boolean
  outsideDestination?: string
  status: number
  statusLabel: string
  approvalRequired: boolean
  approvedBy?: string
  approvalDate?: string
  temperatureCheck?: number
  healthDeclaration: boolean
  createdAt: string
}

interface PaginationData {
  page: number
  limit: number
  total: number
  totalPages: number
}

export default function VisitRegistrationsPage() {
  const [registrations, setRegistrations] = useState<VisitRegistration[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    visitType: '',
    startDate: '',
    endDate: ''
  })
  const router = useRouter()

  useEffect(() => {
    fetchRegistrations()
  }, [pagination.page, filters])

  const fetchRegistrations = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.status && { status: filters.status }),
        ...(filters.visitType && { visitType: filters.visitType }),
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate })
      })

      const response = await fetch(`/api/nursing/visit-registrations?${params}`)
      if (response.ok) {
        const result = await response.json()
        setRegistrations(result.data.registrations)
        setPagination(result.data.pagination)
      }
    } catch (error) {
      console.error('Error fetching visit registrations:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  const getStatusBadgeVariant = (status: number) => {
    switch (status) {
      case 1: return 'secondary'   // 预约
      case 2: return 'default'     // 已到达
      case 3: return 'default'     // 探访中
      case 4: return 'outline'     // 已结束
      case 5: return 'destructive' // 已取消
      case 6: return 'destructive' // 逾期未到
      default: return 'secondary'
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.push('/nursing')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">探访登记</h1>
            <p className="text-muted-foreground">
              管理探访预约和登记
            </p>
          </div>
        </div>
        <Button onClick={() => router.push('/nursing/visit-registrations/new')}>
          <Plus className="mr-2 h-4 w-4" />
          新建探访登记
        </Button>
      </div>

      {/* 筛选器 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            筛选条件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <div className="space-y-2">
              <label className="text-sm font-medium">搜索老人姓名</label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="输入老人姓名"
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">探访状态</label>
              <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部状态</SelectItem>
                  <SelectItem value="1">预约</SelectItem>
                  <SelectItem value="2">已到达</SelectItem>
                  <SelectItem value="3">探访中</SelectItem>
                  <SelectItem value="4">已结束</SelectItem>
                  <SelectItem value="5">已取消</SelectItem>
                  <SelectItem value="6">逾期未到</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">探访类型</label>
              <Select value={filters.visitType} onValueChange={(value) => handleFilterChange('visitType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部类型</SelectItem>
                  <SelectItem value="1">家属探访</SelectItem>
                  <SelectItem value="2">朋友探访</SelectItem>
                  <SelectItem value="3">医疗探访</SelectItem>
                  <SelectItem value="4">法律事务</SelectItem>
                  <SelectItem value="5">其他</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">开始日期</label>
              <Input
                type="date"
                value={filters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">结束日期</label>
              <Input
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 登记列表 */}
      <Card>
        <CardHeader>
          <CardTitle>探访登记列表</CardTitle>
          <CardDescription>
            共 {pagination.total} 条记录，第 {pagination.page} / {pagination.totalPages} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-lg">加载中...</div>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>登记编号</TableHead>
                    <TableHead>老人信息</TableHead>
                    <TableHead>探访者信息</TableHead>
                    <TableHead>探访信息</TableHead>
                    <TableHead>探访时间</TableHead>
                    <TableHead>健康检查</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {registrations.map((registration) => (
                    <TableRow key={registration.id}>
                      <TableCell className="font-medium">
                        {registration.registrationNumber}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{registration.elderName}</div>
                          <div className="text-sm text-muted-foreground">
                            {registration.elderAge}岁 {registration.genderLabel} | 
                            {registration.elderRoomNumber}房{registration.elderBedNumber}床
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{registration.visitorName}</div>
                          <div className="text-sm text-muted-foreground">
                            {registration.relationToElder} | {registration.visitorPhone}
                          </div>
                          {registration.visitorCount > 1 && (
                            <div className="text-sm text-muted-foreground">
                              共{registration.visitorCount}人
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <Badge variant="outline" className="mb-1">{registration.visitTypeLabel}</Badge>
                          <div className="text-sm">
                            <div>{registration.visitLocation}</div>
                            {registration.isOutsideVisit && registration.outsideDestination && (
                              <div className="text-muted-foreground">外出: {registration.outsideDestination}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {registration.visitDate}
                          </div>
                          <div className="flex items-center gap-1 text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {registration.plannedStartTime} - {registration.plannedEndTime}
                          </div>
                          {registration.actualStartTime && (
                            <div className="text-green-600 text-xs">
                              实际: {registration.actualStartTime}
                              {registration.actualEndTime && ` - ${registration.actualEndTime}`}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {registration.temperatureCheck && (
                            <div>体温: {registration.temperatureCheck}°C</div>
                          )}
                          <div className={registration.healthDeclaration ? 'text-green-600' : 'text-red-600'}>
                            {registration.healthDeclaration ? '已声明' : '未声明'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(registration.status)}>
                          {registration.statusLabel}
                        </Badge>
                        {registration.approvalRequired && !registration.approvedBy && (
                          <div className="text-xs text-orange-600 mt-1">需审批</div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => router.push(`/nursing/visit-registrations/${registration.id}`)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 分页 */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    显示第 {(pagination.page - 1) * pagination.limit + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} 条，共 {pagination.total} 条
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                    >
                      上一页
                    </Button>
                    <span className="text-sm">
                      第 {pagination.page} / {pagination.totalPages} 页
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.totalPages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
