'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  ArrowLeft,
  Check,
  X,
  Calendar,
  User,
  Phone,
  MapPin,
  FileText,
  Clock
} from 'lucide-react'
import { useRouter, useParams } from 'next/navigation'

interface LeaveApplicationDetail {
  id: string
  applicationNumber: string
  elderName: string
  elderAge: number
  elderGender: number
  genderLabel: string
  elderRoomNumber: string
  elderBedNumber: string
  applicantName: string
  applicantPhone: string
  applicantRelation: string
  applicantIdCard: string
  leaveType: number
  leaveTypeLabel: string
  leaveReason: string
  startDate: string
  endDate: string
  expectedReturnDate: string
  actualReturnDate?: string
  accompaniedBy?: string
  accompaniedPhone?: string
  accompaniedRelation?: string
  destination: string
  destinationAddress?: string
  destinationContact?: string
  healthCondition?: string
  medicationNeeds?: string
  specialCare?: string
  status: number
  statusLabel: string
  reviewerId?: string
  reviewDate?: string
  reviewComments?: string
  returnProcessedBy?: string
  returnProcessedDate?: string
  returnCondition?: string
  returnNotes?: string
  notes?: string
  attachments?: string
  createdAt: string
  updatedAt: string
}

export default function LeaveApplicationDetailPage() {
  const [application, setApplication] = useState<LeaveApplicationDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [reviewComments, setReviewComments] = useState('')
  const [returnData, setReturnData] = useState({
    actualReturnDate: '',
    returnCondition: '',
    returnNotes: ''
  })
  const router = useRouter()
  const params = useParams()

  useEffect(() => {
    if (params.id) {
      fetchApplicationDetail()
    }
  }, [params.id])

  const fetchApplicationDetail = async () => {
    try {
      const response = await fetch(`/api/nursing/leave-applications/${params.id}`)
      if (response.ok) {
        const result = await response.json()
        setApplication(result.data)
      } else {
        router.push('/nursing/leave-applications')
      }
    } catch (error) {
      console.error('Error fetching application detail:', error)
      router.push('/nursing/leave-applications')
    } finally {
      setLoading(false)
    }
  }

  const handleReview = async (action: 'approve' | 'reject') => {
    if (!application) return

    try {
      setProcessing(true)
      const response = await fetch(`/api/nursing/leave-applications/${application.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'review',
          action: action,
          reviewComments
        })
      })

      if (response.ok) {
        const result = await response.json()
        alert(result.message)
        fetchApplicationDetail()
        setReviewComments('')
      } else {
        const error = await response.json()
        alert(error.error || '操作失败')
      }
    } catch (error) {
      console.error('Error reviewing application:', error)
      alert('操作失败')
    } finally {
      setProcessing(false)
    }
  }

  const handleReturn = async () => {
    if (!application || !returnData.actualReturnDate) return

    try {
      setProcessing(true)
      const response = await fetch(`/api/nursing/leave-applications/${application.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'return',
          ...returnData
        })
      })

      if (response.ok) {
        const result = await response.json()
        alert(result.message)
        fetchApplicationDetail()
        setReturnData({ actualReturnDate: '', returnCondition: '', returnNotes: '' })
      } else {
        const error = await response.json()
        alert(error.error || '销假失败')
      }
    } catch (error) {
      console.error('Error processing return:', error)
      alert('销假失败')
    } finally {
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-500">申请不存在</div>
      </div>
    )
  }

  const getStatusBadgeVariant = (status: number) => {
    switch (status) {
      case 1: return 'secondary' // 待审核
      case 2: return 'default'   // 已批准
      case 3: return 'destructive' // 已拒绝
      case 4: return 'outline'   // 已销假
      case 5: return 'destructive' // 逾期未归
      default: return 'secondary'
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.push('/nursing/leave-applications')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回列表
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">请假申请详情</h1>
            <p className="text-muted-foreground">
              申请编号: {application.applicationNumber}
            </p>
          </div>
        </div>
        <Badge variant={getStatusBadgeVariant(application.status)} className="text-lg px-4 py-2">
          {application.statusLabel}
        </Badge>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              老人信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label>姓名</Label>
              <div className="font-medium">{application.elderName}</div>
            </div>
            <div className="grid gap-2">
              <Label>年龄性别</Label>
              <div>{application.elderAge}岁 {application.genderLabel}</div>
            </div>
            <div className="grid gap-2">
              <Label>房间床位</Label>
              <div>{application.elderRoomNumber}房{application.elderBedNumber}床</div>
            </div>
          </CardContent>
        </Card>

        {/* 申请人信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              申请人信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label>申请人姓名</Label>
              <div className="font-medium">{application.applicantName}</div>
            </div>
            <div className="grid gap-2">
              <Label>联系电话</Label>
              <div>{application.applicantPhone}</div>
            </div>
            <div className="grid gap-2">
              <Label>与老人关系</Label>
              <div>{application.applicantRelation}</div>
            </div>
            {application.applicantIdCard && (
              <div className="grid gap-2">
                <Label>身份证号</Label>
                <div>{application.applicantIdCard}</div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 请假信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              请假信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label>请假类型</Label>
              <Badge variant="outline">{application.leaveTypeLabel}</Badge>
            </div>
            <div className="grid gap-2">
              <Label>请假原因</Label>
              <div>{application.leaveReason}</div>
            </div>
            <div className="grid gap-2">
              <Label>请假时间</Label>
              <div>
                <div>{application.startDate} 至 {application.endDate}</div>
                <div className="text-sm text-muted-foreground">
                  预计返回: {application.expectedReturnDate}
                </div>
              </div>
            </div>
            {application.actualReturnDate && (
              <div className="grid gap-2">
                <Label>实际返回时间</Label>
                <div>{application.actualReturnDate}</div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 目的地信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              目的地信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label>目的地</Label>
              <div>{application.destination}</div>
            </div>
            {application.destinationAddress && (
              <div className="grid gap-2">
                <Label>详细地址</Label>
                <div>{application.destinationAddress}</div>
              </div>
            )}
            {application.destinationContact && (
              <div className="grid gap-2">
                <Label>目的地联系人</Label>
                <div>{application.destinationContact}</div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 陪同人员信息 */}
      {(application.accompaniedBy || application.accompaniedPhone) && (
        <Card>
          <CardHeader>
            <CardTitle>陪同人员信息</CardTitle>
          </CardHeader>
          <CardContent className="grid gap-4 md:grid-cols-3">
            {application.accompaniedBy && (
              <div className="grid gap-2">
                <Label>陪同人员</Label>
                <div>{application.accompaniedBy}</div>
              </div>
            )}
            {application.accompaniedPhone && (
              <div className="grid gap-2">
                <Label>陪同人员电话</Label>
                <div>{application.accompaniedPhone}</div>
              </div>
            )}
            {application.accompaniedRelation && (
              <div className="grid gap-2">
                <Label>与老人关系</Label>
                <div>{application.accompaniedRelation}</div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 健康状况 */}
      {(application.healthCondition || application.medicationNeeds || application.specialCare) && (
        <Card>
          <CardHeader>
            <CardTitle>健康状况</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {application.healthCondition && (
              <div className="grid gap-2">
                <Label>健康状况</Label>
                <div>{application.healthCondition}</div>
              </div>
            )}
            {application.medicationNeeds && (
              <div className="grid gap-2">
                <Label>用药需求</Label>
                <div>{application.medicationNeeds}</div>
              </div>
            )}
            {application.specialCare && (
              <div className="grid gap-2">
                <Label>特殊护理</Label>
                <div>{application.specialCare}</div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 审核信息 */}
      {(application.reviewComments || application.status === 1) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              审核信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {application.reviewComments && (
              <div className="grid gap-2">
                <Label>审核意见</Label>
                <div className="p-3 bg-muted rounded-lg">{application.reviewComments}</div>
              </div>
            )}
            {application.reviewDate && (
              <div className="grid gap-2">
                <Label>审核时间</Label>
                <div>{application.reviewDate}</div>
              </div>
            )}

            {/* 审核操作 */}
            {application.status === 1 && (
              <div className="space-y-4 border-t pt-4">
                <div className="grid gap-2">
                  <Label htmlFor="reviewComments">审核意见</Label>
                  <Textarea
                    id="reviewComments"
                    placeholder="请输入审核意见..."
                    value={reviewComments}
                    onChange={(e) => setReviewComments(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleReview('approve')}
                    disabled={processing}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Check className="mr-2 h-4 w-4" />
                    批准
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => handleReview('reject')}
                    disabled={processing}
                  >
                    <X className="mr-2 h-4 w-4" />
                    拒绝
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 销假信息 */}
      {(application.returnCondition || application.status === 2) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              销假信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {application.returnCondition && (
              <div className="grid gap-2">
                <Label>返回状况</Label>
                <div className="p-3 bg-muted rounded-lg">{application.returnCondition}</div>
              </div>
            )}
            {application.returnNotes && (
              <div className="grid gap-2">
                <Label>销假备注</Label>
                <div className="p-3 bg-muted rounded-lg">{application.returnNotes}</div>
              </div>
            )}
            {application.returnProcessedDate && (
              <div className="grid gap-2">
                <Label>销假时间</Label>
                <div>{application.returnProcessedDate}</div>
              </div>
            )}

            {/* 销假操作 */}
            {application.status === 2 && (
              <div className="space-y-4 border-t pt-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="grid gap-2">
                    <Label htmlFor="actualReturnDate">实际返回日期 *</Label>
                    <Input
                      id="actualReturnDate"
                      type="date"
                      value={returnData.actualReturnDate}
                      onChange={(e) => setReturnData(prev => ({ ...prev, actualReturnDate: e.target.value }))}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="returnCondition">返回状况</Label>
                    <Input
                      id="returnCondition"
                      placeholder="如：身体状况良好"
                      value={returnData.returnCondition}
                      onChange={(e) => setReturnData(prev => ({ ...prev, returnCondition: e.target.value }))}
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="returnNotes">销假备注</Label>
                  <Textarea
                    id="returnNotes"
                    placeholder="请输入销假备注..."
                    value={returnData.returnNotes}
                    onChange={(e) => setReturnData(prev => ({ ...prev, returnNotes: e.target.value }))}
                  />
                </div>
                <Button
                  onClick={handleReturn}
                  disabled={processing || !returnData.actualReturnDate}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Check className="mr-2 h-4 w-4" />
                  确认销假
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 备注信息 */}
      {application.notes && (
        <Card>
          <CardHeader>
            <CardTitle>备注信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="p-3 bg-muted rounded-lg">{application.notes}</div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
