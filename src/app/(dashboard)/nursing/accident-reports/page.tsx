'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Plus, 
  Search, 
  Filter,
  Eye,
  AlertTriangle,
  ArrowLeft,
  FileText
} from 'lucide-react'
import { useRouter } from 'next/navigation'

interface AccidentReport {
  id: string
  reportNumber: string
  elderName: string
  elderAge: number
  elderGender: number
  genderLabel: string
  elderRoomNumber: string
  elderBedNumber: string
  accidentDate: string
  accidentTime: string
  location: string
  accidentType: number
  accidentTypeLabel: string
  severity: number
  severityLabel: string
  description: string
  discoveredBy: string
  discoveredTime: string
  hospitalTransfer: boolean
  hospitalName?: string
  familyNotified: boolean
  status: number
  statusLabel: string
  handlingResult?: string
  createdAt: string
}

interface PaginationData {
  page: number
  limit: number
  total: number
  totalPages: number
}

export default function AccidentReportsPage() {
  const [reports, setReports] = useState<AccidentReport[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    accidentType: '',
    severity: '',
    startDate: '',
    endDate: ''
  })
  const router = useRouter()

  useEffect(() => {
    fetchReports()
  }, [pagination.page, filters])

  const fetchReports = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.status && { status: filters.status }),
        ...(filters.accidentType && { accidentType: filters.accidentType }),
        ...(filters.severity && { severity: filters.severity }),
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate })
      })

      const response = await fetch(`/api/nursing/accident-reports?${params}`)
      if (response.ok) {
        const result = await response.json()
        setReports(result.data.reports)
        setPagination(result.data.pagination)
      }
    } catch (error) {
      console.error('Error fetching accident reports:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  const getStatusBadgeVariant = (status: number) => {
    switch (status) {
      case 1: return 'secondary' // 待处理
      case 2: return 'default'   // 处理中
      case 3: return 'outline'   // 已处理
      case 4: return 'default'   // 已结案
      default: return 'secondary'
    }
  }

  const getSeverityBadgeVariant = (severity: number) => {
    switch (severity) {
      case 1: return 'outline'     // 轻微
      case 2: return 'secondary'   // 一般
      case 3: return 'destructive' // 严重
      case 4: return 'destructive' // 危重
      default: return 'secondary'
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.push('/nursing')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">事故报告</h1>
            <p className="text-muted-foreground">
              记录和处理意外事故
            </p>
          </div>
        </div>
        <Button onClick={() => router.push('/nursing/accident-reports/new')}>
          <Plus className="mr-2 h-4 w-4" />
          新建事故报告
        </Button>
      </div>

      {/* 筛选器 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            筛选条件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">搜索老人姓名</label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="输入老人姓名"
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">处理状态</label>
              <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部状态</SelectItem>
                  <SelectItem value="1">待处理</SelectItem>
                  <SelectItem value="2">处理中</SelectItem>
                  <SelectItem value="3">已处理</SelectItem>
                  <SelectItem value="4">已结案</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">事故类型</label>
              <Select value={filters.accidentType} onValueChange={(value) => handleFilterChange('accidentType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部类型</SelectItem>
                  <SelectItem value="1">跌倒</SelectItem>
                  <SelectItem value="2">烫伤</SelectItem>
                  <SelectItem value="3">走失</SelectItem>
                  <SelectItem value="4">误食</SelectItem>
                  <SelectItem value="5">其他意外</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">严重程度</label>
              <Select value={filters.severity} onValueChange={(value) => handleFilterChange('severity', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择程度" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部程度</SelectItem>
                  <SelectItem value="1">轻微</SelectItem>
                  <SelectItem value="2">一般</SelectItem>
                  <SelectItem value="3">严重</SelectItem>
                  <SelectItem value="4">危重</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">开始日期</label>
              <Input
                type="date"
                value={filters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">结束日期</label>
              <Input
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 报告列表 */}
      <Card>
        <CardHeader>
          <CardTitle>事故报告列表</CardTitle>
          <CardDescription>
            共 {pagination.total} 条记录，第 {pagination.page} / {pagination.totalPages} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-lg">加载中...</div>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>报告编号</TableHead>
                    <TableHead>老人信息</TableHead>
                    <TableHead>事故信息</TableHead>
                    <TableHead>严重程度</TableHead>
                    <TableHead>发现人</TableHead>
                    <TableHead>就医情况</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell className="font-medium">
                        {report.reportNumber}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{report.elderName}</div>
                          <div className="text-sm text-muted-foreground">
                            {report.elderAge}岁 {report.genderLabel} | 
                            {report.elderRoomNumber}房{report.elderBedNumber}床
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <Badge variant="outline" className="mb-1">{report.accidentTypeLabel}</Badge>
                          <div className="text-sm">
                            <div>{report.accidentDate} {report.accidentTime}</div>
                            <div className="text-muted-foreground">{report.location}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getSeverityBadgeVariant(report.severity)}>
                          {report.severityLabel}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{report.discoveredBy}</div>
                          <div className="text-sm text-muted-foreground">
                            {report.discoveredTime}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {report.hospitalTransfer ? (
                            <div>
                              <Badge variant="destructive" className="mb-1">已送医</Badge>
                              {report.hospitalName && (
                                <div className="text-muted-foreground">{report.hospitalName}</div>
                              )}
                            </div>
                          ) : (
                            <Badge variant="outline">未送医</Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(report.status)}>
                          {report.statusLabel}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => router.push(`/nursing/accident-reports/${report.id}`)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 分页 */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    显示第 {(pagination.page - 1) * pagination.limit + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} 条，共 {pagination.total} 条
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                    >
                      上一页
                    </Button>
                    <span className="text-sm">
                      第 {pagination.page} / {pagination.totalPages} 页
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.totalPages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
