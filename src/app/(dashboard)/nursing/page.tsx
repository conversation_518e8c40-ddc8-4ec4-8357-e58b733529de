'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  UserCheck, 
  UserX, 
  Bed, 
  AlertTriangle, 
  Calendar,
  Plus,
  Eye,
  FileText,
  MapPin
} from 'lucide-react'
import { useRouter } from 'next/navigation'

interface OverviewData {
  elderStats: {
    total: number
    inResidence: number
    onLeave: number
    discharged: number
  }
  careLevelDistribution: Array<{
    level: number
    count: number
    label: string
  }>
  roomUsage: {
    totalRooms: number
    occupiedRooms: number
    availableRooms: number
    bedUsageRate: number
  }
  recentLeaveApplications: Array<{
    id: string
    applicationNumber: string
    elderName: string
    leaveType: number
    leaveTypeLabel: string
    startDate: string
    endDate: string
    status: number
    statusLabel: string
  }>
  recentAccidentReports: Array<{
    id: string
    reportNumber: string
    elderName: string
    accidentType: number
    accidentTypeLabel: string
    severity: number
    severityLabel: string
    accidentDate: string
    status: number
    statusLabel: string
  }>
  todayVisits: Array<{
    id: string
    registrationNumber: string
    elderName: string
    visitorName: string
    visitType: number
    visitTypeLabel: string
    plannedStartTime: string
    plannedEndTime: string
    status: number
    statusLabel: string
  }>
}

export default function NursingOverviewPage() {
  const [data, setData] = useState<OverviewData | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    fetchOverviewData()
  }, [])

  const fetchOverviewData = async () => {
    try {
      const response = await fetch('/api/nursing/overview')
      if (response.ok) {
        const result = await response.json()
        setData(result.data)
      }
    } catch (error) {
      console.error('Error fetching overview data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-500">数据加载失败</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">在住服务</h1>
          <p className="text-muted-foreground">
            管理在住老人的日常服务，包括请假、床位调整、事故报告和探访登记
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => router.push('/nursing/leave-applications')}>
            <Plus className="mr-2 h-4 w-4" />
            新建请假申请
          </Button>
          <Button variant="outline" onClick={() => router.push('/nursing/accident-reports')}>
            <AlertTriangle className="mr-2 h-4 w-4" />
            事故报告
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">在住老人总数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.elderStats.total}</div>
            <p className="text-xs text-muted-foreground">
              在住: {data.elderStats.inResidence} | 请假: {data.elderStats.onLeave}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">在住状态</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.elderStats.inResidence}</div>
            <p className="text-xs text-muted-foreground">
              正常在住老人数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">请假中</CardTitle>
            <UserX className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.elderStats.onLeave}</div>
            <p className="text-xs text-muted-foreground">
              当前请假老人数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">床位使用率</CardTitle>
            <Bed className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.roomUsage.bedUsageRate}%</div>
            <p className="text-xs text-muted-foreground">
              已占用: {data.roomUsage.occupiedRooms} / {data.roomUsage.totalRooms}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="leave">请假管理</TabsTrigger>
          <TabsTrigger value="beds">床位调整</TabsTrigger>
          <TabsTrigger value="accidents">事故报告</TabsTrigger>
          <TabsTrigger value="visits">探访登记</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* 护理等级分布 */}
            <Card>
              <CardHeader>
                <CardTitle>护理等级分布</CardTitle>
                <CardDescription>按护理等级统计在住老人分布</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.careLevelDistribution.map((level) => (
                    <div key={level.level} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{level.label}</Badge>
                      </div>
                      <div className="text-sm font-medium">{level.count}人</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 今日探访安排 */}
            <Card>
              <CardHeader>
                <CardTitle>今日探访安排</CardTitle>
                <CardDescription>今天的探访预约情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.todayVisits.length > 0 ? (
                    data.todayVisits.map((visit) => (
                      <div key={visit.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{visit.elderName}</div>
                          <div className="text-sm text-muted-foreground">
                            探访者: {visit.visitorName} | {visit.plannedStartTime} - {visit.plannedEndTime}
                          </div>
                        </div>
                        <Badge variant={visit.status === 1 ? 'default' : 'secondary'}>
                          {visit.statusLabel}
                        </Badge>
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-muted-foreground py-4">
                      今日暂无探访安排
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 最近活动 */}
          <div className="grid gap-4 md:grid-cols-2">
            {/* 最近请假申请 */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>最近请假申请</CardTitle>
                  <CardDescription>最新的请假申请记录</CardDescription>
                </div>
                <Button variant="outline" size="sm" onClick={() => router.push('/nursing/leave-applications')}>
                  <Eye className="mr-2 h-4 w-4" />
                  查看全部
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.recentLeaveApplications.map((application) => (
                    <div key={application.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">{application.elderName}</div>
                        <div className="text-sm text-muted-foreground">
                          {application.leaveTypeLabel} | {application.startDate} - {application.endDate}
                        </div>
                      </div>
                      <Badge variant={application.status === 1 ? 'secondary' : application.status === 2 ? 'default' : 'destructive'}>
                        {application.statusLabel}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 最近事故报告 */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>最近事故报告</CardTitle>
                  <CardDescription>最新的事故报告记录</CardDescription>
                </div>
                <Button variant="outline" size="sm" onClick={() => router.push('/nursing/accident-reports')}>
                  <FileText className="mr-2 h-4 w-4" />
                  查看全部
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.recentAccidentReports.length > 0 ? (
                    data.recentAccidentReports.map((report) => (
                      <div key={report.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{report.elderName}</div>
                          <div className="text-sm text-muted-foreground">
                            {report.accidentTypeLabel} | {report.severityLabel} | {report.accidentDate}
                          </div>
                        </div>
                        <Badge variant={report.status === 1 ? 'secondary' : report.status === 4 ? 'default' : 'outline'}>
                          {report.statusLabel}
                        </Badge>
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-muted-foreground py-4">
                      暂无事故报告
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="leave">
          <Card>
            <CardHeader>
              <CardTitle>请假管理</CardTitle>
              <CardDescription>管理老人的请假申请、审核和销假</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Calendar className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground mb-4">请假管理功能</p>
                <Button onClick={() => router.push('/nursing/leave-applications')}>
                  进入请假管理
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="beds">
          <Card>
            <CardHeader>
              <CardTitle>床位调整</CardTitle>
              <CardDescription>管理床位调整和房间调换</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <MapPin className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground mb-4">床位调整功能</p>
                <Button onClick={() => router.push('/nursing/bed-adjustments')}>
                  进入床位管理
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="accidents">
          <Card>
            <CardHeader>
              <CardTitle>事故报告</CardTitle>
              <CardDescription>记录和处理意外事故</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <AlertTriangle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground mb-4">事故报告功能</p>
                <Button onClick={() => router.push('/nursing/accident-reports')}>
                  进入事故管理
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="visits">
          <Card>
            <CardHeader>
              <CardTitle>探访登记</CardTitle>
              <CardDescription>管理探访预约和登记</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground mb-4">探访登记功能</p>
                <Button onClick={() => router.push('/nursing/visit-registrations')}>
                  进入探访管理
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
