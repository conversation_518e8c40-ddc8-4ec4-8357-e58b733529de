'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { PermissionGuard } from '@/components/auth/PermissionGuard'
import { useAuth } from '@/hooks/use-auth'
import { PERMISSIONS } from '@/lib/permissions'
import { Plus, Edit, Trash2, Search, UserCheck, UserX } from 'lucide-react'

interface User {
  id: string
  email: string
  name: string
  phone?: string
  role?: {
    id: string
    name: string
    description: string
  }
  isActive: boolean
  lastLoginAt?: string
  createdAt: string
}

export default function UsersPage() {
  const { hasPermission } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [error, setError] = useState('')

  // 模拟数据 - 在实际项目中应该从API获取
  useEffect(() => {
    const mockUsers: User[] = [
      {
        id: '1',
        email: '<EMAIL>',
        name: '系统管理员',
        phone: '13800138000',
        role: {
          id: '1',
          name: 'super_admin',
          description: '超级管理员',
        },
        isActive: true,
        lastLoginAt: '2024-01-15T10:30:00Z',
        createdAt: '2024-01-01T00:00:00Z',
      },
      {
        id: '2',
        email: '<EMAIL>',
        name: '张护士',
        phone: '13800138001',
        role: {
          id: '2',
          name: 'nurse',
          description: '护理员',
        },
        isActive: true,
        lastLoginAt: '2024-01-15T09:15:00Z',
        createdAt: '2024-01-02T00:00:00Z',
      },
      {
        id: '3',
        email: '<EMAIL>',
        name: '李医生',
        phone: '13800138002',
        role: {
          id: '3',
          name: 'doctor',
          description: '医生',
        },
        isActive: true,
        lastLoginAt: '2024-01-14T16:45:00Z',
        createdAt: '2024-01-03T00:00:00Z',
      },
      {
        id: '4',
        email: '<EMAIL>',
        name: '王会计',
        phone: '13800138003',
        role: {
          id: '4',
          name: 'finance',
          description: '财务人员',
        },
        isActive: false,
        lastLoginAt: '2024-01-10T14:20:00Z',
        createdAt: '2024-01-04T00:00:00Z',
      },
    ]

    setTimeout(() => {
      setUsers(mockUsers)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredUsers = users.filter(
    user =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role?.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreateUser = () => {
    setIsCreateDialogOpen(true)
  }

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setIsEditDialogOpen(true)
  }

  const handleToggleUserStatus = async (user: User) => {
    try {
      // 在实际项目中，这里应该调用API
      setUsers(prev =>
        prev.map(u => (u.id === user.id ? { ...u, isActive: !u.isActive } : u))
      )
    } catch (error) {
      setError('操作失败，请稍后重试')
    }
  }

  const handleDeleteUser = async (user: User) => {
    if (!confirm(`确定要删除用户 "${user.name}" 吗？此操作不可恢复。`)) {
      return
    }

    try {
      // 在实际项目中，这里应该调用API
      setUsers(prev => prev.filter(u => u.id !== user.id))
    } catch (error) {
      setError('删除失败，请稍后重试')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-48"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">用户管理</h1>
          <p className="text-gray-600">管理系统用户账号和权限</p>
        </div>
        <PermissionGuard permission={PERMISSIONS.USER_CREATE}>
          <Button
            onClick={handleCreateUser}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            新增用户
          </Button>
        </PermissionGuard>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
          <CardDescription>系统中所有用户的信息和状态</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="w-4 h-4 text-gray-400" />
            <Input
              placeholder="搜索用户姓名、邮箱或角色..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>用户信息</TableHead>
                  <TableHead>角色</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>最后登录</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map(user => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-gray-500">
                          {user.email}
                        </div>
                        {user.phone && (
                          <div className="text-sm text-gray-500">
                            {user.phone}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {user.role ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {user.role.description}
                        </span>
                      ) : (
                        <span className="text-gray-400">未分配</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {user.isActive ? (
                          <>
                            <UserCheck className="w-4 h-4 text-green-500 mr-1" />
                            <span className="text-green-600">正常</span>
                          </>
                        ) : (
                          <>
                            <UserX className="w-4 h-4 text-red-500 mr-1" />
                            <span className="text-red-600">禁用</span>
                          </>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {user.lastLoginAt ? (
                        <span className="text-sm">
                          {formatDate(user.lastLoginAt)}
                        </span>
                      ) : (
                        <span className="text-gray-400">从未登录</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">
                        {formatDate(user.createdAt)}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <PermissionGuard permission={PERMISSIONS.USER_UPDATE}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleUserStatus(user)}
                          >
                            {user.isActive ? '禁用' : '启用'}
                          </Button>
                        </PermissionGuard>
                        <PermissionGuard permission={PERMISSIONS.USER_UPDATE}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditUser(user)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </PermissionGuard>
                        <PermissionGuard permission={PERMISSIONS.USER_DELETE}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteUser(user)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </PermissionGuard>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredUsers.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchTerm ? '没有找到匹配的用户' : '暂无用户数据'}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 创建用户对话框 */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>新增用户</DialogTitle>
            <DialogDescription>创建新的系统用户账号</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="create-name">姓名</Label>
              <Input id="create-name" placeholder="请输入用户姓名" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="create-email">邮箱</Label>
              <Input
                id="create-email"
                type="email"
                placeholder="请输入邮箱地址"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="create-phone">手机号</Label>
              <Input id="create-phone" placeholder="请输入手机号" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="create-password">初始密码</Label>
              <Input
                id="create-password"
                type="password"
                placeholder="请输入初始密码"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsCreateDialogOpen(false)}
              >
                取消
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700">
                创建用户
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 编辑用户对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑用户</DialogTitle>
            <DialogDescription>修改用户信息和权限设置</DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">姓名</Label>
                <Input id="edit-name" defaultValue={selectedUser.name} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-email">邮箱</Label>
                <Input
                  id="edit-email"
                  type="email"
                  defaultValue={selectedUser.email}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-phone">手机号</Label>
                <Input
                  id="edit-phone"
                  defaultValue={selectedUser.phone || ''}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  取消
                </Button>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  保存修改
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
