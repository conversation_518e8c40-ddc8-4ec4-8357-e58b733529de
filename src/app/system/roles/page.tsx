'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
// import { PermissionGuard } from '@/components/auth/PermissionGuard'
// import { useAuth } from '@/hooks/use-auth'
import { PERMISSIONS, ROLE_PERMISSIONS } from '@/lib/permissions'
import { Plus, Edit, Trash2, Search, Shield, Users } from 'lucide-react'

interface Role {
  id: string
  name: string
  description: string
  isActive: boolean
  userCount: number
  permissions: string[]
  createdAt: string
}

export default function RolesPage() {
  // const { hasPermission } = useAuth()
  const hasPermission = () => true // 临时简化
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isPermissionDialogOpen, setIsPermissionDialogOpen] = useState(false)
  const [error, setError] = useState('')

  // 模拟数据 - 在实际项目中应该从API获取
  useEffect(() => {
    const mockRoles: Role[] = [
      {
        id: '1',
        name: 'super_admin',
        description: '超级管理员',
        isActive: true,
        userCount: 1,
        permissions: ROLE_PERMISSIONS['super_admin'] || [],
        createdAt: '2024-01-01T00:00:00Z',
      },
      {
        id: '2',
        name: 'admin',
        description: '管理员',
        isActive: true,
        userCount: 2,
        permissions: ROLE_PERMISSIONS['admin'] || [],
        createdAt: '2024-01-01T00:00:00Z',
      },
      {
        id: '3',
        name: 'nurse',
        description: '护理员',
        isActive: true,
        userCount: 5,
        permissions: ROLE_PERMISSIONS['nurse'] || [],
        createdAt: '2024-01-01T00:00:00Z',
      },
      {
        id: '4',
        name: 'doctor',
        description: '医生',
        isActive: true,
        userCount: 3,
        permissions: ROLE_PERMISSIONS['doctor'] || [],
        createdAt: '2024-01-01T00:00:00Z',
      },
      {
        id: '5',
        name: 'finance',
        description: '财务人员',
        isActive: true,
        userCount: 2,
        permissions: ROLE_PERMISSIONS['finance'] || [],
        createdAt: '2024-01-01T00:00:00Z',
      },
      {
        id: '6',
        name: 'receptionist',
        description: '前台接待',
        isActive: true,
        userCount: 3,
        permissions: ROLE_PERMISSIONS['receptionist'] || [],
        createdAt: '2024-01-01T00:00:00Z',
      },
    ]

    setTimeout(() => {
      setRoles(mockRoles)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredRoles = roles.filter(
    role =>
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreateRole = () => {
    setIsCreateDialogOpen(true)
  }

  const handleEditRole = (role: Role) => {
    setSelectedRole(role)
    setIsEditDialogOpen(true)
  }

  const handleViewPermissions = (role: Role) => {
    setSelectedRole(role)
    setIsPermissionDialogOpen(true)
  }

  const handleDeleteRole = async (role: Role) => {
    if (role.userCount > 0) {
      setError(
        `无法删除角色 "${role.description}"，该角色下还有 ${role.userCount} 个用户`
      )
      return
    }

    if (!confirm(`确定要删除角色 "${role.description}" 吗？此操作不可恢复。`)) {
      return
    }

    try {
      // 在实际项目中，这里应该调用API
      setRoles(prev => prev.filter(r => r.id !== role.id))
    } catch (error) {
      setError('删除失败，请稍后重试')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const getPermissionModules = (permissions: string[]) => {
    const modules = new Set<string>()
    permissions.forEach(permission => {
      const [module] = permission.split(':')
      modules.add(module)
    })
    return Array.from(modules)
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-48"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">角色权限管理</h1>
          <p className="text-gray-600">管理系统角色和权限配置</p>
        </div>
        <Button
          onClick={handleCreateRole}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          新增角色
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>角色列表</CardTitle>
          <CardDescription>系统中所有角色的信息和权限配置</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="w-4 h-4 text-gray-400" />
            <Input
              placeholder="搜索角色名称或描述..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>角色信息</TableHead>
                  <TableHead>权限模块</TableHead>
                  <TableHead>用户数量</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRoles.map(role => (
                  <TableRow key={role.id}>
                    <TableCell>
                      <div className="flex items-center">
                        <Shield className="w-5 h-5 text-blue-500 mr-3" />
                        <div>
                          <div className="font-medium">{role.description}</div>
                          <div className="text-sm text-gray-500">
                            {role.name}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {getPermissionModules(role.permissions)
                          .slice(0, 3)
                          .map(module => (
                            <span
                              key={module}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                            >
                              {module}
                            </span>
                          ))}
                        {getPermissionModules(role.permissions).length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{getPermissionModules(role.permissions).length - 3}{' '}
                            个
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Users className="w-4 h-4 text-gray-400 mr-1" />
                        <span>{role.userCount}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          role.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {role.isActive ? '正常' : '禁用'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">
                        {formatDate(role.createdAt)}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewPermissions(role)}
                        >
                          查看权限
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditRole(role)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteRole(role)}
                          className="text-red-600 hover:text-red-700"
                          disabled={role.userCount > 0}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredRoles.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchTerm ? '没有找到匹配的角色' : '暂无角色数据'}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 创建角色对话框 */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>新增角色</DialogTitle>
            <DialogDescription>创建新的系统角色</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="create-name">角色名称</Label>
              <Input id="create-name" placeholder="请输入角色名称（英文）" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="create-description">角色描述</Label>
              <Input
                id="create-description"
                placeholder="请输入角色描述（中文）"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsCreateDialogOpen(false)}
              >
                取消
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700">
                创建角色
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 编辑角色对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑角色</DialogTitle>
            <DialogDescription>修改角色信息和权限配置</DialogDescription>
          </DialogHeader>
          {selectedRole && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">角色名称</Label>
                <Input
                  id="edit-name"
                  defaultValue={selectedRole.name}
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-description">角色描述</Label>
                <Input
                  id="edit-description"
                  defaultValue={selectedRole.description}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  取消
                </Button>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  保存修改
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 权限详情对话框 */}
      <Dialog
        open={isPermissionDialogOpen}
        onOpenChange={setIsPermissionDialogOpen}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>角色权限详情</DialogTitle>
            <DialogDescription>
              {selectedRole?.description} 的权限配置
            </DialogDescription>
          </DialogHeader>
          {selectedRole && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>角色名称</Label>
                  <div className="text-sm text-gray-600">
                    {selectedRole.name}
                  </div>
                </div>
                <div>
                  <Label>权限数量</Label>
                  <div className="text-sm text-gray-600">
                    {selectedRole.permissions.length} 个
                  </div>
                </div>
              </div>

              <div>
                <Label>权限列表</Label>
                <div className="mt-2 max-h-60 overflow-y-auto border rounded-md p-3">
                  <div className="grid grid-cols-1 gap-2">
                    {selectedRole.permissions.map(permission => (
                      <div
                        key={permission}
                        className="flex items-center justify-between py-1 px-2 bg-gray-50 rounded text-sm"
                      >
                        <span>{permission}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => setIsPermissionDialogOpen(false)}
                >
                  关闭
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
