'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { ReservationForm } from '@/components/reservations/ReservationForm'
import { useToast } from '@/hooks/use-toast'
import { useReservations } from '@/hooks/use-reservations'
import {
  Calendar as CalendarIcon,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Eye,
  RefreshCw,
  Download,
  Phone,
  User,
  Home,
  Clock,
  AlertCircle,
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { DateRange } from 'react-day-picker'

export default function ReservationsPage() {
  const { toast } = useToast()
  const { getReservations, deleteReservation, isLoading } = useReservations()

  // 状态管理
  const [data, setData] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [roomFilter, setRoomFilter] = useState('')
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [sortBy, setSortBy] = useState('createdAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  // 加载数据
  const loadData = async () => {
    try {
      const params = {
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
        status: statusFilter || undefined,
        roomId: roomFilter || undefined,
        startDate: dateRange?.from
          ? format(dateRange.from, 'yyyy-MM-dd')
          : undefined,
        endDate: dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined,
        sortBy,
        sortOrder,
      }

      const result = await getReservations(params)
      setData(result)
    } catch (error) {
      toast({
        title: '加载失败',
        description: '获取预订列表失败，请重试',
        variant: 'destructive',
      })
    }
  }

  // 初始化加载
  useEffect(() => {
    loadData()
  }, [
    currentPage,
    pageSize,
    statusFilter,
    roomFilter,
    dateRange,
    sortBy,
    sortOrder,
  ])

  // 搜索处理
  const handleSearch = () => {
    setCurrentPage(1)
    loadData()
  }

  // 重置筛选
  const handleResetFilters = () => {
    setSearchTerm('')
    setStatusFilter('')
    setRoomFilter('')
    setDateRange(undefined)
    setCurrentPage(1)
  }

  // 删除预订
  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这个预订记录吗？')) {
      return
    }

    try {
      await deleteReservation(id)
      toast({
        title: '删除成功',
        description: '预订记录已删除',
      })
      loadData()
    } catch (error) {
      toast({
        title: '删除失败',
        description: '删除预订记录失败，请重试',
        variant: 'destructive',
      })
    }
  }

  // 获取状态样式
  const getStatusBadge = (status: number, statusName: string) => {
    const variants = {
      1: 'bg-yellow-100 text-yellow-800',
      2: 'bg-blue-100 text-blue-800',
      3: 'bg-green-100 text-green-800',
      4: 'bg-red-100 text-red-800',
      5: 'bg-gray-100 text-gray-800',
    }
    return (
      <Badge
        className={
          variants[status as keyof typeof variants] ||
          'bg-gray-100 text-gray-800'
        }
      >
        {statusName}
      </Badge>
    )
  }

  // 获取预订类型样式
  const getTypeBadge = (type: number, typeName: string) => {
    const variants = {
      1: 'bg-purple-100 text-purple-800',
      2: 'bg-orange-100 text-orange-800',
      3: 'bg-cyan-100 text-cyan-800',
    }
    return (
      <Badge
        variant="outline"
        className={
          variants[type as keyof typeof variants] || 'bg-gray-100 text-gray-800'
        }
      >
        {typeName}
      </Badge>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题和操作栏 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <CalendarIcon className="h-8 w-8 text-blue-600" />
            预订管理
          </h1>
          <p className="text-gray-600 mt-2">
            管理房间预订记录，跟踪预订状态和入住流程
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={loadData}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
            />
            刷新
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            导出
          </Button>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                新建预订
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>新建预订</DialogTitle>
              </DialogHeader>
              <ReservationForm
                onSuccess={() => {
                  setShowCreateDialog(false)
                  loadData()
                  toast({
                    title: '创建成功',
                    description: '预订记录已创建',
                  })
                }}
                onCancel={() => setShowCreateDialog(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </motion.div>

      {/* 筛选条件 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              筛选条件
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* 搜索框 */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  搜索
                </label>
                <div className="flex gap-2">
                  <Input
                    placeholder="预订号、联系人、电话..."
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    onKeyPress={e => e.key === 'Enter' && handleSearch()}
                  />
                  <Button size="sm" onClick={handleSearch}>
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* 状态筛选 */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  预订状态
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部状态</SelectItem>
                    <SelectItem value="1">待确认</SelectItem>
                    <SelectItem value="2">已确认</SelectItem>
                    <SelectItem value="3">已入住</SelectItem>
                    <SelectItem value="4">已取消</SelectItem>
                    <SelectItem value="5">已过期</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 日期范围 */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  预订日期
                </label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange?.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, 'MM/dd', { locale: zhCN })}{' '}
                            - {format(dateRange.to, 'MM/dd', { locale: zhCN })}
                          </>
                        ) : (
                          format(dateRange.from, 'MM/dd', { locale: zhCN })
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange?.from}
                      selected={dateRange}
                      onSelect={setDateRange}
                      numberOfMonths={2}
                      locale={zhCN}
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* 操作按钮 */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  操作
                </label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetFilters}
                  >
                    重置
                  </Button>
                  <Select
                    value={`${pageSize}`}
                    onValueChange={value => setPageSize(parseInt(value))}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 预订列表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>预订列表</span>
              {data?.pagination && (
                <span className="text-sm font-normal text-gray-500">
                  共 {data.pagination.total} 条记录
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="h-20 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : data?.list?.length > 0 ? (
              <div className="space-y-4">
                {data.list.map((reservation: any, index: number) => (
                  <motion.div
                    key={reservation.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-4">
                          <h3 className="font-medium text-gray-900">
                            {reservation.reservationNumber}
                          </h3>
                          {getStatusBadge(
                            reservation.status,
                            reservation.statusName
                          )}
                          {getTypeBadge(
                            reservation.reservationType,
                            reservation.reservationTypeName
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            <span>{reservation.contactName}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4" />
                            <span>{reservation.contactPhone}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Home className="h-4 w-4" />
                            <span>
                              {reservation.room?.roomNumber || '未分配'}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            <span>
                              {format(
                                new Date(reservation.expectedCheckInDate),
                                'MM/dd',
                                { locale: zhCN }
                              )}
                              {reservation.expectedCheckOutDate &&
                                ` - ${format(new Date(reservation.expectedCheckOutDate), 'MM/dd', { locale: zhCN })}`}
                            </span>
                          </div>
                        </div>

                        {reservation.elderInfo && (
                          <div className="text-sm text-gray-600">
                            <span>
                              入住老人：{reservation.elderInfo.name} (
                              {reservation.elderInfo.age}岁,{' '}
                              {reservation.elderInfo.genderName})
                            </span>
                          </div>
                        )}

                        {reservation.specialRequirements && (
                          <div className="text-sm text-gray-600">
                            <span>
                              特殊要求：{reservation.specialRequirements}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(reservation.id)}
                          disabled={reservation.status === 3} // 已入住不能删除
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <CalendarIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>暂无预订记录</p>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* 分页 */}
      {data?.pagination && data.pagination.totalPages > 1 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center"
        >
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              上一页
            </Button>
            <span className="text-sm text-gray-600">
              第 {currentPage} 页，共 {data.pagination.totalPages} 页
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentPage(
                  Math.min(data.pagination.totalPages, currentPage + 1)
                )
              }
              disabled={currentPage === data.pagination.totalPages}
            >
              下一页
            </Button>
          </div>
        </motion.div>
      )}
    </div>
  )
}
