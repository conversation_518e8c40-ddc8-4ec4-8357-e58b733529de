import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  admissionApplications, 
  admissionAssessments,
  admissionReviews,
  admissionContracts,
  admissionPayments,
  roomAssignments,
  elderInfo, 
  reservations, 
  betterAuthUsers,
  rooms
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, desc } from 'drizzle-orm'

// 入住申请更新验证 schema
const updateAdmissionApplicationSchema = z.object({
  applicantName: z.string().min(1, '请输入申请人姓名').optional(),
  applicantPhone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码').optional(),
  applicantRelation: z.string().min(1, '请输入与老人关系').optional(),
  applicantIdCard: z.string().optional(),
  elderName: z.string().min(1, '请输入老人姓名').optional(),
  elderAge: z.number().min(1).max(150, '请输入有效的年龄').optional(),
  elderGender: z.number().min(1).max(2, '请选择性别').optional(),
  elderIdCard: z.string().optional(),
  elderPhone: z.string().optional(),
  emergencyContactName: z.string().min(1, '请输入紧急联系人姓名').optional(),
  emergencyContactPhone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码').optional(),
  emergencyContactRelation: z.string().min(1, '请输入紧急联系人关系').optional(),
  medicalHistory: z.string().optional(),
  currentMedications: z.string().optional(),
  allergies: z.string().optional(),
  specialNeeds: z.string().optional(),
  preferredRoomType: z.number().min(1).max(4).optional(),
  expectedAdmissionDate: z.string().optional(),
  status: z.number().min(1).max(8).optional(),
  notes: z.string().optional(),
  attachments: z.array(z.string()).optional(),
})

// GET - 获取单个入住申请详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const applicationId = params.id

    // 获取申请详情
    const applicationResult = await db
      .select({
        id: admissionApplications.id,
        applicationNumber: admissionApplications.applicationNumber,
        elderInfoId: admissionApplications.elderInfoId,
        reservationId: admissionApplications.reservationId,
        applicantName: admissionApplications.applicantName,
        applicantPhone: admissionApplications.applicantPhone,
        applicantRelation: admissionApplications.applicantRelation,
        applicantIdCard: admissionApplications.applicantIdCard,
        elderName: admissionApplications.elderName,
        elderAge: admissionApplications.elderAge,
        elderGender: admissionApplications.elderGender,
        elderIdCard: admissionApplications.elderIdCard,
        elderPhone: admissionApplications.elderPhone,
        emergencyContactName: admissionApplications.emergencyContactName,
        emergencyContactPhone: admissionApplications.emergencyContactPhone,
        emergencyContactRelation: admissionApplications.emergencyContactRelation,
        medicalHistory: admissionApplications.medicalHistory,
        currentMedications: admissionApplications.currentMedications,
        allergies: admissionApplications.allergies,
        specialNeeds: admissionApplications.specialNeeds,
        preferredRoomType: admissionApplications.preferredRoomType,
        expectedAdmissionDate: admissionApplications.expectedAdmissionDate,
        status: admissionApplications.status,
        applicationDate: admissionApplications.applicationDate,
        notes: admissionApplications.notes,
        attachments: admissionApplications.attachments,
        createdAt: admissionApplications.createdAt,
        updatedAt: admissionApplications.updatedAt,
        // 关联数据
        elderInfo: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
          careLevel: elderInfo.careLevel,
          roomId: elderInfo.roomId,
          status: elderInfo.status,
        },
        reservation: {
          id: reservations.id,
          reservationNumber: reservations.reservationNumber,
          roomId: reservations.roomId,
          status: reservations.status,
          expectedCheckInDate: reservations.expectedCheckInDate,
        },
        createdByUser: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(admissionApplications)
      .leftJoin(elderInfo, eq(admissionApplications.elderInfoId, elderInfo.id))
      .leftJoin(reservations, eq(admissionApplications.reservationId, reservations.id))
      .leftJoin(betterAuthUsers, eq(admissionApplications.createdBy, betterAuthUsers.id))
      .where(eq(admissionApplications.id, applicationId))
      .limit(1)

    if (applicationResult.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '入住申请不存在' },
        { status: 404 }
      )
    }

    const application = applicationResult[0]

    // 解析附件
    if (application.attachments) {
      try {
        application.attachments = JSON.parse(application.attachments)
      } catch {
        application.attachments = []
      }
    }

    // 获取评估记录
    const assessments = await db
      .select({
        id: admissionAssessments.id,
        assessmentType: admissionAssessments.assessmentType,
        assessmentDate: admissionAssessments.assessmentDate,
        physicalCondition: admissionAssessments.physicalCondition,
        mentalCondition: admissionAssessments.mentalCondition,
        mobilityLevel: admissionAssessments.mobilityLevel,
        careLevel: admissionAssessments.careLevel,
        fallRisk: admissionAssessments.fallRisk,
        cognitiveRisk: admissionAssessments.cognitiveRisk,
        behaviorRisk: admissionAssessments.behaviorRisk,
        overallScore: admissionAssessments.overallScore,
        recommendedCareLevel: admissionAssessments.recommendedCareLevel,
        suitabilityAssessment: admissionAssessments.suitabilityAssessment,
        recommendations: admissionAssessments.recommendations,
        status: admissionAssessments.status,
        createdAt: admissionAssessments.createdAt,
        assessor: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(admissionAssessments)
      .leftJoin(betterAuthUsers, eq(admissionAssessments.assessorId, betterAuthUsers.id))
      .where(eq(admissionAssessments.applicationId, applicationId))
      .orderBy(desc(admissionAssessments.createdAt))

    // 获取审核记录
    const reviews = await db
      .select({
        id: admissionReviews.id,
        reviewLevel: admissionReviews.reviewLevel,
        reviewDate: admissionReviews.reviewDate,
        reviewResult: admissionReviews.reviewResult,
        reviewComments: admissionReviews.reviewComments,
        conditions: admissionReviews.conditions,
        createdAt: admissionReviews.createdAt,
        reviewer: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(admissionReviews)
      .leftJoin(betterAuthUsers, eq(admissionReviews.reviewerId, betterAuthUsers.id))
      .where(eq(admissionReviews.applicationId, applicationId))
      .orderBy(desc(admissionReviews.createdAt))

    // 获取合同信息
    const contracts = await db
      .select({
        id: admissionContracts.id,
        contractNumber: admissionContracts.contractNumber,
        contractType: admissionContracts.contractType,
        status: admissionContracts.status,
        signDate: admissionContracts.signDate,
        effectiveDate: admissionContracts.effectiveDate,
        expiryDate: admissionContracts.expiryDate,
        createdAt: admissionContracts.createdAt,
      })
      .from(admissionContracts)
      .where(eq(admissionContracts.applicationId, applicationId))
      .orderBy(desc(admissionContracts.createdAt))

    // 获取缴费记录
    const payments = await db
      .select({
        id: admissionPayments.id,
        paymentNumber: admissionPayments.paymentNumber,
        paymentType: admissionPayments.paymentType,
        totalAmount: admissionPayments.totalAmount,
        actualAmount: admissionPayments.actualAmount,
        paymentStatus: admissionPayments.paymentStatus,
        paymentDate: admissionPayments.paymentDate,
        paymentMethod: admissionPayments.paymentMethod,
        createdAt: admissionPayments.createdAt,
      })
      .from(admissionPayments)
      .where(eq(admissionPayments.applicationId, applicationId))
      .orderBy(desc(admissionPayments.createdAt))

    // 获取房间分配记录
    const roomAssignmentRecords = await db
      .select({
        id: roomAssignments.id,
        roomId: roomAssignments.roomId,
        bedNumber: roomAssignments.bedNumber,
        assignmentType: roomAssignments.assignmentType,
        assignmentDate: roomAssignments.assignmentDate,
        effectiveDate: roomAssignments.effectiveDate,
        status: roomAssignments.status,
        confirmDate: roomAssignments.confirmDate,
        createdAt: roomAssignments.createdAt,
        room: {
          id: rooms.id,
          roomNumber: rooms.roomNumber,
          roomType: rooms.roomType,
          floor: rooms.floor,
          capacity: rooms.capacity,
          currentOccupancy: rooms.currentOccupancy,
        },
        assignedByUser: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(roomAssignments)
      .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
      .leftJoin(betterAuthUsers, eq(roomAssignments.assignedBy, betterAuthUsers.id))
      .where(eq(roomAssignments.applicationId, applicationId))
      .orderBy(desc(roomAssignments.createdAt))

    return NextResponse.json({
      success: true,
      data: {
        ...application,
        assessments,
        reviews,
        contracts,
        payments,
        roomAssignments: roomAssignmentRecords,
      },
    })
  } catch (error) {
    console.error('获取入住申请详情失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// PUT - 更新入住申请
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const applicationId = params.id
    const body = await request.json()
    const validatedData = updateAdmissionApplicationSchema.parse(body)

    // 检查申请是否存在
    const existingApplication = await db
      .select()
      .from(admissionApplications)
      .where(eq(admissionApplications.id, applicationId))
      .limit(1)

    if (existingApplication.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '入住申请不存在' },
        { status: 404 }
      )
    }

    // 检查是否可以更新
    const currentStatus = existingApplication[0].status
    if (currentStatus === 7 || currentStatus === 8) { // 已入住或已取消
      return NextResponse.json(
        { 
          success: false, 
          code: 'CANNOT_UPDATE', 
          message: '该申请已完成或已取消，无法更新' 
        },
        { status: 400 }
      )
    }

    // 更新申请
    const updateData: any = { ...validatedData }
    if (validatedData.attachments) {
      updateData.attachments = JSON.stringify(validatedData.attachments)
    }

    const updatedApplication = await db
      .update(admissionApplications)
      .set({
        ...updateData,
        updatedAt: new Date(),
      })
      .where(eq(admissionApplications.id, applicationId))
      .returning()

    return NextResponse.json({
      success: true,
      data: updatedApplication[0],
      message: '入住申请更新成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('更新入住申请失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// DELETE - 删除入住申请
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const applicationId = params.id

    // 检查申请是否存在
    const existingApplication = await db
      .select()
      .from(admissionApplications)
      .where(eq(admissionApplications.id, applicationId))
      .limit(1)

    if (existingApplication.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '入住申请不存在' },
        { status: 404 }
      )
    }

    // 检查是否可以删除
    const currentStatus = existingApplication[0].status
    if (currentStatus === 7) { // 已入住
      return NextResponse.json(
        { 
          success: false, 
          code: 'CANNOT_DELETE', 
          message: '该申请已入住，无法删除' 
        },
        { status: 400 }
      )
    }

    // 删除申请（级联删除相关记录）
    await db
      .delete(admissionApplications)
      .where(eq(admissionApplications.id, applicationId))

    return NextResponse.json({
      success: true,
      message: '入住申请删除成功',
    })
  } catch (error) {
    console.error('删除入住申请失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
