import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  admissionApplications, 
  admissionReviews,
  elderInfo,
  reservations,
  betterAuthUsers
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and } from 'drizzle-orm'
import { format } from 'date-fns'

// 状态更新验证 schema
const updateStatusSchema = z.object({
  status: z.number().min(1).max(8, '无效的状态值'),
  reviewComments: z.string().optional(),
  conditions: z.string().optional(),
  nextReviewerId: z.string().optional(),
})

// 状态流转规则
const statusTransitions: Record<number, number[]> = {
  1: [2, 6, 8], // 待评估 -> 评估中, 已拒绝, 已取消
  2: [3, 6, 8], // 评估中 -> 待审核, 已拒绝, 已取消
  3: [4, 6, 8], // 待审核 -> 审核中, 已拒绝, 已取消
  4: [5, 6, 8], // 审核中 -> 已通过, 已拒绝, 已取消
  5: [7, 8],    // 已通过 -> 已入住, 已取消
  6: [1, 8],    // 已拒绝 -> 待评估(重新申请), 已取消
  7: [],        // 已入住 -> 无法变更
  8: [],        // 已取消 -> 无法变更
}

// 状态名称映射
const statusNames: Record<number, string> = {
  1: '待评估',
  2: '评估中',
  3: '待审核',
  4: '审核中',
  5: '已通过',
  6: '已拒绝',
  7: '已入住',
  8: '已取消',
}

// 获取审核级别
function getReviewLevel(fromStatus: number, toStatus: number): number {
  if (fromStatus === 3 && toStatus === 4) return 1 // 初审
  if (fromStatus === 4 && toStatus === 5) return 2 // 复审
  if (fromStatus === 4 && toStatus === 6) return 2 // 拒绝
  return 1
}

// PUT - 更新入住申请状态
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const applicationId = params.id
    const body = await request.json()
    const validatedData = updateStatusSchema.parse(body)

    // 检查申请是否存在
    const existingApplication = await db
      .select()
      .from(admissionApplications)
      .where(eq(admissionApplications.id, applicationId))
      .limit(1)

    if (existingApplication.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '入住申请不存在' },
        { status: 404 }
      )
    }

    const currentStatus = existingApplication[0].status
    const newStatus = validatedData.status

    // 检查状态流转是否合法
    if (!statusTransitions[currentStatus]?.includes(newStatus)) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_STATUS_TRANSITION', 
          message: `无法从"${statusNames[currentStatus]}"变更为"${statusNames[newStatus]}"` 
        },
        { status: 400 }
      )
    }

    // 开始事务
    await db.transaction(async (tx) => {
      // 更新申请状态
      await tx
        .update(admissionApplications)
        .set({
          status: newStatus,
          updatedAt: new Date(),
        })
        .where(eq(admissionApplications.id, applicationId))

      // 如果是审核相关的状态变更，记录审核日志
      if ([4, 5, 6].includes(newStatus)) {
        const reviewLevel = getReviewLevel(currentStatus, newStatus)
        let reviewResult: number

        if (newStatus === 5) reviewResult = 1 // 通过
        else if (newStatus === 6) reviewResult = 2 // 拒绝
        else reviewResult = 4 // 转下级审核

        await tx
          .insert(admissionReviews)
          .values({
            applicationId,
            reviewLevel,
            reviewerId: user.id,
            reviewDate: format(new Date(), 'yyyy-MM-dd'),
            reviewResult,
            reviewComments: validatedData.reviewComments || '',
            conditions: validatedData.conditions || '',
            nextReviewerId: validatedData.nextReviewerId || null,
          })
      }

      // 如果状态变更为已入住，需要处理相关业务逻辑
      if (newStatus === 7) {
        // 如果有关联的预订，更新预订状态为已入住
        if (existingApplication[0].reservationId) {
          await tx
            .update(reservations)
            .set({
              status: 3, // 已入住
              actualCheckInDate: format(new Date(), 'yyyy-MM-dd'),
              updatedAt: new Date(),
            })
            .where(eq(reservations.id, existingApplication[0].reservationId))
        }

        // 如果有关联的老人信息，更新老人状态
        if (existingApplication[0].elderInfoId) {
          await tx
            .update(elderInfo)
            .set({
              status: 1, // 在院
              admissionDate: format(new Date(), 'yyyy-MM-dd'),
              updatedAt: new Date(),
            })
            .where(eq(elderInfo.id, existingApplication[0].elderInfoId))
        }
      }
    })

    // 获取更新后的申请信息
    const updatedApplication = await db
      .select({
        id: admissionApplications.id,
        applicationNumber: admissionApplications.applicationNumber,
        elderName: admissionApplications.elderName,
        applicantName: admissionApplications.applicantName,
        status: admissionApplications.status,
        updatedAt: admissionApplications.updatedAt,
      })
      .from(admissionApplications)
      .where(eq(admissionApplications.id, applicationId))
      .limit(1)

    return NextResponse.json({
      success: true,
      data: updatedApplication[0],
      message: `申请状态已更新为"${statusNames[newStatus]}"`,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('更新申请状态失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// GET - 获取状态流转历史
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const applicationId = params.id

    // 检查申请是否存在
    const existingApplication = await db
      .select()
      .from(admissionApplications)
      .where(eq(admissionApplications.id, applicationId))
      .limit(1)

    if (existingApplication.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '入住申请不存在' },
        { status: 404 }
      )
    }

    // 获取审核历史
    const reviewHistory = await db
      .select({
        id: admissionReviews.id,
        reviewLevel: admissionReviews.reviewLevel,
        reviewDate: admissionReviews.reviewDate,
        reviewResult: admissionReviews.reviewResult,
        reviewComments: admissionReviews.reviewComments,
        conditions: admissionReviews.conditions,
        createdAt: admissionReviews.createdAt,
        reviewer: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(admissionReviews)
      .leftJoin(betterAuthUsers, eq(admissionReviews.reviewerId, betterAuthUsers.id))
      .where(eq(admissionReviews.applicationId, applicationId))
      .orderBy(admissionReviews.createdAt)

    // 构建状态流转时间线
    const statusTimeline = [
      {
        status: 1,
        statusName: '待评估',
        timestamp: existingApplication[0].createdAt,
        description: '申请已提交',
      },
    ]

    // 根据审核历史构建时间线
    reviewHistory.forEach((review) => {
      let status: number
      let statusName: string
      let description: string

      switch (review.reviewResult) {
        case 1: // 通过
          if (review.reviewLevel === 1) {
            status = 5
            statusName = '初审通过'
            description = '初审已通过'
          } else {
            status = 5
            statusName = '复审通过'
            description = '复审已通过'
          }
          break
        case 2: // 拒绝
          status = 6
          statusName = '审核拒绝'
          description = review.reviewComments || '审核未通过'
          break
        case 3: // 需要补充材料
          status = 3
          statusName = '待补充材料'
          description = review.reviewComments || '需要补充材料'
          break
        case 4: // 转下级审核
          status = 4
          statusName = '转下级审核'
          description = '已转交下级审核'
          break
        default:
          return
      }

      statusTimeline.push({
        status,
        statusName,
        timestamp: review.createdAt,
        description,
        reviewer: review.reviewer,
        reviewComments: review.reviewComments,
      })
    })

    // 如果当前状态是已入住，添加入住记录
    if (existingApplication[0].status === 7) {
      statusTimeline.push({
        status: 7,
        statusName: '已入住',
        timestamp: existingApplication[0].updatedAt,
        description: '已成功入住',
      })
    }

    // 获取当前可用的状态转换选项
    const currentStatus = existingApplication[0].status
    const availableTransitions = statusTransitions[currentStatus]?.map(status => ({
      status,
      statusName: statusNames[status],
    })) || []

    return NextResponse.json({
      success: true,
      data: {
        currentStatus,
        currentStatusName: statusNames[currentStatus],
        statusTimeline,
        availableTransitions,
        reviewHistory,
      },
    })
  } catch (error) {
    console.error('获取状态流转历史失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
