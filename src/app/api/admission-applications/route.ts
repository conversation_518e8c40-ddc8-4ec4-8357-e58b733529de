import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { admissionApplications, elderInfo, reservations, betterAuthUsers } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, like, desc, asc, count, gte, lte } from 'drizzle-orm'
import { format } from 'date-fns'

// 入住申请创建验证 schema
const createAdmissionApplicationSchema = z.object({
  reservationId: z.string().optional(),
  elderInfoId: z.string().optional(),
  applicantName: z.string().min(1, '请输入申请人姓名'),
  applicantPhone: z.string().min(1, '请输入申请人电话').regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码'),
  applicantRelation: z.string().min(1, '请输入与老人关系'),
  applicantIdCard: z.string().optional(),
  elderName: z.string().min(1, '请输入老人姓名'),
  elderAge: z.number().min(1).max(150, '请输入有效的年龄'),
  elderGender: z.number().min(1).max(2, '请选择性别'),
  elderIdCard: z.string().optional(),
  elderPhone: z.string().optional(),
  emergencyContactName: z.string().min(1, '请输入紧急联系人姓名'),
  emergencyContactPhone: z.string().min(1, '请输入紧急联系人电话').regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码'),
  emergencyContactRelation: z.string().min(1, '请输入紧急联系人关系'),
  medicalHistory: z.string().optional(),
  currentMedications: z.string().optional(),
  allergies: z.string().optional(),
  specialNeeds: z.string().optional(),
  preferredRoomType: z.number().min(1).max(4).optional(),
  expectedAdmissionDate: z.string().min(1, '请选择预期入住日期'),
  notes: z.string().optional(),
  attachments: z.array(z.string()).optional(),
})

// 入住申请更新验证 schema
const updateAdmissionApplicationSchema = createAdmissionApplicationSchema.partial().extend({
  status: z.number().min(1).max(8).optional(),
})

// 生成申请编号
function generateApplicationNumber(): string {
  const now = new Date()
  const dateStr = format(now, 'yyyyMMdd')
  const timeStr = format(now, 'HHmmss')
  return `APP${dateStr}${timeStr}`
}

// GET - 获取入住申请列表
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const offset = (page - 1) * limit
    const limitNum = Math.min(limit, 100)

    // 构建查询条件
    const whereConditions = []

    if (search) {
      whereConditions.push(
        or(
          like(admissionApplications.applicationNumber, `%${search}%`),
          like(admissionApplications.elderName, `%${search}%`),
          like(admissionApplications.applicantName, `%${search}%`),
          like(admissionApplications.applicantPhone, `%${search}%`)
        )
      )
    }

    if (status) {
      whereConditions.push(eq(admissionApplications.status, parseInt(status)))
    }

    if (startDate) {
      whereConditions.push(gte(admissionApplications.applicationDate, startDate))
    }

    if (endDate) {
      whereConditions.push(lte(admissionApplications.applicationDate, endDate))
    }

    // 排序
    const orderBy = sortOrder === 'asc' 
      ? asc(admissionApplications[sortBy as keyof typeof admissionApplications] || admissionApplications.createdAt)
      : desc(admissionApplications[sortBy as keyof typeof admissionApplications] || admissionApplications.createdAt)

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(admissionApplications)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 获取申请列表
    const applicationList = await db
      .select({
        id: admissionApplications.id,
        applicationNumber: admissionApplications.applicationNumber,
        elderName: admissionApplications.elderName,
        elderAge: admissionApplications.elderAge,
        elderGender: admissionApplications.elderGender,
        applicantName: admissionApplications.applicantName,
        applicantPhone: admissionApplications.applicantPhone,
        applicantRelation: admissionApplications.applicantRelation,
        expectedAdmissionDate: admissionApplications.expectedAdmissionDate,
        status: admissionApplications.status,
        applicationDate: admissionApplications.applicationDate,
        createdAt: admissionApplications.createdAt,
        updatedAt: admissionApplications.updatedAt,
        // 关联数据
        elderInfo: {
          id: elderInfo.id,
          name: elderInfo.name,
          roomId: elderInfo.roomId,
        },
        reservation: {
          id: reservations.id,
          reservationNumber: reservations.reservationNumber,
          status: reservations.status,
        },
        createdByUser: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(admissionApplications)
      .leftJoin(elderInfo, eq(admissionApplications.elderInfoId, elderInfo.id))
      .leftJoin(reservations, eq(admissionApplications.reservationId, reservations.id))
      .leftJoin(betterAuthUsers, eq(admissionApplications.createdBy, betterAuthUsers.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limitNum)
      .offset(offset)

    return NextResponse.json({
      success: true,
      data: {
        list: applicationList,
        pagination: {
          page,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      },
    })
  } catch (error) {
    console.error('获取入住申请列表失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// POST - 创建入住申请
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createAdmissionApplicationSchema.parse(body)

    // 检查是否已存在相同老人的待处理申请
    const existingApplication = await db
      .select()
      .from(admissionApplications)
      .where(
        and(
          eq(admissionApplications.elderName, validatedData.elderName),
          eq(admissionApplications.elderIdCard, validatedData.elderIdCard || ''),
          or(
            eq(admissionApplications.status, 1), // 待评估
            eq(admissionApplications.status, 2), // 评估中
            eq(admissionApplications.status, 3), // 待审核
            eq(admissionApplications.status, 4), // 审核中
            eq(admissionApplications.status, 5)  // 已通过
          )
        )
      )
      .limit(1)

    if (existingApplication.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'APPLICATION_EXISTS', 
          message: '该老人已有待处理的入住申请' 
        },
        { status: 400 }
      )
    }

    // 如果关联了预订，检查预订状态
    if (validatedData.reservationId) {
      const reservation = await db
        .select()
        .from(reservations)
        .where(eq(reservations.id, validatedData.reservationId))
        .limit(1)

      if (reservation.length === 0) {
        return NextResponse.json(
          { success: false, code: 'RESERVATION_NOT_FOUND', message: '预订记录不存在' },
          { status: 404 }
        )
      }

      if (reservation[0].status !== 2) { // 已确认
        return NextResponse.json(
          { 
            success: false, 
            code: 'RESERVATION_NOT_CONFIRMED', 
            message: '预订尚未确认，无法创建入住申请' 
          },
          { status: 400 }
        )
      }
    }

    // 创建入住申请
    const applicationNumber = generateApplicationNumber()
    const applicationDate = format(new Date(), 'yyyy-MM-dd')

    const newApplication = await db
      .insert(admissionApplications)
      .values({
        applicationNumber,
        applicationDate,
        ...validatedData,
        attachments: validatedData.attachments ? JSON.stringify(validatedData.attachments) : null,
        createdBy: user.id,
      })
      .returning()

    return NextResponse.json({
      success: true,
      data: newApplication[0],
      message: '入住申请创建成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('创建入住申请失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
