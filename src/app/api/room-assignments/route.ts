import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  roomAssignments, 
  admissionApplications,
  rooms,
  betterAuthUsers 
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, like, desc, asc, count, isNull, inArray } from 'drizzle-orm'
import { format } from 'date-fns'

// 房间分配创建验证 schema
const createAssignmentSchema = z.object({
  applicationId: z.string().min(1, '请选择入住申请'),
  roomId: z.string().min(1, '请选择房间'),
  assignmentDate: z.string().min(1, '请选择分配日期'),
  moveInDate: z.string().optional(),
  notes: z.string().optional(),
  specialRequirements: z.string().optional(),
})

// 房间分配更新验证 schema
const updateAssignmentSchema = createAssignmentSchema.partial().extend({
  status: z.number().min(1).max(4).optional(),
  moveOutDate: z.string().optional(),
  moveOutReason: z.string().optional(),
  confirmDate: z.string().optional(),
})

// 生成分配编号
function generateAssignmentNumber(): string {
  const now = new Date()
  const dateStr = format(now, 'yyyyMMdd')
  const timeStr = format(now, 'HHmmss')
  return `RA${dateStr}${timeStr}`
}

// GET - 获取房间分配列表
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const roomId = searchParams.get('roomId')
    const applicationId = searchParams.get('applicationId')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const offset = (page - 1) * limit
    const limitNum = Math.min(limit, 100)

    // 构建查询条件
    const whereConditions = []

    if (search) {
      whereConditions.push(
        or(
          like(roomAssignments.assignmentNumber, `%${search}%`),
          like(admissionApplications.elderName, `%${search}%`),
          like(admissionApplications.applicantName, `%${search}%`),
          like(rooms.roomNumber, `%${search}%`)
        )
      )
    }

    if (status) {
      whereConditions.push(eq(roomAssignments.status, parseInt(status)))
    }

    if (roomId) {
      whereConditions.push(eq(roomAssignments.roomId, roomId))
    }

    if (applicationId) {
      whereConditions.push(eq(roomAssignments.applicationId, applicationId))
    }

    // 排序
    const orderBy = sortOrder === 'asc' 
      ? asc(roomAssignments[sortBy as keyof typeof roomAssignments] || roomAssignments.createdAt)
      : desc(roomAssignments[sortBy as keyof typeof roomAssignments] || roomAssignments.createdAt)

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(roomAssignments)
      .leftJoin(admissionApplications, eq(roomAssignments.applicationId, admissionApplications.id))
      .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 获取分配列表
    const assignmentList = await db
      .select({
        id: roomAssignments.id,
        applicationId: roomAssignments.applicationId,
        roomId: roomAssignments.roomId,
        assignmentNumber: roomAssignments.assignmentNumber,
        assignmentDate: roomAssignments.assignmentDate,
        moveInDate: roomAssignments.moveInDate,
        moveOutDate: roomAssignments.moveOutDate,
        moveOutReason: roomAssignments.moveOutReason,
        status: roomAssignments.status,
        confirmDate: roomAssignments.confirmDate,
        notes: roomAssignments.notes,
        specialRequirements: roomAssignments.specialRequirements,
        createdAt: roomAssignments.createdAt,
        updatedAt: roomAssignments.updatedAt,
        // 关联数据
        application: {
          id: admissionApplications.id,
          applicationNumber: admissionApplications.applicationNumber,
          elderName: admissionApplications.elderName,
          applicantName: admissionApplications.applicantName,
          applicantPhone: admissionApplications.applicantPhone,
          status: admissionApplications.status,
        },
        room: {
          id: rooms.id,
          roomNumber: rooms.roomNumber,
          roomType: rooms.roomType,
          floor: rooms.floor,
          bedCount: rooms.bedCount,
          status: rooms.status,
        },
        assignedByUser: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(roomAssignments)
      .leftJoin(admissionApplications, eq(roomAssignments.applicationId, admissionApplications.id))
      .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
      .leftJoin(betterAuthUsers, eq(roomAssignments.assignedBy, betterAuthUsers.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limitNum)
      .offset(offset)

    return NextResponse.json({
      success: true,
      data: {
        list: assignmentList,
        pagination: {
          page,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      },
    })
  } catch (error) {
    console.error('获取房间分配列表失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// POST - 创建房间分配
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createAssignmentSchema.parse(body)

    // 检查申请是否存在且状态正确
    const application = await db
      .select()
      .from(admissionApplications)
      .where(eq(admissionApplications.id, validatedData.applicationId))
      .limit(1)

    if (application.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '入住申请不存在' },
        { status: 404 }
      )
    }

    // 检查申请状态（应该是已通过审核的）
    if (![5, 7].includes(application[0].status)) { // 已通过或已入住
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_APPLICATION_STATUS', 
          message: '申请状态不允许分配房间' 
        },
        { status: 400 }
      )
    }

    // 检查房间是否存在且可用
    const room = await db
      .select()
      .from(rooms)
      .where(eq(rooms.id, validatedData.roomId))
      .limit(1)

    if (room.length === 0) {
      return NextResponse.json(
        { success: false, code: 'ROOM_NOT_FOUND', message: '房间不存在' },
        { status: 404 }
      )
    }

    if (room[0].status !== 1) { // 可用
      return NextResponse.json(
        { 
          success: false, 
          code: 'ROOM_NOT_AVAILABLE', 
          message: '房间不可用' 
        },
        { status: 400 }
      )
    }

    // 检查房间是否已被分配（有效分配）
    const existingAssignment = await db
      .select()
      .from(roomAssignments)
      .where(
        and(
          eq(roomAssignments.roomId, validatedData.roomId),
          inArray(roomAssignments.status, [1, 2, 3]) // 待确认、已确认、已入住
        )
      )
      .limit(1)

    if (existingAssignment.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'ROOM_ALREADY_ASSIGNED', 
          message: '房间已被分配' 
        },
        { status: 400 }
      )
    }

    // 检查申请是否已有有效的房间分配
    const existingApplicationAssignment = await db
      .select()
      .from(roomAssignments)
      .where(
        and(
          eq(roomAssignments.applicationId, validatedData.applicationId),
          inArray(roomAssignments.status, [1, 2, 3]) // 待确认、已确认、已入住
        )
      )
      .limit(1)

    if (existingApplicationAssignment.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'APPLICATION_ALREADY_ASSIGNED', 
          message: '该申请已有房间分配' 
        },
        { status: 400 }
      )
    }

    // 生成分配编号
    const assignmentNumber = generateAssignmentNumber()

    // 开始事务处理
    await db.transaction(async (tx) => {
      // 创建房间分配
      const assignmentData = {
        ...validatedData,
        assignmentNumber,
        assignedBy: user.id,
      }

      await tx
        .insert(roomAssignments)
        .values(assignmentData)

      // 更新房间状态为已分配
      await tx
        .update(rooms)
        .set({
          status: 2, // 已分配
          updatedAt: new Date(),
        })
        .where(eq(rooms.id, validatedData.roomId))
    })

    return NextResponse.json({
      success: true,
      message: '房间分配成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('创建房间分配失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
