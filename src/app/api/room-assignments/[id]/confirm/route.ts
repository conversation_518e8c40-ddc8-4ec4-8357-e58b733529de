import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  roomAssignments,
  admissionApplications,
  rooms
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and } from 'drizzle-orm'
import { format } from 'date-fns'

// 确认分配验证 schema
const confirmAssignmentSchema = z.object({
  moveInDate: z.string().min(1, '请选择入住日期'),
  notes: z.string().optional(),
  specialArrangements: z.string().optional(),
})

// POST - 确认房间分配
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const assignmentId = params.id
    const body = await request.json()
    const validatedData = confirmAssignmentSchema.parse(body)

    // 检查分配记录是否存在
    const existingAssignment = await db
      .select({
        id: roomAssignments.id,
        applicationId: roomAssignments.applicationId,
        roomId: roomAssignments.roomId,
        status: roomAssignments.status,
        assignmentDate: roomAssignments.assignmentDate,
        moveInDate: roomAssignments.moveInDate,
        notes: roomAssignments.notes,
        application: {
          id: admissionApplications.id,
          elderName: admissionApplications.elderName,
          applicantName: admissionApplications.applicantName,
          status: admissionApplications.status,
        },
        room: {
          id: rooms.id,
          roomNumber: rooms.roomNumber,
          status: rooms.status,
        },
      })
      .from(roomAssignments)
      .leftJoin(admissionApplications, eq(roomAssignments.applicationId, admissionApplications.id))
      .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
      .where(eq(roomAssignments.id, assignmentId))
      .limit(1)

    if (existingAssignment.length === 0) {
      return NextResponse.json(
        { success: false, code: 'ASSIGNMENT_NOT_FOUND', message: '房间分配记录不存在' },
        { status: 404 }
      )
    }

    const assignment = existingAssignment[0]

    // 检查分配状态是否允许确认
    if (assignment.status !== 1) { // 待确认
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_ASSIGNMENT_STATUS', 
          message: '分配状态不允许确认' 
        },
        { status: 400 }
      )
    }

    // 检查房间是否仍然可用
    if (assignment.room?.status !== 2) { // 已分配
      return NextResponse.json(
        { 
          success: false, 
          code: 'ROOM_NOT_AVAILABLE', 
          message: '房间状态异常，无法确认分配' 
        },
        { status: 400 }
      )
    }

    // 验证入住日期
    const moveInDate = new Date(validatedData.moveInDate)
    const today = new Date()
    const assignmentDate = new Date(assignment.assignmentDate)

    if (moveInDate < assignmentDate) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_MOVE_IN_DATE', 
          message: '入住日期不能早于分配日期' 
        },
        { status: 400 }
      )
    }

    // 开始事务处理
    await db.transaction(async (tx) => {
      // 更新分配记录状态
      await tx
        .update(roomAssignments)
        .set({
          status: 2, // 已确认
          moveInDate: validatedData.moveInDate,
          confirmDate: format(new Date(), 'yyyy-MM-dd'),
          notes: validatedData.notes || assignment.notes,
          specialRequirements: validatedData.specialArrangements,
          updatedAt: new Date(),
        })
        .where(eq(roomAssignments.id, assignmentId))

      // 更新房间状态为已入住（如果入住日期是今天或之前）
      if (moveInDate <= today) {
        await tx
          .update(rooms)
          .set({
            status: 3, // 已入住
            updatedAt: new Date(),
          })
          .where(eq(rooms.id, assignment.roomId))

        // 同时更新分配状态为已入住
        await tx
          .update(roomAssignments)
          .set({
            status: 3, // 已入住
            updatedAt: new Date(),
          })
          .where(eq(roomAssignments.id, assignmentId))

        // 更新申请状态为已入住
        await tx
          .update(admissionApplications)
          .set({
            status: 7, // 已入住
            updatedAt: new Date(),
          })
          .where(eq(admissionApplications.id, assignment.applicationId))
      }
    })

    // 构建响应消息
    let message = '房间分配确认成功'
    if (moveInDate <= today) {
      message += '，老人已入住'
    } else {
      message += `，预计入住日期：${validatedData.moveInDate}`
    }

    return NextResponse.json({
      success: true,
      data: {
        assignmentId,
        status: moveInDate <= today ? 3 : 2, // 已入住或已确认
        moveInDate: validatedData.moveInDate,
        confirmDate: format(new Date(), 'yyyy-MM-dd'),
      },
      message,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('确认房间分配失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// GET - 获取分配确认信息
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const assignmentId = params.id

    // 获取分配详情
    const assignmentResult = await db
      .select({
        id: roomAssignments.id,
        applicationId: roomAssignments.applicationId,
        roomId: roomAssignments.roomId,
        assignmentNumber: roomAssignments.assignmentNumber,
        assignmentDate: roomAssignments.assignmentDate,
        moveInDate: roomAssignments.moveInDate,
        confirmDate: roomAssignments.confirmDate,
        status: roomAssignments.status,
        notes: roomAssignments.notes,
        specialRequirements: roomAssignments.specialRequirements,
        createdAt: roomAssignments.createdAt,
        // 关联数据
        application: {
          id: admissionApplications.id,
          applicationNumber: admissionApplications.applicationNumber,
          elderName: admissionApplications.elderName,
          elderAge: admissionApplications.elderAge,
          elderGender: admissionApplications.elderGender,
          applicantName: admissionApplications.applicantName,
          applicantPhone: admissionApplications.applicantPhone,
          emergencyContact: admissionApplications.emergencyContact,
          emergencyPhone: admissionApplications.emergencyPhone,
          healthCondition: admissionApplications.healthCondition,
          careNeeds: admissionApplications.careNeeds,
          specialRequirements: admissionApplications.specialRequirements,
          status: admissionApplications.status,
        },
        room: {
          id: rooms.id,
          roomNumber: rooms.roomNumber,
          roomType: rooms.roomType,
          floor: rooms.floor,
          area: rooms.area,
          bedCount: rooms.bedCount,
          monthlyRent: rooms.monthlyRent,
          facilities: rooms.facilities,
          orientation: rooms.orientation,
          description: rooms.description,
          status: rooms.status,
        },
      })
      .from(roomAssignments)
      .leftJoin(admissionApplications, eq(roomAssignments.applicationId, admissionApplications.id))
      .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
      .where(eq(roomAssignments.id, assignmentId))
      .limit(1)

    if (assignmentResult.length === 0) {
      return NextResponse.json(
        { success: false, code: 'ASSIGNMENT_NOT_FOUND', message: '房间分配记录不存在' },
        { status: 404 }
      )
    }

    const assignment = assignmentResult[0]

    // 解析房间设施
    if (assignment.room?.facilities) {
      try {
        assignment.room.facilities = JSON.parse(assignment.room.facilities)
      } catch {
        assignment.room.facilities = []
      }
    }

    // 计算状态信息
    const statusInfo = {
      canConfirm: assignment.status === 1, // 待确认
      canMoveIn: assignment.status === 2, // 已确认
      isMovedIn: assignment.status === 3, // 已入住
      isCancelled: assignment.status === 4, // 已取消
    }

    // 计算时间信息
    const timeInfo = {
      assignmentDate: assignment.assignmentDate,
      moveInDate: assignment.moveInDate,
      confirmDate: assignment.confirmDate,
      daysUntilMoveIn: assignment.moveInDate 
        ? Math.ceil((new Date(assignment.moveInDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
        : null,
    }

    return NextResponse.json({
      success: true,
      data: {
        assignment,
        statusInfo,
        timeInfo,
      },
    })
  } catch (error) {
    console.error('获取分配确认信息失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
