import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  rooms,
  admissionApplications,
  admissionAssessments,
  roomAssignments
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, inArray, not, isNull } from 'drizzle-orm'

// 房间推荐请求验证 schema
const recommendRoomSchema = z.object({
  applicationId: z.string().min(1, '请提供申请ID'),
  preferences: z.object({
    roomType: z.number().optional(), // 房间类型偏好
    floor: z.number().optional(), // 楼层偏好
    maxRent: z.number().optional(), // 最大租金
    facilities: z.array(z.string()).optional(), // 设施要求
    quietArea: z.boolean().optional(), // 是否需要安静区域
    nearNursingStation: z.boolean().optional(), // 是否靠近护理站
  }).optional(),
})

// 房间评分算法
function calculateRoomScore(room: any, application: any, assessments: any[], preferences: any = {}): number {
  let score = 100 // 基础分数

  // 1. 护理等级匹配 (权重: 30%)
  const nursingAssessment = assessments.find(a => a.assessmentType === 2) // 护理评估
  if (nursingAssessment) {
    const careLevel = nursingAssessment.careLevel || 1
    
    // 根据护理等级推荐房间类型
    const idealRoomType = careLevel <= 2 ? 1 : careLevel === 3 ? 2 : 3
    if (room.roomType === idealRoomType) {
      score += 30
    } else if (Math.abs(room.roomType - idealRoomType) === 1) {
      score += 15
    } else {
      score -= 15
    }
  }

  // 2. 健康状况匹配 (权重: 25%)
  const healthAssessment = assessments.find(a => a.assessmentType === 1) // 健康评估
  if (healthAssessment) {
    const mobilityLevel = healthAssessment.mobilityLevel || 1
    
    // 行动不便的老人推荐低楼层
    if (mobilityLevel >= 3 && room.floor <= 2) {
      score += 25
    } else if (mobilityLevel >= 3 && room.floor > 3) {
      score -= 20
    }
    
    // 认知风险高的老人推荐靠近护理站
    const cognitiveRisk = healthAssessment.cognitiveRisk || 1
    if (cognitiveRisk >= 2) {
      // 假设房间号末尾为1-5的靠近护理站
      const roomNum = parseInt(room.roomNumber.slice(-1))
      if (roomNum <= 5) {
        score += 15
      }
    }
  }

  // 3. 用户偏好匹配 (权重: 20%)
  if (preferences.roomType && room.roomType === preferences.roomType) {
    score += 20
  }
  
  if (preferences.floor && room.floor === preferences.floor) {
    score += 15
  }
  
  if (preferences.maxRent && room.monthlyRent <= preferences.maxRent) {
    score += 10
  } else if (preferences.maxRent && room.monthlyRent > preferences.maxRent) {
    score -= 30 // 超预算严重扣分
  }

  // 4. 设施匹配 (权重: 15%)
  if (preferences.facilities && preferences.facilities.length > 0) {
    const roomFacilities = room.facilities ? JSON.parse(room.facilities) : []
    const matchedFacilities = preferences.facilities.filter(f => roomFacilities.includes(f))
    const matchRate = matchedFacilities.length / preferences.facilities.length
    score += matchRate * 15
  }

  // 5. 房间条件加分 (权重: 10%)
  // 面积加分
  if (room.area >= 25) score += 5
  if (room.area >= 30) score += 5
  
  // 朝向加分（假设南向最好）
  if (room.orientation === '南') score += 5
  else if (room.orientation === '东南' || room.orientation === '西南') score += 3
  
  // 新装修加分
  if (room.lastRenovation) {
    const renovationDate = new Date(room.lastRenovation)
    const monthsAgo = (Date.now() - renovationDate.getTime()) / (1000 * 60 * 60 * 24 * 30)
    if (monthsAgo <= 12) score += 5 // 一年内装修
  }

  // 6. 特殊需求处理
  if (preferences.quietArea && room.floor >= 3) {
    score += 10 // 高楼层相对安静
  }
  
  if (preferences.nearNursingStation) {
    const roomNum = parseInt(room.roomNumber.slice(-1))
    if (roomNum <= 5) score += 15
  }

  // 确保分数在合理范围内
  return Math.max(0, Math.min(150, score))
}

// GET - 获取房间推荐
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const applicationId = searchParams.get('applicationId')
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!applicationId) {
      return NextResponse.json(
        { success: false, code: 'MISSING_APPLICATION_ID', message: '请提供申请ID' },
        { status: 400 }
      )
    }

    // 获取申请信息
    const application = await db
      .select()
      .from(admissionApplications)
      .where(eq(admissionApplications.id, applicationId))
      .limit(1)

    if (application.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '申请不存在' },
        { status: 404 }
      )
    }

    // 获取评估信息
    const assessments = await db
      .select()
      .from(admissionAssessments)
      .where(eq(admissionAssessments.applicationId, applicationId))

    // 获取已分配的房间ID列表
    const assignedRooms = await db
      .select({ roomId: roomAssignments.roomId })
      .from(roomAssignments)
      .where(inArray(roomAssignments.status, [1, 2, 3])) // 待确认、已确认、已入住

    const assignedRoomIds = assignedRooms.map(r => r.roomId)

    // 获取可用房间
    let availableRoomsQuery = db
      .select()
      .from(rooms)
      .where(eq(rooms.status, 1)) // 可用状态

    // 排除已分配的房间
    if (assignedRoomIds.length > 0) {
      availableRoomsQuery = availableRoomsQuery.where(not(inArray(rooms.id, assignedRoomIds)))
    }

    const availableRooms = await availableRoomsQuery

    if (availableRooms.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          recommendations: [],
          message: '暂无可用房间',
        },
      })
    }

    // 解析用户偏好（如果有的话，可以从申请的特殊要求中提取）
    const preferences: any = {}
    
    // 从申请的特殊要求中提取偏好
    if (application[0].specialRequirements) {
      const requirements = application[0].specialRequirements.toLowerCase()
      
      if (requirements.includes('安静')) preferences.quietArea = true
      if (requirements.includes('护理站') || requirements.includes('监护')) preferences.nearNursingStation = true
      if (requirements.includes('低楼层') || requirements.includes('一楼')) preferences.floor = 1
      if (requirements.includes('单人间')) preferences.roomType = 1
      if (requirements.includes('双人间')) preferences.roomType = 2
    }

    // 计算每个房间的推荐分数
    const roomScores = availableRooms.map(room => ({
      ...room,
      score: calculateRoomScore(room, application[0], assessments, preferences),
      facilities: room.facilities ? JSON.parse(room.facilities) : [],
    }))

    // 按分数排序并取前N个
    const recommendations = roomScores
      .sort((a, b) => b.score - a.score)
      .slice(0, Math.min(limit, 20))
      .map(room => ({
        ...room,
        recommendationReason: generateRecommendationReason(room, application[0], assessments, preferences),
      }))

    // 生成推荐总结
    const summary = {
      totalAvailable: availableRooms.length,
      recommendedCount: recommendations.length,
      averageScore: recommendations.length > 0 
        ? Math.round(recommendations.reduce((sum, r) => sum + r.score, 0) / recommendations.length)
        : 0,
      topScore: recommendations.length > 0 ? recommendations[0].score : 0,
    }

    return NextResponse.json({
      success: true,
      data: {
        recommendations,
        summary,
        applicationInfo: {
          elderName: application[0].elderName,
          specialRequirements: application[0].specialRequirements,
        },
        assessmentSummary: assessments.map(a => ({
          type: a.assessmentType,
          careLevel: a.careLevel,
          overallScore: a.overallScore,
          suitabilityAssessment: a.suitabilityAssessment,
        })),
      },
    })
  } catch (error) {
    console.error('获取房间推荐失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 生成推荐理由
function generateRecommendationReason(room: any, application: any, assessments: any[], preferences: any): string {
  const reasons = []

  // 护理等级匹配
  const nursingAssessment = assessments.find(a => a.assessmentType === 2)
  if (nursingAssessment) {
    const careLevel = nursingAssessment.careLevel || 1
    const idealRoomType = careLevel <= 2 ? 1 : careLevel === 3 ? 2 : 3
    
    if (room.roomType === idealRoomType) {
      const roomTypeNames = { 1: '单人间', 2: '双人间', 3: '多人间' }
      reasons.push(`${roomTypeNames[room.roomType]}适合您的护理等级`)
    }
  }

  // 健康状况匹配
  const healthAssessment = assessments.find(a => a.assessmentType === 1)
  if (healthAssessment) {
    const mobilityLevel = healthAssessment.mobilityLevel || 1
    if (mobilityLevel >= 3 && room.floor <= 2) {
      reasons.push('低楼层便于行动')
    }
    
    const cognitiveRisk = healthAssessment.cognitiveRisk || 1
    if (cognitiveRisk >= 2) {
      const roomNum = parseInt(room.roomNumber.slice(-1))
      if (roomNum <= 5) {
        reasons.push('靠近护理站，便于照护')
      }
    }
  }

  // 设施匹配
  if (room.facilities) {
    const facilities = JSON.parse(room.facilities)
    if (facilities.includes('无障碍设施')) reasons.push('配备无障碍设施')
    if (facilities.includes('独立卫生间')) reasons.push('独立卫生间')
    if (facilities.includes('阳台')) reasons.push('带阳台，采光良好')
  }

  // 房间条件
  if (room.area >= 25) reasons.push('房间面积宽敞')
  if (room.orientation === '南') reasons.push('南向采光好')

  return reasons.length > 0 ? reasons.join('；') : '房间条件良好'
}

// POST - 根据偏好获取推荐
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = recommendRoomSchema.parse(body)

    // 重新调用 GET 逻辑，但使用 POST 中的偏好设置
    const url = new URL(request.url)
    url.searchParams.set('applicationId', validatedData.applicationId)
    
    const getRequest = new NextRequest(url.toString(), { method: 'GET' })
    return await GET(getRequest)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('获取房间推荐失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
