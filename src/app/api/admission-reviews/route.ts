import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  admissionReviews, 
  admissionApplications,
  betterAuthUsers 
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, desc, asc, count, inArray } from 'drizzle-orm'
import { format } from 'date-fns'

// 审核创建验证 schema
const createReviewSchema = z.object({
  applicationId: z.string().min(1, '请选择入住申请'),
  reviewLevel: z.number().min(1).max(3, '请选择审核级别'),
  reviewResult: z.number().min(1).max(4, '请选择审核结果'),
  reviewComments: z.string().min(1, '请输入审核意见'),
  conditions: z.string().optional(),
  nextReviewerId: z.string().optional(),
  attachments: z.array(z.string()).optional(),
})

// 审核更新验证 schema
const updateReviewSchema = createReviewSchema.partial()

// 审核级别名称
const reviewLevelNames: Record<number, string> = {
  1: '初审',
  2: '复审',
  3: '终审',
}

// 审核结果名称
const reviewResultNames: Record<number, string> = {
  1: '通过',
  2: '拒绝',
  3: '需要补充材料',
  4: '转下级审核',
}

// GET - 获取审核列表
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const applicationId = searchParams.get('applicationId')
    const reviewLevel = searchParams.get('reviewLevel')
    const reviewResult = searchParams.get('reviewResult')
    const reviewerId = searchParams.get('reviewerId')
    const pending = searchParams.get('pending') // 是否只显示待审核的
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const offset = (page - 1) * limit
    const limitNum = Math.min(limit, 100)

    // 构建查询条件
    const whereConditions = []

    if (applicationId) {
      whereConditions.push(eq(admissionReviews.applicationId, applicationId))
    }

    if (reviewLevel) {
      whereConditions.push(eq(admissionReviews.reviewLevel, parseInt(reviewLevel)))
    }

    if (reviewResult) {
      whereConditions.push(eq(admissionReviews.reviewResult, parseInt(reviewResult)))
    }

    if (reviewerId) {
      whereConditions.push(eq(admissionReviews.reviewerId, reviewerId))
    }

    // 如果只显示待审核的，需要查找状态为待审核或审核中的申请
    if (pending === 'true') {
      const pendingApplications = await db
        .select({ id: admissionApplications.id })
        .from(admissionApplications)
        .where(inArray(admissionApplications.status, [3, 4])) // 待审核或审核中

      if (pendingApplications.length > 0) {
        whereConditions.push(
          inArray(
            admissionReviews.applicationId, 
            pendingApplications.map(app => app.id)
          )
        )
      } else {
        // 如果没有待审核的申请，返回空列表
        return NextResponse.json({
          success: true,
          data: {
            list: [],
            pagination: {
              page,
              limit: limitNum,
              total: 0,
              totalPages: 0,
            },
          },
        })
      }
    }

    // 排序
    const orderBy = sortOrder === 'asc' 
      ? asc(admissionReviews[sortBy as keyof typeof admissionReviews] || admissionReviews.createdAt)
      : desc(admissionReviews[sortBy as keyof typeof admissionReviews] || admissionReviews.createdAt)

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(admissionReviews)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 获取审核列表
    const reviewList = await db
      .select({
        id: admissionReviews.id,
        applicationId: admissionReviews.applicationId,
        reviewLevel: admissionReviews.reviewLevel,
        reviewDate: admissionReviews.reviewDate,
        reviewResult: admissionReviews.reviewResult,
        reviewComments: admissionReviews.reviewComments,
        conditions: admissionReviews.conditions,
        nextReviewerId: admissionReviews.nextReviewerId,
        attachments: admissionReviews.attachments,
        createdAt: admissionReviews.createdAt,
        // 关联数据
        application: {
          id: admissionApplications.id,
          applicationNumber: admissionApplications.applicationNumber,
          elderName: admissionApplications.elderName,
          applicantName: admissionApplications.applicantName,
          status: admissionApplications.status,
          expectedAdmissionDate: admissionApplications.expectedAdmissionDate,
        },
        reviewer: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(admissionReviews)
      .leftJoin(admissionApplications, eq(admissionReviews.applicationId, admissionApplications.id))
      .leftJoin(betterAuthUsers, eq(admissionReviews.reviewerId, betterAuthUsers.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limitNum)
      .offset(offset)

    // 解析附件字段
    const processedList = reviewList.map(review => ({
      ...review,
      reviewLevelName: reviewLevelNames[review.reviewLevel],
      reviewResultName: reviewResultNames[review.reviewResult],
      attachments: review.attachments ? JSON.parse(review.attachments) : [],
    }))

    return NextResponse.json({
      success: true,
      data: {
        list: processedList,
        pagination: {
          page,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      },
    })
  } catch (error) {
    console.error('获取审核列表失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// POST - 创建审核记录
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createReviewSchema.parse(body)

    // 检查申请是否存在
    const application = await db
      .select()
      .from(admissionApplications)
      .where(eq(admissionApplications.id, validatedData.applicationId))
      .limit(1)

    if (application.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '入住申请不存在' },
        { status: 404 }
      )
    }

    // 检查申请状态是否允许审核
    if (![3, 4].includes(application[0].status)) { // 待审核或审核中
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_APPLICATION_STATUS', 
          message: '申请状态不允许进行审核' 
        },
        { status: 400 }
      )
    }

    // 检查审核级别是否合理
    const existingReviews = await db
      .select()
      .from(admissionReviews)
      .where(eq(admissionReviews.applicationId, validatedData.applicationId))
      .orderBy(desc(admissionReviews.reviewLevel))

    if (existingReviews.length > 0) {
      const lastReview = existingReviews[0]
      
      // 如果上一级审核未通过，不能进行下一级审核
      if (lastReview.reviewResult !== 1 && validatedData.reviewLevel > lastReview.reviewLevel) {
        return NextResponse.json(
          { 
            success: false, 
            code: 'INVALID_REVIEW_LEVEL', 
            message: '上一级审核未通过，无法进行下一级审核' 
          },
          { status: 400 }
        )
      }
    }

    // 开始事务处理
    await db.transaction(async (tx) => {
      // 创建审核记录
      const reviewData = {
        ...validatedData,
        reviewerId: user.id,
        reviewDate: format(new Date(), 'yyyy-MM-dd'),
        attachments: validatedData.attachments ? JSON.stringify(validatedData.attachments) : null,
      }

      const newReview = await tx
        .insert(admissionReviews)
        .values(reviewData)
        .returning()

      // 根据审核结果更新申请状态
      let newApplicationStatus = application[0].status

      switch (validatedData.reviewResult) {
        case 1: // 通过
          if (validatedData.reviewLevel === 3) { // 终审通过
            newApplicationStatus = 5 // 已通过
          } else {
            newApplicationStatus = 4 // 审核中（等待下一级审核）
          }
          break
        case 2: // 拒绝
          newApplicationStatus = 6 // 已拒绝
          break
        case 3: // 需要补充材料
          newApplicationStatus = 3 // 待审核（等待补充材料）
          break
        case 4: // 转下级审核
          newApplicationStatus = 4 // 审核中
          break
      }

      // 更新申请状态
      await tx
        .update(admissionApplications)
        .set({
          status: newApplicationStatus,
          updatedAt: new Date(),
        })
        .where(eq(admissionApplications.id, validatedData.applicationId))
    })

    return NextResponse.json({
      success: true,
      message: '审核记录创建成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('创建审核记录失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
