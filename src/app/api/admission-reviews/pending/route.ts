import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { 
  admissionApplications,
  admissionAssessments,
  admissionReviews,
  betterAuthUsers 
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, inArray, desc, count } from 'drizzle-orm'
import { format, differenceInDays } from 'date-fns'

// GET - 获取待办事项列表
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'all' // all, assessment, review
    const priority = searchParams.get('priority') // high, medium, low
    const limit = parseInt(searchParams.get('limit') || '20')

    const limitNum = Math.min(limit, 100)
    const pendingItems: any[] = []

    // 1. 待评估的申请
    if (type === 'all' || type === 'assessment') {
      const pendingAssessments = await db
        .select({
          id: admissionApplications.id,
          applicationNumber: admissionApplications.applicationNumber,
          elderName: admissionApplications.elderName,
          applicantName: admissionApplications.applicantName,
          applicantPhone: admissionApplications.applicantPhone,
          expectedAdmissionDate: admissionApplications.expectedAdmissionDate,
          applicationDate: admissionApplications.applicationDate,
          status: admissionApplications.status,
          createdAt: admissionApplications.createdAt,
          createdBy: admissionApplications.createdBy,
          createdByUser: {
            id: betterAuthUsers.id,
            name: betterAuthUsers.name,
          },
        })
        .from(admissionApplications)
        .leftJoin(betterAuthUsers, eq(admissionApplications.createdBy, betterAuthUsers.id))
        .where(inArray(admissionApplications.status, [1, 2])) // 待评估或评估中
        .orderBy(desc(admissionApplications.createdAt))
        .limit(limitNum)

      // 检查每个申请的评估完成情况
      for (const app of pendingAssessments) {
        const assessments = await db
          .select({
            assessmentType: admissionAssessments.assessmentType,
            status: admissionAssessments.status,
          })
          .from(admissionAssessments)
          .where(eq(admissionAssessments.applicationId, app.id))

        const hasHealthAssessment = assessments.some(a => a.assessmentType === 1 && a.status === 2)
        const hasNursingAssessment = assessments.some(a => a.assessmentType === 2 && a.status === 2)
        const hasRiskAssessment = assessments.some(a => a.assessmentType === 4 && a.status === 2)

        let taskType = ''
        let taskDescription = ''
        let urgency = 'medium'

        if (!hasHealthAssessment) {
          taskType = 'health_assessment'
          taskDescription = '需要进行健康评估'
        } else if (!hasNursingAssessment) {
          taskType = 'nursing_assessment'
          taskDescription = '需要进行护理评估'
        } else if (!hasRiskAssessment) {
          taskType = 'risk_assessment'
          taskDescription = '建议进行风险评估'
          urgency = 'low'
        } else {
          taskType = 'assessment_complete'
          taskDescription = '评估已完成，等待提交审核'
          urgency = 'high'
        }

        // 计算紧急程度
        const daysSinceApplication = differenceInDays(new Date(), new Date(app.applicationDate))
        if (daysSinceApplication > 7) urgency = 'high'
        else if (daysSinceApplication > 3) urgency = 'medium'

        // 根据预期入住日期调整紧急程度
        if (app.expectedAdmissionDate) {
          const daysToAdmission = differenceInDays(new Date(app.expectedAdmissionDate), new Date())
          if (daysToAdmission <= 3) urgency = 'high'
          else if (daysToAdmission <= 7) urgency = 'medium'
        }

        pendingItems.push({
          id: `assessment_${app.id}`,
          type: 'assessment',
          taskType,
          applicationId: app.id,
          applicationNumber: app.applicationNumber,
          elderName: app.elderName,
          applicantName: app.applicantName,
          applicantPhone: app.applicantPhone,
          description: taskDescription,
          urgency,
          daysPending: daysSinceApplication,
          expectedAdmissionDate: app.expectedAdmissionDate,
          createdAt: app.createdAt,
          createdBy: app.createdByUser,
        })
      }
    }

    // 2. 待审核的申请
    if (type === 'all' || type === 'review') {
      const pendingReviews = await db
        .select({
          id: admissionApplications.id,
          applicationNumber: admissionApplications.applicationNumber,
          elderName: admissionApplications.elderName,
          applicantName: admissionApplications.applicantName,
          applicantPhone: admissionApplications.applicantPhone,
          expectedAdmissionDate: admissionApplications.expectedAdmissionDate,
          applicationDate: admissionApplications.applicationDate,
          status: admissionApplications.status,
          createdAt: admissionApplications.createdAt,
          createdBy: admissionApplications.createdBy,
          createdByUser: {
            id: betterAuthUsers.id,
            name: betterAuthUsers.name,
          },
        })
        .from(admissionApplications)
        .leftJoin(betterAuthUsers, eq(admissionApplications.createdBy, betterAuthUsers.id))
        .where(inArray(admissionApplications.status, [3, 4])) // 待审核或审核中
        .orderBy(desc(admissionApplications.createdAt))
        .limit(limitNum)

      // 检查每个申请的审核情况
      for (const app of pendingReviews) {
        const reviews = await db
          .select({
            reviewLevel: admissionReviews.reviewLevel,
            reviewResult: admissionReviews.reviewResult,
            createdAt: admissionReviews.createdAt,
          })
          .from(admissionReviews)
          .where(eq(admissionReviews.applicationId, app.id))
          .orderBy(desc(admissionReviews.createdAt))

        let taskType = ''
        let taskDescription = ''
        let urgency = 'medium'
        let nextReviewLevel = 1

        if (reviews.length === 0) {
          taskType = 'initial_review'
          taskDescription = '需要进行初审'
          nextReviewLevel = 1
        } else {
          const lastReview = reviews[0]
          
          if (lastReview.reviewResult === 1) { // 通过
            if (lastReview.reviewLevel === 1) {
              taskType = 'secondary_review'
              taskDescription = '初审已通过，需要复审'
              nextReviewLevel = 2
            } else if (lastReview.reviewLevel === 2) {
              taskType = 'final_review'
              taskDescription = '复审已通过，需要终审'
              nextReviewLevel = 3
            }
          } else if (lastReview.reviewResult === 3) { // 需要补充材料
            taskType = 'supplementary_review'
            taskDescription = '材料已补充，需要重新审核'
            nextReviewLevel = lastReview.reviewLevel
          } else if (lastReview.reviewResult === 4) { // 转下级审核
            taskType = 'transferred_review'
            taskDescription = '已转交下级审核'
            nextReviewLevel = lastReview.reviewLevel + 1
          }
        }

        // 计算紧急程度
        const daysSinceApplication = differenceInDays(new Date(), new Date(app.applicationDate))
        if (daysSinceApplication > 10) urgency = 'high'
        else if (daysSinceApplication > 5) urgency = 'medium'

        // 根据预期入住日期调整紧急程度
        if (app.expectedAdmissionDate) {
          const daysToAdmission = differenceInDays(new Date(app.expectedAdmissionDate), new Date())
          if (daysToAdmission <= 5) urgency = 'high'
          else if (daysToAdmission <= 10) urgency = 'medium'
        }

        pendingItems.push({
          id: `review_${app.id}`,
          type: 'review',
          taskType,
          applicationId: app.id,
          applicationNumber: app.applicationNumber,
          elderName: app.elderName,
          applicantName: app.applicantName,
          applicantPhone: app.applicantPhone,
          description: taskDescription,
          urgency,
          daysPending: daysSinceApplication,
          nextReviewLevel,
          expectedAdmissionDate: app.expectedAdmissionDate,
          createdAt: app.createdAt,
          createdBy: app.createdByUser,
        })
      }
    }

    // 3. 过期提醒
    const overdueItems = pendingItems.filter(item => {
      if (item.expectedAdmissionDate) {
        const daysToAdmission = differenceInDays(new Date(item.expectedAdmissionDate), new Date())
        return daysToAdmission < 0 // 已过期
      }
      return false
    })

    overdueItems.forEach(item => {
      item.urgency = 'high'
      item.isOverdue = true
    })

    // 按优先级过滤
    let filteredItems = pendingItems
    if (priority) {
      filteredItems = pendingItems.filter(item => item.urgency === priority)
    }

    // 按紧急程度和时间排序
    filteredItems.sort((a, b) => {
      const urgencyOrder = { high: 3, medium: 2, low: 1 }
      const urgencyDiff = urgencyOrder[b.urgency as keyof typeof urgencyOrder] - urgencyOrder[a.urgency as keyof typeof urgencyOrder]
      
      if (urgencyDiff !== 0) return urgencyDiff
      
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    })

    // 统计信息
    const stats = {
      total: filteredItems.length,
      high: filteredItems.filter(item => item.urgency === 'high').length,
      medium: filteredItems.filter(item => item.urgency === 'medium').length,
      low: filteredItems.filter(item => item.urgency === 'low').length,
      overdue: overdueItems.length,
      assessment: filteredItems.filter(item => item.type === 'assessment').length,
      review: filteredItems.filter(item => item.type === 'review').length,
    }

    return NextResponse.json({
      success: true,
      data: {
        items: filteredItems.slice(0, limitNum),
        stats,
      },
    })
  } catch (error) {
    console.error('获取待办事项失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
