import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { 
  admissionReviews, 
  admissionApplications,
  betterAuthUsers 
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, count, gte, lte, inArray } from 'drizzle-orm'
import { format, subDays, startOfDay, endOfDay } from 'date-fns'

// GET - 获取审核统计数据
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const reviewerId = searchParams.get('reviewerId')

    // 设置默认时间范围（最近30天）
    const defaultEndDate = new Date()
    const defaultStartDate = subDays(defaultEndDate, 30)
    
    const queryStartDate = startDate ? new Date(startDate) : defaultStartDate
    const queryEndDate = endDate ? new Date(endDate) : defaultEndDate

    // 构建基础查询条件
    const baseConditions = [
      gte(admissionReviews.createdAt, startOfDay(queryStartDate)),
      lte(admissionReviews.createdAt, endOfDay(queryEndDate)),
    ]

    if (reviewerId) {
      baseConditions.push(eq(admissionReviews.reviewerId, reviewerId))
    }

    // 1. 总体统计
    const totalReviewsResult = await db
      .select({ count: count() })
      .from(admissionReviews)
      .where(and(...baseConditions))

    const totalReviews = totalReviewsResult[0]?.count || 0

    // 2. 按审核结果统计
    const reviewResultStats = await db
      .select({
        reviewResult: admissionReviews.reviewResult,
        count: count(),
      })
      .from(admissionReviews)
      .where(and(...baseConditions))
      .groupBy(admissionReviews.reviewResult)

    const resultStatsMap = {
      1: 0, // 通过
      2: 0, // 拒绝
      3: 0, // 需要补充材料
      4: 0, // 转下级审核
    }

    reviewResultStats.forEach(stat => {
      resultStatsMap[stat.reviewResult as keyof typeof resultStatsMap] = stat.count
    })

    // 3. 按审核级别统计
    const reviewLevelStats = await db
      .select({
        reviewLevel: admissionReviews.reviewLevel,
        count: count(),
      })
      .from(admissionReviews)
      .where(and(...baseConditions))
      .groupBy(admissionReviews.reviewLevel)

    const levelStatsMap = {
      1: 0, // 初审
      2: 0, // 复审
      3: 0, // 终审
    }

    reviewLevelStats.forEach(stat => {
      levelStatsMap[stat.reviewLevel as keyof typeof levelStatsMap] = stat.count
    })

    // 4. 待审核统计
    const pendingApplicationsResult = await db
      .select({ count: count() })
      .from(admissionApplications)
      .where(inArray(admissionApplications.status, [3, 4])) // 待审核或审核中

    const pendingApplications = pendingApplicationsResult[0]?.count || 0

    // 5. 审核员工作量统计
    const reviewerWorkloadStats = await db
      .select({
        reviewerId: admissionReviews.reviewerId,
        count: count(),
        reviewer: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(admissionReviews)
      .leftJoin(betterAuthUsers, eq(admissionReviews.reviewerId, betterAuthUsers.id))
      .where(and(...baseConditions))
      .groupBy(admissionReviews.reviewerId, betterAuthUsers.id, betterAuthUsers.name)
      .orderBy(count())

    // 6. 每日审核趋势（最近7天）
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = subDays(new Date(), i)
      return format(date, 'yyyy-MM-dd')
    }).reverse()

    const dailyTrendPromises = last7Days.map(async (date) => {
      const dayStart = startOfDay(new Date(date))
      const dayEnd = endOfDay(new Date(date))
      
      const dayReviewsResult = await db
        .select({ count: count() })
        .from(admissionReviews)
        .where(
          and(
            gte(admissionReviews.createdAt, dayStart),
            lte(admissionReviews.createdAt, dayEnd),
            ...(reviewerId ? [eq(admissionReviews.reviewerId, reviewerId)] : [])
          )
        )

      return {
        date,
        count: dayReviewsResult[0]?.count || 0,
      }
    })

    const dailyTrend = await Promise.all(dailyTrendPromises)

    // 7. 平均审核时间（从申请提交到审核完成）
    const completedApplications = await db
      .select({
        applicationDate: admissionApplications.applicationDate,
        reviewDate: admissionReviews.reviewDate,
      })
      .from(admissionApplications)
      .innerJoin(admissionReviews, eq(admissionApplications.id, admissionReviews.applicationId))
      .where(
        and(
          inArray(admissionApplications.status, [5, 6]), // 已通过或已拒绝
          gte(admissionReviews.createdAt, startOfDay(queryStartDate)),
          lte(admissionReviews.createdAt, endOfDay(queryEndDate)),
          ...(reviewerId ? [eq(admissionReviews.reviewerId, reviewerId)] : [])
        )
      )

    let averageReviewDays = 0
    if (completedApplications.length > 0) {
      const totalDays = completedApplications.reduce((sum, app) => {
        const appDate = new Date(app.applicationDate)
        const reviewDate = new Date(app.reviewDate)
        const diffTime = reviewDate.getTime() - appDate.getTime()
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        return sum + diffDays
      }, 0)
      
      averageReviewDays = Math.round(totalDays / completedApplications.length)
    }

    // 8. 通过率统计
    const passedReviews = resultStatsMap[1]
    const rejectedReviews = resultStatsMap[2]
    const totalDecisiveReviews = passedReviews + rejectedReviews
    const passRate = totalDecisiveReviews > 0 ? Math.round((passedReviews / totalDecisiveReviews) * 100) : 0

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          totalReviews,
          pendingApplications,
          averageReviewDays,
          passRate,
        },
        reviewResultStats: {
          passed: resultStatsMap[1],
          rejected: resultStatsMap[2],
          needsSupplementary: resultStatsMap[3],
          transferred: resultStatsMap[4],
        },
        reviewLevelStats: {
          initial: levelStatsMap[1],
          secondary: levelStatsMap[2],
          final: levelStatsMap[3],
        },
        reviewerWorkload: reviewerWorkloadStats,
        dailyTrend,
        dateRange: {
          startDate: format(queryStartDate, 'yyyy-MM-dd'),
          endDate: format(queryEndDate, 'yyyy-MM-dd'),
        },
      },
    })
  } catch (error) {
    console.error('获取审核统计失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
