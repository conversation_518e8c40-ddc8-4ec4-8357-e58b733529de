import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { 
  admissionPayments, 
  admissionApplications 
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, count, sum, gte, lte, inArray } from 'drizzle-orm'
import { format, subDays, startOfDay, endOfDay, startOfMonth, endOfMonth } from 'date-fns'

// GET - 获取缴费统计数据
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const applicationId = searchParams.get('applicationId')

    // 设置默认时间范围（当月）
    const defaultStartDate = startOfMonth(new Date())
    const defaultEndDate = endOfMonth(new Date())
    
    const queryStartDate = startDate ? new Date(startDate) : defaultStartDate
    const queryEndDate = endDate ? new Date(endDate) : defaultEndDate

    // 构建基础查询条件
    const baseConditions = [
      gte(admissionPayments.paymentDate, format(queryStartDate, 'yyyy-MM-dd')),
      lte(admissionPayments.paymentDate, format(queryEndDate, 'yyyy-MM-dd')),
    ]

    if (applicationId) {
      baseConditions.push(eq(admissionPayments.applicationId, applicationId))
    }

    // 1. 总体统计
    const totalStatsResult = await db
      .select({
        count: count(),
        totalAmount: sum(admissionPayments.amount),
      })
      .from(admissionPayments)
      .where(and(...baseConditions))

    const totalStats = totalStatsResult[0] || { count: 0, totalAmount: 0 }

    // 2. 按状态统计
    const statusStatsResult = await db
      .select({
        status: admissionPayments.status,
        count: count(),
        totalAmount: sum(admissionPayments.amount),
      })
      .from(admissionPayments)
      .where(and(...baseConditions))
      .groupBy(admissionPayments.status)

    const statusStats = {
      1: { count: 0, amount: 0 }, // 待确认
      2: { count: 0, amount: 0 }, // 已确认
      3: { count: 0, amount: 0 }, // 已退款
      4: { count: 0, amount: 0 }, // 已作废
    }

    statusStatsResult.forEach(stat => {
      statusStats[stat.status as keyof typeof statusStats] = {
        count: stat.count,
        amount: Number(stat.totalAmount) || 0,
      }
    })

    // 3. 按缴费类型统计
    const typeStatsResult = await db
      .select({
        paymentType: admissionPayments.paymentType,
        count: count(),
        totalAmount: sum(admissionPayments.amount),
      })
      .from(admissionPayments)
      .where(and(...baseConditions, eq(admissionPayments.status, 2))) // 只统计已确认的
      .groupBy(admissionPayments.paymentType)

    const typeStats = {
      1: { count: 0, amount: 0 }, // 押金
      2: { count: 0, amount: 0 }, // 月费
      3: { count: 0, amount: 0 }, // 服务费
      4: { count: 0, amount: 0 }, // 护理费
      5: { count: 0, amount: 0 }, // 其他费用
    }

    typeStatsResult.forEach(stat => {
      typeStats[stat.paymentType as keyof typeof typeStats] = {
        count: stat.count,
        amount: Number(stat.totalAmount) || 0,
      }
    })

    // 4. 按支付方式统计
    const methodStatsResult = await db
      .select({
        paymentMethod: admissionPayments.paymentMethod,
        count: count(),
        totalAmount: sum(admissionPayments.amount),
      })
      .from(admissionPayments)
      .where(and(...baseConditions, eq(admissionPayments.status, 2))) // 只统计已确认的
      .groupBy(admissionPayments.paymentMethod)

    const methodStats = {
      1: { count: 0, amount: 0 }, // 现金
      2: { count: 0, amount: 0 }, // 银行转账
      3: { count: 0, amount: 0 }, // 支付宝
      4: { count: 0, amount: 0 }, // 微信支付
      5: { count: 0, amount: 0 }, // 刷卡
    }

    methodStatsResult.forEach(stat => {
      methodStats[stat.paymentMethod as keyof typeof methodStats] = {
        count: stat.count,
        amount: Number(stat.totalAmount) || 0,
      }
    })

    // 5. 每日收入趋势（最近7天）
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = subDays(new Date(), i)
      return format(date, 'yyyy-MM-dd')
    }).reverse()

    const dailyIncomePromises = last7Days.map(async (date) => {
      const dayIncomeResult = await db
        .select({
          count: count(),
          totalAmount: sum(admissionPayments.amount),
        })
        .from(admissionPayments)
        .where(
          and(
            eq(admissionPayments.paymentDate, date),
            eq(admissionPayments.status, 2), // 已确认
            ...(applicationId ? [eq(admissionPayments.applicationId, applicationId)] : [])
          )
        )

      return {
        date,
        count: dayIncomeResult[0]?.count || 0,
        amount: Number(dayIncomeResult[0]?.totalAmount) || 0,
      }
    })

    const dailyIncome = await Promise.all(dailyIncomePromises)

    // 6. 待确认缴费统计
    const pendingPaymentsResult = await db
      .select({
        count: count(),
        totalAmount: sum(admissionPayments.amount),
      })
      .from(admissionPayments)
      .where(
        and(
          eq(admissionPayments.status, 1), // 待确认
          ...(applicationId ? [eq(admissionPayments.applicationId, applicationId)] : [])
        )
      )

    const pendingPayments = {
      count: pendingPaymentsResult[0]?.count || 0,
      amount: Number(pendingPaymentsResult[0]?.totalAmount) || 0,
    }

    // 7. 退款统计
    const refundStatsResult = await db
      .select({
        count: count(),
        totalRefund: sum(admissionPayments.refundAmount),
      })
      .from(admissionPayments)
      .where(
        and(
          eq(admissionPayments.status, 3), // 已退款
          gte(admissionPayments.refundDate, format(queryStartDate, 'yyyy-MM-dd')),
          lte(admissionPayments.refundDate, format(queryEndDate, 'yyyy-MM-dd')),
          ...(applicationId ? [eq(admissionPayments.applicationId, applicationId)] : [])
        )
      )

    const refundStats = {
      count: refundStatsResult[0]?.count || 0,
      amount: Number(refundStatsResult[0]?.totalRefund) || 0,
    }

    // 8. 按申请统计（如果没有指定applicationId）
    let applicationStats = []
    if (!applicationId) {
      const applicationStatsResult = await db
        .select({
          applicationId: admissionPayments.applicationId,
          elderName: admissionApplications.elderName,
          applicantName: admissionApplications.applicantName,
          count: count(),
          totalAmount: sum(admissionPayments.amount),
        })
        .from(admissionPayments)
        .leftJoin(admissionApplications, eq(admissionPayments.applicationId, admissionApplications.id))
        .where(and(...baseConditions, eq(admissionPayments.status, 2))) // 已确认
        .groupBy(admissionPayments.applicationId, admissionApplications.elderName, admissionApplications.applicantName)
        .orderBy(sum(admissionPayments.amount))
        .limit(10)

      applicationStats = applicationStatsResult.map(stat => ({
        applicationId: stat.applicationId,
        elderName: stat.elderName,
        applicantName: stat.applicantName,
        count: stat.count,
        amount: Number(stat.totalAmount) || 0,
      }))
    }

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          totalPayments: totalStats.count,
          totalAmount: Number(totalStats.totalAmount) || 0,
          confirmedAmount: statusStats[2].amount,
          pendingAmount: pendingPayments.amount,
          refundAmount: refundStats.amount,
        },
        statusStats,
        typeStats,
        methodStats,
        pendingPayments,
        refundStats,
        dailyIncome,
        applicationStats,
        dateRange: {
          startDate: format(queryStartDate, 'yyyy-MM-dd'),
          endDate: format(queryEndDate, 'yyyy-MM-dd'),
        },
      },
    })
  } catch (error) {
    console.error('获取缴费统计失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
