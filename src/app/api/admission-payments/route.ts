import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  admissionPayments, 
  admissionApplications,
  admissionContracts,
  betterAuthUsers 
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, like, desc, asc, count, gte, lte, sum } from 'drizzle-orm'
import { format } from 'date-fns'

// 缴费创建验证 schema
const createPaymentSchema = z.object({
  applicationId: z.string().min(1, '请选择入住申请'),
  contractId: z.string().optional(),
  paymentType: z.number().min(1).max(5, '请选择缴费类型'),
  amount: z.number().min(0.01, '缴费金额必须大于0'),
  paymentMethod: z.number().min(1).max(5, '请选择支付方式'),
  paymentDate: z.string().min(1, '请选择缴费日期'),
  dueDate: z.string().optional(),
  description: z.string().optional(),
  notes: z.string().optional(),
  receiptNumber: z.string().optional(),
  attachments: z.array(z.string()).optional(),
})

// 缴费更新验证 schema
const updatePaymentSchema = createPaymentSchema.partial().extend({
  status: z.number().min(1).max(4).optional(),
  confirmDate: z.string().optional(),
  refundAmount: z.number().min(0).optional(),
  refundDate: z.string().optional(),
  refundReason: z.string().optional(),
})

// 生成缴费单号
function generatePaymentNumber(): string {
  const now = new Date()
  const dateStr = format(now, 'yyyyMMdd')
  const timeStr = format(now, 'HHmmss')
  return `PAY${dateStr}${timeStr}`
}

// 缴费类型名称
const paymentTypeNames: Record<number, string> = {
  1: '押金',
  2: '月费',
  3: '服务费',
  4: '护理费',
  5: '其他费用',
}

// 支付方式名称
const paymentMethodNames: Record<number, string> = {
  1: '现金',
  2: '银行转账',
  3: '支付宝',
  4: '微信支付',
  5: '刷卡',
}

// GET - 获取缴费列表
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const paymentType = searchParams.get('paymentType')
    const paymentMethod = searchParams.get('paymentMethod')
    const applicationId = searchParams.get('applicationId')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const offset = (page - 1) * limit
    const limitNum = Math.min(limit, 100)

    // 构建查询条件
    const whereConditions = []

    if (search) {
      whereConditions.push(
        or(
          like(admissionPayments.paymentNumber, `%${search}%`),
          like(admissionPayments.receiptNumber, `%${search}%`),
          like(admissionApplications.elderName, `%${search}%`),
          like(admissionApplications.applicantName, `%${search}%`)
        )
      )
    }

    if (status) {
      whereConditions.push(eq(admissionPayments.status, parseInt(status)))
    }

    if (paymentType) {
      whereConditions.push(eq(admissionPayments.paymentType, parseInt(paymentType)))
    }

    if (paymentMethod) {
      whereConditions.push(eq(admissionPayments.paymentMethod, parseInt(paymentMethod)))
    }

    if (applicationId) {
      whereConditions.push(eq(admissionPayments.applicationId, applicationId))
    }

    if (startDate) {
      whereConditions.push(gte(admissionPayments.paymentDate, startDate))
    }

    if (endDate) {
      whereConditions.push(lte(admissionPayments.paymentDate, endDate))
    }

    // 排序
    const orderBy = sortOrder === 'asc' 
      ? asc(admissionPayments[sortBy as keyof typeof admissionPayments] || admissionPayments.createdAt)
      : desc(admissionPayments[sortBy as keyof typeof admissionPayments] || admissionPayments.createdAt)

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(admissionPayments)
      .leftJoin(admissionApplications, eq(admissionPayments.applicationId, admissionApplications.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 获取缴费列表
    const paymentList = await db
      .select({
        id: admissionPayments.id,
        applicationId: admissionPayments.applicationId,
        contractId: admissionPayments.contractId,
        paymentNumber: admissionPayments.paymentNumber,
        paymentType: admissionPayments.paymentType,
        amount: admissionPayments.amount,
        paymentMethod: admissionPayments.paymentMethod,
        paymentDate: admissionPayments.paymentDate,
        dueDate: admissionPayments.dueDate,
        status: admissionPayments.status,
        confirmDate: admissionPayments.confirmDate,
        description: admissionPayments.description,
        receiptNumber: admissionPayments.receiptNumber,
        refundAmount: admissionPayments.refundAmount,
        refundDate: admissionPayments.refundDate,
        createdAt: admissionPayments.createdAt,
        // 关联数据
        application: {
          id: admissionApplications.id,
          applicationNumber: admissionApplications.applicationNumber,
          elderName: admissionApplications.elderName,
          applicantName: admissionApplications.applicantName,
          applicantPhone: admissionApplications.applicantPhone,
        },
        contract: {
          id: admissionContracts.id,
          contractNumber: admissionContracts.contractNumber,
        },
        createdByUser: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(admissionPayments)
      .leftJoin(admissionApplications, eq(admissionPayments.applicationId, admissionApplications.id))
      .leftJoin(admissionContracts, eq(admissionPayments.contractId, admissionContracts.id))
      .leftJoin(betterAuthUsers, eq(admissionPayments.createdBy, betterAuthUsers.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limitNum)
      .offset(offset)

    // 添加类型名称
    const processedList = paymentList.map(payment => ({
      ...payment,
      paymentTypeName: paymentTypeNames[payment.paymentType],
      paymentMethodName: paymentMethodNames[payment.paymentMethod],
    }))

    return NextResponse.json({
      success: true,
      data: {
        list: processedList,
        pagination: {
          page,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      },
    })
  } catch (error) {
    console.error('获取缴费列表失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// POST - 创建缴费记录
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createPaymentSchema.parse(body)

    // 检查申请是否存在
    const application = await db
      .select()
      .from(admissionApplications)
      .where(eq(admissionApplications.id, validatedData.applicationId))
      .limit(1)

    if (application.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '入住申请不存在' },
        { status: 404 }
      )
    }

    // 如果指定了合同ID，检查合同是否存在且属于该申请
    if (validatedData.contractId) {
      const contract = await db
        .select()
        .from(admissionContracts)
        .where(
          and(
            eq(admissionContracts.id, validatedData.contractId),
            eq(admissionContracts.applicationId, validatedData.applicationId)
          )
        )
        .limit(1)

      if (contract.length === 0) {
        return NextResponse.json(
          { success: false, code: 'CONTRACT_NOT_FOUND', message: '合同不存在或不属于该申请' },
          { status: 404 }
        )
      }
    }

    // 生成缴费单号
    const paymentNumber = generatePaymentNumber()

    // 创建缴费记录
    const paymentData = {
      ...validatedData,
      paymentNumber,
      attachments: validatedData.attachments ? JSON.stringify(validatedData.attachments) : null,
      createdBy: user.id,
    }

    const newPayment = await db
      .insert(admissionPayments)
      .values(paymentData)
      .returning()

    return NextResponse.json({
      success: true,
      data: newPayment[0],
      message: '缴费记录创建成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('创建缴费记录失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
