import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  roomCleaningRecords,
  checkoutApplications,
  elderInfo,
  rooms,
  betterAuthUsers
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, like, desc, asc, count, gte, lte } from 'drizzle-orm'
import { format } from 'date-fns'

// 查询参数验证 schema
const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('10'),
  search: z.string().optional(),
  cleaningStatus: z.string().optional(),
  roomId: z.string().uuid().optional(),
  assignedTo: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
})

// 创建清理记录验证 schema
const createCleaningRecordSchema = z.object({
  applicationId: z.string().uuid('请选择有效的申请'),
  roomId: z.string().uuid('请选择有效的房间'),
  assignedTo: z.string().uuid('请选择清理人员'),
  
  // 清理任务
  cleaningTasks: z.array(z.object({
    task: z.string(),
    description: z.string().optional(),
    completed: z.boolean().optional().default(false),
    completedAt: z.string().optional(),
    notes: z.string().optional(),
  })).optional(),
  
  // 个人物品清点
  personalItemsInventory: z.array(z.object({
    item: z.string(),
    description: z.string().optional(),
    quantity: z.number().optional(),
    condition: z.string().optional(),
    action: z.string().optional(), // 处理方式：保留、丢弃、联系家属等
  })).optional(),
  
  // 设施检查
  facilityInspection: z.array(z.object({
    facility: z.string(),
    condition: z.string(),
    needsRepair: z.boolean().optional().default(false),
    repairDescription: z.string().optional(),
    priority: z.string().optional(), // 优先级：高、中、低
  })).optional(),
  
  // 损坏评估
  damageAssessment: z.array(z.object({
    item: z.string(),
    damageType: z.string(),
    severity: z.string(), // 严重程度：轻微、中等、严重
    repairCost: z.number().optional(),
    responsible: z.string().optional(), // 责任方
    notes: z.string().optional(),
  })).optional(),
  
  notes: z.string().optional(),
})

// GET - 获取房间清理记录列表
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const validatedParams = querySchema.parse(Object.fromEntries(searchParams))

    const page = parseInt(validatedParams.page)
    const limit = parseInt(validatedParams.limit)
    const offset = (page - 1) * limit

    // 构建查询条件
    const whereConditions = []

    // 搜索条件
    if (validatedParams.search) {
      const searchTerm = `%${validatedParams.search}%`
      whereConditions.push(
        or(
          like(roomCleaningRecords.cleaningNumber, searchTerm),
          like(checkoutApplications.applicationNumber, searchTerm),
          like(elderInfo.name, searchTerm),
          like(rooms.roomNumber, searchTerm)
        )
      )
    }

    // 清理状态筛选
    if (validatedParams.cleaningStatus) {
      whereConditions.push(eq(roomCleaningRecords.cleaningStatus, parseInt(validatedParams.cleaningStatus)))
    }

    // 房间筛选
    if (validatedParams.roomId) {
      whereConditions.push(eq(roomCleaningRecords.roomId, validatedParams.roomId))
    }

    // 清理人员筛选
    if (validatedParams.assignedTo) {
      whereConditions.push(eq(roomCleaningRecords.assignedTo, validatedParams.assignedTo))
    }

    // 日期范围筛选
    if (validatedParams.dateFrom) {
      whereConditions.push(gte(roomCleaningRecords.createdAt, new Date(validatedParams.dateFrom)))
    }
    if (validatedParams.dateTo) {
      whereConditions.push(lte(roomCleaningRecords.createdAt, new Date(validatedParams.dateTo)))
    }

    // 排序
    const orderBy = validatedParams.sortOrder === 'asc' 
      ? asc(roomCleaningRecords[validatedParams.sortBy as keyof typeof roomCleaningRecords])
      : desc(roomCleaningRecords[validatedParams.sortBy as keyof typeof roomCleaningRecords])

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(roomCleaningRecords)
      .leftJoin(checkoutApplications, eq(roomCleaningRecords.applicationId, checkoutApplications.id))
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .leftJoin(rooms, eq(roomCleaningRecords.roomId, rooms.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0].count

    // 获取清理记录列表
    const cleaningRecords = await db
      .select({
        // 清理记录信息
        id: roomCleaningRecords.id,
        cleaningNumber: roomCleaningRecords.cleaningNumber,
        cleaningTasks: roomCleaningRecords.cleaningTasks,
        personalItemsInventory: roomCleaningRecords.personalItemsInventory,
        facilityInspection: roomCleaningRecords.facilityInspection,
        damageAssessment: roomCleaningRecords.damageAssessment,
        cleaningStatus: roomCleaningRecords.cleaningStatus,
        startTime: roomCleaningRecords.startTime,
        endTime: roomCleaningRecords.endTime,
        inspectionTime: roomCleaningRecords.inspectionTime,
        cleaningResult: roomCleaningRecords.cleaningResult,
        foundItems: roomCleaningRecords.foundItems,
        maintenanceNeeded: roomCleaningRecords.maintenanceNeeded,
        maintenanceItems: roomCleaningRecords.maintenanceItems,
        notes: roomCleaningRecords.notes,
        createdAt: roomCleaningRecords.createdAt,
        updatedAt: roomCleaningRecords.updatedAt,
        
        // 申请信息
        application: {
          id: checkoutApplications.id,
          applicationNumber: checkoutApplications.applicationNumber,
          applicantName: checkoutApplications.applicantName,
          checkoutType: checkoutApplications.checkoutType,
          expectedCheckoutDate: checkoutApplications.expectedCheckoutDate,
          actualCheckoutDate: checkoutApplications.actualCheckoutDate,
          status: checkoutApplications.status,
        },
        
        // 老人信息
        elder: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
          idCard: elderInfo.idCard,
          careLevel: elderInfo.careLevel,
        },
        
        // 房间信息
        room: {
          id: rooms.id,
          roomNumber: rooms.roomNumber,
          roomType: rooms.roomType,
          floor: rooms.floor,
          capacity: rooms.capacity,
        },
        
        // 清理人员
        assignedTo: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(roomCleaningRecords)
      .leftJoin(checkoutApplications, eq(roomCleaningRecords.applicationId, checkoutApplications.id))
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .leftJoin(rooms, eq(roomCleaningRecords.roomId, rooms.id))
      .leftJoin(betterAuthUsers, eq(roomCleaningRecords.assignedTo, betterAuthUsers.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset)

    // 处理数据格式
    const processedRecords = cleaningRecords.map(record => ({
      ...record,
      cleaningTasks: record.cleaningTasks ? JSON.parse(record.cleaningTasks) : [],
      personalItemsInventory: record.personalItemsInventory ? JSON.parse(record.personalItemsInventory) : [],
      facilityInspection: record.facilityInspection ? JSON.parse(record.facilityInspection) : [],
      damageAssessment: record.damageAssessment ? JSON.parse(record.damageAssessment) : [],
      foundItems: record.foundItems ? JSON.parse(record.foundItems) : [],
      maintenanceItems: record.maintenanceItems ? JSON.parse(record.maintenanceItems) : [],
    }))

    return NextResponse.json({
      success: true,
      data: {
        list: processedRecords,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error('获取房间清理记录列表失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// POST - 创建房间清理记录
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createCleaningRecordSchema.parse(body)

    // 检查申请是否存在且状态正确
    const application = await db
      .select({
        id: checkoutApplications.id,
        status: checkoutApplications.status,
        applicationNumber: checkoutApplications.applicationNumber,
      })
      .from(checkoutApplications)
      .where(eq(checkoutApplications.id, validatedData.applicationId))
      .limit(1)

    if (application.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '退住申请不存在' },
        { status: 404 }
      )
    }

    if (![5, 6].includes(application[0].status)) {
      return NextResponse.json(
        { success: false, code: 'INVALID_STATUS', message: '只有费用结算中或房间清理中的申请才能创建清理记录' },
        { status: 400 }
      )
    }

    // 检查房间是否存在
    const room = await db
      .select()
      .from(rooms)
      .where(eq(rooms.id, validatedData.roomId))
      .limit(1)

    if (room.length === 0) {
      return NextResponse.json(
        { success: false, code: 'ROOM_NOT_FOUND', message: '房间不存在' },
        { status: 404 }
      )
    }

    // 检查是否已有清理记录
    const existingRecord = await db
      .select()
      .from(roomCleaningRecords)
      .where(eq(roomCleaningRecords.applicationId, validatedData.applicationId))
      .limit(1)

    if (existingRecord.length > 0) {
      return NextResponse.json(
        { success: false, code: 'RECORD_EXISTS', message: '该申请已有清理记录' },
        { status: 400 }
      )
    }

    // 生成清理编号
    const today = format(new Date(), 'yyyyMMdd')
    const countResult = await db
      .select({ count: count() })
      .from(roomCleaningRecords)
      .where(like(roomCleaningRecords.cleaningNumber, `CL${today}%`))

    const sequence = String(countResult[0].count + 1).padStart(3, '0')
    const cleaningNumber = `CL${today}${sequence}`

    // 默认清理任务
    const defaultCleaningTasks = [
      { task: '清理个人物品', description: '整理和清点老人的个人物品', completed: false },
      { task: '清洁房间卫生', description: '全面清洁房间内的卫生', completed: false },
      { task: '检查设施设备', description: '检查房间内的设施设备状况', completed: false },
      { task: '评估维修需求', description: '评估是否需要维修或更换设备', completed: false },
      { task: '消毒处理', description: '对房间进行全面消毒', completed: false },
    ]

    // 创建清理记录
    const newRecord = await db
      .insert(roomCleaningRecords)
      .values({
        applicationId: validatedData.applicationId,
        roomId: validatedData.roomId,
        cleaningNumber,
        cleaningTasks: JSON.stringify(validatedData.cleaningTasks || defaultCleaningTasks),
        personalItemsInventory: validatedData.personalItemsInventory ? JSON.stringify(validatedData.personalItemsInventory) : null,
        facilityInspection: validatedData.facilityInspection ? JSON.stringify(validatedData.facilityInspection) : null,
        damageAssessment: validatedData.damageAssessment ? JSON.stringify(validatedData.damageAssessment) : null,
        assignedTo: validatedData.assignedTo,
        notes: validatedData.notes,
      })
      .returning()

    return NextResponse.json({
      success: true,
      data: newRecord[0],
      message: '房间清理记录创建成功',
    })
  } catch (error) {
    console.error('创建房间清理记录失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'VALIDATION_ERROR', 
          message: '数据验证失败',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
