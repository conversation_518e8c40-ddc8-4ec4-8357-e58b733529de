import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  roomCleaningRecords,
  checkoutApplications,
  elderInfo,
  rooms,
  betterAuthUsers
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq } from 'drizzle-orm'

// 更新清理记录验证 schema
const updateCleaningRecordSchema = z.object({
  // 清理任务
  cleaningTasks: z.array(z.object({
    task: z.string(),
    description: z.string().optional(),
    completed: z.boolean().optional().default(false),
    completedAt: z.string().optional(),
    notes: z.string().optional(),
  })).optional(),
  
  // 个人物品清点
  personalItemsInventory: z.array(z.object({
    item: z.string(),
    description: z.string().optional(),
    quantity: z.number().optional(),
    condition: z.string().optional(),
    action: z.string().optional(),
  })).optional(),
  
  // 设施检查
  facilityInspection: z.array(z.object({
    facility: z.string(),
    condition: z.string(),
    needsRepair: z.boolean().optional().default(false),
    repairDescription: z.string().optional(),
    priority: z.string().optional(),
  })).optional(),
  
  // 损坏评估
  damageAssessment: z.array(z.object({
    item: z.string(),
    damageType: z.string(),
    severity: z.string(),
    repairCost: z.number().optional(),
    responsible: z.string().optional(),
    notes: z.string().optional(),
  })).optional(),
  
  // 发现物品
  foundItems: z.array(z.object({
    item: z.string(),
    description: z.string().optional(),
    location: z.string().optional(),
    action: z.string().optional(),
  })).optional(),
  
  // 维修项目
  maintenanceItems: z.array(z.object({
    item: z.string(),
    description: z.string(),
    priority: z.string(),
    estimatedCost: z.number().optional(),
    assignedTo: z.string().optional(),
  })).optional(),
  
  cleaningStatus: z.number().min(1).max(4).optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
  inspectionTime: z.string().optional(),
  cleaningResult: z.string().optional(),
  maintenanceNeeded: z.boolean().optional(),
  notes: z.string().optional(),
})

// GET - 获取房间清理记录详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const recordId = params.id

    // 获取清理记录详情
    const record = await db
      .select({
        // 清理记录信息
        id: roomCleaningRecords.id,
        cleaningNumber: roomCleaningRecords.cleaningNumber,
        cleaningTasks: roomCleaningRecords.cleaningTasks,
        personalItemsInventory: roomCleaningRecords.personalItemsInventory,
        facilityInspection: roomCleaningRecords.facilityInspection,
        damageAssessment: roomCleaningRecords.damageAssessment,
        cleaningStatus: roomCleaningRecords.cleaningStatus,
        startTime: roomCleaningRecords.startTime,
        endTime: roomCleaningRecords.endTime,
        inspectionTime: roomCleaningRecords.inspectionTime,
        cleaningResult: roomCleaningRecords.cleaningResult,
        foundItems: roomCleaningRecords.foundItems,
        maintenanceNeeded: roomCleaningRecords.maintenanceNeeded,
        maintenanceItems: roomCleaningRecords.maintenanceItems,
        notes: roomCleaningRecords.notes,
        createdAt: roomCleaningRecords.createdAt,
        updatedAt: roomCleaningRecords.updatedAt,
        
        // 申请信息
        application: {
          id: checkoutApplications.id,
          applicationNumber: checkoutApplications.applicationNumber,
          applicantName: checkoutApplications.applicantName,
          applicantPhone: checkoutApplications.applicantPhone,
          applicantRelation: checkoutApplications.applicantRelation,
          checkoutReason: checkoutApplications.checkoutReason,
          checkoutType: checkoutApplications.checkoutType,
          expectedCheckoutDate: checkoutApplications.expectedCheckoutDate,
          actualCheckoutDate: checkoutApplications.actualCheckoutDate,
          status: checkoutApplications.status,
          applicationDate: checkoutApplications.applicationDate,
        },
        
        // 老人信息
        elder: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
          idCard: elderInfo.idCard,
          phone: elderInfo.phone,
          address: elderInfo.address,
          emergencyContactName: elderInfo.emergencyContactName,
          emergencyContactPhone: elderInfo.emergencyContactPhone,
          emergencyContactRelation: elderInfo.emergencyContactRelation,
          careLevel: elderInfo.careLevel,
          status: elderInfo.status,
          admissionDate: elderInfo.admissionDate,
        },
        
        // 房间信息
        room: {
          id: rooms.id,
          roomNumber: rooms.roomNumber,
          roomType: rooms.roomType,
          floor: rooms.floor,
          capacity: rooms.capacity,
          monthlyRate: rooms.monthlyRate,
          facilities: rooms.facilities,
          status: rooms.status,
        },
        
        // 清理人员
        assignedTo: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(roomCleaningRecords)
      .leftJoin(checkoutApplications, eq(roomCleaningRecords.applicationId, checkoutApplications.id))
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .leftJoin(rooms, eq(roomCleaningRecords.roomId, rooms.id))
      .leftJoin(betterAuthUsers, eq(roomCleaningRecords.assignedTo, betterAuthUsers.id))
      .where(eq(roomCleaningRecords.id, recordId))
      .limit(1)

    if (record.length === 0) {
      return NextResponse.json(
        { success: false, code: 'RECORD_NOT_FOUND', message: '房间清理记录不存在' },
        { status: 404 }
      )
    }

    // 处理数据格式
    const processedRecord = {
      ...record[0],
      cleaningTasks: record[0].cleaningTasks ? JSON.parse(record[0].cleaningTasks) : [],
      personalItemsInventory: record[0].personalItemsInventory ? JSON.parse(record[0].personalItemsInventory) : [],
      facilityInspection: record[0].facilityInspection ? JSON.parse(record[0].facilityInspection) : [],
      damageAssessment: record[0].damageAssessment ? JSON.parse(record[0].damageAssessment) : [],
      foundItems: record[0].foundItems ? JSON.parse(record[0].foundItems) : [],
      maintenanceItems: record[0].maintenanceItems ? JSON.parse(record[0].maintenanceItems) : [],
      room: record[0].room ? {
        ...record[0].room,
        facilities: record[0].room.facilities ? JSON.parse(record[0].room.facilities) : [],
      } : null,
    }

    return NextResponse.json({
      success: true,
      data: processedRecord,
    })
  } catch (error) {
    console.error('获取房间清理记录详情失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// PUT - 更新房间清理记录
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const recordId = params.id
    const body = await request.json()
    const validatedData = updateCleaningRecordSchema.parse(body)

    // 检查清理记录是否存在
    const existingRecord = await db
      .select({
        id: roomCleaningRecords.id,
        cleaningStatus: roomCleaningRecords.cleaningStatus,
        assignedTo: roomCleaningRecords.assignedTo,
        applicationId: roomCleaningRecords.applicationId,
      })
      .from(roomCleaningRecords)
      .where(eq(roomCleaningRecords.id, recordId))
      .limit(1)

    if (existingRecord.length === 0) {
      return NextResponse.json(
        { success: false, code: 'RECORD_NOT_FOUND', message: '房间清理记录不存在' },
        { status: 404 }
      )
    }

    // 检查权限（只有分配的清理人员或管理员可以修改）
    if (existingRecord[0].assignedTo !== user.id) {
      // 这里可以添加管理员权限检查
      return NextResponse.json(
        { success: false, code: 'PERMISSION_DENIED', message: '只有分配的清理人员可以修改清理记录' },
        { status: 403 }
      )
    }

    // 检查是否可以修改
    const currentStatus = existingRecord[0].cleaningStatus
    if (currentStatus === 4) { // 已完成
      return NextResponse.json(
        { 
          success: false, 
          code: 'CANNOT_UPDATE', 
          message: '该清理记录已完成，无法修改' 
        },
        { status: 400 }
      )
    }

    // 准备更新数据
    const updateData: any = {
      updatedAt: new Date(),
    }

    // 只更新提供的字段
    if (validatedData.cleaningTasks !== undefined) updateData.cleaningTasks = JSON.stringify(validatedData.cleaningTasks)
    if (validatedData.personalItemsInventory !== undefined) updateData.personalItemsInventory = JSON.stringify(validatedData.personalItemsInventory)
    if (validatedData.facilityInspection !== undefined) updateData.facilityInspection = JSON.stringify(validatedData.facilityInspection)
    if (validatedData.damageAssessment !== undefined) updateData.damageAssessment = JSON.stringify(validatedData.damageAssessment)
    if (validatedData.foundItems !== undefined) updateData.foundItems = JSON.stringify(validatedData.foundItems)
    if (validatedData.maintenanceItems !== undefined) updateData.maintenanceItems = JSON.stringify(validatedData.maintenanceItems)
    if (validatedData.cleaningStatus !== undefined) updateData.cleaningStatus = validatedData.cleaningStatus
    if (validatedData.startTime !== undefined) updateData.startTime = validatedData.startTime
    if (validatedData.endTime !== undefined) updateData.endTime = validatedData.endTime
    if (validatedData.inspectionTime !== undefined) updateData.inspectionTime = validatedData.inspectionTime
    if (validatedData.cleaningResult !== undefined) updateData.cleaningResult = validatedData.cleaningResult
    if (validatedData.maintenanceNeeded !== undefined) updateData.maintenanceNeeded = validatedData.maintenanceNeeded
    if (validatedData.notes !== undefined) updateData.notes = validatedData.notes

    // 开始事务处理更新
    await db.transaction(async (tx) => {
      // 更新清理记录
      await tx
        .update(roomCleaningRecords)
        .set(updateData)
        .where(eq(roomCleaningRecords.id, recordId))

      // 如果状态更新为已完成，同时更新申请状态
      if (validatedData.cleaningStatus === 4) {
        await tx
          .update(checkoutApplications)
          .set({
            status: 7, // 已完成
            updatedAt: new Date(),
          })
          .where(eq(checkoutApplications.id, existingRecord[0].applicationId))
      }
    })

    return NextResponse.json({
      success: true,
      message: '房间清理记录更新成功',
    })
  } catch (error) {
    console.error('更新房间清理记录失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'VALIDATION_ERROR', 
          message: '数据验证失败',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// DELETE - 删除房间清理记录
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const recordId = params.id

    // 检查清理记录是否存在
    const existingRecord = await db
      .select({
        id: roomCleaningRecords.id,
        cleaningStatus: roomCleaningRecords.cleaningStatus,
        assignedTo: roomCleaningRecords.assignedTo,
      })
      .from(roomCleaningRecords)
      .where(eq(roomCleaningRecords.id, recordId))
      .limit(1)

    if (existingRecord.length === 0) {
      return NextResponse.json(
        { success: false, code: 'RECORD_NOT_FOUND', message: '房间清理记录不存在' },
        { status: 404 }
      )
    }

    // 检查权限（只有分配的清理人员或管理员可以删除）
    if (existingRecord[0].assignedTo !== user.id) {
      // 这里可以添加管理员权限检查
      return NextResponse.json(
        { success: false, code: 'PERMISSION_DENIED', message: '只有分配的清理人员可以删除清理记录' },
        { status: 403 }
      )
    }

    // 检查是否可以删除
    const currentStatus = existingRecord[0].cleaningStatus
    if (currentStatus === 4) { // 已完成
      return NextResponse.json(
        { 
          success: false, 
          code: 'CANNOT_DELETE', 
          message: '该清理记录已完成，无法删除' 
        },
        { status: 400 }
      )
    }

    // 删除清理记录
    await db
      .delete(roomCleaningRecords)
      .where(eq(roomCleaningRecords.id, recordId))

    return NextResponse.json({
      success: true,
      message: '房间清理记录删除成功',
    })
  } catch (error) {
    console.error('删除房间清理记录失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
