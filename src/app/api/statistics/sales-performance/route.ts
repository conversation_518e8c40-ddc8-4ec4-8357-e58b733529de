import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { consultations, consultationFollowUps, betterAuthUsers } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, gte, lte, count, sql } from 'drizzle-orm'

// 查询参数验证
const querySchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  userId: z.string().optional(),
  period: z.enum(['week', 'month', 'quarter', 'year']).optional(),
})

/**
 * GET /api/statistics/sales-performance - 获取销售员业绩统计
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问', code: 'UNAUTHORIZED' },
        { status: 401 }
      )
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url)
    const queryParams = {
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
      userId: searchParams.get('userId'),
      period: searchParams.get('period'),
    }

    // 验证参数
    const validatedParams = querySchema.safeParse(queryParams)
    if (!validatedParams.success) {
      return NextResponse.json(
        { success: false, error: '请求参数格式错误', code: 'INVALID_PARAMS' },
        { status: 400 }
      )
    }

    const { startDate, endDate, userId, period } = validatedParams.data

    // 计算时间范围
    const dateRange = calculateDateRange(period, startDate, endDate)

    // 构建查询条件
    const whereConditions = []
    if (dateRange.start) {
      whereConditions.push(gte(consultations.createdAt, new Date(dateRange.start)))
    }
    if (dateRange.end) {
      whereConditions.push(lte(consultations.createdAt, new Date(dateRange.end)))
    }
    if (userId) {
      whereConditions.push(eq(consultations.assignedUserId, userId))
    }

    // 获取销售员业绩统计
    const performanceStats = await getSalesPerformanceStats(whereConditions)

    // 获取详细的销售员排行榜
    const salesRanking = await getSalesRanking(whereConditions)

    // 获取时间趋势数据
    const trendData = await getSalesTrendData(whereConditions, period || 'month')

    return NextResponse.json({
      success: true,
      data: {
        overview: performanceStats,
        ranking: salesRanking,
        trend: trendData,
        dateRange,
      },
      message: '销售业绩统计获取成功',
    })
  } catch (error) {
    console.error('获取销售业绩统计失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误', code: 'INTERNAL_ERROR' },
      { status: 500 }
    )
  }
}

/**
 * 计算日期范围
 */
function calculateDateRange(period?: string, startDate?: string, endDate?: string) {
  const now = new Date()
  let start: string | undefined
  let end: string | undefined

  if (startDate && endDate) {
    start = startDate
    end = endDate
  } else if (period) {
    switch (period) {
      case 'week':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        end = now.toISOString().split('T')[0]
        break
      case 'month':
        start = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0]
        end = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0]
        break
      case 'quarter':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3
        start = new Date(now.getFullYear(), quarterStart, 1).toISOString().split('T')[0]
        end = new Date(now.getFullYear(), quarterStart + 3, 0).toISOString().split('T')[0]
        break
      case 'year':
        start = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0]
        end = new Date(now.getFullYear(), 11, 31).toISOString().split('T')[0]
        break
    }
  }

  return { start, end }
}

/**
 * 获取销售业绩统计概览
 */
async function getSalesPerformanceStats(whereConditions: any[]) {
  // 总咨询数
  const [totalConsultations] = await db
    .select({ count: count() })
    .from(consultations)
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

  // 已签约数 (status = 3)
  const [signedConsultations] = await db
    .select({ count: count() })
    .from(consultations)
    .where(
      whereConditions.length > 0
        ? and(eq(consultations.status, 3), ...whereConditions)
        : eq(consultations.status, 3)
    )

  // 跟进记录总数
  const [totalFollowUps] = await db
    .select({ count: count() })
    .from(consultationFollowUps)
    .leftJoin(consultations, eq(consultationFollowUps.consultationId, consultations.id))
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

  // 活跃销售员数量
  const activeSalesCount = await db
    .selectDistinct({ userId: consultations.assignedUserId })
    .from(consultations)
    .where(
      whereConditions.length > 0
        ? and(...whereConditions, sql`${consultations.assignedUserId} IS NOT NULL`)
        : sql`${consultations.assignedUserId} IS NOT NULL`
    )

  // 计算转化率
  const conversionRate = totalConsultations.count > 0
    ? (signedConsultations.count / totalConsultations.count * 100).toFixed(2)
    : '0.00'

  return {
    totalConsultations: totalConsultations.count,
    signedConsultations: signedConsultations.count,
    totalFollowUps: totalFollowUps.count,
    activeSalesCount: activeSalesCount.length,
    conversionRate: parseFloat(conversionRate),
  }
}

/**
 * 获取销售员排行榜
 */
async function getSalesRanking(whereConditions: any[]) {
  const ranking = await db
    .select({
      userId: consultations.assignedUserId,
      userName: betterAuthUsers.name,
      userEmail: betterAuthUsers.email,
      consultationCount: count(consultations.id),
      signedCount: sql<number>`SUM(CASE WHEN ${consultations.status} = 3 THEN 1 ELSE 0 END)`,
    })
    .from(consultations)
    .leftJoin(betterAuthUsers, eq(consultations.assignedUserId, betterAuthUsers.id))
    .where(
      whereConditions.length > 0
        ? and(...whereConditions, sql`${consultations.assignedUserId} IS NOT NULL`)
        : sql`${consultations.assignedUserId} IS NOT NULL`
    )
    .groupBy(consultations.assignedUserId, betterAuthUsers.name, betterAuthUsers.email)
    .orderBy(sql`COUNT(${consultations.id}) DESC`)
    .limit(10)

  // 获取每个销售员的跟进次数
  const followUpCounts = await db
    .select({
      userId: consultationFollowUps.userId,
      followUpCount: count(consultationFollowUps.id),
    })
    .from(consultationFollowUps)
    .leftJoin(consultations, eq(consultationFollowUps.consultationId, consultations.id))
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
    .groupBy(consultationFollowUps.userId)

  // 合并数据
  const followUpMap = new Map(followUpCounts.map(item => [item.userId, item.followUpCount]))

  return ranking.map(item => ({
    userId: item.userId,
    userName: item.userName,
    userEmail: item.userEmail,
    consultationCount: item.consultationCount,
    signedCount: Number(item.signedCount),
    followUpCount: followUpMap.get(item.userId) || 0,
    conversionRate: item.consultationCount > 0
      ? (Number(item.signedCount) / item.consultationCount * 100).toFixed(2)
      : '0.00',
  }))
}

/**
 * 获取销售趋势数据
 */
async function getSalesTrendData(whereConditions: any[], period: string) {
  const dateFormat = period === 'week' || period === 'month' ? '%Y-%m-%d' : '%Y-%m'

  const trendData = await db
    .select({
      date: sql<string>`DATE_FORMAT(${consultations.createdAt}, ${dateFormat})`,
      consultationCount: count(consultations.id),
      signedCount: sql<number>`SUM(CASE WHEN ${consultations.status} = 3 THEN 1 ELSE 0 END)`,
    })
    .from(consultations)
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
    .groupBy(sql`DATE_FORMAT(${consultations.createdAt}, ${dateFormat})`)
    .orderBy(sql`DATE_FORMAT(${consultations.createdAt}, ${dateFormat})`)

  return trendData.map(item => ({
    date: item.date,
    consultationCount: item.consultationCount,
    signedCount: Number(item.signedCount),
    conversionRate: item.consultationCount > 0
      ? (Number(item.signedCount) / item.consultationCount * 100).toFixed(2)
      : '0.00',
  }))
}
