import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { consultations } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { and, gte, lte, count, sql, eq } from 'drizzle-orm'

// 查询参数验证
const querySchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  period: z.enum(['week', 'month', 'quarter', 'year']).optional(),
  groupBy: z.enum(['channel', 'time', 'both']).optional().default('channel'),
})

// 媒介渠道映射
const MEDIA_CHANNELS = {
  1: '电话咨询',
  2: '微信咨询',
  3: '现场咨询',
  4: '其他渠道',
} as const

/**
 * GET /api/statistics/channel-analysis - 获取媒介渠道效果分析
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问', code: 'UNAUTHORIZED' },
        { status: 401 }
      )
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url)
    const queryParams = {
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
      period: searchParams.get('period'),
      groupBy: searchParams.get('groupBy'),
    }

    // 验证参数
    const validatedParams = querySchema.safeParse(queryParams)
    if (!validatedParams.success) {
      return NextResponse.json(
        { success: false, error: '请求参数格式错误', code: 'INVALID_PARAMS' },
        { status: 400 }
      )
    }

    const { startDate, endDate, period, groupBy } = validatedParams.data

    // 计算时间范围
    const dateRange = calculateDateRange(period, startDate, endDate)

    // 构建查询条件
    const whereConditions = []
    if (dateRange.start) {
      whereConditions.push(gte(consultations.createdAt, new Date(dateRange.start)))
    }
    if (dateRange.end) {
      whereConditions.push(lte(consultations.createdAt, new Date(dateRange.end)))
    }

    // 根据分组方式获取数据
    let analysisData
    switch (groupBy) {
      case 'channel':
        analysisData = await getChannelAnalysis(whereConditions)
        break
      case 'time':
        analysisData = await getTimeAnalysis(whereConditions, period || 'month')
        break
      case 'both':
        analysisData = await getChannelTimeAnalysis(whereConditions, period || 'month')
        break
      default:
        analysisData = await getChannelAnalysis(whereConditions)
    }

    // 获取渠道对比数据
    const channelComparison = await getChannelComparison(whereConditions)

    // 获取渠道转化漏斗数据
    const conversionFunnel = await getConversionFunnel(whereConditions)

    return NextResponse.json({
      success: true,
      data: {
        analysis: analysisData,
        comparison: channelComparison,
        funnel: conversionFunnel,
        dateRange,
        channels: MEDIA_CHANNELS,
      },
      message: '媒介渠道分析获取成功',
    })
  } catch (error) {
    console.error('获取媒介渠道分析失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误', code: 'INTERNAL_ERROR' },
      { status: 500 }
    )
  }
}

/**
 * 计算日期范围
 */
function calculateDateRange(period?: string, startDate?: string, endDate?: string) {
  const now = new Date()
  let start: string | undefined
  let end: string | undefined

  if (startDate && endDate) {
    start = startDate
    end = endDate
  } else if (period) {
    switch (period) {
      case 'week':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        end = now.toISOString().split('T')[0]
        break
      case 'month':
        start = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0]
        end = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0]
        break
      case 'quarter':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3
        start = new Date(now.getFullYear(), quarterStart, 1).toISOString().split('T')[0]
        end = new Date(now.getFullYear(), quarterStart + 3, 0).toISOString().split('T')[0]
        break
      case 'year':
        start = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0]
        end = new Date(now.getFullYear(), 11, 31).toISOString().split('T')[0]
        break
    }
  }

  return { start, end }
}

/**
 * 获取渠道分析数据
 */
async function getChannelAnalysis(whereConditions: any[]) {
  const channelStats = await db
    .select({
      mediaChannel: consultations.mediaChannel,
      totalCount: count(consultations.id),
      signedCount: sql<number>`SUM(CASE WHEN ${consultations.status} = 3 THEN 1 ELSE 0 END)`,
      followingCount: sql<number>`SUM(CASE WHEN ${consultations.status} = 2 THEN 1 ELSE 0 END)`,
      pendingCount: sql<number>`SUM(CASE WHEN ${consultations.status} = 1 THEN 1 ELSE 0 END)`,
      abandonedCount: sql<number>`SUM(CASE WHEN ${consultations.status} = 4 THEN 1 ELSE 0 END)`,
    })
    .from(consultations)
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
    .groupBy(consultations.mediaChannel)
    .orderBy(consultations.mediaChannel)

  return channelStats.map(item => ({
    channel: item.mediaChannel,
    channelName: MEDIA_CHANNELS[item.mediaChannel as keyof typeof MEDIA_CHANNELS] || '未知渠道',
    totalCount: item.totalCount,
    signedCount: Number(item.signedCount),
    followingCount: Number(item.followingCount),
    pendingCount: Number(item.pendingCount),
    abandonedCount: Number(item.abandonedCount),
    conversionRate: item.totalCount > 0
      ? (Number(item.signedCount) / item.totalCount * 100).toFixed(2)
      : '0.00',
    followUpRate: item.totalCount > 0
      ? ((Number(item.followingCount) + Number(item.signedCount)) / item.totalCount * 100).toFixed(2)
      : '0.00',
  }))
}

/**
 * 获取时间趋势分析
 */
async function getTimeAnalysis(whereConditions: any[], period: string) {
  const dateFormat = period === 'week' || period === 'month' ? '%Y-%m-%d' : '%Y-%m'

  const timeStats = await db
    .select({
      date: sql<string>`DATE_FORMAT(${consultations.createdAt}, ${dateFormat})`,
      totalCount: count(consultations.id),
      signedCount: sql<number>`SUM(CASE WHEN ${consultations.status} = 3 THEN 1 ELSE 0 END)`,
    })
    .from(consultations)
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
    .groupBy(sql`DATE_FORMAT(${consultations.createdAt}, ${dateFormat})`)
    .orderBy(sql`DATE_FORMAT(${consultations.createdAt}, ${dateFormat})`)

  return timeStats.map(item => ({
    date: item.date,
    totalCount: item.totalCount,
    signedCount: Number(item.signedCount),
    conversionRate: item.totalCount > 0
      ? (Number(item.signedCount) / item.totalCount * 100).toFixed(2)
      : '0.00',
  }))
}

/**
 * 获取渠道时间分析
 */
async function getChannelTimeAnalysis(whereConditions: any[], period: string) {
  const dateFormat = period === 'week' || period === 'month' ? '%Y-%m-%d' : '%Y-%m'

  const channelTimeStats = await db
    .select({
      date: sql<string>`DATE_FORMAT(${consultations.createdAt}, ${dateFormat})`,
      mediaChannel: consultations.mediaChannel,
      totalCount: count(consultations.id),
      signedCount: sql<number>`SUM(CASE WHEN ${consultations.status} = 3 THEN 1 ELSE 0 END)`,
    })
    .from(consultations)
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
    .groupBy(
      sql`DATE_FORMAT(${consultations.createdAt}, ${dateFormat})`,
      consultations.mediaChannel
    )
    .orderBy(
      sql`DATE_FORMAT(${consultations.createdAt}, ${dateFormat})`,
      consultations.mediaChannel
    )

  return channelTimeStats.map(item => ({
    date: item.date,
    channel: item.mediaChannel,
    channelName: MEDIA_CHANNELS[item.mediaChannel as keyof typeof MEDIA_CHANNELS] || '未知渠道',
    totalCount: item.totalCount,
    signedCount: Number(item.signedCount),
    conversionRate: item.totalCount > 0
      ? (Number(item.signedCount) / item.totalCount * 100).toFixed(2)
      : '0.00',
  }))
}

/**
 * 获取渠道对比数据
 */
async function getChannelComparison(whereConditions: any[]) {
  const comparison = await getChannelAnalysis(whereConditions)
  
  // 计算总计
  const totals = comparison.reduce(
    (acc, item) => ({
      totalCount: acc.totalCount + item.totalCount,
      signedCount: acc.signedCount + item.signedCount,
      followingCount: acc.followingCount + item.followingCount,
      pendingCount: acc.pendingCount + item.pendingCount,
      abandonedCount: acc.abandonedCount + item.abandonedCount,
    }),
    { totalCount: 0, signedCount: 0, followingCount: 0, pendingCount: 0, abandonedCount: 0 }
  )

  // 计算各渠道占比
  return comparison.map(item => ({
    ...item,
    percentage: totals.totalCount > 0
      ? (item.totalCount / totals.totalCount * 100).toFixed(2)
      : '0.00',
  }))
}

/**
 * 获取转化漏斗数据
 */
async function getConversionFunnel(whereConditions: any[]) {
  const funnelData = await db
    .select({
      mediaChannel: consultations.mediaChannel,
      totalConsultations: count(consultations.id),
      followedUp: sql<number>`SUM(CASE WHEN ${consultations.status} IN (2, 3) THEN 1 ELSE 0 END)`,
      signed: sql<number>`SUM(CASE WHEN ${consultations.status} = 3 THEN 1 ELSE 0 END)`,
    })
    .from(consultations)
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
    .groupBy(consultations.mediaChannel)

  return funnelData.map(item => {
    const total = item.totalConsultations
    const followed = Number(item.followedUp)
    const signed = Number(item.signed)

    return {
      channel: item.mediaChannel,
      channelName: MEDIA_CHANNELS[item.mediaChannel as keyof typeof MEDIA_CHANNELS] || '未知渠道',
      stages: [
        {
          name: '咨询接待',
          count: total,
          percentage: 100,
        },
        {
          name: '跟进阶段',
          count: followed,
          percentage: total > 0 ? (followed / total * 100).toFixed(2) : '0.00',
        },
        {
          name: '成功签约',
          count: signed,
          percentage: total > 0 ? (signed / total * 100).toFixed(2) : '0.00',
        },
      ],
    }
  })
}
