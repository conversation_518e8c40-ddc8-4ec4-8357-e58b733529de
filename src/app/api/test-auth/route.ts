import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth-utils'

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)

    if (!user) {
      return NextResponse.json(
        { error: '未授权访问', code: 'UNAUTHORIZED' },
        { status: 401 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        },
        message: '认证成功',
      },
    })
  } catch (error) {
    console.error('认证测试失败:', error)
    return NextResponse.json(
      { error: '服务器错误', code: 'SERVER_ERROR' },
      { status: 500 }
    )
  }
}
