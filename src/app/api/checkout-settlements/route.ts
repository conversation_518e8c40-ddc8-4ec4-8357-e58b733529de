import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import {
  checkoutSettlements,
  checkoutApplications,
  elderInfo,
  roomAssignments,
  rooms,
  bills,
  payments,
  betterAuthUsers,
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, like, desc, asc, count, gte, lte, sum } from 'drizzle-orm'
import { format, differenceInDays } from 'date-fns'

// 查询参数验证 schema
const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('10'),
  search: z.string().optional(),
  status: z.string().optional(),
  refundStatus: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
})

// 创建费用结算验证 schema
const createSettlementSchema = z.object({
  applicationId: z.string().uuid('请选择有效的申请'),
  settlementPeriodStart: z.string().min(1, '请选择结算开始日期'),
  settlementPeriodEnd: z.string().min(1, '请选择结算结束日期'),

  // 费用明细
  accommodationFees: z
    .number()
    .min(0, '住宿费不能为负数')
    .optional()
    .default(0),
  careFees: z.number().min(0, '护理费不能为负数').optional().default(0),
  mealFees: z.number().min(0, '餐费不能为负数').optional().default(0),
  medicalFees: z.number().min(0, '医疗费不能为负数').optional().default(0),
  otherFees: z.number().min(0, '其他费用不能为负数').optional().default(0),

  // 扣除项目
  damageFees: z.number().min(0, '损坏赔偿不能为负数').optional().default(0),
  cleaningFees: z.number().min(0, '清洁费不能为负数').optional().default(0),
  penaltyFees: z.number().min(0, '违约金不能为负数').optional().default(0),

  // 押金信息
  depositAmount: z.number().min(0, '押金金额不能为负数').optional().default(0),

  // 退款信息
  refundMethod: z.number().min(1).max(4).optional(),
  refundAccount: z.string().optional(),

  notes: z.string().optional(),
})

// GET - 获取费用结算列表
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const validatedParams = querySchema.parse(Object.fromEntries(searchParams))

    const page = parseInt(validatedParams.page)
    const limit = parseInt(validatedParams.limit)
    const offset = (page - 1) * limit

    // 构建查询条件
    const whereConditions = []

    // 搜索条件
    if (validatedParams.search) {
      const searchTerm = `%${validatedParams.search}%`
      whereConditions.push(
        or(
          like(checkoutSettlements.settlementNumber, searchTerm),
          like(checkoutApplications.applicationNumber, searchTerm),
          like(elderInfo.name, searchTerm),
          like(elderInfo.idCard, searchTerm)
        )
      )
    }

    // 状态筛选
    if (validatedParams.status) {
      whereConditions.push(
        eq(checkoutSettlements.status, parseInt(validatedParams.status))
      )
    }

    // 退款状态筛选
    if (validatedParams.refundStatus) {
      whereConditions.push(
        eq(
          checkoutSettlements.refundStatus,
          parseInt(validatedParams.refundStatus)
        )
      )
    }

    // 日期范围筛选
    if (validatedParams.dateFrom) {
      whereConditions.push(
        gte(checkoutSettlements.settlementDate, validatedParams.dateFrom)
      )
    }
    if (validatedParams.dateTo) {
      whereConditions.push(
        lte(checkoutSettlements.settlementDate, validatedParams.dateTo)
      )
    }

    // 排序
    const orderBy =
      validatedParams.sortOrder === 'asc'
        ? asc(
            checkoutSettlements[
              validatedParams.sortBy as keyof typeof checkoutSettlements
            ]
          )
        : desc(
            checkoutSettlements[
              validatedParams.sortBy as keyof typeof checkoutSettlements
            ]
          )

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(checkoutSettlements)
      .leftJoin(
        checkoutApplications,
        eq(checkoutSettlements.applicationId, checkoutApplications.id)
      )
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0].count

    // 获取结算列表
    const settlements = await db
      .select({
        // 结算信息
        id: checkoutSettlements.id,
        settlementNumber: checkoutSettlements.settlementNumber,
        totalCharges: checkoutSettlements.totalCharges,
        totalPayments: checkoutSettlements.totalPayments,
        depositAmount: checkoutSettlements.depositAmount,
        refundableDeposit: checkoutSettlements.refundableDeposit,
        outstandingBalance: checkoutSettlements.outstandingBalance,
        finalRefund: checkoutSettlements.finalRefund,
        accommodationFees: checkoutSettlements.accommodationFees,
        careFees: checkoutSettlements.careFees,
        mealFees: checkoutSettlements.mealFees,
        medicalFees: checkoutSettlements.medicalFees,
        otherFees: checkoutSettlements.otherFees,
        damageFees: checkoutSettlements.damageFees,
        cleaningFees: checkoutSettlements.cleaningFees,
        penaltyFees: checkoutSettlements.penaltyFees,
        settlementDate: checkoutSettlements.settlementDate,
        settlementPeriodStart: checkoutSettlements.settlementPeriodStart,
        settlementPeriodEnd: checkoutSettlements.settlementPeriodEnd,
        refundMethod: checkoutSettlements.refundMethod,
        refundAccount: checkoutSettlements.refundAccount,
        refundDate: checkoutSettlements.refundDate,
        refundStatus: checkoutSettlements.refundStatus,
        status: checkoutSettlements.status,
        notes: checkoutSettlements.notes,
        confirmedAt: checkoutSettlements.confirmedAt,
        createdAt: checkoutSettlements.createdAt,
        updatedAt: checkoutSettlements.updatedAt,

        // 申请信息
        application: {
          id: checkoutApplications.id,
          applicationNumber: checkoutApplications.applicationNumber,
          applicantName: checkoutApplications.applicantName,
          checkoutType: checkoutApplications.checkoutType,
          expectedCheckoutDate: checkoutApplications.expectedCheckoutDate,
          actualCheckoutDate: checkoutApplications.actualCheckoutDate,
          status: checkoutApplications.status,
        },

        // 老人信息
        elder: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
          idCard: elderInfo.idCard,
          careLevel: elderInfo.careLevel,
          admissionDate: elderInfo.admissionDate,
        },

        // 房间信息
        room: {
          id: rooms.id,
          roomNumber: rooms.roomNumber,
          roomType: rooms.roomType,
          monthlyRate: rooms.monthlyRate,
        },

        // 计算人员
        calculatedBy: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(checkoutSettlements)
      .leftJoin(
        checkoutApplications,
        eq(checkoutSettlements.applicationId, checkoutApplications.id)
      )
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .leftJoin(
        roomAssignments,
        eq(checkoutApplications.roomAssignmentId, roomAssignments.id)
      )
      .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
      .leftJoin(
        betterAuthUsers,
        eq(checkoutSettlements.calculatedBy, betterAuthUsers.id)
      )
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset)

    return NextResponse.json({
      success: true,
      data: {
        list: settlements,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error('获取费用结算列表失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// POST - 创建费用结算
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createSettlementSchema.parse(body)

    // 检查申请是否存在且状态正确
    const application = await db
      .select({
        id: checkoutApplications.id,
        status: checkoutApplications.status,
        elderInfoId: checkoutApplications.elderInfoId,
        roomAssignmentId: checkoutApplications.roomAssignmentId,
        applicationNumber: checkoutApplications.applicationNumber,
      })
      .from(checkoutApplications)
      .where(eq(checkoutApplications.id, validatedData.applicationId))
      .limit(1)

    if (application.length === 0) {
      return NextResponse.json(
        {
          success: false,
          code: 'APPLICATION_NOT_FOUND',
          message: '退住申请不存在',
        },
        { status: 404 }
      )
    }

    if (application[0].status !== 3) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_STATUS',
          message: '只有已通过的申请才能进行费用结算',
        },
        { status: 400 }
      )
    }

    // 检查是否已有结算记录
    const existingSettlement = await db
      .select()
      .from(checkoutSettlements)
      .where(eq(checkoutSettlements.applicationId, validatedData.applicationId))
      .limit(1)

    if (existingSettlement.length > 0) {
      return NextResponse.json(
        {
          success: false,
          code: 'SETTLEMENT_EXISTS',
          message: '该申请已有费用结算记录',
        },
        { status: 400 }
      )
    }

    // 生成结算编号
    const today = format(new Date(), 'yyyyMMdd')
    const countResult = await db
      .select({ count: count() })
      .from(checkoutSettlements)
      .where(like(checkoutSettlements.settlementNumber, `ST${today}%`))

    const sequence = String(countResult[0].count + 1).padStart(3, '0')
    const settlementNumber = `ST${today}${sequence}`

    // 获取老人和房间信息用于自动计算费用
    const elderData = await db
      .select({
        elder: elderInfo,
        room: rooms,
        assignment: roomAssignments,
      })
      .from(elderInfo)
      .leftJoin(roomAssignments, eq(elderInfo.id, roomAssignments.elderInfoId))
      .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
      .where(eq(elderInfo.id, application[0].elderInfoId))
      .limit(1)

    // 计算费用
    let autoCalculatedFees = {
      accommodationFees: validatedData.accommodationFees,
      careFees: validatedData.careFees,
      mealFees: validatedData.mealFees,
      medicalFees: validatedData.medicalFees,
      otherFees: validatedData.otherFees,
    }

    if (elderData.length > 0 && elderData[0].room) {
      const elder = elderData[0].elder
      const room = elderData[0].room

      // 计算住宿天数
      const admissionDate = new Date(elder.admissionDate || new Date())
      const settlementEndDate = new Date(validatedData.settlementPeriodEnd)
      const daysStayed = differenceInDays(settlementEndDate, admissionDate) + 1

      // 计算住宿费（如果未提供）
      if (validatedData.accommodationFees === 0) {
        const monthlyRate = parseFloat(room.monthlyRate || '0')
        const dailyRate = monthlyRate / 30
        autoCalculatedFees.accommodationFees = dailyRate * daysStayed
      }
    }

    // 获取已支付费用 (简化处理，实际应该根据具体业务逻辑计算)
    const totalPayments = 0 // 这里应该查询实际的支付记录

    // 计算总费用和最终退款
    const totalCharges =
      autoCalculatedFees.accommodationFees +
      autoCalculatedFees.careFees +
      autoCalculatedFees.mealFees +
      autoCalculatedFees.medicalFees +
      autoCalculatedFees.otherFees
    const totalDeductions =
      validatedData.damageFees +
      validatedData.cleaningFees +
      validatedData.penaltyFees
    const refundableDeposit = Math.max(
      0,
      validatedData.depositAmount - totalDeductions
    )
    const outstandingBalance = Math.max(0, totalCharges - totalPayments)
    const finalRefund = Math.max(0, refundableDeposit - outstandingBalance)

    // 创建费用结算记录
    const newSettlement = await db
      .insert(checkoutSettlements)
      .values({
        applicationId: validatedData.applicationId,
        settlementNumber,
        totalCharges: totalCharges.toString(),
        totalPayments: totalPayments.toString(),
        depositAmount: validatedData.depositAmount.toString(),
        refundableDeposit: refundableDeposit.toString(),
        outstandingBalance: outstandingBalance.toString(),
        finalRefund: finalRefund.toString(),
        accommodationFees: autoCalculatedFees.accommodationFees.toString(),
        careFees: autoCalculatedFees.careFees.toString(),
        mealFees: autoCalculatedFees.mealFees.toString(),
        medicalFees: autoCalculatedFees.medicalFees.toString(),
        otherFees: autoCalculatedFees.otherFees.toString(),
        damageFees: validatedData.damageFees.toString(),
        cleaningFees: validatedData.cleaningFees.toString(),
        penaltyFees: validatedData.penaltyFees.toString(),
        settlementDate: format(new Date(), 'yyyy-MM-dd'),
        settlementPeriodStart: validatedData.settlementPeriodStart,
        settlementPeriodEnd: validatedData.settlementPeriodEnd,
        refundMethod: validatedData.refundMethod,
        refundAccount: validatedData.refundAccount,
        notes: validatedData.notes,
        calculatedBy: user.id,
      })
      .returning()

    return NextResponse.json({
      success: true,
      data: newSettlement[0],
      message: '费用结算创建成功',
    })
  } catch (error) {
    console.error('创建费用结算失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'VALIDATION_ERROR',
          message: '数据验证失败',
          errors: error.errors,
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
