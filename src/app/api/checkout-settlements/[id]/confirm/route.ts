import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  checkoutSettlements,
  checkoutApplications
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq } from 'drizzle-orm'

// 确认结算验证 schema
const confirmSettlementSchema = z.object({
  confirmed: z.boolean(),
  notes: z.string().optional(),
})

// POST - 确认费用结算
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const settlementId = params.id
    const body = await request.json()
    const validatedData = confirmSettlementSchema.parse(body)

    // 检查结算记录是否存在
    const existingSettlement = await db
      .select({
        id: checkoutSettlements.id,
        status: checkoutSettlements.status,
        applicationId: checkoutSettlements.applicationId,
      })
      .from(checkoutSettlements)
      .where(eq(checkoutSettlements.id, settlementId))
      .limit(1)

    if (existingSettlement.length === 0) {
      return NextResponse.json(
        { success: false, code: 'SETTLEMENT_NOT_FOUND', message: '费用结算记录不存在' },
        { status: 404 }
      )
    }

    // 检查当前状态
    const currentStatus = existingSettlement[0].status
    if (currentStatus === 4) { // 已完成
      return NextResponse.json(
        { 
          success: false, 
          code: 'ALREADY_CONFIRMED', 
          message: '该结算已确认完成' 
        },
        { status: 400 }
      )
    }

    if (currentStatus !== 2) { // 待确认
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_STATUS', 
          message: '只有待确认状态的结算才能进行确认操作' 
        },
        { status: 400 }
      )
    }

    // 开始事务处理确认
    await db.transaction(async (tx) => {
      if (validatedData.confirmed) {
        // 确认结算
        await tx
          .update(checkoutSettlements)
          .set({
            status: 3, // 已确认
            confirmedBy: user.id,
            confirmedAt: new Date(),
            notes: validatedData.notes,
            updatedAt: new Date(),
          })
          .where(eq(checkoutSettlements.id, settlementId))

        // 更新申请状态为费用结算中
        await tx
          .update(checkoutApplications)
          .set({
            status: 5, // 费用结算中
            updatedAt: new Date(),
          })
          .where(eq(checkoutApplications.id, existingSettlement[0].applicationId))
      } else {
        // 拒绝确认，回到计算中状态
        await tx
          .update(checkoutSettlements)
          .set({
            status: 1, // 计算中
            notes: validatedData.notes,
            updatedAt: new Date(),
          })
          .where(eq(checkoutSettlements.id, settlementId))
      }
    })

    return NextResponse.json({
      success: true,
      message: validatedData.confirmed ? '费用结算确认成功' : '费用结算已退回修改',
    })
  } catch (error) {
    console.error('确认费用结算失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'VALIDATION_ERROR', 
          message: '数据验证失败',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
