import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  checkoutApplications,
  elderInfo,
  checkoutSettlements,
  roomCleaningRecords
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, gte, lte, count, sum, avg, sql } from 'drizzle-orm'
import { format, subDays, subMonths, startOfMonth, endOfMonth } from 'date-fns'

// 查询参数验证 schema
const statsQuerySchema = z.object({
  period: z.enum(['week', 'month', 'quarter', 'year']).optional().default('month'),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// GET - 获取退住申请统计数据
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const validatedParams = statsQuerySchema.parse(Object.fromEntries(searchParams))

    // 计算时间范围
    let startDate: string
    let endDate: string

    if (validatedParams.startDate && validatedParams.endDate) {
      startDate = validatedParams.startDate
      endDate = validatedParams.endDate
    } else {
      const now = new Date()
      endDate = format(now, 'yyyy-MM-dd')
      
      switch (validatedParams.period) {
        case 'week':
          startDate = format(subDays(now, 7), 'yyyy-MM-dd')
          break
        case 'month':
          startDate = format(subMonths(now, 1), 'yyyy-MM-dd')
          break
        case 'quarter':
          startDate = format(subMonths(now, 3), 'yyyy-MM-dd')
          break
        case 'year':
          startDate = format(subMonths(now, 12), 'yyyy-MM-dd')
          break
        default:
          startDate = format(subMonths(now, 1), 'yyyy-MM-dd')
      }
    }

    // 1. 基础统计数据
    const basicStats = await getBasicStats(startDate, endDate)

    // 2. 状态分布统计
    const statusStats = await getStatusStats(startDate, endDate)

    // 3. 退住类型统计
    const typeStats = await getTypeStats(startDate, endDate)

    // 4. 时间趋势统计
    const trendStats = await getTrendStats(startDate, endDate, validatedParams.period)

    // 5. 费用统计
    const feeStats = await getFeeStats(startDate, endDate)

    // 6. 处理时长统计
    const processingTimeStats = await getProcessingTimeStats(startDate, endDate)

    // 7. 房间清理统计
    const cleaningStats = await getCleaningStats(startDate, endDate)

    return NextResponse.json({
      success: true,
      data: {
        period: {
          start: startDate,
          end: endDate,
          type: validatedParams.period,
        },
        basicStats,
        statusStats,
        typeStats,
        trendStats,
        feeStats,
        processingTimeStats,
        cleaningStats,
      },
    })
  } catch (error) {
    console.error('获取退住申请统计数据失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'VALIDATION_ERROR', 
          message: '参数验证失败',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 基础统计数据
async function getBasicStats(startDate: string, endDate: string) {
  const whereCondition = and(
    gte(checkoutApplications.applicationDate, startDate),
    lte(checkoutApplications.applicationDate, endDate)
  )

  // 总申请数
  const totalApplications = await db
    .select({ count: count() })
    .from(checkoutApplications)
    .where(whereCondition)

  // 已完成申请数
  const completedApplications = await db
    .select({ count: count() })
    .from(checkoutApplications)
    .where(and(whereCondition, eq(checkoutApplications.status, 7)))

  // 待处理申请数
  const pendingApplications = await db
    .select({ count: count() })
    .from(checkoutApplications)
    .where(and(
      whereCondition,
      sql`${checkoutApplications.status} IN (1, 2, 3, 5, 6)`
    ))

  // 被拒绝申请数
  const rejectedApplications = await db
    .select({ count: count() })
    .from(checkoutApplications)
    .where(and(whereCondition, eq(checkoutApplications.status, 4)))

  // 计算完成率
  const total = totalApplications[0].count
  const completed = completedApplications[0].count
  const completionRate = total > 0 ? (completed / total * 100).toFixed(2) : '0.00'

  return {
    total: total,
    completed: completed,
    pending: pendingApplications[0].count,
    rejected: rejectedApplications[0].count,
    completionRate: parseFloat(completionRate),
  }
}

// 状态分布统计
async function getStatusStats(startDate: string, endDate: string) {
  const statusStats = await db
    .select({
      status: checkoutApplications.status,
      count: count(),
    })
    .from(checkoutApplications)
    .where(and(
      gte(checkoutApplications.applicationDate, startDate),
      lte(checkoutApplications.applicationDate, endDate)
    ))
    .groupBy(checkoutApplications.status)

  const statusLabels = {
    1: '待审核',
    2: '审核中',
    3: '已通过',
    4: '已拒绝',
    5: '费用结算中',
    6: '房间清理中',
    7: '已完成',
  }

  return statusStats.map(stat => ({
    status: stat.status,
    label: statusLabels[stat.status as keyof typeof statusLabels] || '未知',
    count: stat.count,
  }))
}

// 退住类型统计
async function getTypeStats(startDate: string, endDate: string) {
  const typeStats = await db
    .select({
      checkoutType: checkoutApplications.checkoutType,
      count: count(),
    })
    .from(checkoutApplications)
    .where(and(
      gte(checkoutApplications.applicationDate, startDate),
      lte(checkoutApplications.applicationDate, endDate)
    ))
    .groupBy(checkoutApplications.checkoutType)

  const typeLabels = {
    1: '正常退住',
    2: '转院',
    3: '死亡',
    4: '其他',
  }

  return typeStats.map(stat => ({
    type: stat.checkoutType,
    label: typeLabels[stat.checkoutType as keyof typeof typeLabels] || '未知',
    count: stat.count,
  }))
}

// 时间趋势统计
async function getTrendStats(startDate: string, endDate: string, period: string) {
  // 根据时间周期生成日期范围
  const dates = generateDateRange(startDate, endDate, period)
  
  const trendData = []
  
  for (const date of dates) {
    const dayStart = date
    const dayEnd = date
    
    const dayStats = await db
      .select({
        applications: count(),
        completed: count(sql`CASE WHEN ${checkoutApplications.status} = 7 THEN 1 END`),
      })
      .from(checkoutApplications)
      .where(and(
        gte(checkoutApplications.applicationDate, dayStart),
        lte(checkoutApplications.applicationDate, dayEnd)
      ))

    trendData.push({
      date: date,
      applications: dayStats[0].applications,
      completed: dayStats[0].completed,
    })
  }

  return trendData
}

// 费用统计
async function getFeeStats(startDate: string, endDate: string) {
  const feeStats = await db
    .select({
      totalRefunds: sum(checkoutSettlements.finalRefund),
      avgRefund: avg(checkoutSettlements.finalRefund),
      totalDamageFees: sum(checkoutSettlements.damageFees),
      totalCleaningFees: sum(checkoutSettlements.cleaningFees),
      count: count(),
    })
    .from(checkoutSettlements)
    .leftJoin(checkoutApplications, eq(checkoutSettlements.applicationId, checkoutApplications.id))
    .where(and(
      gte(checkoutApplications.applicationDate, startDate),
      lte(checkoutApplications.applicationDate, endDate)
    ))

  return {
    totalRefunds: parseFloat(feeStats[0].totalRefunds || '0'),
    avgRefund: parseFloat(feeStats[0].avgRefund || '0'),
    totalDamageFees: parseFloat(feeStats[0].totalDamageFees || '0'),
    totalCleaningFees: parseFloat(feeStats[0].totalCleaningFees || '0'),
    settledCount: feeStats[0].count,
  }
}

// 处理时长统计
async function getProcessingTimeStats(startDate: string, endDate: string) {
  const completedApplications = await db
    .select({
      applicationDate: checkoutApplications.applicationDate,
      actualCheckoutDate: checkoutApplications.actualCheckoutDate,
    })
    .from(checkoutApplications)
    .where(and(
      gte(checkoutApplications.applicationDate, startDate),
      lte(checkoutApplications.applicationDate, endDate),
      eq(checkoutApplications.status, 7)
    ))

  const processingTimes = completedApplications
    .filter(app => app.actualCheckoutDate)
    .map(app => {
      const start = new Date(app.applicationDate)
      const end = new Date(app.actualCheckoutDate!)
      return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
    })

  if (processingTimes.length === 0) {
    return {
      avgProcessingDays: 0,
      minProcessingDays: 0,
      maxProcessingDays: 0,
      totalProcessed: 0,
    }
  }

  return {
    avgProcessingDays: Math.round(processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length),
    minProcessingDays: Math.min(...processingTimes),
    maxProcessingDays: Math.max(...processingTimes),
    totalProcessed: processingTimes.length,
  }
}

// 房间清理统计
async function getCleaningStats(startDate: string, endDate: string) {
  const cleaningStats = await db
    .select({
      total: count(),
      completed: count(sql`CASE WHEN ${roomCleaningRecords.cleaningStatus} = 5 THEN 1 END`),
      needMaintenance: count(sql`CASE WHEN ${roomCleaningRecords.maintenanceNeeded} = true THEN 1 END`),
    })
    .from(roomCleaningRecords)
    .leftJoin(checkoutApplications, eq(roomCleaningRecords.applicationId, checkoutApplications.id))
    .where(and(
      gte(checkoutApplications.applicationDate, startDate),
      lte(checkoutApplications.applicationDate, endDate)
    ))

  return {
    totalCleaningTasks: cleaningStats[0].total,
    completedCleaningTasks: cleaningStats[0].completed,
    roomsNeedMaintenance: cleaningStats[0].needMaintenance,
  }
}

// 生成日期范围
function generateDateRange(startDate: string, endDate: string, period: string): string[] {
  const dates: string[] = []
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  if (period === 'week' || period === 'month') {
    // 按天生成
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      dates.push(format(d, 'yyyy-MM-dd'))
    }
  } else {
    // 按月生成
    for (let d = new Date(start); d <= end; d.setMonth(d.getMonth() + 1)) {
      dates.push(format(startOfMonth(d), 'yyyy-MM-dd'))
    }
  }
  
  return dates
}
