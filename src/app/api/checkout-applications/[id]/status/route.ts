import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  checkoutApplications,
  elderInfo,
  roomAssignments,
  rooms,
  checkoutReviews,
  checkoutSettlements,
  roomCleaningRecords
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and } from 'drizzle-orm'
import { format } from 'date-fns'

// 更新状态验证 schema
const updateStatusSchema = z.object({
  status: z.number().min(1).max(7, '请选择有效的状态'),
  reviewNotes: z.string().optional(),
  actualCheckoutDate: z.string().optional(),
})

// 状态流转规则
const STATUS_TRANSITIONS = {
  1: [2, 4], // 待审核 -> 审核中, 已拒绝
  2: [3, 4], // 审核中 -> 已通过, 已拒绝
  3: [5],    // 已通过 -> 费用结算中
  4: [],     // 已拒绝 (终态)
  5: [6],    // 费用结算中 -> 房间清理中
  6: [7],    // 房间清理中 -> 已完成
  7: [],     // 已完成 (终态)
}

// 状态描述
const STATUS_DESCRIPTIONS = {
  1: '待审核',
  2: '审核中',
  3: '已通过',
  4: '已拒绝',
  5: '费用结算中',
  6: '房间清理中',
  7: '已完成',
}

// PUT - 更新退住申请状态
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const applicationId = params.id
    const body = await request.json()
    const validatedData = updateStatusSchema.parse(body)

    // 检查申请是否存在
    const existingApplication = await db
      .select({
        id: checkoutApplications.id,
        status: checkoutApplications.status,
        elderInfoId: checkoutApplications.elderInfoId,
        roomAssignmentId: checkoutApplications.roomAssignmentId,
        applicationNumber: checkoutApplications.applicationNumber,
      })
      .from(checkoutApplications)
      .where(eq(checkoutApplications.id, applicationId))
      .limit(1)

    if (existingApplication.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '退住申请不存在' },
        { status: 404 }
      )
    }

    const currentStatus = existingApplication[0].status
    const newStatus = validatedData.status

    // 检查状态流转是否合法
    if (!STATUS_TRANSITIONS[currentStatus as keyof typeof STATUS_TRANSITIONS].includes(newStatus)) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_STATUS_TRANSITION', 
          message: `无法从"${STATUS_DESCRIPTIONS[currentStatus as keyof typeof STATUS_DESCRIPTIONS]}"状态变更为"${STATUS_DESCRIPTIONS[newStatus as keyof typeof STATUS_DESCRIPTIONS]}"状态` 
        },
        { status: 400 }
      )
    }

    // 开始事务处理状态更新
    await db.transaction(async (tx) => {
      // 更新申请状态
      const updateData: any = {
        status: newStatus,
        updatedAt: new Date(),
      }

      if (validatedData.reviewNotes) {
        updateData.reviewNotes = validatedData.reviewNotes
        updateData.reviewedBy = user.id
        updateData.reviewedAt = new Date()
      }

      if (validatedData.actualCheckoutDate) {
        updateData.actualCheckoutDate = validatedData.actualCheckoutDate
      }

      await tx
        .update(checkoutApplications)
        .set(updateData)
        .where(eq(checkoutApplications.id, applicationId))

      // 根据新状态执行相应的业务逻辑
      switch (newStatus) {
        case 3: // 已通过 - 创建费用结算记录
          await createSettlementRecord(tx, applicationId, existingApplication[0])
          break

        case 5: // 费用结算中 - 可以开始房间清理准备
          // 这里可以添加通知相关人员的逻辑
          break

        case 6: // 房间清理中 - 创建房间清理记录
          await createCleaningRecord(tx, applicationId, existingApplication[0])
          break

        case 7: // 已完成 - 更新老人和房间状态
          await completeCheckout(tx, existingApplication[0])
          break
      }
    })

    return NextResponse.json({
      success: true,
      message: `状态已更新为"${STATUS_DESCRIPTIONS[newStatus as keyof typeof STATUS_DESCRIPTIONS]}"`,
    })
  } catch (error) {
    console.error('更新退住申请状态失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'VALIDATION_ERROR', 
          message: '数据验证失败',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 创建费用结算记录
async function createSettlementRecord(tx: any, applicationId: string, application: any) {
  // 生成结算编号
  const today = format(new Date(), 'yyyyMMdd')
  const settlementNumber = `ST${today}${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`

  // 获取老人信息和房间信息用于费用计算
  const elderData = await tx
    .select({
      elder: elderInfo,
      room: rooms,
      assignment: roomAssignments,
    })
    .from(elderInfo)
    .leftJoin(roomAssignments, eq(elderInfo.roomId, roomAssignments.roomId))
    .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
    .where(eq(elderInfo.id, application.elderInfoId))
    .limit(1)

  if (elderData.length > 0) {
    const elder = elderData[0].elder
    const room = elderData[0].room
    
    // 计算入住天数和基本费用（这里简化处理，实际应该根据具体业务规则计算）
    const admissionDate = new Date(elder.admissionDate || new Date())
    const checkoutDate = new Date()
    const daysStayed = Math.ceil((checkoutDate.getTime() - admissionDate.getTime()) / (1000 * 60 * 60 * 24))
    
    const monthlyRate = parseFloat(room?.monthlyRate || '0')
    const dailyRate = monthlyRate / 30
    const accommodationFees = dailyRate * daysStayed

    await tx
      .insert(checkoutSettlements)
      .values({
        applicationId,
        settlementNumber,
        totalCharges: accommodationFees.toString(),
        accommodationFees: accommodationFees.toString(),
        settlementDate: format(new Date(), 'yyyy-MM-dd'),
        settlementPeriodStart: format(admissionDate, 'yyyy-MM-dd'),
        settlementPeriodEnd: format(checkoutDate, 'yyyy-MM-dd'),
        calculatedBy: getCurrentUser().then(user => user?.id || ''),
      })
  }
}

// 创建房间清理记录
async function createCleaningRecord(tx: any, applicationId: string, application: any) {
  // 生成清理编号
  const today = format(new Date(), 'yyyyMMdd')
  const cleaningNumber = `CL${today}${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`

  // 获取房间信息
  const roomData = await tx
    .select({
      roomId: rooms.id,
    })
    .from(roomAssignments)
    .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
    .where(eq(roomAssignments.id, application.roomAssignmentId))
    .limit(1)

  if (roomData.length > 0) {
    const user = await getCurrentUser()
    
    await tx
      .insert(roomCleaningRecords)
      .values({
        applicationId,
        roomId: roomData[0].roomId,
        cleaningNumber,
        cleaningTasks: JSON.stringify([
          { task: '清理个人物品', completed: false },
          { task: '清洁房间卫生', completed: false },
          { task: '检查设施设备', completed: false },
          { task: '评估维修需求', completed: false },
        ]),
        assignedTo: user?.id || '',
      })
  }
}

// 完成退住流程
async function completeCheckout(tx: any, application: any) {
  // 更新老人状态为出院
  await tx
    .update(elderInfo)
    .set({
      status: 3, // 出院
      roomId: null,
      updatedAt: new Date(),
    })
    .where(eq(elderInfo.id, application.elderInfoId))

  // 更新房间分配状态
  if (application.roomAssignmentId) {
    await tx
      .update(roomAssignments)
      .set({
        status: 5, // 已退房
        updatedAt: new Date(),
      })
      .where(eq(roomAssignments.id, application.roomAssignmentId))

    // 获取房间信息并更新房间占用数
    const roomData = await tx
      .select({
        roomId: roomAssignments.roomId,
        currentOccupancy: rooms.currentOccupancy,
      })
      .from(roomAssignments)
      .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
      .where(eq(roomAssignments.id, application.roomAssignmentId))
      .limit(1)

    if (roomData.length > 0) {
      const newOccupancy = Math.max(0, (roomData[0].currentOccupancy || 1) - 1)
      
      await tx
        .update(rooms)
        .set({
          currentOccupancy: newOccupancy,
          updatedAt: new Date(),
        })
        .where(eq(rooms.id, roomData[0].roomId))
    }
  }
}

// GET - 获取状态流转历史
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const applicationId = params.id

    // 获取审核记录作为状态流转历史
    const statusHistory = await db
      .select({
        id: checkoutReviews.id,
        reviewLevel: checkoutReviews.reviewLevel,
        reviewType: checkoutReviews.reviewType,
        reviewResult: checkoutReviews.reviewResult,
        reviewNotes: checkoutReviews.reviewNotes,
        reviewedAt: checkoutReviews.reviewedAt,
        reviewer: {
          id: checkoutReviews.reviewedBy,
          name: 'Unknown', // 这里需要关联用户表获取名称
        },
      })
      .from(checkoutReviews)
      .where(eq(checkoutReviews.applicationId, applicationId))
      .orderBy(checkoutReviews.reviewedAt)

    return NextResponse.json({
      success: true,
      data: {
        statusHistory,
        availableTransitions: STATUS_TRANSITIONS,
        statusDescriptions: STATUS_DESCRIPTIONS,
      },
    })
  } catch (error) {
    console.error('获取状态流转历史失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
