import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  checkoutApplications,
  elderInfo,
  roomAssignments,
  rooms,
  betterAuthUsers,
  checkoutReviews,
  checkoutSettlements,
  roomCleaningRecords
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, desc } from 'drizzle-orm'

// 更新退住申请验证 schema
const updateApplicationSchema = z.object({
  applicantName: z.string().min(1, '请输入申请人姓名').optional(),
  applicantPhone: z.string().min(11, '请输入有效手机号').max(11).optional(),
  applicantRelation: z.string().min(1, '请输入与老人关系').optional(),
  applicantIdCard: z.string().optional(),
  checkoutReason: z.string().min(1, '请输入退住原因').optional(),
  checkoutType: z.number().min(1).max(4, '请选择有效的退住类型').optional(),
  expectedCheckoutDate: z.string().min(1, '请选择预期退住日期').optional(),
  actualCheckoutDate: z.string().optional(),
  personalBelongings: z.array(z.object({
    name: z.string(),
    description: z.string().optional(),
    quantity: z.number().optional(),
    condition: z.string().optional(),
  })).optional(),
  medicalRecords: z.array(z.object({
    type: z.string(),
    description: z.string(),
    location: z.string().optional(),
  })).optional(),
  attachments: z.array(z.object({
    name: z.string(),
    url: z.string(),
    type: z.string(),
    size: z.number().optional(),
  })).optional(),
  notes: z.string().optional(),
})

// GET - 获取退住申请详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const applicationId = params.id

    // 获取申请详情
    const application = await db
      .select({
        // 申请信息
        id: checkoutApplications.id,
        applicationNumber: checkoutApplications.applicationNumber,
        applicantName: checkoutApplications.applicantName,
        applicantPhone: checkoutApplications.applicantPhone,
        applicantRelation: checkoutApplications.applicantRelation,
        applicantIdCard: checkoutApplications.applicantIdCard,
        checkoutReason: checkoutApplications.checkoutReason,
        checkoutType: checkoutApplications.checkoutType,
        expectedCheckoutDate: checkoutApplications.expectedCheckoutDate,
        actualCheckoutDate: checkoutApplications.actualCheckoutDate,
        personalBelongings: checkoutApplications.personalBelongings,
        medicalRecords: checkoutApplications.medicalRecords,
        attachments: checkoutApplications.attachments,
        status: checkoutApplications.status,
        applicationDate: checkoutApplications.applicationDate,
        notes: checkoutApplications.notes,
        reviewNotes: checkoutApplications.reviewNotes,
        reviewedBy: checkoutApplications.reviewedBy,
        reviewedAt: checkoutApplications.reviewedAt,
        createdAt: checkoutApplications.createdAt,
        updatedAt: checkoutApplications.updatedAt,
        
        // 老人信息
        elder: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
          idCard: elderInfo.idCard,
          phone: elderInfo.phone,
          address: elderInfo.address,
          emergencyContactName: elderInfo.emergencyContactName,
          emergencyContactPhone: elderInfo.emergencyContactPhone,
          emergencyContactRelation: elderInfo.emergencyContactRelation,
          medicalHistory: elderInfo.medicalHistory,
          allergies: elderInfo.allergies,
          medications: elderInfo.medications,
          careLevel: elderInfo.careLevel,
          status: elderInfo.status,
          admissionDate: elderInfo.admissionDate,
        },
        
        // 房间信息
        room: {
          id: rooms.id,
          roomNumber: rooms.roomNumber,
          roomType: rooms.roomType,
          floor: rooms.floor,
          capacity: rooms.capacity,
          currentOccupancy: rooms.currentOccupancy,
          monthlyRate: rooms.monthlyRate,
          facilities: rooms.facilities,
        },
        
        // 创建人信息
        createdBy: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(checkoutApplications)
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .leftJoin(roomAssignments, eq(checkoutApplications.roomAssignmentId, roomAssignments.id))
      .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
      .leftJoin(betterAuthUsers, eq(checkoutApplications.createdBy, betterAuthUsers.id))
      .where(eq(checkoutApplications.id, applicationId))
      .limit(1)

    if (application.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '退住申请不存在' },
        { status: 404 }
      )
    }

    // 获取审核记录
    const reviews = await db
      .select({
        id: checkoutReviews.id,
        reviewLevel: checkoutReviews.reviewLevel,
        reviewType: checkoutReviews.reviewType,
        reviewResult: checkoutReviews.reviewResult,
        reviewNotes: checkoutReviews.reviewNotes,
        requirements: checkoutReviews.requirements,
        suggestions: checkoutReviews.suggestions,
        reviewedAt: checkoutReviews.reviewedAt,
        nextReviewLevel: checkoutReviews.nextReviewLevel,
        reviewer: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(checkoutReviews)
      .leftJoin(betterAuthUsers, eq(checkoutReviews.reviewedBy, betterAuthUsers.id))
      .where(eq(checkoutReviews.applicationId, applicationId))
      .orderBy(desc(checkoutReviews.reviewedAt))

    // 获取费用结算信息
    const settlement = await db
      .select()
      .from(checkoutSettlements)
      .where(eq(checkoutSettlements.applicationId, applicationId))
      .limit(1)

    // 获取房间清理记录
    const cleaningRecord = await db
      .select({
        id: roomCleaningRecords.id,
        cleaningNumber: roomCleaningRecords.cleaningNumber,
        cleaningTasks: roomCleaningRecords.cleaningTasks,
        personalItemsInventory: roomCleaningRecords.personalItemsInventory,
        facilityInspection: roomCleaningRecords.facilityInspection,
        damageAssessment: roomCleaningRecords.damageAssessment,
        cleaningStatus: roomCleaningRecords.cleaningStatus,
        startTime: roomCleaningRecords.startTime,
        endTime: roomCleaningRecords.endTime,
        inspectionTime: roomCleaningRecords.inspectionTime,
        cleaningResult: roomCleaningRecords.cleaningResult,
        foundItems: roomCleaningRecords.foundItems,
        maintenanceNeeded: roomCleaningRecords.maintenanceNeeded,
        maintenanceItems: roomCleaningRecords.maintenanceItems,
        notes: roomCleaningRecords.notes,
        assignedTo: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(roomCleaningRecords)
      .leftJoin(betterAuthUsers, eq(roomCleaningRecords.assignedTo, betterAuthUsers.id))
      .where(eq(roomCleaningRecords.applicationId, applicationId))
      .limit(1)

    // 处理数据格式
    const processedApplication = {
      ...application[0],
      personalBelongings: application[0].personalBelongings ? JSON.parse(application[0].personalBelongings) : [],
      medicalRecords: application[0].medicalRecords ? JSON.parse(application[0].medicalRecords) : [],
      attachments: application[0].attachments ? JSON.parse(application[0].attachments) : [],
      room: application[0].room ? {
        ...application[0].room,
        facilities: application[0].room.facilities ? JSON.parse(application[0].room.facilities) : [],
      } : null,
      reviews: reviews.map(review => ({
        ...review,
        requirements: review.requirements ? JSON.parse(review.requirements) : [],
      })),
      settlement: settlement.length > 0 ? settlement[0] : null,
      cleaningRecord: cleaningRecord.length > 0 ? {
        ...cleaningRecord[0],
        cleaningTasks: cleaningRecord[0].cleaningTasks ? JSON.parse(cleaningRecord[0].cleaningTasks) : [],
        personalItemsInventory: cleaningRecord[0].personalItemsInventory ? JSON.parse(cleaningRecord[0].personalItemsInventory) : [],
        facilityInspection: cleaningRecord[0].facilityInspection ? JSON.parse(cleaningRecord[0].facilityInspection) : [],
        damageAssessment: cleaningRecord[0].damageAssessment ? JSON.parse(cleaningRecord[0].damageAssessment) : [],
        foundItems: cleaningRecord[0].foundItems ? JSON.parse(cleaningRecord[0].foundItems) : [],
        maintenanceItems: cleaningRecord[0].maintenanceItems ? JSON.parse(cleaningRecord[0].maintenanceItems) : [],
      } : null,
    }

    return NextResponse.json({
      success: true,
      data: processedApplication,
    })
  } catch (error) {
    console.error('获取退住申请详情失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// PUT - 更新退住申请
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const applicationId = params.id
    const body = await request.json()
    const validatedData = updateApplicationSchema.parse(body)

    // 检查申请是否存在
    const existingApplication = await db
      .select()
      .from(checkoutApplications)
      .where(eq(checkoutApplications.id, applicationId))
      .limit(1)

    if (existingApplication.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '退住申请不存在' },
        { status: 404 }
      )
    }

    // 检查是否可以修改
    const currentStatus = existingApplication[0].status
    if (currentStatus === 7) { // 已完成
      return NextResponse.json(
        { 
          success: false, 
          code: 'CANNOT_UPDATE', 
          message: '该申请已完成，无法修改' 
        },
        { status: 400 }
      )
    }

    // 准备更新数据
    const updateData: any = {
      updatedAt: new Date(),
    }

    // 只更新提供的字段
    if (validatedData.applicantName !== undefined) updateData.applicantName = validatedData.applicantName
    if (validatedData.applicantPhone !== undefined) updateData.applicantPhone = validatedData.applicantPhone
    if (validatedData.applicantRelation !== undefined) updateData.applicantRelation = validatedData.applicantRelation
    if (validatedData.applicantIdCard !== undefined) updateData.applicantIdCard = validatedData.applicantIdCard
    if (validatedData.checkoutReason !== undefined) updateData.checkoutReason = validatedData.checkoutReason
    if (validatedData.checkoutType !== undefined) updateData.checkoutType = validatedData.checkoutType
    if (validatedData.expectedCheckoutDate !== undefined) updateData.expectedCheckoutDate = validatedData.expectedCheckoutDate
    if (validatedData.actualCheckoutDate !== undefined) updateData.actualCheckoutDate = validatedData.actualCheckoutDate
    if (validatedData.personalBelongings !== undefined) updateData.personalBelongings = JSON.stringify(validatedData.personalBelongings)
    if (validatedData.medicalRecords !== undefined) updateData.medicalRecords = JSON.stringify(validatedData.medicalRecords)
    if (validatedData.attachments !== undefined) updateData.attachments = JSON.stringify(validatedData.attachments)
    if (validatedData.notes !== undefined) updateData.notes = validatedData.notes

    // 更新申请
    const updatedApplication = await db
      .update(checkoutApplications)
      .set(updateData)
      .where(eq(checkoutApplications.id, applicationId))
      .returning()

    return NextResponse.json({
      success: true,
      data: updatedApplication[0],
      message: '退住申请更新成功',
    })
  } catch (error) {
    console.error('更新退住申请失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'VALIDATION_ERROR', 
          message: '数据验证失败',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// DELETE - 删除退住申请
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const applicationId = params.id

    // 检查申请是否存在
    const existingApplication = await db
      .select()
      .from(checkoutApplications)
      .where(eq(checkoutApplications.id, applicationId))
      .limit(1)

    if (existingApplication.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '退住申请不存在' },
        { status: 404 }
      )
    }

    // 检查是否可以删除
    const currentStatus = existingApplication[0].status
    if (currentStatus === 7) { // 已完成
      return NextResponse.json(
        { 
          success: false, 
          code: 'CANNOT_DELETE', 
          message: '该申请已完成，无法删除' 
        },
        { status: 400 }
      )
    }

    // 删除申请（级联删除相关记录）
    await db
      .delete(checkoutApplications)
      .where(eq(checkoutApplications.id, applicationId))

    return NextResponse.json({
      success: true,
      message: '退住申请删除成功',
    })
  } catch (error) {
    console.error('删除退住申请失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
