import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  checkoutApplications,
  elderInfo,
  roomAssignments,
  rooms,
  betterAuthUsers
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, like, desc, asc, count, gte, lte } from 'drizzle-orm'
import { format } from 'date-fns'

// 查询参数验证 schema
const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('10'),
  search: z.string().optional(),
  status: z.string().optional(),
  checkoutType: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
})

// 创建退住申请验证 schema
const createApplicationSchema = z.object({
  elderInfoId: z.string().uuid('请选择有效的老人'),
  roomAssignmentId: z.string().uuid().optional(),
  
  // 申请人信息
  applicantName: z.string().min(1, '请输入申请人姓名'),
  applicantPhone: z.string().min(11, '请输入有效手机号').max(11),
  applicantRelation: z.string().min(1, '请输入与老人关系'),
  applicantIdCard: z.string().optional(),
  
  // 退住信息
  checkoutReason: z.string().min(1, '请输入退住原因'),
  checkoutType: z.number().min(1).max(4, '请选择有效的退住类型'),
  expectedCheckoutDate: z.string().min(1, '请选择预期退住日期'),
  
  // 物品和文件
  personalBelongings: z.array(z.object({
    name: z.string(),
    description: z.string().optional(),
    quantity: z.number().optional(),
    condition: z.string().optional(),
  })).optional(),
  medicalRecords: z.array(z.object({
    type: z.string(),
    description: z.string(),
    location: z.string().optional(),
  })).optional(),
  attachments: z.array(z.object({
    name: z.string(),
    url: z.string(),
    type: z.string(),
    size: z.number().optional(),
  })).optional(),
  
  notes: z.string().optional(),
})

// GET - 获取退住申请列表
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const validatedParams = querySchema.parse(Object.fromEntries(searchParams))

    const page = parseInt(validatedParams.page)
    const limit = parseInt(validatedParams.limit)
    const offset = (page - 1) * limit

    // 构建查询条件
    const whereConditions = []

    // 搜索条件
    if (validatedParams.search) {
      const searchTerm = `%${validatedParams.search}%`
      whereConditions.push(
        or(
          like(checkoutApplications.applicationNumber, searchTerm),
          like(checkoutApplications.applicantName, searchTerm),
          like(checkoutApplications.applicantPhone, searchTerm),
          like(elderInfo.name, searchTerm),
          like(elderInfo.idCard, searchTerm)
        )
      )
    }

    // 状态筛选
    if (validatedParams.status) {
      whereConditions.push(eq(checkoutApplications.status, parseInt(validatedParams.status)))
    }

    // 退住类型筛选
    if (validatedParams.checkoutType) {
      whereConditions.push(eq(checkoutApplications.checkoutType, parseInt(validatedParams.checkoutType)))
    }

    // 日期范围筛选
    if (validatedParams.dateFrom) {
      whereConditions.push(gte(checkoutApplications.applicationDate, validatedParams.dateFrom))
    }
    if (validatedParams.dateTo) {
      whereConditions.push(lte(checkoutApplications.applicationDate, validatedParams.dateTo))
    }

    // 排序
    const orderBy = validatedParams.sortOrder === 'asc' 
      ? asc(checkoutApplications[validatedParams.sortBy as keyof typeof checkoutApplications])
      : desc(checkoutApplications[validatedParams.sortBy as keyof typeof checkoutApplications])

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(checkoutApplications)
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0].count

    // 获取申请列表
    const applications = await db
      .select({
        // 申请信息
        id: checkoutApplications.id,
        applicationNumber: checkoutApplications.applicationNumber,
        applicantName: checkoutApplications.applicantName,
        applicantPhone: checkoutApplications.applicantPhone,
        applicantRelation: checkoutApplications.applicantRelation,
        checkoutReason: checkoutApplications.checkoutReason,
        checkoutType: checkoutApplications.checkoutType,
        expectedCheckoutDate: checkoutApplications.expectedCheckoutDate,
        actualCheckoutDate: checkoutApplications.actualCheckoutDate,
        status: checkoutApplications.status,
        applicationDate: checkoutApplications.applicationDate,
        notes: checkoutApplications.notes,
        reviewNotes: checkoutApplications.reviewNotes,
        createdAt: checkoutApplications.createdAt,
        updatedAt: checkoutApplications.updatedAt,
        
        // 老人信息
        elder: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
          idCard: elderInfo.idCard,
          phone: elderInfo.phone,
          careLevel: elderInfo.careLevel,
          status: elderInfo.status,
          admissionDate: elderInfo.admissionDate,
        },
        
        // 房间信息
        room: {
          id: rooms.id,
          roomNumber: rooms.roomNumber,
          roomType: rooms.roomType,
          floor: rooms.floor,
        },
        
        // 创建人信息
        createdBy: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(checkoutApplications)
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .leftJoin(roomAssignments, eq(checkoutApplications.roomAssignmentId, roomAssignments.id))
      .leftJoin(rooms, eq(roomAssignments.roomId, rooms.id))
      .leftJoin(betterAuthUsers, eq(checkoutApplications.createdBy, betterAuthUsers.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset)

    // 处理数据格式
    const processedApplications = applications.map(app => ({
      ...app,
      personalBelongings: app.personalBelongings ? JSON.parse(app.personalBelongings) : [],
      medicalRecords: app.medicalRecords ? JSON.parse(app.medicalRecords) : [],
      attachments: app.attachments ? JSON.parse(app.attachments) : [],
    }))

    return NextResponse.json({
      success: true,
      data: {
        list: processedApplications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error('获取退住申请列表失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// POST - 创建退住申请
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createApplicationSchema.parse(body)

    // 检查老人是否存在且在院
    const elder = await db
      .select()
      .from(elderInfo)
      .where(eq(elderInfo.id, validatedData.elderInfoId))
      .limit(1)

    if (elder.length === 0) {
      return NextResponse.json(
        { success: false, code: 'ELDER_NOT_FOUND', message: '老人信息不存在' },
        { status: 404 }
      )
    }

    if (elder[0].status !== 1) {
      return NextResponse.json(
        { success: false, code: 'ELDER_NOT_IN_RESIDENCE', message: '老人当前不在院，无法申请退住' },
        { status: 400 }
      )
    }

    // 检查是否已有未完成的退住申请
    const existingApplication = await db
      .select()
      .from(checkoutApplications)
      .where(
        and(
          eq(checkoutApplications.elderInfoId, validatedData.elderInfoId),
          or(
            eq(checkoutApplications.status, 1), // 待审核
            eq(checkoutApplications.status, 2), // 审核中
            eq(checkoutApplications.status, 3), // 已通过
            eq(checkoutApplications.status, 5), // 费用结算中
            eq(checkoutApplications.status, 6)  // 房间清理中
          )
        )
      )
      .limit(1)

    if (existingApplication.length > 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_EXISTS', message: '该老人已有未完成的退住申请' },
        { status: 400 }
      )
    }

    // 生成申请编号
    const today = format(new Date(), 'yyyyMMdd')
    const countResult = await db
      .select({ count: count() })
      .from(checkoutApplications)
      .where(like(checkoutApplications.applicationNumber, `CO${today}%`))

    const sequence = String(countResult[0].count + 1).padStart(3, '0')
    const applicationNumber = `CO${today}${sequence}`

    // 创建退住申请
    const newApplication = await db
      .insert(checkoutApplications)
      .values({
        applicationNumber,
        elderInfoId: validatedData.elderInfoId,
        roomAssignmentId: validatedData.roomAssignmentId,
        applicantName: validatedData.applicantName,
        applicantPhone: validatedData.applicantPhone,
        applicantRelation: validatedData.applicantRelation,
        applicantIdCard: validatedData.applicantIdCard,
        checkoutReason: validatedData.checkoutReason,
        checkoutType: validatedData.checkoutType,
        expectedCheckoutDate: validatedData.expectedCheckoutDate,
        personalBelongings: validatedData.personalBelongings ? JSON.stringify(validatedData.personalBelongings) : null,
        medicalRecords: validatedData.medicalRecords ? JSON.stringify(validatedData.medicalRecords) : null,
        attachments: validatedData.attachments ? JSON.stringify(validatedData.attachments) : null,
        applicationDate: format(new Date(), 'yyyy-MM-dd'),
        notes: validatedData.notes,
        createdBy: user.id,
      })
      .returning()

    return NextResponse.json({
      success: true,
      data: newApplication[0],
      message: '退住申请创建成功',
    })
  } catch (error) {
    console.error('创建退住申请失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'VALIDATION_ERROR', 
          message: '数据验证失败',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
