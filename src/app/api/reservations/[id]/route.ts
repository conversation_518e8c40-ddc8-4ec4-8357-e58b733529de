import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { reservations, rooms, elderInfo, betterAuthUsers } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq } from 'drizzle-orm'

// 更新预订验证
const updateReservationSchema = z.object({
  elderInfoId: z.string().uuid().optional(),
  roomId: z.string().uuid().optional(),
  expectedCheckInDate: z.string().optional(),
  expectedCheckOutDate: z.string().optional(),
  actualCheckInDate: z.string().optional(),
  actualCheckOutDate: z.string().optional(),
  status: z.number().int().min(1).max(5).optional(),
  reservationType: z.number().int().min(1).max(3).optional(),
  contactName: z.string().min(1).optional(),
  contactPhone: z.string().min(1).optional(),
  contactRelation: z.string().optional(),
  specialRequirements: z.string().optional(),
  depositAmount: z.number().optional(),
  depositPaid: z.boolean().optional(),
  notes: z.string().optional(),
})

/**
 * 获取单个预订记录
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
          code: 'UNAUTHORIZED',
        },
        { status: 401 }
      )
    }

    const { id } = params

    // 获取预订详情
    const reservation = await db
      .select({
        id: reservations.id,
        reservationNumber: reservations.reservationNumber,
        reservationDate: reservations.reservationDate,
        expectedCheckInDate: reservations.expectedCheckInDate,
        expectedCheckOutDate: reservations.expectedCheckOutDate,
        actualCheckInDate: reservations.actualCheckInDate,
        actualCheckOutDate: reservations.actualCheckOutDate,
        status: reservations.status,
        reservationType: reservations.reservationType,
        contactName: reservations.contactName,
        contactPhone: reservations.contactPhone,
        contactRelation: reservations.contactRelation,
        specialRequirements: reservations.specialRequirements,
        depositAmount: reservations.depositAmount,
        depositPaid: reservations.depositPaid,
        notes: reservations.notes,
        createdAt: reservations.createdAt,
        updatedAt: reservations.updatedAt,
        // 关联数据
        elderInfo: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
          careLevel: elderInfo.careLevel,
          healthStatus: elderInfo.healthStatus,
        },
        room: {
          id: rooms.id,
          roomNumber: rooms.roomNumber,
          roomType: rooms.roomType,
          floor: rooms.floor,
          capacity: rooms.capacity,
          currentOccupancy: rooms.currentOccupancy,
          monthlyRate: rooms.monthlyRate,
          facilities: rooms.facilities,
        },
        createdByUser: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(reservations)
      .leftJoin(elderInfo, eq(reservations.elderInfoId, elderInfo.id))
      .leftJoin(rooms, eq(reservations.roomId, rooms.id))
      .leftJoin(betterAuthUsers, eq(reservations.createdBy, betterAuthUsers.id))
      .where(eq(reservations.id, id))
      .limit(1)

    if (reservation.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '预订记录不存在',
          code: 'RESERVATION_NOT_FOUND',
        },
        { status: 404 }
      )
    }

    // 添加状态和类型名称
    const statusNames = {
      1: '待确认',
      2: '已确认',
      3: '已入住',
      4: '已取消',
      5: '已过期',
    }

    const typeNames = {
      1: '长期入住',
      2: '短期体验',
      3: '临时住宿',
    }

    const roomTypeNames = {
      1: '单人间',
      2: '双人间',
      3: '三人间',
      4: '四人间',
    }

    const careLevelNames = {
      1: '自理',
      2: '半自理',
      3: '不能自理',
    }

    const healthStatusNames = {
      1: '健康',
      2: '一般',
      3: '较差',
      4: '危重',
    }

    const result = reservation[0]
    const formattedReservation = {
      ...result,
      statusName: statusNames[result.status as keyof typeof statusNames] || '未知',
      reservationTypeName: typeNames[result.reservationType as keyof typeof typeNames] || '未知',
      elderInfo: result.elderInfo.id ? {
        ...result.elderInfo,
        genderName: result.elderInfo.gender === 1 ? '男' : '女',
        careLevelName: careLevelNames[result.elderInfo.careLevel as keyof typeof careLevelNames] || '未知',
        healthStatusName: healthStatusNames[result.elderInfo.healthStatus as keyof typeof healthStatusNames] || '未知',
      } : null,
      room: result.room.id ? {
        ...result.room,
        roomTypeName: roomTypeNames[result.room.roomType as keyof typeof roomTypeNames] || '未知',
        facilities: result.room.facilities ? JSON.parse(result.room.facilities) : [],
      } : null,
    }

    return NextResponse.json({
      success: true,
      data: formattedReservation,
      message: '预订详情获取成功',
    })
  } catch (error) {
    console.error('获取预订详情失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    )
  }
}

/**
 * 更新预订记录
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
          code: 'UNAUTHORIZED',
        },
        { status: 401 }
      )
    }

    const { id } = params

    // 检查预订是否存在
    const existingReservation = await db
      .select()
      .from(reservations)
      .where(eq(reservations.id, id))
      .limit(1)

    if (existingReservation.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '预订记录不存在',
          code: 'RESERVATION_NOT_FOUND',
        },
        { status: 404 }
      )
    }

    // 解析请求体
    const body = await request.json()
    const validatedData = updateReservationSchema.safeParse(body)

    if (!validatedData.success) {
      return NextResponse.json(
        {
          success: false,
          error: '请求数据格式错误',
          code: 'INVALID_DATA',
          details: validatedData.error.errors,
        },
        { status: 400 }
      )
    }

    const data = validatedData.data

    // 如果更新房间，检查房间是否存在且可用
    if (data.roomId && data.roomId !== existingReservation[0].roomId) {
      const room = await db
        .select()
        .from(rooms)
        .where(eq(rooms.id, data.roomId))
        .limit(1)

      if (room.length === 0) {
        return NextResponse.json(
          {
            success: false,
            error: '房间不存在',
            code: 'ROOM_NOT_FOUND',
          },
          { status: 404 }
        )
      }

      if (room[0].status !== 1) {
        return NextResponse.json(
          {
            success: false,
            error: '房间不可用',
            code: 'ROOM_UNAVAILABLE',
          },
          { status: 400 }
        )
      }
    }

    // 更新预订记录
    const updatedReservation = await db
      .update(reservations)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(reservations.id, id))
      .returning()

    return NextResponse.json({
      success: true,
      data: updatedReservation[0],
      message: '预订更新成功',
    })
  } catch (error) {
    console.error('更新预订失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    )
  }
}

/**
 * 删除预订记录
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
          code: 'UNAUTHORIZED',
        },
        { status: 401 }
      )
    }

    const { id } = params

    // 检查预订是否存在
    const existingReservation = await db
      .select()
      .from(reservations)
      .where(eq(reservations.id, id))
      .limit(1)

    if (existingReservation.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '预订记录不存在',
          code: 'RESERVATION_NOT_FOUND',
        },
        { status: 404 }
      )
    }

    // 检查预订状态，已入住的预订不能删除
    if (existingReservation[0].status === 3) {
      return NextResponse.json(
        {
          success: false,
          error: '已入住的预订不能删除',
          code: 'CANNOT_DELETE_CHECKED_IN',
        },
        { status: 400 }
      )
    }

    // 删除预订记录
    await db
      .delete(reservations)
      .where(eq(reservations.id, id))

    return NextResponse.json({
      success: true,
      message: '预订删除成功',
    })
  } catch (error) {
    console.error('删除预订失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    )
  }
}
