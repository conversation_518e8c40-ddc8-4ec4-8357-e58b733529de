import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { reservations, rooms, elderInfo } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, gte, lte, ne, count, sql } from 'drizzle-orm'

// 查询参数验证
const availabilityQuerySchema = z.object({
  checkInDate: z.string(),
  checkOutDate: z.string().optional(),
  roomType: z.string().optional(),
  capacity: z.string().optional(),
  excludeReservationId: z.string().optional(),
})

// 冲突检测参数验证
const conflictCheckSchema = z.object({
  roomId: z.string().uuid(),
  checkInDate: z.string(),
  checkOutDate: z.string().optional(),
  excludeReservationId: z.string().optional(),
})

/**
 * 查询房间可用性
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
          code: 'UNAUTHORIZED',
        },
        { status: 401 }
      )
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url)
    const params = availabilityQuerySchema.safeParse({
      checkInDate: searchParams.get('checkInDate'),
      checkOutDate: searchParams.get('checkOutDate'),
      roomType: searchParams.get('roomType'),
      capacity: searchParams.get('capacity'),
      excludeReservationId: searchParams.get('excludeReservationId'),
    })

    if (!params.success) {
      return NextResponse.json(
        {
          success: false,
          error: '请求参数格式错误',
          code: 'INVALID_PARAMS',
          details: params.error.errors,
        },
        { status: 400 }
      )
    }

    const {
      checkInDate,
      checkOutDate,
      roomType,
      capacity,
      excludeReservationId,
    } = params.data

    // 构建房间筛选条件
    const roomWhereConditions = [eq(rooms.status, 1)] // 只查询可用房间

    if (roomType) {
      roomWhereConditions.push(eq(rooms.roomType, parseInt(roomType)))
    }

    if (capacity) {
      roomWhereConditions.push(gte(rooms.capacity, parseInt(capacity)))
    }

    // 获取所有符合条件的房间
    const availableRooms = await db
      .select({
        id: rooms.id,
        roomNumber: rooms.roomNumber,
        roomType: rooms.roomType,
        floor: rooms.floor,
        capacity: rooms.capacity,
        currentOccupancy: rooms.currentOccupancy,
        monthlyRate: rooms.monthlyRate,
        facilities: rooms.facilities,
        status: rooms.status,
      })
      .from(rooms)
      .where(and(...roomWhereConditions))

    // 检查每个房间的预订冲突
    const roomsWithAvailability = await Promise.all(
      availableRooms.map(async room => {
        const hasConflict = await checkReservationConflict({
          roomId: room.id,
          checkInDate,
          checkOutDate,
          excludeReservationId,
        })

        // 检查房间是否已满
        const isRoomFull = room.currentOccupancy >= room.capacity

        return {
          ...room,
          isAvailable: !hasConflict && !isRoomFull,
          hasReservationConflict: hasConflict,
          isRoomFull,
          availableBeds: Math.max(0, room.capacity - room.currentOccupancy),
          facilities: room.facilities ? JSON.parse(room.facilities) : [],
        }
      })
    )

    // 添加房间类型名称
    const roomTypeNames = {
      1: '单人间',
      2: '双人间',
      3: '三人间',
      4: '四人间',
    }

    const formattedRooms = roomsWithAvailability.map(room => ({
      ...room,
      roomTypeName:
        roomTypeNames[room.roomType as keyof typeof roomTypeNames] || '未知',
    }))

    // 分类统计
    const totalRooms = formattedRooms.length
    const availableRoomsCount = formattedRooms.filter(
      room => room.isAvailable
    ).length
    const conflictRooms = formattedRooms.filter(
      room => room.hasReservationConflict
    ).length
    const fullRooms = formattedRooms.filter(room => room.isRoomFull).length

    return NextResponse.json({
      success: true,
      data: {
        rooms: formattedRooms,
        summary: {
          totalRooms,
          availableRooms: availableRoomsCount,
          conflictRooms,
          fullRooms,
          checkInDate,
          checkOutDate,
        },
      },
      message: '房间可用性查询成功',
    })
  } catch (error) {
    console.error('查询房间可用性失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    )
  }
}

/**
 * 检查预订冲突
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
          code: 'UNAUTHORIZED',
        },
        { status: 401 }
      )
    }

    // 解析请求体
    const body = await request.json()
    const validatedData = conflictCheckSchema.safeParse(body)

    if (!validatedData.success) {
      return NextResponse.json(
        {
          success: false,
          error: '请求数据格式错误',
          code: 'INVALID_DATA',
          details: validatedData.error.errors,
        },
        { status: 400 }
      )
    }

    const { roomId, checkInDate, checkOutDate, excludeReservationId } =
      validatedData.data

    // 检查房间是否存在
    const room = await db
      .select()
      .from(rooms)
      .where(eq(rooms.id, roomId))
      .limit(1)

    if (room.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '房间不存在',
          code: 'ROOM_NOT_FOUND',
        },
        { status: 404 }
      )
    }

    // 检查预订冲突
    const hasConflict = await checkReservationConflict({
      roomId,
      checkInDate,
      checkOutDate,
      excludeReservationId,
    })

    // 获取冲突的预订记录
    let conflictingReservations = []
    if (hasConflict) {
      conflictingReservations = await getConflictingReservations({
        roomId,
        checkInDate,
        checkOutDate,
        excludeReservationId,
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        hasConflict,
        room: room[0],
        conflictingReservations,
        checkInDate,
        checkOutDate,
      },
      message: hasConflict ? '检测到预订冲突' : '无预订冲突',
    })
  } catch (error) {
    console.error('检查预订冲突失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    )
  }
}

/**
 * 检查预订冲突的辅助函数
 */
async function checkReservationConflict({
  roomId,
  checkInDate,
  checkOutDate,
  excludeReservationId,
}: {
  roomId: string
  checkInDate: string
  checkOutDate?: string
  excludeReservationId?: string
}): Promise<boolean> {
  const whereConditions = [
    eq(reservations.roomId, roomId),
    // 只检查有效状态的预订（待确认、已确认、已入住）
    or(
      eq(reservations.status, 1), // 待确认
      eq(reservations.status, 2), // 已确认
      eq(reservations.status, 3) // 已入住
    ),
  ]

  // 排除指定的预订记录
  if (excludeReservationId) {
    whereConditions.push(ne(reservations.id, excludeReservationId))
  }

  // 构建时间冲突条件
  let timeConflictCondition
  if (checkOutDate) {
    // 有退房日期的情况：检查时间段重叠
    timeConflictCondition = and(
      lte(reservations.expectedCheckInDate, checkOutDate),
      or(
        gte(reservations.expectedCheckOutDate, checkInDate),
        sql`${reservations.expectedCheckOutDate} IS NULL` // 长期入住没有退房日期
      )
    )
  } else {
    // 没有退房日期的情况（长期入住）：检查入住日期之后是否有其他预订
    timeConflictCondition = gte(reservations.expectedCheckInDate, checkInDate)
  }

  whereConditions.push(timeConflictCondition)

  const conflictingReservations = await db
    .select({ count: count() })
    .from(reservations)
    .where(and(...whereConditions))

  return (conflictingReservations[0]?.count || 0) > 0
}

/**
 * 获取冲突的预订记录
 */
async function getConflictingReservations({
  roomId,
  checkInDate,
  checkOutDate,
  excludeReservationId,
}: {
  roomId: string
  checkInDate: string
  checkOutDate?: string
  excludeReservationId?: string
}) {
  const whereConditions = [
    eq(reservations.roomId, roomId),
    or(
      eq(reservations.status, 1),
      eq(reservations.status, 2),
      eq(reservations.status, 3)
    ),
  ]

  if (excludeReservationId) {
    whereConditions.push(ne(reservations.id, excludeReservationId))
  }

  let timeConflictCondition
  if (checkOutDate) {
    timeConflictCondition = and(
      lte(reservations.expectedCheckInDate, checkOutDate),
      or(
        gte(reservations.expectedCheckOutDate, checkInDate),
        sql`${reservations.expectedCheckOutDate} IS NULL`
      )
    )
  } else {
    timeConflictCondition = gte(reservations.expectedCheckInDate, checkInDate)
  }

  whereConditions.push(timeConflictCondition)

  const conflictingReservations = await db
    .select({
      id: reservations.id,
      reservationNumber: reservations.reservationNumber,
      expectedCheckInDate: reservations.expectedCheckInDate,
      expectedCheckOutDate: reservations.expectedCheckOutDate,
      status: reservations.status,
      contactName: reservations.contactName,
      contactPhone: reservations.contactPhone,
    })
    .from(reservations)
    .where(and(...whereConditions))

  const statusNames = {
    1: '待确认',
    2: '已确认',
    3: '已入住',
  }

  return conflictingReservations.map(reservation => ({
    ...reservation,
    statusName:
      statusNames[reservation.status as keyof typeof statusNames] || '未知',
  }))
}
