import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { reservations, rooms, elderInfo, betterAuthUsers } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, gte, lte, like, or, desc, asc, count, sql } from 'drizzle-orm'
import { format } from 'date-fns'

// 查询参数验证
const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('10'),
  search: z.string().optional(),
  status: z.string().optional(),
  roomId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  sortBy: z.enum(['reservationDate', 'expectedCheckInDate', 'createdAt']).optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
})

// 创建预订验证
const createReservationSchema = z.object({
  elderInfoId: z.string().uuid().optional(),
  roomId: z.string().uuid(),
  expectedCheckInDate: z.string(),
  expectedCheckOutDate: z.string().optional(),
  reservationType: z.number().int().min(1).max(3).default(1),
  contactName: z.string().min(1, '联系人姓名不能为空'),
  contactPhone: z.string().min(1, '联系电话不能为空'),
  contactRelation: z.string().optional(),
  specialRequirements: z.string().optional(),
  depositAmount: z.number().optional(),
  depositPaid: z.boolean().default(false),
  notes: z.string().optional(),
})

/**
 * 获取预订列表
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
          code: 'UNAUTHORIZED',
        },
        { status: 401 }
      )
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url)
    const params = querySchema.safeParse({
      page: searchParams.get('page'),
      limit: searchParams.get('limit'),
      search: searchParams.get('search'),
      status: searchParams.get('status'),
      roomId: searchParams.get('roomId'),
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
      sortBy: searchParams.get('sortBy'),
      sortOrder: searchParams.get('sortOrder'),
    })

    if (!params.success) {
      return NextResponse.json(
        {
          success: false,
          error: '请求参数格式错误',
          code: 'INVALID_PARAMS',
          details: params.error.errors,
        },
        { status: 400 }
      )
    }

    const { page, limit, search, status, roomId, startDate, endDate, sortBy, sortOrder } = params.data
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // 构建查询条件
    const whereConditions = []

    if (search) {
      whereConditions.push(
        or(
          like(reservations.reservationNumber, `%${search}%`),
          like(reservations.contactName, `%${search}%`),
          like(reservations.contactPhone, `%${search}%`),
          like(elderInfo.name, `%${search}%`)
        )
      )
    }

    if (status) {
      whereConditions.push(eq(reservations.status, parseInt(status)))
    }

    if (roomId) {
      whereConditions.push(eq(reservations.roomId, roomId))
    }

    if (startDate) {
      whereConditions.push(gte(reservations.reservationDate, startDate))
    }

    if (endDate) {
      whereConditions.push(lte(reservations.reservationDate, endDate))
    }

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(reservations)
      .leftJoin(elderInfo, eq(reservations.elderInfoId, elderInfo.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 获取预订列表
    const sortColumn = sortBy === 'reservationDate' 
      ? reservations.reservationDate 
      : sortBy === 'expectedCheckInDate'
      ? reservations.expectedCheckInDate
      : reservations.createdAt

    const orderBy = sortOrder === 'asc' ? asc(sortColumn) : desc(sortColumn)

    const reservationList = await db
      .select({
        id: reservations.id,
        reservationNumber: reservations.reservationNumber,
        reservationDate: reservations.reservationDate,
        expectedCheckInDate: reservations.expectedCheckInDate,
        expectedCheckOutDate: reservations.expectedCheckOutDate,
        actualCheckInDate: reservations.actualCheckInDate,
        actualCheckOutDate: reservations.actualCheckOutDate,
        status: reservations.status,
        reservationType: reservations.reservationType,
        contactName: reservations.contactName,
        contactPhone: reservations.contactPhone,
        contactRelation: reservations.contactRelation,
        specialRequirements: reservations.specialRequirements,
        depositAmount: reservations.depositAmount,
        depositPaid: reservations.depositPaid,
        notes: reservations.notes,
        createdAt: reservations.createdAt,
        updatedAt: reservations.updatedAt,
        // 关联数据
        elderInfo: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
        },
        room: {
          id: rooms.id,
          roomNumber: rooms.roomNumber,
          roomType: rooms.roomType,
          floor: rooms.floor,
        },
        createdByUser: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(reservations)
      .leftJoin(elderInfo, eq(reservations.elderInfoId, elderInfo.id))
      .leftJoin(rooms, eq(reservations.roomId, rooms.id))
      .leftJoin(betterAuthUsers, eq(reservations.createdBy, betterAuthUsers.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limitNum)
      .offset(offset)

    // 添加状态和类型名称
    const statusNames = {
      1: '待确认',
      2: '已确认',
      3: '已入住',
      4: '已取消',
      5: '已过期',
    }

    const typeNames = {
      1: '长期入住',
      2: '短期体验',
      3: '临时住宿',
    }

    const roomTypeNames = {
      1: '单人间',
      2: '双人间',
      3: '三人间',
      4: '四人间',
    }

    const formattedList = reservationList.map(item => ({
      ...item,
      statusName: statusNames[item.status as keyof typeof statusNames] || '未知',
      reservationTypeName: typeNames[item.reservationType as keyof typeof typeNames] || '未知',
      elderInfo: item.elderInfo.id ? {
        ...item.elderInfo,
        genderName: item.elderInfo.gender === 1 ? '男' : '女',
      } : null,
      room: item.room.id ? {
        ...item.room,
        roomTypeName: roomTypeNames[item.room.roomType as keyof typeof roomTypeNames] || '未知',
      } : null,
    }))

    return NextResponse.json({
      success: true,
      data: {
        list: formattedList,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      },
      message: '预订列表获取成功',
    })
  } catch (error) {
    console.error('获取预订列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    )
  }
}

/**
 * 创建预订记录
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
          code: 'UNAUTHORIZED',
        },
        { status: 401 }
      )
    }

    // 解析请求体
    const body = await request.json()
    const validatedData = createReservationSchema.safeParse(body)

    if (!validatedData.success) {
      return NextResponse.json(
        {
          success: false,
          error: '请求数据格式错误',
          code: 'INVALID_DATA',
          details: validatedData.error.errors,
        },
        { status: 400 }
      )
    }

    const data = validatedData.data

    // 检查房间是否存在且可用
    const room = await db
      .select()
      .from(rooms)
      .where(eq(rooms.id, data.roomId))
      .limit(1)

    if (room.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '房间不存在',
          code: 'ROOM_NOT_FOUND',
        },
        { status: 404 }
      )
    }

    if (room[0].status !== 1) {
      return NextResponse.json(
        {
          success: false,
          error: '房间不可用',
          code: 'ROOM_UNAVAILABLE',
        },
        { status: 400 }
      )
    }

    // 生成预订编号
    const reservationNumber = generateReservationNumber()

    // 创建预订记录
    const newReservation = await db
      .insert(reservations)
      .values({
        ...data,
        reservationNumber,
        reservationDate: format(new Date(), 'yyyy-MM-dd'),
        createdBy: user.id,
      })
      .returning()

    return NextResponse.json({
      success: true,
      data: newReservation[0],
      message: '预订创建成功',
    })
  } catch (error) {
    console.error('创建预订失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    )
  }
}

/**
 * 生成预订编号
 */
function generateReservationNumber(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const timestamp = now.getTime().toString().slice(-6)
  
  return `RES${year}${month}${day}${timestamp}`
}
