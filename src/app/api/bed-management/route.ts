import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  rooms,
  roomAssignments,
  admissionApplications,
  betterAuthUsers
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, like, desc, asc, count, inArray, isNull } from 'drizzle-orm'

// 床位查询验证 schema
const bedQuerySchema = z.object({
  status: z.number().optional(), // 床位状态
  roomType: z.number().optional(), // 房间类型
  floor: z.number().optional(), // 楼层
  search: z.string().optional(), // 搜索关键词
  sortBy: z.string().optional(), // 排序字段
  sortOrder: z.enum(['asc', 'desc']).optional(), // 排序方向
})

// GET - 获取床位管理信息
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const roomType = searchParams.get('roomType')
    const floor = searchParams.get('floor')
    const search = searchParams.get('search')
    const sortBy = searchParams.get('sortBy') || 'roomNumber'
    const sortOrder = searchParams.get('sortOrder') || 'asc'

    const offset = (page - 1) * limit
    const limitNum = Math.min(limit, 100)

    // 构建查询条件
    const whereConditions = []

    if (status) {
      whereConditions.push(eq(rooms.status, parseInt(status)))
    }

    if (roomType) {
      whereConditions.push(eq(rooms.roomType, parseInt(roomType)))
    }

    if (floor) {
      whereConditions.push(eq(rooms.floor, parseInt(floor)))
    }

    if (search) {
      whereConditions.push(
        or(
          like(rooms.roomNumber, `%${search}%`),
          like(rooms.description, `%${search}%`),
          like(admissionApplications.elderName, `%${search}%`)
        )
      )
    }

    // 排序
    const orderBy = sortOrder === 'asc' 
      ? asc(rooms[sortBy as keyof typeof rooms] || rooms.roomNumber)
      : desc(rooms[sortBy as keyof typeof rooms] || rooms.roomNumber)

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(rooms)
      .leftJoin(roomAssignments, and(
        eq(rooms.id, roomAssignments.roomId),
        inArray(roomAssignments.status, [1, 2, 3]) // 有效分配
      ))
      .leftJoin(admissionApplications, eq(roomAssignments.applicationId, admissionApplications.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 获取床位列表
    const bedList = await db
      .select({
        // 房间信息
        id: rooms.id,
        roomNumber: rooms.roomNumber,
        roomType: rooms.roomType,
        floor: rooms.floor,
        area: rooms.area,
        bedCount: rooms.bedCount,
        monthlyRent: rooms.monthlyRent,
        facilities: rooms.facilities,
        orientation: rooms.orientation,
        description: rooms.description,
        status: rooms.status,
        lastRenovation: rooms.lastRenovation,
        createdAt: rooms.createdAt,
        updatedAt: rooms.updatedAt,
        // 分配信息
        assignment: {
          id: roomAssignments.id,
          assignmentNumber: roomAssignments.assignmentNumber,
          assignmentDate: roomAssignments.assignmentDate,
          moveInDate: roomAssignments.moveInDate,
          confirmDate: roomAssignments.confirmDate,
          status: roomAssignments.status,
          notes: roomAssignments.notes,
        },
        // 入住老人信息
        resident: {
          id: admissionApplications.id,
          elderName: admissionApplications.elderName,
          elderAge: admissionApplications.elderAge,
          elderGender: admissionApplications.elderGender,
          applicantName: admissionApplications.applicantName,
          applicantPhone: admissionApplications.applicantPhone,
          emergencyContact: admissionApplications.emergencyContact,
          emergencyPhone: admissionApplications.emergencyPhone,
          healthCondition: admissionApplications.healthCondition,
          careNeeds: admissionApplications.careNeeds,
        },
        // 分配人员信息
        assignedBy: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
      })
      .from(rooms)
      .leftJoin(roomAssignments, and(
        eq(rooms.id, roomAssignments.roomId),
        inArray(roomAssignments.status, [1, 2, 3]) // 有效分配
      ))
      .leftJoin(admissionApplications, eq(roomAssignments.applicationId, admissionApplications.id))
      .leftJoin(betterAuthUsers, eq(roomAssignments.assignedBy, betterAuthUsers.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limitNum)
      .offset(offset)

    // 处理数据格式
    const processedBedList = bedList.map(bed => ({
      ...bed,
      facilities: bed.facilities ? JSON.parse(bed.facilities) : [],
      occupancyStatus: getOccupancyStatus(bed),
      bedUtilization: calculateBedUtilization(bed),
    }))

    // 统计信息
    const stats = await calculateBedStats()

    return NextResponse.json({
      success: true,
      data: {
        list: processedBedList,
        pagination: {
          page,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
        stats,
      },
    })
  } catch (error) {
    console.error('获取床位管理信息失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 获取入住状态
function getOccupancyStatus(bed: any): string {
  if (!bed.assignment) {
    return bed.status === 1 ? 'available' : 'unavailable'
  }

  switch (bed.assignment.status) {
    case 1: return 'assigned' // 已分配待确认
    case 2: return 'confirmed' // 已确认待入住
    case 3: return 'occupied' // 已入住
    case 4: return 'cancelled' // 已取消
    default: return 'unknown'
  }
}

// 计算床位利用率
function calculateBedUtilization(bed: any): number {
  if (!bed.assignment || bed.assignment.status !== 3) {
    return 0 // 未入住
  }

  // 简化计算：已入住的床位利用率为100%
  return 100
}

// 计算床位统计信息
async function calculateBedStats() {
  // 总床位数
  const totalBedsResult = await db
    .select({ 
      totalRooms: count(),
      totalBeds: count(rooms.bedCount) // 这里需要用 SUM，但为了简化使用 count
    })
    .from(rooms)

  // 各状态床位数
  const statusStatsResult = await db
    .select({
      status: rooms.status,
      count: count(),
    })
    .from(rooms)
    .groupBy(rooms.status)

  // 已分配床位数
  const assignedBedsResult = await db
    .select({
      assignmentStatus: roomAssignments.status,
      count: count(),
    })
    .from(roomAssignments)
    .where(inArray(roomAssignments.status, [1, 2, 3]))
    .groupBy(roomAssignments.status)

  // 按房间类型统计
  const roomTypeStatsResult = await db
    .select({
      roomType: rooms.roomType,
      count: count(),
      occupiedCount: count(roomAssignments.id),
    })
    .from(rooms)
    .leftJoin(roomAssignments, and(
      eq(rooms.id, roomAssignments.roomId),
      eq(roomAssignments.status, 3) // 已入住
    ))
    .groupBy(rooms.roomType)

  // 按楼层统计
  const floorStatsResult = await db
    .select({
      floor: rooms.floor,
      count: count(),
      occupiedCount: count(roomAssignments.id),
    })
    .from(rooms)
    .leftJoin(roomAssignments, and(
      eq(rooms.id, roomAssignments.roomId),
      eq(roomAssignments.status, 3) // 已入住
    ))
    .groupBy(rooms.floor)

  const totalRooms = totalBedsResult[0]?.totalRooms || 0
  const statusStats = statusStatsResult.reduce((acc, item) => {
    acc[item.status] = item.count
    return acc
  }, {} as Record<number, number>)

  const assignmentStats = assignedBedsResult.reduce((acc, item) => {
    acc[item.assignmentStatus] = item.count
    return acc
  }, {} as Record<number, number>)

  // 计算利用率
  const availableRooms = statusStats[1] || 0 // 可用
  const occupiedRooms = assignmentStats[3] || 0 // 已入住
  const occupancyRate = totalRooms > 0 ? Math.round((occupiedRooms / totalRooms) * 100) : 0

  return {
    total: {
      rooms: totalRooms,
      beds: totalRooms, // 简化：假设每个房间一张床
      available: availableRooms,
      occupied: occupiedRooms,
      assigned: assignmentStats[1] || 0, // 待确认
      confirmed: assignmentStats[2] || 0, // 已确认
    },
    occupancyRate,
    statusDistribution: statusStats,
    roomTypeStats: roomTypeStatsResult.map(item => ({
      roomType: item.roomType,
      total: item.count,
      occupied: item.occupiedCount,
      occupancyRate: item.count > 0 ? Math.round((item.occupiedCount / item.count) * 100) : 0,
    })),
    floorStats: floorStatsResult.map(item => ({
      floor: item.floor,
      total: item.count,
      occupied: item.occupiedCount,
      occupancyRate: item.count > 0 ? Math.round((item.occupiedCount / item.count) * 100) : 0,
    })),
  }
}
