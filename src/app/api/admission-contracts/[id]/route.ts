import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  admissionContracts, 
  admissionApplications,
  betterAuthUsers 
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq } from 'drizzle-orm'

// 合同更新验证 schema
const updateContractSchema = z.object({
  contractTitle: z.string().min(1, '请输入合同标题').optional(),
  partyA: z.string().min(1, '请输入甲方信息').optional(),
  partyB: z.string().min(1, '请输入乙方信息').optional(),
  elderName: z.string().min(1, '请输入老人姓名').optional(),
  roomNumber: z.string().optional(),
  serviceContent: z.string().optional(),
  serviceStandard: z.string().optional(),
  feeStructure: z.object({
    monthlyFee: z.number().min(0),
    deposit: z.number().min(0),
    serviceFee: z.number().min(0).optional(),
    otherFees: z.array(z.object({
      name: z.string(),
      amount: z.number(),
      description: z.string().optional(),
    })).optional(),
  }).optional(),
  paymentTerms: z.string().optional(),
  contractPeriod: z.string().optional(),
  terminationConditions: z.string().optional(),
  liabilityClause: z.string().optional(),
  disputeResolution: z.string().optional(),
  effectiveDate: z.string().optional(),
  expiryDate: z.string().optional(),
  witnessName: z.string().optional(),
  notes: z.string().optional(),
  attachments: z.array(z.string()).optional(),
  status: z.number().min(1).max(6).optional(),
  contractFile: z.string().optional(),
})

// GET - 获取单个合同详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const contractId = params.id

    // 获取合同详情
    const contractResult = await db
      .select({
        id: admissionContracts.id,
        applicationId: admissionContracts.applicationId,
        contractNumber: admissionContracts.contractNumber,
        contractType: admissionContracts.contractType,
        templateId: admissionContracts.templateId,
        contractTitle: admissionContracts.contractTitle,
        partyA: admissionContracts.partyA,
        partyB: admissionContracts.partyB,
        elderName: admissionContracts.elderName,
        roomNumber: admissionContracts.roomNumber,
        serviceContent: admissionContracts.serviceContent,
        serviceStandard: admissionContracts.serviceStandard,
        feeStructure: admissionContracts.feeStructure,
        paymentTerms: admissionContracts.paymentTerms,
        contractPeriod: admissionContracts.contractPeriod,
        terminationConditions: admissionContracts.terminationConditions,
        liabilityClause: admissionContracts.liabilityClause,
        disputeResolution: admissionContracts.disputeResolution,
        status: admissionContracts.status,
        signDate: admissionContracts.signDate,
        effectiveDate: admissionContracts.effectiveDate,
        expiryDate: admissionContracts.expiryDate,
        partyASignature: admissionContracts.partyASignature,
        partyBSignature: admissionContracts.partyBSignature,
        partyASignDate: admissionContracts.partyASignDate,
        partyBSignDate: admissionContracts.partyBSignDate,
        witnessSignature: admissionContracts.witnessSignature,
        witnessName: admissionContracts.witnessName,
        contractFile: admissionContracts.contractFile,
        attachments: admissionContracts.attachments,
        notes: admissionContracts.notes,
        createdAt: admissionContracts.createdAt,
        updatedAt: admissionContracts.updatedAt,
        // 关联数据
        application: {
          id: admissionApplications.id,
          applicationNumber: admissionApplications.applicationNumber,
          elderName: admissionApplications.elderName,
          applicantName: admissionApplications.applicantName,
          applicantPhone: admissionApplications.applicantPhone,
          status: admissionApplications.status,
        },
        createdByUser: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(admissionContracts)
      .leftJoin(admissionApplications, eq(admissionContracts.applicationId, admissionApplications.id))
      .leftJoin(betterAuthUsers, eq(admissionContracts.createdBy, betterAuthUsers.id))
      .where(eq(admissionContracts.id, contractId))
      .limit(1)

    if (contractResult.length === 0) {
      return NextResponse.json(
        { success: false, code: 'CONTRACT_NOT_FOUND', message: '合同不存在' },
        { status: 404 }
      )
    }

    const contract = contractResult[0]

    // 解析 JSON 字段
    if (contract.feeStructure) {
      try {
        contract.feeStructure = JSON.parse(contract.feeStructure)
      } catch {
        contract.feeStructure = {}
      }
    }

    if (contract.attachments) {
      try {
        contract.attachments = JSON.parse(contract.attachments)
      } catch {
        contract.attachments = []
      }
    }

    return NextResponse.json({
      success: true,
      data: contract,
    })
  } catch (error) {
    console.error('获取合同详情失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// PUT - 更新合同
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const contractId = params.id
    const body = await request.json()
    const validatedData = updateContractSchema.parse(body)

    // 检查合同是否存在
    const existingContract = await db
      .select()
      .from(admissionContracts)
      .where(eq(admissionContracts.id, contractId))
      .limit(1)

    if (existingContract.length === 0) {
      return NextResponse.json(
        { success: false, code: 'CONTRACT_NOT_FOUND', message: '合同不存在' },
        { status: 404 }
      )
    }

    // 检查是否可以更新
    const currentStatus = existingContract[0].status
    if (currentStatus === 5 || currentStatus === 6) { // 已终止或已作废
      return NextResponse.json(
        { 
          success: false, 
          code: 'CANNOT_UPDATE', 
          message: '已终止或已作废的合同无法更新' 
        },
        { status: 400 }
      )
    }

    // 如果是已签署或已生效的合同，只能更新部分字段
    if (currentStatus >= 3) { // 已签署或已生效
      const allowedFields = ['notes', 'attachments', 'contractFile', 'status']
      const updateFields = Object.keys(validatedData)
      const invalidFields = updateFields.filter(field => !allowedFields.includes(field))
      
      if (invalidFields.length > 0) {
        return NextResponse.json(
          { 
            success: false, 
            code: 'INVALID_UPDATE_FIELDS', 
            message: `已签署的合同只能更新以下字段: ${allowedFields.join(', ')}` 
          },
          { status: 400 }
        )
      }
    }

    // 更新合同
    const updateData: any = { ...validatedData }
    
    if (validatedData.feeStructure) {
      updateData.feeStructure = JSON.stringify(validatedData.feeStructure)
    }

    if (validatedData.attachments) {
      updateData.attachments = JSON.stringify(validatedData.attachments)
    }

    updateData.updatedAt = new Date()

    const updatedContract = await db
      .update(admissionContracts)
      .set(updateData)
      .where(eq(admissionContracts.id, contractId))
      .returning()

    return NextResponse.json({
      success: true,
      data: updatedContract[0],
      message: '合同更新成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('更新合同失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// DELETE - 删除合同
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const contractId = params.id

    // 检查合同是否存在
    const existingContract = await db
      .select()
      .from(admissionContracts)
      .where(eq(admissionContracts.id, contractId))
      .limit(1)

    if (existingContract.length === 0) {
      return NextResponse.json(
        { success: false, code: 'CONTRACT_NOT_FOUND', message: '合同不存在' },
        { status: 404 }
      )
    }

    // 检查是否可以删除
    const currentStatus = existingContract[0].status
    if (currentStatus >= 3) { // 已签署、已生效、已终止
      return NextResponse.json(
        { 
          success: false, 
          code: 'CANNOT_DELETE', 
          message: '已签署的合同无法删除，只能作废' 
        },
        { status: 400 }
      )
    }

    // 删除合同
    await db
      .delete(admissionContracts)
      .where(eq(admissionContracts.id, contractId))

    return NextResponse.json({
      success: true,
      message: '合同删除成功',
    })
  } catch (error) {
    console.error('删除合同失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
