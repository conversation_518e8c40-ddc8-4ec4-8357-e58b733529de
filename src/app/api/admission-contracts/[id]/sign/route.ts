import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { admissionContracts } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq } from 'drizzle-orm'
import { format } from 'date-fns'

// 签名验证 schema
const signContractSchema = z.object({
  signatureType: z.enum(['partyA', 'partyB', 'witness'], {
    required_error: '请选择签名类型',
  }),
  signatureData: z.string().min(1, '请提供签名数据'),
  signerName: z.string().min(1, '请输入签名人姓名').optional(),
})

// POST - 签署合同
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const contractId = params.id
    const body = await request.json()
    const validatedData = signContractSchema.parse(body)

    // 检查合同是否存在
    const existingContract = await db
      .select()
      .from(admissionContracts)
      .where(eq(admissionContracts.id, contractId))
      .limit(1)

    if (existingContract.length === 0) {
      return NextResponse.json(
        { success: false, code: 'CONTRACT_NOT_FOUND', message: '合同不存在' },
        { status: 404 }
      )
    }

    const contract = existingContract[0]

    // 检查合同状态是否允许签署
    if (![1, 2].includes(contract.status)) { // 草稿或待签署
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_CONTRACT_STATUS', 
          message: '合同状态不允许签署' 
        },
        { status: 400 }
      )
    }

    // 检查是否已经签署过
    const { signatureType } = validatedData
    let alreadySigned = false
    let signatureField = ''
    let signDateField = ''

    switch (signatureType) {
      case 'partyA':
        alreadySigned = !!contract.partyASignature
        signatureField = 'partyASignature'
        signDateField = 'partyASignDate'
        break
      case 'partyB':
        alreadySigned = !!contract.partyBSignature
        signatureField = 'partyBSignature'
        signDateField = 'partyBSignDate'
        break
      case 'witness':
        alreadySigned = !!contract.witnessSignature
        signatureField = 'witnessSignature'
        break
    }

    if (alreadySigned) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'ALREADY_SIGNED', 
          message: '该方已经签署过合同' 
        },
        { status: 400 }
      )
    }

    // 更新签名信息
    const updateData: any = {
      [signatureField]: validatedData.signatureData,
      updatedAt: new Date(),
    }

    if (signDateField) {
      updateData[signDateField] = new Date()
    }

    // 如果是见证人签名，更新见证人姓名
    if (signatureType === 'witness' && validatedData.signerName) {
      updateData.witnessName = validatedData.signerName
    }

    // 检查是否所有必要的签名都已完成
    const partyAWillBeSigned = signatureType === 'partyA' || contract.partyASignature
    const partyBWillBeSigned = signatureType === 'partyB' || contract.partyBSignature

    // 如果甲乙双方都已签署，更新合同状态
    if (partyAWillBeSigned && partyBWillBeSigned) {
      updateData.status = 3 // 已签署
      updateData.signDate = format(new Date(), 'yyyy-MM-dd')
      
      // 如果设置了生效日期且是今天或之前，直接设为已生效
      if (contract.effectiveDate) {
        const effectiveDate = new Date(contract.effectiveDate)
        const today = new Date()
        if (effectiveDate <= today) {
          updateData.status = 4 // 已生效
        }
      }
    } else {
      // 如果还有一方未签署，设为待签署状态
      updateData.status = 2 // 待签署
    }

    // 执行更新
    const updatedContract = await db
      .update(admissionContracts)
      .set(updateData)
      .where(eq(admissionContracts.id, contractId))
      .returning()

    // 构建响应消息
    let message = ''
    switch (signatureType) {
      case 'partyA':
        message = '甲方签署成功'
        break
      case 'partyB':
        message = '乙方签署成功'
        break
      case 'witness':
        message = '见证人签署成功'
        break
    }

    if (updateData.status === 3) {
      message += '，合同已完成签署'
    } else if (updateData.status === 4) {
      message += '，合同已生效'
    }

    return NextResponse.json({
      success: true,
      data: {
        contractId,
        signatureType,
        status: updateData.status,
        signDate: updateData.signDate,
      },
      message,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('签署合同失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// GET - 获取合同签署状态
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const contractId = params.id

    // 获取合同签署信息
    const contractResult = await db
      .select({
        id: admissionContracts.id,
        contractNumber: admissionContracts.contractNumber,
        status: admissionContracts.status,
        partyA: admissionContracts.partyA,
        partyB: admissionContracts.partyB,
        partyASignature: admissionContracts.partyASignature,
        partyBSignature: admissionContracts.partyBSignature,
        partyASignDate: admissionContracts.partyASignDate,
        partyBSignDate: admissionContracts.partyBSignDate,
        witnessSignature: admissionContracts.witnessSignature,
        witnessName: admissionContracts.witnessName,
        signDate: admissionContracts.signDate,
        effectiveDate: admissionContracts.effectiveDate,
        expiryDate: admissionContracts.expiryDate,
      })
      .from(admissionContracts)
      .where(eq(admissionContracts.id, contractId))
      .limit(1)

    if (contractResult.length === 0) {
      return NextResponse.json(
        { success: false, code: 'CONTRACT_NOT_FOUND', message: '合同不存在' },
        { status: 404 }
      )
    }

    const contract = contractResult[0]

    // 构建签署状态信息
    const signatureStatus = {
      partyA: {
        signed: !!contract.partyASignature,
        signDate: contract.partyASignDate,
        signerName: contract.partyA,
      },
      partyB: {
        signed: !!contract.partyBSignature,
        signDate: contract.partyBSignDate,
        signerName: contract.partyB,
      },
      witness: {
        signed: !!contract.witnessSignature,
        signerName: contract.witnessName,
      },
      contract: {
        status: contract.status,
        signDate: contract.signDate,
        effectiveDate: contract.effectiveDate,
        expiryDate: contract.expiryDate,
        isFullySigned: !!contract.partyASignature && !!contract.partyBSignature,
        isEffective: contract.status === 4,
      },
    }

    // 计算下一步操作
    let nextAction = ''
    if (!contract.partyASignature) {
      nextAction = '等待甲方签署'
    } else if (!contract.partyBSignature) {
      nextAction = '等待乙方签署'
    } else if (contract.status === 3) {
      nextAction = '合同已签署，等待生效'
    } else if (contract.status === 4) {
      nextAction = '合同已生效'
    }

    return NextResponse.json({
      success: true,
      data: {
        ...signatureStatus,
        nextAction,
      },
    })
  } catch (error) {
    console.error('获取签署状态失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
