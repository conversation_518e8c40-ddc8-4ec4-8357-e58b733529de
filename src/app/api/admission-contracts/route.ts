import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  admissionContracts, 
  admissionApplications,
  betterAuthUsers 
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, like, desc, asc, count, gte, lte } from 'drizzle-orm'
import { format } from 'date-fns'

// 合同创建验证 schema
const createContractSchema = z.object({
  applicationId: z.string().min(1, '请选择入住申请'),
  contractType: z.number().min(1).max(3, '请选择合同类型'),
  templateId: z.string().optional(),
  contractTitle: z.string().min(1, '请输入合同标题'),
  partyA: z.string().min(1, '请输入甲方信息'),
  partyB: z.string().min(1, '请输入乙方信息'),
  elderName: z.string().min(1, '请输入老人姓名'),
  roomNumber: z.string().optional(),
  serviceContent: z.string().optional(),
  serviceStandard: z.string().optional(),
  feeStructure: z.object({
    monthlyFee: z.number().min(0),
    deposit: z.number().min(0),
    serviceFee: z.number().min(0).optional(),
    otherFees: z.array(z.object({
      name: z.string(),
      amount: z.number(),
      description: z.string().optional(),
    })).optional(),
  }),
  paymentTerms: z.string().optional(),
  contractPeriod: z.string().optional(),
  terminationConditions: z.string().optional(),
  liabilityClause: z.string().optional(),
  disputeResolution: z.string().optional(),
  effectiveDate: z.string().optional(),
  expiryDate: z.string().optional(),
  witnessName: z.string().optional(),
  notes: z.string().optional(),
  attachments: z.array(z.string()).optional(),
})

// 合同更新验证 schema
const updateContractSchema = createContractSchema.partial().extend({
  status: z.number().min(1).max(6).optional(),
  signDate: z.string().optional(),
  partyASignDate: z.string().optional(),
  partyBSignDate: z.string().optional(),
  partyASignature: z.string().optional(),
  partyBSignature: z.string().optional(),
  witnessSignature: z.string().optional(),
  contractFile: z.string().optional(),
})

// 生成合同编号
function generateContractNumber(): string {
  const now = new Date()
  const dateStr = format(now, 'yyyyMMdd')
  const timeStr = format(now, 'HHmmss')
  return `CON${dateStr}${timeStr}`
}

// GET - 获取合同列表
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const contractType = searchParams.get('contractType')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const offset = (page - 1) * limit
    const limitNum = Math.min(limit, 100)

    // 构建查询条件
    const whereConditions = []

    if (search) {
      whereConditions.push(
        or(
          like(admissionContracts.contractNumber, `%${search}%`),
          like(admissionContracts.elderName, `%${search}%`),
          like(admissionContracts.partyB, `%${search}%`),
          like(admissionContracts.contractTitle, `%${search}%`)
        )
      )
    }

    if (status) {
      whereConditions.push(eq(admissionContracts.status, parseInt(status)))
    }

    if (contractType) {
      whereConditions.push(eq(admissionContracts.contractType, parseInt(contractType)))
    }

    if (startDate) {
      whereConditions.push(gte(admissionContracts.createdAt, new Date(startDate)))
    }

    if (endDate) {
      whereConditions.push(lte(admissionContracts.createdAt, new Date(endDate)))
    }

    // 排序
    const orderBy = sortOrder === 'asc' 
      ? asc(admissionContracts[sortBy as keyof typeof admissionContracts] || admissionContracts.createdAt)
      : desc(admissionContracts[sortBy as keyof typeof admissionContracts] || admissionContracts.createdAt)

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(admissionContracts)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 获取合同列表
    const contractList = await db
      .select({
        id: admissionContracts.id,
        applicationId: admissionContracts.applicationId,
        contractNumber: admissionContracts.contractNumber,
        contractType: admissionContracts.contractType,
        contractTitle: admissionContracts.contractTitle,
        partyA: admissionContracts.partyA,
        partyB: admissionContracts.partyB,
        elderName: admissionContracts.elderName,
        roomNumber: admissionContracts.roomNumber,
        status: admissionContracts.status,
        signDate: admissionContracts.signDate,
        effectiveDate: admissionContracts.effectiveDate,
        expiryDate: admissionContracts.expiryDate,
        contractFile: admissionContracts.contractFile,
        createdAt: admissionContracts.createdAt,
        updatedAt: admissionContracts.updatedAt,
        // 关联数据
        application: {
          id: admissionApplications.id,
          applicationNumber: admissionApplications.applicationNumber,
          status: admissionApplications.status,
        },
        createdByUser: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(admissionContracts)
      .leftJoin(admissionApplications, eq(admissionContracts.applicationId, admissionApplications.id))
      .leftJoin(betterAuthUsers, eq(admissionContracts.createdBy, betterAuthUsers.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limitNum)
      .offset(offset)

    return NextResponse.json({
      success: true,
      data: {
        list: contractList,
        pagination: {
          page,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      },
    })
  } catch (error) {
    console.error('获取合同列表失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// POST - 创建合同
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createContractSchema.parse(body)

    // 检查申请是否存在且状态正确
    const application = await db
      .select()
      .from(admissionApplications)
      .where(eq(admissionApplications.id, validatedData.applicationId))
      .limit(1)

    if (application.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '入住申请不存在' },
        { status: 404 }
      )
    }

    if (application[0].status !== 5) { // 已通过
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_APPLICATION_STATUS', 
          message: '申请尚未通过审核，无法创建合同' 
        },
        { status: 400 }
      )
    }

    // 检查是否已存在有效合同
    const existingContract = await db
      .select()
      .from(admissionContracts)
      .where(
        and(
          eq(admissionContracts.applicationId, validatedData.applicationId),
          or(
            eq(admissionContracts.status, 3), // 已签署
            eq(admissionContracts.status, 4)  // 已生效
          )
        )
      )
      .limit(1)

    if (existingContract.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'CONTRACT_EXISTS', 
          message: '该申请已存在有效合同' 
        },
        { status: 400 }
      )
    }

    // 创建合同
    const contractNumber = generateContractNumber()

    const contractData = {
      ...validatedData,
      contractNumber,
      feeStructure: JSON.stringify(validatedData.feeStructure),
      attachments: validatedData.attachments ? JSON.stringify(validatedData.attachments) : null,
      createdBy: user.id,
    }

    const newContract = await db
      .insert(admissionContracts)
      .values(contractData)
      .returning()

    return NextResponse.json({
      success: true,
      data: newContract[0],
      message: '合同创建成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('创建合同失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
