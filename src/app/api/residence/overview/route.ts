import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { elderInfo, rooms, reservations } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, gte, lte, count, sql } from 'drizzle-orm'
import { format, subDays, startOfMonth, endOfMonth, startOfYear, endOfYear } from 'date-fns'

// 查询参数验证
const querySchema = z.object({
  period: z.enum(['week', 'month', 'quarter', 'year']).optional().default('month'),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

/**
 * 获取入住统计概览数据
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
          code: 'UNAUTHORIZED',
        },
        { status: 401 }
      )
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url)
    const params = querySchema.safeParse({
      period: searchParams.get('period'),
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
    })

    if (!params.success) {
      return NextResponse.json(
        {
          success: false,
          error: '请求参数格式错误',
          code: 'INVALID_PARAMS',
          details: params.error.errors,
        },
        { status: 400 }
      )
    }

    const { period, startDate, endDate } = params.data

    // 计算时间范围
    let dateRange: { start: string; end: string }
    const now = new Date()

    if (startDate && endDate) {
      dateRange = { start: startDate, end: endDate }
    } else {
      switch (period) {
        case 'week':
          dateRange = {
            start: format(subDays(now, 7), 'yyyy-MM-dd'),
            end: format(now, 'yyyy-MM-dd'),
          }
          break
        case 'month':
          dateRange = {
            start: format(startOfMonth(now), 'yyyy-MM-dd'),
            end: format(endOfMonth(now), 'yyyy-MM-dd'),
          }
          break
        case 'quarter':
          const quarterStart = Math.floor(now.getMonth() / 3) * 3
          dateRange = {
            start: format(new Date(now.getFullYear(), quarterStart, 1), 'yyyy-MM-dd'),
            end: format(new Date(now.getFullYear(), quarterStart + 3, 0), 'yyyy-MM-dd'),
          }
          break
        case 'year':
          dateRange = {
            start: format(startOfYear(now), 'yyyy-MM-dd'),
            end: format(endOfYear(now), 'yyyy-MM-dd'),
          }
          break
        default:
          dateRange = {
            start: format(startOfMonth(now), 'yyyy-MM-dd'),
            end: format(endOfMonth(now), 'yyyy-MM-dd'),
          }
      }
    }

    // 获取统计数据
    const [
      roomStats,
      occupancyStats,
      roomTypeStats,
      reservationStats,
      recentAdmissions,
      trendData,
    ] = await Promise.all([
      getRoomStatistics(),
      getOccupancyStatistics(),
      getRoomTypeStatistics(),
      getReservationStatistics(dateRange),
      getRecentAdmissions(dateRange),
      getTrendData(period, dateRange),
    ])

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          totalRooms: roomStats.totalRooms,
          availableRooms: roomStats.availableRooms,
          occupiedRooms: roomStats.occupiedRooms,
          maintenanceRooms: roomStats.maintenanceRooms,
          totalBeds: roomStats.totalBeds,
          occupiedBeds: roomStats.occupiedBeds,
          occupancyRate: roomStats.occupancyRate,
          totalResidents: occupancyStats.totalResidents,
          newAdmissions: recentAdmissions.length,
          pendingReservations: reservationStats.pendingReservations,
        },
        roomTypes: roomTypeStats,
        reservations: reservationStats,
        recentAdmissions,
        trend: trendData,
        dateRange,
      },
      message: '入住统计数据获取成功',
    })
  } catch (error) {
    console.error('获取入住统计数据失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    )
  }
}

/**
 * 获取房间统计数据
 */
async function getRoomStatistics() {
  // 获取房间总数和状态分布
  const roomStatusStats = await db
    .select({
      status: rooms.status,
      count: count(rooms.id),
      totalCapacity: sql<number>`SUM(${rooms.capacity})`,
      currentOccupancy: sql<number>`SUM(${rooms.currentOccupancy})`,
    })
    .from(rooms)
    .groupBy(rooms.status)

  let totalRooms = 0
  let availableRooms = 0
  let occupiedRooms = 0
  let maintenanceRooms = 0
  let totalBeds = 0
  let occupiedBeds = 0

  roomStatusStats.forEach(stat => {
    totalRooms += stat.count
    totalBeds += stat.totalCapacity || 0
    occupiedBeds += stat.currentOccupancy || 0

    switch (stat.status) {
      case 1: // 可用
        availableRooms += stat.count
        break
      case 2: // 维修中
        maintenanceRooms += stat.count
        break
      case 3: // 停用
        break
    }
  })

  // 计算已占用房间数（有人入住的房间）
  const occupiedRoomsResult = await db
    .select({
      count: count(rooms.id),
    })
    .from(rooms)
    .where(and(eq(rooms.status, 1), sql`${rooms.currentOccupancy} > 0`))

  occupiedRooms = occupiedRoomsResult[0]?.count || 0

  const occupancyRate = totalBeds > 0 ? Number(((occupiedBeds / totalBeds) * 100).toFixed(2)) : 0

  return {
    totalRooms,
    availableRooms,
    occupiedRooms,
    maintenanceRooms,
    totalBeds,
    occupiedBeds,
    occupancyRate,
  }
}

/**
 * 获取入住统计数据
 */
async function getOccupancyStatistics() {
  // 获取在院老人总数
  const totalResidentsResult = await db
    .select({
      count: count(elderInfo.id),
    })
    .from(elderInfo)
    .where(eq(elderInfo.status, 1)) // 1: 在院

  const totalResidents = totalResidentsResult[0]?.count || 0

  return {
    totalResidents,
  }
}

/**
 * 获取房间类型统计
 */
async function getRoomTypeStatistics() {
  const roomTypeStats = await db
    .select({
      roomType: rooms.roomType,
      count: count(rooms.id),
      totalCapacity: sql<number>`SUM(${rooms.capacity})`,
      currentOccupancy: sql<number>`SUM(${rooms.currentOccupancy})`,
    })
    .from(rooms)
    .where(eq(rooms.status, 1)) // 只统计可用房间
    .groupBy(rooms.roomType)

  const roomTypeNames = {
    1: '单人间',
    2: '双人间',
    3: '三人间',
    4: '四人间',
  }

  return roomTypeStats.map(stat => ({
    roomType: stat.roomType,
    roomTypeName: roomTypeNames[stat.roomType as keyof typeof roomTypeNames] || '未知',
    roomCount: stat.count,
    totalBeds: stat.totalCapacity || 0,
    occupiedBeds: stat.currentOccupancy || 0,
    occupancyRate: stat.totalCapacity > 0 
      ? Number(((stat.currentOccupancy || 0) / stat.totalCapacity * 100).toFixed(2))
      : 0,
  }))
}

/**
 * 获取预订统计数据
 */
async function getReservationStatistics(dateRange: { start: string; end: string }) {
  // 获取待确认预订数量
  const pendingReservationsResult = await db
    .select({
      count: count(reservations.id),
    })
    .from(reservations)
    .where(eq(reservations.status, 1)) // 1: 待确认

  // 获取时间范围内的预订统计
  const reservationStats = await db
    .select({
      status: reservations.status,
      count: count(reservations.id),
    })
    .from(reservations)
    .where(
      and(
        gte(reservations.reservationDate, dateRange.start),
        lte(reservations.reservationDate, dateRange.end)
      )
    )
    .groupBy(reservations.status)

  const statusNames = {
    1: '待确认',
    2: '已确认',
    3: '已入住',
    4: '已取消',
    5: '已过期',
  }

  return {
    pendingReservations: pendingReservationsResult[0]?.count || 0,
    statusDistribution: reservationStats.map(stat => ({
      status: stat.status,
      statusName: statusNames[stat.status as keyof typeof statusNames] || '未知',
      count: stat.count,
    })),
  }
}

/**
 * 获取最近入住记录
 */
async function getRecentAdmissions(dateRange: { start: string; end: string }) {
  const recentAdmissions = await db
    .select({
      id: elderInfo.id,
      name: elderInfo.name,
      age: elderInfo.age,
      gender: elderInfo.gender,
      roomNumber: rooms.roomNumber,
      admissionDate: elderInfo.admissionDate,
      careLevel: elderInfo.careLevel,
    })
    .from(elderInfo)
    .leftJoin(rooms, eq(elderInfo.roomId, rooms.id))
    .where(
      and(
        eq(elderInfo.status, 1), // 在院
        gte(elderInfo.admissionDate, dateRange.start),
        lte(elderInfo.admissionDate, dateRange.end)
      )
    )
    .orderBy(sql`${elderInfo.admissionDate} DESC`)
    .limit(10)

  const careLevelNames = {
    1: '自理',
    2: '半自理',
    3: '不能自理',
  }

  return recentAdmissions.map(admission => ({
    ...admission,
    genderName: admission.gender === 1 ? '男' : '女',
    careLevelName: careLevelNames[admission.careLevel as keyof typeof careLevelNames] || '未知',
  }))
}

/**
 * 获取趋势数据
 */
async function getTrendData(period: string, dateRange: { start: string; end: string }) {
  // 根据时间周期生成日期序列
  const dates = generateDateSeries(period, dateRange)
  
  // 获取每日入住数据
  const trendData = await Promise.all(
    dates.map(async date => {
      const dayStart = date
      const dayEnd = date

      // 获取当日新入住人数
      const newAdmissionsResult = await db
        .select({
          count: count(elderInfo.id),
        })
        .from(elderInfo)
        .where(
          and(
            eq(elderInfo.status, 1),
            gte(elderInfo.admissionDate, dayStart),
            lte(elderInfo.admissionDate, dayEnd)
          )
        )

      // 获取当日预订数量
      const reservationsResult = await db
        .select({
          count: count(reservations.id),
        })
        .from(reservations)
        .where(
          and(
            gte(reservations.reservationDate, dayStart),
            lte(reservations.reservationDate, dayEnd)
          )
        )

      return {
        date,
        newAdmissions: newAdmissionsResult[0]?.count || 0,
        newReservations: reservationsResult[0]?.count || 0,
      }
    })
  )

  return trendData
}

/**
 * 生成日期序列
 */
function generateDateSeries(period: string, dateRange: { start: string; end: string }): string[] {
  const dates: string[] = []
  const start = new Date(dateRange.start)
  const end = new Date(dateRange.end)

  let current = new Date(start)
  while (current <= end) {
    dates.push(format(current, 'yyyy-MM-dd'))
    current.setDate(current.getDate() + 1)
  }

  return dates
}
