import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  checkoutReviews,
  checkoutApplications,
  elderInfo
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, inArray } from 'drizzle-orm'

// 批量审核验证 schema
const batchReviewSchema = z.object({
  applicationIds: z.array(z.string().uuid()).min(1, '请选择至少一个申请'),
  reviewLevel: z.number().min(1).max(3, '请选择有效的审核级别'),
  reviewType: z.number().min(1).max(4, '请选择有效的审核类型'),
  reviewResult: z.number().min(1).max(3, '请选择审核结果'),
  reviewNotes: z.string().optional(),
  requirements: z.array(z.object({
    requirement: z.string(),
    completed: z.boolean().optional().default(false),
    notes: z.string().optional(),
  })).optional(),
  suggestions: z.string().optional(),
  nextReviewLevel: z.number().min(1).max(3).optional(),
  nextReviewer: z.string().uuid().optional(),
})

// 批量状态更新验证 schema
const batchStatusUpdateSchema = z.object({
  applicationIds: z.array(z.string().uuid()).min(1, '请选择至少一个申请'),
  status: z.number().min(1).max(7, '请选择有效的状态'),
  reviewNotes: z.string().optional(),
})

// POST - 批量审核
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = batchReviewSchema.parse(body)

    // 检查所有申请是否存在且状态允许审核
    const applications = await db
      .select({
        id: checkoutApplications.id,
        status: checkoutApplications.status,
        applicationNumber: checkoutApplications.applicationNumber,
        elderName: elderInfo.name,
      })
      .from(checkoutApplications)
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .where(inArray(checkoutApplications.id, validatedData.applicationIds))

    if (applications.length !== validatedData.applicationIds.length) {
      return NextResponse.json(
        { success: false, code: 'APPLICATIONS_NOT_FOUND', message: '部分申请不存在' },
        { status: 404 }
      )
    }

    // 检查申请状态
    const invalidApplications = applications.filter(app => ![1, 2, 3].includes(app.status))
    if (invalidApplications.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_STATUS', 
          message: '部分申请状态不允许审核',
          data: {
            invalidApplications: invalidApplications.map(app => ({
              id: app.id,
              applicationNumber: app.applicationNumber,
              elderName: app.elderName,
              status: app.status,
            }))
          }
        },
        { status: 400 }
      )
    }

    // 开始事务处理批量审核
    const results = await db.transaction(async (tx) => {
      const reviewResults = []
      const updateResults = []

      for (const application of applications) {
        try {
          // 创建审核记录
          const newReview = await tx
            .insert(checkoutReviews)
            .values({
              applicationId: application.id,
              reviewLevel: validatedData.reviewLevel,
              reviewType: validatedData.reviewType,
              reviewResult: validatedData.reviewResult,
              reviewNotes: validatedData.reviewNotes,
              requirements: validatedData.requirements ? JSON.stringify(validatedData.requirements) : null,
              suggestions: validatedData.suggestions,
              reviewedBy: user.id,
              nextReviewLevel: validatedData.nextReviewLevel,
              nextReviewer: validatedData.nextReviewer,
            })
            .returning()

          reviewResults.push({
            applicationId: application.id,
            reviewId: newReview[0].id,
            success: true,
          })

          // 根据审核结果更新申请状态
          let newApplicationStatus = application.status
          
          switch (validatedData.reviewResult) {
            case 1: // 通过
              if (validatedData.nextReviewLevel) {
                // 还有下一级审核
                newApplicationStatus = 2 // 审核中
              } else {
                // 所有审核完成
                newApplicationStatus = 3 // 已通过
              }
              break
            case 2: // 拒绝
              newApplicationStatus = 4 // 已拒绝
              break
            case 3: // 需要补充材料
              newApplicationStatus = 1 // 待审核
              break
          }

          // 更新申请状态
          await tx
            .update(checkoutApplications)
            .set({
              status: newApplicationStatus,
              reviewedBy: user.id,
              reviewedAt: new Date(),
              reviewNotes: validatedData.reviewNotes,
              updatedAt: new Date(),
            })
            .where(eq(checkoutApplications.id, application.id))

          updateResults.push({
            applicationId: application.id,
            oldStatus: application.status,
            newStatus: newApplicationStatus,
            success: true,
          })

        } catch (error) {
          console.error(`处理申请 ${application.id} 失败:`, error)
          reviewResults.push({
            applicationId: application.id,
            success: false,
            error: error instanceof Error ? error.message : '未知错误',
          })
          updateResults.push({
            applicationId: application.id,
            success: false,
            error: error instanceof Error ? error.message : '未知错误',
          })
        }
      }

      return { reviewResults, updateResults }
    })

    // 统计结果
    const successCount = results.reviewResults.filter(r => r.success).length
    const failureCount = results.reviewResults.filter(r => !r.success).length

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          total: validatedData.applicationIds.length,
          success: successCount,
          failure: failureCount,
        },
        details: results,
      },
      message: `批量审核完成，成功 ${successCount} 个，失败 ${failureCount} 个`,
    })
  } catch (error) {
    console.error('批量审核失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'VALIDATION_ERROR', 
          message: '数据验证失败',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// PUT - 批量更新状态
export async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = batchStatusUpdateSchema.parse(body)

    // 检查所有申请是否存在
    const applications = await db
      .select({
        id: checkoutApplications.id,
        status: checkoutApplications.status,
        applicationNumber: checkoutApplications.applicationNumber,
        elderName: elderInfo.name,
      })
      .from(checkoutApplications)
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .where(inArray(checkoutApplications.id, validatedData.applicationIds))

    if (applications.length !== validatedData.applicationIds.length) {
      return NextResponse.json(
        { success: false, code: 'APPLICATIONS_NOT_FOUND', message: '部分申请不存在' },
        { status: 404 }
      )
    }

    // 状态流转规则检查
    const STATUS_TRANSITIONS = {
      1: [2, 4], // 待审核 -> 审核中, 已拒绝
      2: [3, 4], // 审核中 -> 已通过, 已拒绝
      3: [5],    // 已通过 -> 费用结算中
      4: [],     // 已拒绝 (终态)
      5: [6],    // 费用结算中 -> 房间清理中
      6: [7],    // 房间清理中 -> 已完成
      7: [],     // 已完成 (终态)
    }

    // 检查状态流转是否合法
    const invalidTransitions = applications.filter(app => {
      const allowedTransitions = STATUS_TRANSITIONS[app.status as keyof typeof STATUS_TRANSITIONS] || []
      return !allowedTransitions.includes(validatedData.status)
    })

    if (invalidTransitions.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_STATUS_TRANSITION', 
          message: '部分申请的状态流转不合法',
          data: {
            invalidTransitions: invalidTransitions.map(app => ({
              id: app.id,
              applicationNumber: app.applicationNumber,
              elderName: app.elderName,
              currentStatus: app.status,
              targetStatus: validatedData.status,
            }))
          }
        },
        { status: 400 }
      )
    }

    // 开始事务处理批量状态更新
    const results = await db.transaction(async (tx) => {
      const updateResults = []

      for (const application of applications) {
        try {
          // 更新申请状态
          await tx
            .update(checkoutApplications)
            .set({
              status: validatedData.status,
              reviewedBy: user.id,
              reviewedAt: new Date(),
              reviewNotes: validatedData.reviewNotes,
              updatedAt: new Date(),
            })
            .where(eq(checkoutApplications.id, application.id))

          updateResults.push({
            applicationId: application.id,
            oldStatus: application.status,
            newStatus: validatedData.status,
            success: true,
          })

        } catch (error) {
          console.error(`更新申请 ${application.id} 状态失败:`, error)
          updateResults.push({
            applicationId: application.id,
            success: false,
            error: error instanceof Error ? error.message : '未知错误',
          })
        }
      }

      return updateResults
    })

    // 统计结果
    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          total: validatedData.applicationIds.length,
          success: successCount,
          failure: failureCount,
        },
        details: results,
      },
      message: `批量状态更新完成，成功 ${successCount} 个，失败 ${failureCount} 个`,
    })
  } catch (error) {
    console.error('批量状态更新失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'VALIDATION_ERROR', 
          message: '数据验证失败',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
