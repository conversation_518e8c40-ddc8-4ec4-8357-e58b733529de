import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  checkoutReviews,
  checkoutApplications,
  elderInfo,
  betterAuthUsers
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq } from 'drizzle-orm'

// 更新审核记录验证 schema
const updateReviewSchema = z.object({
  reviewNotes: z.string().optional(),
  requirements: z.array(z.object({
    requirement: z.string(),
    completed: z.boolean().optional().default(false),
    notes: z.string().optional(),
  })).optional(),
  suggestions: z.string().optional(),
  nextReviewLevel: z.number().min(1).max(3).optional(),
  nextReviewer: z.string().uuid().optional(),
})

// GET - 获取审核记录详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const reviewId = params.id

    // 获取审核记录详情
    const review = await db
      .select({
        // 审核记录信息
        id: checkoutReviews.id,
        reviewLevel: checkoutReviews.reviewLevel,
        reviewType: checkoutReviews.reviewType,
        reviewResult: checkoutReviews.reviewResult,
        reviewNotes: checkoutReviews.reviewNotes,
        requirements: checkoutReviews.requirements,
        suggestions: checkoutReviews.suggestions,
        reviewedAt: checkoutReviews.reviewedAt,
        nextReviewLevel: checkoutReviews.nextReviewLevel,
        createdAt: checkoutReviews.createdAt,
        updatedAt: checkoutReviews.updatedAt,
        
        // 申请信息
        application: {
          id: checkoutApplications.id,
          applicationNumber: checkoutApplications.applicationNumber,
          applicantName: checkoutApplications.applicantName,
          applicantPhone: checkoutApplications.applicantPhone,
          applicantRelation: checkoutApplications.applicantRelation,
          checkoutReason: checkoutApplications.checkoutReason,
          checkoutType: checkoutApplications.checkoutType,
          expectedCheckoutDate: checkoutApplications.expectedCheckoutDate,
          actualCheckoutDate: checkoutApplications.actualCheckoutDate,
          status: checkoutApplications.status,
          applicationDate: checkoutApplications.applicationDate,
          notes: checkoutApplications.notes,
        },
        
        // 老人信息
        elder: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
          idCard: elderInfo.idCard,
          phone: elderInfo.phone,
          address: elderInfo.address,
          emergencyContactName: elderInfo.emergencyContactName,
          emergencyContactPhone: elderInfo.emergencyContactPhone,
          emergencyContactRelation: elderInfo.emergencyContactRelation,
          medicalHistory: elderInfo.medicalHistory,
          allergies: elderInfo.allergies,
          medications: elderInfo.medications,
          careLevel: elderInfo.careLevel,
          status: elderInfo.status,
          admissionDate: elderInfo.admissionDate,
        },
        
        // 审核人信息
        reviewer: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(checkoutReviews)
      .leftJoin(checkoutApplications, eq(checkoutReviews.applicationId, checkoutApplications.id))
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .leftJoin(betterAuthUsers, eq(checkoutReviews.reviewedBy, betterAuthUsers.id))
      .where(eq(checkoutReviews.id, reviewId))
      .limit(1)

    if (review.length === 0) {
      return NextResponse.json(
        { success: false, code: 'REVIEW_NOT_FOUND', message: '审核记录不存在' },
        { status: 404 }
      )
    }

    // 获取下一审核人信息
    let nextReviewer = null
    if (review[0].nextReviewer) {
      const nextReviewerData = await db
        .select({
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        })
        .from(betterAuthUsers)
        .where(eq(betterAuthUsers.id, review[0].nextReviewer))
        .limit(1)
      
      if (nextReviewerData.length > 0) {
        nextReviewer = nextReviewerData[0]
      }
    }

    // 处理数据格式
    const processedReview = {
      ...review[0],
      requirements: review[0].requirements ? JSON.parse(review[0].requirements) : [],
      nextReviewer,
    }

    return NextResponse.json({
      success: true,
      data: processedReview,
    })
  } catch (error) {
    console.error('获取审核记录详情失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// PUT - 更新审核记录
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const reviewId = params.id
    const body = await request.json()
    const validatedData = updateReviewSchema.parse(body)

    // 检查审核记录是否存在
    const existingReview = await db
      .select({
        id: checkoutReviews.id,
        reviewedBy: checkoutReviews.reviewedBy,
        reviewResult: checkoutReviews.reviewResult,
      })
      .from(checkoutReviews)
      .where(eq(checkoutReviews.id, reviewId))
      .limit(1)

    if (existingReview.length === 0) {
      return NextResponse.json(
        { success: false, code: 'REVIEW_NOT_FOUND', message: '审核记录不存在' },
        { status: 404 }
      )
    }

    // 检查权限（只有审核人本人可以修改）
    if (existingReview[0].reviewedBy !== user.id) {
      return NextResponse.json(
        { success: false, code: 'PERMISSION_DENIED', message: '只有审核人本人可以修改审核记录' },
        { status: 403 }
      )
    }

    // 准备更新数据
    const updateData: any = {
      updatedAt: new Date(),
    }

    // 只更新提供的字段
    if (validatedData.reviewNotes !== undefined) updateData.reviewNotes = validatedData.reviewNotes
    if (validatedData.requirements !== undefined) updateData.requirements = JSON.stringify(validatedData.requirements)
    if (validatedData.suggestions !== undefined) updateData.suggestions = validatedData.suggestions
    if (validatedData.nextReviewLevel !== undefined) updateData.nextReviewLevel = validatedData.nextReviewLevel
    if (validatedData.nextReviewer !== undefined) updateData.nextReviewer = validatedData.nextReviewer

    // 更新审核记录
    const updatedReview = await db
      .update(checkoutReviews)
      .set(updateData)
      .where(eq(checkoutReviews.id, reviewId))
      .returning()

    return NextResponse.json({
      success: true,
      data: updatedReview[0],
      message: '审核记录更新成功',
    })
  } catch (error) {
    console.error('更新审核记录失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'VALIDATION_ERROR', 
          message: '数据验证失败',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// DELETE - 删除审核记录
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const reviewId = params.id

    // 检查审核记录是否存在
    const existingReview = await db
      .select({
        id: checkoutReviews.id,
        reviewedBy: checkoutReviews.reviewedBy,
        applicationId: checkoutReviews.applicationId,
      })
      .from(checkoutReviews)
      .where(eq(checkoutReviews.id, reviewId))
      .limit(1)

    if (existingReview.length === 0) {
      return NextResponse.json(
        { success: false, code: 'REVIEW_NOT_FOUND', message: '审核记录不存在' },
        { status: 404 }
      )
    }

    // 检查权限（只有审核人本人或管理员可以删除）
    if (existingReview[0].reviewedBy !== user.id) {
      // 这里可以添加管理员权限检查
      return NextResponse.json(
        { success: false, code: 'PERMISSION_DENIED', message: '只有审核人本人可以删除审核记录' },
        { status: 403 }
      )
    }

    // 检查是否可以删除（如果是最新的审核记录，可能需要回滚申请状态）
    const applicationReviews = await db
      .select({
        id: checkoutReviews.id,
        reviewedAt: checkoutReviews.reviewedAt,
      })
      .from(checkoutReviews)
      .where(eq(checkoutReviews.applicationId, existingReview[0].applicationId))
      .orderBy(checkoutReviews.reviewedAt)

    const isLatestReview = applicationReviews[applicationReviews.length - 1]?.id === reviewId

    // 开始事务处理删除
    await db.transaction(async (tx) => {
      // 删除审核记录
      await tx
        .delete(checkoutReviews)
        .where(eq(checkoutReviews.id, reviewId))

      // 如果是最新的审核记录，需要回滚申请状态
      if (isLatestReview && applicationReviews.length > 1) {
        // 获取前一个审核记录的结果，据此更新申请状态
        const previousReview = applicationReviews[applicationReviews.length - 2]
        if (previousReview) {
          const previousReviewData = await tx
            .select({
              reviewResult: checkoutReviews.reviewResult,
              nextReviewLevel: checkoutReviews.nextReviewLevel,
            })
            .from(checkoutReviews)
            .where(eq(checkoutReviews.id, previousReview.id))
            .limit(1)

          if (previousReviewData.length > 0) {
            let newStatus = 1 // 默认待审核
            const prevResult = previousReviewData[0].reviewResult
            const prevNextLevel = previousReviewData[0].nextReviewLevel

            if (prevResult === 1 && prevNextLevel) {
              newStatus = 2 // 审核中
            } else if (prevResult === 1 && !prevNextLevel) {
              newStatus = 3 // 已通过
            } else if (prevResult === 2) {
              newStatus = 4 // 已拒绝
            }

            await tx
              .update(checkoutApplications)
              .set({
                status: newStatus,
                updatedAt: new Date(),
              })
              .where(eq(checkoutApplications.id, existingReview[0].applicationId))
          }
        }
      } else if (isLatestReview && applicationReviews.length === 1) {
        // 如果是唯一的审核记录，回滚到待审核状态
        await tx
          .update(checkoutApplications)
          .set({
            status: 1, // 待审核
            reviewedBy: null,
            reviewedAt: null,
            reviewNotes: null,
            updatedAt: new Date(),
          })
          .where(eq(checkoutApplications.id, existingReview[0].applicationId))
      }
    })

    return NextResponse.json({
      success: true,
      message: '审核记录删除成功',
    })
  } catch (error) {
    console.error('删除审核记录失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
