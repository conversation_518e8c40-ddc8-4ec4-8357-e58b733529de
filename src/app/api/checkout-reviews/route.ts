import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  checkoutReviews,
  checkoutApplications,
  elderInfo,
  betterAuthUsers
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, like, desc, asc, count, gte, lte } from 'drizzle-orm'

// 查询参数验证 schema
const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('10'),
  applicationId: z.string().uuid().optional(),
  reviewLevel: z.string().optional(),
  reviewType: z.string().optional(),
  reviewResult: z.string().optional(),
  reviewedBy: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.string().optional().default('reviewedAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
})

// 创建审核记录验证 schema
const createReviewSchema = z.object({
  applicationId: z.string().uuid('请选择有效的申请'),
  reviewLevel: z.number().min(1).max(3, '请选择有效的审核级别'),
  reviewType: z.number().min(1).max(4, '请选择有效的审核类型'),
  reviewResult: z.number().min(1).max(3, '请选择审核结果'),
  reviewNotes: z.string().optional(),
  requirements: z.array(z.object({
    requirement: z.string(),
    completed: z.boolean().optional().default(false),
    notes: z.string().optional(),
  })).optional(),
  suggestions: z.string().optional(),
  nextReviewLevel: z.number().min(1).max(3).optional(),
  nextReviewer: z.string().uuid().optional(),
})

// GET - 获取审核记录列表
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const validatedParams = querySchema.parse(Object.fromEntries(searchParams))

    const page = parseInt(validatedParams.page)
    const limit = parseInt(validatedParams.limit)
    const offset = (page - 1) * limit

    // 构建查询条件
    const whereConditions = []

    // 申请ID筛选
    if (validatedParams.applicationId) {
      whereConditions.push(eq(checkoutReviews.applicationId, validatedParams.applicationId))
    }

    // 审核级别筛选
    if (validatedParams.reviewLevel) {
      whereConditions.push(eq(checkoutReviews.reviewLevel, parseInt(validatedParams.reviewLevel)))
    }

    // 审核类型筛选
    if (validatedParams.reviewType) {
      whereConditions.push(eq(checkoutReviews.reviewType, parseInt(validatedParams.reviewType)))
    }

    // 审核结果筛选
    if (validatedParams.reviewResult) {
      whereConditions.push(eq(checkoutReviews.reviewResult, parseInt(validatedParams.reviewResult)))
    }

    // 审核人筛选
    if (validatedParams.reviewedBy) {
      whereConditions.push(eq(checkoutReviews.reviewedBy, validatedParams.reviewedBy))
    }

    // 日期范围筛选
    if (validatedParams.dateFrom) {
      whereConditions.push(gte(checkoutReviews.reviewedAt, new Date(validatedParams.dateFrom)))
    }
    if (validatedParams.dateTo) {
      whereConditions.push(lte(checkoutReviews.reviewedAt, new Date(validatedParams.dateTo)))
    }

    // 排序
    const orderBy = validatedParams.sortOrder === 'asc' 
      ? asc(checkoutReviews[validatedParams.sortBy as keyof typeof checkoutReviews])
      : desc(checkoutReviews[validatedParams.sortBy as keyof typeof checkoutReviews])

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(checkoutReviews)
      .leftJoin(checkoutApplications, eq(checkoutReviews.applicationId, checkoutApplications.id))
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0].count

    // 获取审核记录列表
    const reviews = await db
      .select({
        // 审核记录信息
        id: checkoutReviews.id,
        reviewLevel: checkoutReviews.reviewLevel,
        reviewType: checkoutReviews.reviewType,
        reviewResult: checkoutReviews.reviewResult,
        reviewNotes: checkoutReviews.reviewNotes,
        requirements: checkoutReviews.requirements,
        suggestions: checkoutReviews.suggestions,
        reviewedAt: checkoutReviews.reviewedAt,
        nextReviewLevel: checkoutReviews.nextReviewLevel,
        createdAt: checkoutReviews.createdAt,
        
        // 申请信息
        application: {
          id: checkoutApplications.id,
          applicationNumber: checkoutApplications.applicationNumber,
          applicantName: checkoutApplications.applicantName,
          checkoutReason: checkoutApplications.checkoutReason,
          checkoutType: checkoutApplications.checkoutType,
          expectedCheckoutDate: checkoutApplications.expectedCheckoutDate,
          status: checkoutApplications.status,
          applicationDate: checkoutApplications.applicationDate,
        },
        
        // 老人信息
        elder: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
          idCard: elderInfo.idCard,
          careLevel: elderInfo.careLevel,
        },
        
        // 审核人信息
        reviewer: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
        },
        
        // 下一审核人信息
        nextReviewer: {
          id: 'nextReviewer.id', // 需要额外查询
          name: 'nextReviewer.name',
        },
      })
      .from(checkoutReviews)
      .leftJoin(checkoutApplications, eq(checkoutReviews.applicationId, checkoutApplications.id))
      .leftJoin(elderInfo, eq(checkoutApplications.elderInfoId, elderInfo.id))
      .leftJoin(betterAuthUsers, eq(checkoutReviews.reviewedBy, betterAuthUsers.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset)

    // 处理数据格式
    const processedReviews = reviews.map(review => ({
      ...review,
      requirements: review.requirements ? JSON.parse(review.requirements) : [],
    }))

    return NextResponse.json({
      success: true,
      data: {
        list: processedReviews,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error('获取审核记录列表失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// POST - 创建审核记录
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createReviewSchema.parse(body)

    // 检查申请是否存在
    const application = await db
      .select({
        id: checkoutApplications.id,
        status: checkoutApplications.status,
        applicationNumber: checkoutApplications.applicationNumber,
      })
      .from(checkoutApplications)
      .where(eq(checkoutApplications.id, validatedData.applicationId))
      .limit(1)

    if (application.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '退住申请不存在' },
        { status: 404 }
      )
    }

    // 检查申请状态是否允许审核
    const currentStatus = application[0].status
    if (![1, 2, 3].includes(currentStatus)) {
      return NextResponse.json(
        { success: false, code: 'INVALID_STATUS', message: '当前申请状态不允许审核' },
        { status: 400 }
      )
    }

    // 开始事务处理审核
    const result = await db.transaction(async (tx) => {
      // 创建审核记录
      const newReview = await tx
        .insert(checkoutReviews)
        .values({
          applicationId: validatedData.applicationId,
          reviewLevel: validatedData.reviewLevel,
          reviewType: validatedData.reviewType,
          reviewResult: validatedData.reviewResult,
          reviewNotes: validatedData.reviewNotes,
          requirements: validatedData.requirements ? JSON.stringify(validatedData.requirements) : null,
          suggestions: validatedData.suggestions,
          reviewedBy: user.id,
          nextReviewLevel: validatedData.nextReviewLevel,
          nextReviewer: validatedData.nextReviewer,
        })
        .returning()

      // 根据审核结果更新申请状态
      let newApplicationStatus = currentStatus
      
      switch (validatedData.reviewResult) {
        case 1: // 通过
          if (validatedData.nextReviewLevel) {
            // 还有下一级审核
            newApplicationStatus = 2 // 审核中
          } else {
            // 所有审核完成
            newApplicationStatus = 3 // 已通过
          }
          break
        case 2: // 拒绝
          newApplicationStatus = 4 // 已拒绝
          break
        case 3: // 需要补充材料
          newApplicationStatus = 1 // 待审核
          break
      }

      // 更新申请状态
      await tx
        .update(checkoutApplications)
        .set({
          status: newApplicationStatus,
          reviewedBy: user.id,
          reviewedAt: new Date(),
          reviewNotes: validatedData.reviewNotes,
          updatedAt: new Date(),
        })
        .where(eq(checkoutApplications.id, validatedData.applicationId))

      return newReview[0]
    })

    return NextResponse.json({
      success: true,
      data: result,
      message: '审核记录创建成功',
    })
  } catch (error) {
    console.error('创建审核记录失败:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'VALIDATION_ERROR', 
          message: '数据验证失败',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
