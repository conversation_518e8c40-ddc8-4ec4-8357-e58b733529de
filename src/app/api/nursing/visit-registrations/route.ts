import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { visitRegistrations, elderInfo } from '@/lib/db/schema'
import { eq, and, gte, lte, ilike, desc, asc, sql } from 'drizzle-orm'
import { auth } from '@/lib/auth'
import { z } from 'zod'

// 探访登记验证模式
const visitRegistrationSchema = z.object({
  elderInfoId: z.string().uuid(),
  visitorName: z.string().min(1, '探访者姓名不能为空'),
  visitorPhone: z.string().min(1, '探访者电话不能为空'),
  visitorIdCard: z.string().optional(),
  relationToElder: z.string().min(1, '与老人关系不能为空'),
  visitorCount: z.number().int().min(1).default(1),
  additionalVisitors: z.string().optional(),
  visitDate: z.string(),
  plannedStartTime: z.string(),
  plannedEndTime: z.string(),
  visitPurpose: z.string().min(1, '探访目的不能为空'),
  visitType: z.number().int().min(1).max(5),
  plannedActivities: z.string().optional(),
  itemsBrought: z.string().optional(),
  temperatureCheck: z.number().optional(),
  healthDeclaration: z.boolean().default(false),
  healthStatus: z.string().optional(),
  visitLocation: z.string().min(1, '探访地点不能为空'),
  roomNumber: z.string().optional(),
  isOutsideVisit: z.boolean().default(false),
  outsideDestination: z.string().optional(),
  approvalRequired: z.boolean().default(false),
  specialRequirements: z.string().optional(),
  emergencyContact: z.string().optional(),
  emergencyPhone: z.string().optional(),
  notes: z.string().optional(),
  attachments: z.string().optional(),
})

// 获取探访登记列表
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status')
    const visitType = searchParams.get('visitType')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const offset = (page - 1) * limit

    // 构建查询条件
    let whereConditions = []

    if (search) {
      whereConditions.push(
        ilike(elderInfo.name, `%${search}%`)
      )
    }

    if (status) {
      whereConditions.push(eq(visitRegistrations.status, parseInt(status)))
    }

    if (visitType) {
      whereConditions.push(eq(visitRegistrations.visitType, parseInt(visitType)))
    }

    if (startDate && endDate) {
      whereConditions.push(
        and(
          gte(visitRegistrations.visitDate, startDate),
          lte(visitRegistrations.visitDate, endDate)
        )
      )
    }

    // 构建排序
    const orderBy =
      sortOrder === 'desc'
        ? desc(visitRegistrations[sortBy as keyof typeof visitRegistrations])
        : asc(visitRegistrations[sortBy as keyof typeof visitRegistrations])

    // 查询数据
    const registrations = await db
      .select({
        id: visitRegistrations.id,
        registrationNumber: visitRegistrations.registrationNumber,
        elderName: elderInfo.name,
        elderAge: elderInfo.age,
        elderGender: elderInfo.gender,
        elderRoomNumber: elderInfo.roomNumber,
        elderBedNumber: elderInfo.bedNumber,
        visitorName: visitRegistrations.visitorName,
        visitorPhone: visitRegistrations.visitorPhone,
        relationToElder: visitRegistrations.relationToElder,
        visitorCount: visitRegistrations.visitorCount,
        visitDate: visitRegistrations.visitDate,
        plannedStartTime: visitRegistrations.plannedStartTime,
        plannedEndTime: visitRegistrations.plannedEndTime,
        actualStartTime: visitRegistrations.actualStartTime,
        actualEndTime: visitRegistrations.actualEndTime,
        visitPurpose: visitRegistrations.visitPurpose,
        visitType: visitRegistrations.visitType,
        visitLocation: visitRegistrations.visitLocation,
        isOutsideVisit: visitRegistrations.isOutsideVisit,
        outsideDestination: visitRegistrations.outsideDestination,
        status: visitRegistrations.status,
        approvalRequired: visitRegistrations.approvalRequired,
        approvedBy: visitRegistrations.approvedBy,
        approvalDate: visitRegistrations.approvalDate,
        temperatureCheck: visitRegistrations.temperatureCheck,
        healthDeclaration: visitRegistrations.healthDeclaration,
        createdAt: visitRegistrations.createdAt,
        updatedAt: visitRegistrations.updatedAt,
      })
      .from(visitRegistrations)
      .innerJoin(elderInfo, eq(visitRegistrations.elderInfoId, elderInfo.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset)

    // 获取总数
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(visitRegistrations)
      .innerJoin(elderInfo, eq(visitRegistrations.elderInfoId, elderInfo.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 添加标签
    const registrationsWithLabels = registrations.map(reg => ({
      ...reg,
      visitTypeLabel: getVisitTypeLabel(reg.visitType),
      statusLabel: getVisitStatusLabel(reg.status),
      genderLabel: reg.elderGender === 1 ? '男' : '女',
    }))

    return NextResponse.json({
      success: true,
      data: {
        registrations: registrationsWithLabels,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error('Error fetching visit registrations:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 创建探访登记
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = visitRegistrationSchema.parse(body)

    // 生成登记编号
    const today = new Date()
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '')
    const randomSuffix = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0')
    const registrationNumber = `VR${dateStr}${randomSuffix}`

    // 创建探访登记
    const newRegistration = await db
      .insert(visitRegistrations)
      .values({
        registrationNumber,
        ...validatedData,
        registeredBy: session.user.id,
      })
      .returning()

    return NextResponse.json({
      success: true,
      data: newRegistration[0],
      message: '探访登记创建成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating visit registration:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 辅助函数
function getVisitTypeLabel(type: number): string {
  const labels = {
    1: '家属探访',
    2: '朋友探访',
    3: '医疗探访',
    4: '法律事务',
    5: '其他',
  }
  return labels[type as keyof typeof labels] || '未知'
}

function getVisitStatusLabel(status: number): string {
  const labels = {
    1: '预约',
    2: '已到达',
    3: '探访中',
    4: '已结束',
    5: '已取消',
    6: '逾期未到',
  }
  return labels[status as keyof typeof labels] || '未知'
}
