import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { visitRegistrations, elderInfo } from '@/lib/db/schema'
import { eq } from 'drizzle-orm'
import { auth } from '@/lib/auth'
import { z } from 'zod'

// 探访操作验证模式
const visitActionSchema = z.object({
  action: z.enum(['approve', 'reject', 'checkin', 'checkout', 'cancel']),
  approvalComments: z.string().optional(),
  actualStartTime: z.string().optional(),
  actualEndTime: z.string().optional(),
  actualActivities: z.string().optional(),
  itemsLeft: z.string().optional(),
  restrictedItems: z.string().optional(),
})

// 获取探访登记详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const registration = await db
      .select({
        id: visitRegistrations.id,
        registrationNumber: visitRegistrations.registrationNumber,
        elderInfoId: visitRegistrations.elderInfoId,
        elderName: elderInfo.name,
        elderAge: elderInfo.age,
        elderGender: elderInfo.gender,
        elderRoomNumber: elderInfo.roomNumber,
        elderBedNumber: elderInfo.bedNumber,
        visitorName: visitRegistrations.visitorName,
        visitorPhone: visitRegistrations.visitorPhone,
        visitorIdCard: visitRegistrations.visitorIdCard,
        relationToElder: visitRegistrations.relationToElder,
        visitorCount: visitRegistrations.visitorCount,
        additionalVisitors: visitRegistrations.additionalVisitors,
        visitDate: visitRegistrations.visitDate,
        plannedStartTime: visitRegistrations.plannedStartTime,
        plannedEndTime: visitRegistrations.plannedEndTime,
        actualStartTime: visitRegistrations.actualStartTime,
        actualEndTime: visitRegistrations.actualEndTime,
        visitPurpose: visitRegistrations.visitPurpose,
        visitType: visitRegistrations.visitType,
        plannedActivities: visitRegistrations.plannedActivities,
        actualActivities: visitRegistrations.actualActivities,
        itemsBrought: visitRegistrations.itemsBrought,
        itemsLeft: visitRegistrations.itemsLeft,
        restrictedItems: visitRegistrations.restrictedItems,
        temperatureCheck: visitRegistrations.temperatureCheck,
        healthDeclaration: visitRegistrations.healthDeclaration,
        healthStatus: visitRegistrations.healthStatus,
        visitLocation: visitRegistrations.visitLocation,
        roomNumber: visitRegistrations.roomNumber,
        isOutsideVisit: visitRegistrations.isOutsideVisit,
        outsideDestination: visitRegistrations.outsideDestination,
        status: visitRegistrations.status,
        approvalRequired: visitRegistrations.approvalRequired,
        approvedBy: visitRegistrations.approvedBy,
        approvalDate: visitRegistrations.approvalDate,
        approvalComments: visitRegistrations.approvalComments,
        registeredBy: visitRegistrations.registeredBy,
        receivedBy: visitRegistrations.receivedBy,
        specialRequirements: visitRegistrations.specialRequirements,
        emergencyContact: visitRegistrations.emergencyContact,
        emergencyPhone: visitRegistrations.emergencyPhone,
        notes: visitRegistrations.notes,
        attachments: visitRegistrations.attachments,
        createdAt: visitRegistrations.createdAt,
        updatedAt: visitRegistrations.updatedAt,
      })
      .from(visitRegistrations)
      .innerJoin(elderInfo, eq(visitRegistrations.elderInfoId, elderInfo.id))
      .where(eq(visitRegistrations.id, params.id))

    if (!registration.length) {
      return NextResponse.json(
        { success: false, error: 'Visit registration not found' },
        { status: 404 }
      )
    }

    const registrationData = {
      ...registration[0],
      visitTypeLabel: getVisitTypeLabel(registration[0].visitType),
      statusLabel: getVisitStatusLabel(registration[0].status),
      genderLabel: registration[0].elderGender === 1 ? '男' : '女',
    }

    return NextResponse.json({
      success: true,
      data: registrationData,
    })
  } catch (error) {
    console.error('Error fetching visit registration:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 更新探访登记
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = visitActionSchema.parse(body)

    let updateData: any = {
      updatedAt: new Date(),
    }

    if (validatedData.action === 'approve') {
      // 批准探访
      updateData = {
        ...updateData,
        status: 1, // 预约状态
        approvedBy: session.user.id,
        approvalDate: new Date().toISOString().split('T')[0],
        approvalComments: validatedData.approvalComments,
      }
    } else if (validatedData.action === 'reject') {
      // 拒绝探访
      updateData = {
        ...updateData,
        status: 5, // 已取消
        approvedBy: session.user.id,
        approvalDate: new Date().toISOString().split('T')[0],
        approvalComments: validatedData.approvalComments,
      }
    } else if (validatedData.action === 'checkin') {
      // 探访签到
      updateData = {
        ...updateData,
        status: 2, // 已到达
        actualStartTime: validatedData.actualStartTime || new Date(),
        receivedBy: session.user.id,
      }
    } else if (validatedData.action === 'checkout') {
      // 探访签退
      updateData = {
        ...updateData,
        status: 4, // 已结束
        actualEndTime: validatedData.actualEndTime || new Date(),
        actualActivities: validatedData.actualActivities,
        itemsLeft: validatedData.itemsLeft,
        restrictedItems: validatedData.restrictedItems,
      }
    } else if (validatedData.action === 'cancel') {
      // 取消探访
      updateData = {
        ...updateData,
        status: 5, // 已取消
      }
    }

    const updatedRegistration = await db
      .update(visitRegistrations)
      .set(updateData)
      .where(eq(visitRegistrations.id, params.id))
      .returning()

    const actionMessages = {
      approve: '探访申请已批准',
      reject: '探访申请已拒绝',
      checkin: '探访签到成功',
      checkout: '探访签退成功',
      cancel: '探访已取消',
    }

    return NextResponse.json({
      success: true,
      data: updatedRegistration[0],
      message: actionMessages[validatedData.action],
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating visit registration:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 删除探访登记
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // 检查登记状态，只有预约状态才能删除
    const registration = await db
      .select({ status: visitRegistrations.status })
      .from(visitRegistrations)
      .where(eq(visitRegistrations.id, params.id))

    if (!registration.length) {
      return NextResponse.json(
        { success: false, error: 'Visit registration not found' },
        { status: 404 }
      )
    }

    if (registration[0].status !== 1) {
      return NextResponse.json(
        { success: false, error: 'Only scheduled visits can be deleted' },
        { status: 400 }
      )
    }

    await db
      .delete(visitRegistrations)
      .where(eq(visitRegistrations.id, params.id))

    return NextResponse.json({
      success: true,
      message: '探访登记已删除',
    })
  } catch (error) {
    console.error('Error deleting visit registration:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 辅助函数
function getVisitTypeLabel(type: number): string {
  const labels = {
    1: '家属探访',
    2: '朋友探访',
    3: '医疗探访',
    4: '法律事务',
    5: '其他',
  }
  return labels[type as keyof typeof labels] || '未知'
}

function getVisitStatusLabel(status: number): string {
  const labels = {
    1: '预约',
    2: '已到达',
    3: '探访中',
    4: '已结束',
    5: '已取消',
    6: '逾期未到',
  }
  return labels[status as keyof typeof labels] || '未知'
}
