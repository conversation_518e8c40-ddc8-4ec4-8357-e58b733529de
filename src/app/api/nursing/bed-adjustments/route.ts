import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { bedAdjustments, elderInfo, rooms } from '@/lib/db/schema'
import { eq, and, gte, lte, ilike, desc, asc, sql } from 'drizzle-orm'
import { auth } from '@/lib/auth'
import { z } from 'zod'

// 床位调整验证模式
const bedAdjustmentSchema = z.object({
  adjustmentType: z.number().int().min(1).max(3),
  elderInfoId: z.string().uuid(),
  currentRoomId: z.string().uuid(),
  currentBedNumber: z.string().optional(),
  targetRoomId: z.string().uuid(),
  targetBedNumber: z.string().optional(),
  swapElderInfoId: z.string().uuid().optional(),
  adjustmentReason: z.string().min(1, '调整原因不能为空'),
  reasonCategory: z.number().int().min(1).max(5),
  detailedReason: z.string().optional(),
  applicantName: z.string().min(1, '申请人姓名不能为空'),
  applicantPhone: z.string().min(1, '申请人电话不能为空'),
  applicantRelation: z.string().min(1, '与老人关系不能为空'),
  plannedDate: z.string(),
  feeAdjustment: z.number().optional(),
  feeAdjustmentReason: z.string().optional(),
  notes: z.string().optional(),
  attachments: z.string().optional(),
})

// 获取床位调整列表
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status')
    const adjustmentType = searchParams.get('adjustmentType')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const offset = (page - 1) * limit

    // 构建查询条件
    let whereConditions = []

    if (search) {
      whereConditions.push(
        ilike(elderInfo.name, `%${search}%`)
      )
    }

    if (status) {
      whereConditions.push(eq(bedAdjustments.status, parseInt(status)))
    }

    if (adjustmentType) {
      whereConditions.push(eq(bedAdjustments.adjustmentType, parseInt(adjustmentType)))
    }

    if (startDate && endDate) {
      whereConditions.push(
        and(
          gte(bedAdjustments.plannedDate, startDate),
          lte(bedAdjustments.plannedDate, endDate)
        )
      )
    }

    // 构建排序
    const orderBy = sortOrder === 'desc' 
      ? desc(bedAdjustments[sortBy as keyof typeof bedAdjustments])
      : asc(bedAdjustments[sortBy as keyof typeof bedAdjustments])

    // 查询数据
    const adjustments = await db
      .select({
        id: bedAdjustments.id,
        adjustmentNumber: bedAdjustments.adjustmentNumber,
        adjustmentType: bedAdjustments.adjustmentType,
        elderName: elderInfo.name,
        elderAge: elderInfo.age,
        elderGender: elderInfo.gender,
        currentRoomNumber: sql<string>`current_room.room_number`,
        currentBedNumber: bedAdjustments.currentBedNumber,
        targetRoomNumber: sql<string>`target_room.room_number`,
        targetBedNumber: bedAdjustments.targetBedNumber,
        adjustmentReason: bedAdjustments.adjustmentReason,
        reasonCategory: bedAdjustments.reasonCategory,
        applicantName: bedAdjustments.applicantName,
        applicantRelation: bedAdjustments.applicantRelation,
        plannedDate: bedAdjustments.plannedDate,
        actualDate: bedAdjustments.actualDate,
        status: bedAdjustments.status,
        reviewComments: bedAdjustments.reviewComments,
        feeAdjustment: bedAdjustments.feeAdjustment,
        createdAt: bedAdjustments.createdAt,
        updatedAt: bedAdjustments.updatedAt,
      })
      .from(bedAdjustments)
      .innerJoin(elderInfo, eq(bedAdjustments.elderInfoId, elderInfo.id))
      .innerJoin(rooms, eq(bedAdjustments.currentRoomId, rooms.id), 'current_room')
      .innerJoin(rooms, eq(bedAdjustments.targetRoomId, rooms.id), 'target_room')
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset)

    // 获取总数
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(bedAdjustments)
      .innerJoin(elderInfo, eq(bedAdjustments.elderInfoId, elderInfo.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 添加标签
    const adjustmentsWithLabels = adjustments.map(adj => ({
      ...adj,
      adjustmentTypeLabel: getAdjustmentTypeLabel(adj.adjustmentType),
      reasonCategoryLabel: getReasonCategoryLabel(adj.reasonCategory),
      statusLabel: getAdjustmentStatusLabel(adj.status),
      genderLabel: adj.elderGender === 1 ? '男' : '女',
    }))

    return NextResponse.json({
      success: true,
      data: {
        adjustments: adjustmentsWithLabels,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error('Error fetching bed adjustments:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 创建床位调整申请
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = bedAdjustmentSchema.parse(body)

    // 生成调整编号
    const today = new Date()
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '')
    const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    const adjustmentNumber = `BA${dateStr}${randomSuffix}`

    // 验证床位调整的合理性
    if (validatedData.adjustmentType === 2 && !validatedData.swapElderInfoId) {
      return NextResponse.json(
        { success: false, error: '床位对调需要指定对调老人' },
        { status: 400 }
      )
    }

    // 创建床位调整申请
    const newAdjustment = await db
      .insert(bedAdjustments)
      .values({
        adjustmentNumber,
        ...validatedData,
        createdBy: session.user.id,
      })
      .returning()

    return NextResponse.json({
      success: true,
      data: newAdjustment[0],
      message: '床位调整申请创建成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating bed adjustment:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 辅助函数
function getAdjustmentTypeLabel(type: number): string {
  const labels = {
    1: '单人调整',
    2: '床位对调',
    3: '房间调整',
  }
  return labels[type as keyof typeof labels] || '未知'
}

function getReasonCategoryLabel(category: number): string {
  const labels = {
    1: '健康需要',
    2: '护理需要',
    3: '个人要求',
    4: '管理需要',
    5: '其他',
  }
  return labels[category as keyof typeof labels] || '未知'
}

function getAdjustmentStatusLabel(status: number): string {
  const labels = {
    1: '待审核',
    2: '已批准',
    3: '已拒绝',
    4: '执行中',
    5: '已完成',
    6: '已取消',
  }
  return labels[status as keyof typeof labels] || '未知'
}
