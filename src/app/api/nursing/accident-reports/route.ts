import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { accidentReports, elderInfo } from '@/lib/db/schema'
import { eq, and, gte, lte, ilike, desc, asc, sql } from 'drizzle-orm'
import { auth } from '@/lib/auth'
import { z } from 'zod'

// 事故报告验证模式
const accidentReportSchema = z.object({
  elderInfoId: z.string().uuid(),
  accidentDate: z.string(),
  accidentTime: z.string(),
  location: z.string().min(1, '事故地点不能为空'),
  accidentType: z.number().int().min(1).max(5),
  severity: z.number().int().min(1).max(4),
  description: z.string().min(1, '事故描述不能为空'),
  cause: z.string().optional(),
  circumstances: z.string().optional(),
  discoveredBy: z.string().min(1, '发现人不能为空'),
  discoveredTime: z.string(),
  injuryDescription: z.string().optional(),
  immediateAction: z.string().optional(),
  medicalTreatment: z.string().optional(),
  hospitalTransfer: z.boolean().default(false),
  hospitalName: z.string().optional(),
  familyNotified: z.boolean().default(false),
  familyNotifyTime: z.string().optional(),
  familyResponse: z.string().optional(),
  responsibility: z.string().optional(),
  preventiveMeasures: z.string().optional(),
  followUpActions: z.string().optional(),
  notes: z.string().optional(),
  attachments: z.string().optional(),
})

// 获取事故报告列表
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status')
    const accidentType = searchParams.get('accidentType')
    const severity = searchParams.get('severity')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const offset = (page - 1) * limit

    // 构建查询条件
    let whereConditions = []

    if (search) {
      whereConditions.push(ilike(elderInfo.name, `%${search}%`))
    }

    if (status) {
      whereConditions.push(eq(accidentReports.status, parseInt(status)))
    }

    if (accidentType) {
      whereConditions.push(eq(accidentReports.accidentType, parseInt(accidentType)))
    }

    if (severity) {
      whereConditions.push(eq(accidentReports.severity, parseInt(severity)))
    }

    if (startDate && endDate) {
      whereConditions.push(
        and(
          gte(accidentReports.accidentDate, startDate),
          lte(accidentReports.accidentDate, endDate)
        )
      )
    }

    // 构建排序
    const orderBy =
      sortOrder === 'desc'
        ? desc(accidentReports[sortBy as keyof typeof accidentReports])
        : asc(accidentReports[sortBy as keyof typeof accidentReports])

    // 查询数据
    const reports = await db
      .select({
        id: accidentReports.id,
        reportNumber: accidentReports.reportNumber,
        elderName: elderInfo.name,
        elderAge: elderInfo.age,
        elderGender: elderInfo.gender,
        elderRoomNumber: elderInfo.roomNumber,
        elderBedNumber: elderInfo.bedNumber,
        accidentDate: accidentReports.accidentDate,
        accidentTime: accidentReports.accidentTime,
        location: accidentReports.location,
        accidentType: accidentReports.accidentType,
        severity: accidentReports.severity,
        description: accidentReports.description,
        discoveredBy: accidentReports.discoveredBy,
        discoveredTime: accidentReports.discoveredTime,
        hospitalTransfer: accidentReports.hospitalTransfer,
        hospitalName: accidentReports.hospitalName,
        familyNotified: accidentReports.familyNotified,
        status: accidentReports.status,
        handlingResult: accidentReports.handlingResult,
        createdAt: accidentReports.createdAt,
        updatedAt: accidentReports.updatedAt,
      })
      .from(accidentReports)
      .innerJoin(elderInfo, eq(accidentReports.elderInfoId, elderInfo.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset)

    // 获取总数
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(accidentReports)
      .innerJoin(elderInfo, eq(accidentReports.elderInfoId, elderInfo.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 添加标签
    const reportsWithLabels = reports.map(report => ({
      ...report,
      accidentTypeLabel: getAccidentTypeLabel(report.accidentType),
      severityLabel: getSeverityLabel(report.severity),
      statusLabel: getAccidentStatusLabel(report.status),
      genderLabel: report.elderGender === 1 ? '男' : '女',
    }))

    return NextResponse.json({
      success: true,
      data: {
        reports: reportsWithLabels,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error('Error fetching accident reports:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 创建事故报告
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = accidentReportSchema.parse(body)

    // 生成报告编号
    const today = new Date()
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '')
    const randomSuffix = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0')
    const reportNumber = `AR${dateStr}${randomSuffix}`

    // 创建事故报告
    const newReport = await db
      .insert(accidentReports)
      .values({
        reportNumber,
        ...validatedData,
        reportedBy: session.user.id,
        reportedTime: new Date(),
      })
      .returning()

    return NextResponse.json({
      success: true,
      data: newReport[0],
      message: '事故报告创建成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating accident report:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 辅助函数
function getAccidentTypeLabel(type: number): string {
  const labels = {
    1: '跌倒',
    2: '烫伤',
    3: '走失',
    4: '误食',
    5: '其他意外',
  }
  return labels[type as keyof typeof labels] || '未知'
}

function getSeverityLabel(severity: number): string {
  const labels = {
    1: '轻微',
    2: '一般',
    3: '严重',
    4: '危重',
  }
  return labels[severity as keyof typeof labels] || '未知'
}

function getAccidentStatusLabel(status: number): string {
  const labels = {
    1: '待处理',
    2: '处理中',
    3: '已处理',
    4: '已结案',
  }
  return labels[status as keyof typeof labels] || '未知'
}
