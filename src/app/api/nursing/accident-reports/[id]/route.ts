import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { accidentReports, elderInfo } from '@/lib/db/schema'
import { eq } from 'drizzle-orm'
import { auth } from '@/lib/auth'
import { z } from 'zod'

// 处理事故报告验证模式
const handleReportSchema = z.object({
  action: z.enum(['handle', 'review', 'close']),
  handlingResult: z.string().optional(),
  reviewComments: z.string().optional(),
  preventiveMeasures: z.string().optional(),
  followUpActions: z.string().optional(),
})

// 获取事故报告详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const report = await db
      .select({
        id: accidentReports.id,
        reportNumber: accidentReports.reportNumber,
        elderInfoId: accidentReports.elderInfoId,
        elderName: elderInfo.name,
        elderAge: elderInfo.age,
        elderGender: elderInfo.gender,
        elderRoomNumber: elderInfo.roomNumber,
        elderBedNumber: elderInfo.bedNumber,
        accidentDate: accidentReports.accidentDate,
        accidentTime: accidentReports.accidentTime,
        location: accidentReports.location,
        accidentType: accidentReports.accidentType,
        severity: accidentReports.severity,
        description: accidentReports.description,
        cause: accidentReports.cause,
        circumstances: accidentReports.circumstances,
        discoveredBy: accidentReports.discoveredBy,
        discoveredTime: accidentReports.discoveredTime,
        reportedBy: accidentReports.reportedBy,
        reportedTime: accidentReports.reportedTime,
        injuryDescription: accidentReports.injuryDescription,
        immediateAction: accidentReports.immediateAction,
        medicalTreatment: accidentReports.medicalTreatment,
        hospitalTransfer: accidentReports.hospitalTransfer,
        hospitalName: accidentReports.hospitalName,
        familyNotified: accidentReports.familyNotified,
        familyNotifyTime: accidentReports.familyNotifyTime,
        familyResponse: accidentReports.familyResponse,
        responsibility: accidentReports.responsibility,
        preventiveMeasures: accidentReports.preventiveMeasures,
        followUpActions: accidentReports.followUpActions,
        status: accidentReports.status,
        handledBy: accidentReports.handledBy,
        handledDate: accidentReports.handledDate,
        handlingResult: accidentReports.handlingResult,
        reviewerId: accidentReports.reviewerId,
        reviewDate: accidentReports.reviewDate,
        reviewComments: accidentReports.reviewComments,
        notes: accidentReports.notes,
        attachments: accidentReports.attachments,
        createdAt: accidentReports.createdAt,
        updatedAt: accidentReports.updatedAt,
      })
      .from(accidentReports)
      .innerJoin(elderInfo, eq(accidentReports.elderInfoId, elderInfo.id))
      .where(eq(accidentReports.id, params.id))

    if (!report.length) {
      return NextResponse.json(
        { success: false, error: 'Accident report not found' },
        { status: 404 }
      )
    }

    const reportData = {
      ...report[0],
      accidentTypeLabel: getAccidentTypeLabel(report[0].accidentType),
      severityLabel: getSeverityLabel(report[0].severity),
      statusLabel: getAccidentStatusLabel(report[0].status),
      genderLabel: report[0].elderGender === 1 ? '男' : '女',
    }

    return NextResponse.json({
      success: true,
      data: reportData,
    })
  } catch (error) {
    console.error('Error fetching accident report:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 更新事故报告
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = handleReportSchema.parse(body)

    let updateData: any = {
      updatedAt: new Date(),
    }

    if (validatedData.action === 'handle') {
      // 开始处理事故
      updateData = {
        ...updateData,
        status: 2, // 处理中
        handledBy: session.user.id,
        handledDate: new Date().toISOString().split('T')[0],
        handlingResult: validatedData.handlingResult,
      }
    } else if (validatedData.action === 'review') {
      // 审核事故报告
      updateData = {
        ...updateData,
        status: 3, // 已处理
        reviewerId: session.user.id,
        reviewDate: new Date().toISOString().split('T')[0],
        reviewComments: validatedData.reviewComments,
        preventiveMeasures: validatedData.preventiveMeasures,
        followUpActions: validatedData.followUpActions,
      }
    } else if (validatedData.action === 'close') {
      // 结案
      updateData = {
        ...updateData,
        status: 4, // 已结案
      }
    }

    const updatedReport = await db
      .update(accidentReports)
      .set(updateData)
      .where(eq(accidentReports.id, params.id))
      .returning()

    const actionMessages = {
      handle: '事故处理已开始',
      review: '事故审核完成',
      close: '事故已结案',
    }

    return NextResponse.json({
      success: true,
      data: updatedReport[0],
      message: actionMessages[validatedData.action],
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating accident report:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 删除事故报告
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // 检查报告状态，只有待处理状态才能删除
    const report = await db
      .select({ status: accidentReports.status })
      .from(accidentReports)
      .where(eq(accidentReports.id, params.id))

    if (!report.length) {
      return NextResponse.json(
        { success: false, error: 'Accident report not found' },
        { status: 404 }
      )
    }

    if (report[0].status !== 1) {
      return NextResponse.json(
        { success: false, error: 'Only pending reports can be deleted' },
        { status: 400 }
      )
    }

    await db.delete(accidentReports).where(eq(accidentReports.id, params.id))

    return NextResponse.json({
      success: true,
      message: '事故报告已删除',
    })
  } catch (error) {
    console.error('Error deleting accident report:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 辅助函数
function getAccidentTypeLabel(type: number): string {
  const labels = {
    1: '跌倒',
    2: '烫伤',
    3: '走失',
    4: '误食',
    5: '其他意外',
  }
  return labels[type as keyof typeof labels] || '未知'
}

function getSeverityLabel(severity: number): string {
  const labels = {
    1: '轻微',
    2: '一般',
    3: '严重',
    4: '危重',
  }
  return labels[severity as keyof typeof labels] || '未知'
}

function getAccidentStatusLabel(status: number): string {
  const labels = {
    1: '待处理',
    2: '处理中',
    3: '已处理',
    4: '已结案',
  }
  return labels[status as keyof typeof labels] || '未知'
}
