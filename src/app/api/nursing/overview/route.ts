import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { elderInfo, rooms, leaveApplications, accidentReports, visitRegistrations } from '@/lib/db/schema'
import { eq, and, gte, lte, count, sql } from 'drizzle-orm'
import { auth } from '@/lib/auth'

// 获取在住总览数据
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // 获取在住老人统计
    const elderStats = await db
      .select({
        total: count(),
        inResidence: count(sql`CASE WHEN ${elderInfo.status} = 1 THEN 1 END`),
        onLeave: count(sql`CASE WHEN ${elderInfo.status} = 2 THEN 1 END`),
        discharged: count(sql`CASE WHEN ${elderInfo.status} = 3 THEN 1 END`),
      })
      .from(elderInfo)

    // 获取护理等级分布
    const careLevelStats = await db
      .select({
        careLevel: elderInfo.careLevel,
        count: count(),
      })
      .from(elderInfo)
      .where(eq(elderInfo.status, 1)) // 只统计在住老人
      .groupBy(elderInfo.careLevel)

    // 获取房间使用情况
    const roomStats = await db
      .select({
        total: count(),
        occupied: count(sql`CASE WHEN ${rooms.currentOccupancy} > 0 THEN 1 END`),
        available: count(sql`CASE WHEN ${rooms.currentOccupancy} = 0 THEN 1 END`),
        totalCapacity: sql<number>`SUM(${rooms.capacity})`,
        currentOccupancy: sql<number>`SUM(${rooms.currentOccupancy})`,
      })
      .from(rooms)
      .where(eq(rooms.status, 1)) // 只统计可用房间

    // 获取最近的请假申请
    let recentLeaveQuery = db
      .select({
        id: leaveApplications.id,
        applicationNumber: leaveApplications.applicationNumber,
        elderName: elderInfo.name,
        leaveType: leaveApplications.leaveType,
        startDate: leaveApplications.startDate,
        endDate: leaveApplications.endDate,
        status: leaveApplications.status,
        createdAt: leaveApplications.createdAt,
      })
      .from(leaveApplications)
      .innerJoin(elderInfo, eq(leaveApplications.elderInfoId, elderInfo.id))
      .orderBy(sql`${leaveApplications.createdAt} DESC`)
      .limit(10)

    if (startDate && endDate) {
      recentLeaveQuery = recentLeaveQuery.where(
        and(
          gte(leaveApplications.createdAt, new Date(startDate)),
          lte(leaveApplications.createdAt, new Date(endDate))
        )
      )
    }

    const recentLeaveApplications = await recentLeaveQuery

    // 获取最近的事故报告
    let recentAccidentQuery = db
      .select({
        id: accidentReports.id,
        reportNumber: accidentReports.reportNumber,
        elderName: elderInfo.name,
        accidentType: accidentReports.accidentType,
        severity: accidentReports.severity,
        accidentDate: accidentReports.accidentDate,
        status: accidentReports.status,
        createdAt: accidentReports.createdAt,
      })
      .from(accidentReports)
      .innerJoin(elderInfo, eq(accidentReports.elderInfoId, elderInfo.id))
      .orderBy(sql`${accidentReports.createdAt} DESC`)
      .limit(10)

    if (startDate && endDate) {
      recentAccidentQuery = recentAccidentQuery.where(
        and(
          gte(accidentReports.createdAt, new Date(startDate)),
          lte(accidentReports.createdAt, new Date(endDate))
        )
      )
    }

    const recentAccidentReports = await recentAccidentQuery

    // 获取今日探访安排
    const today = new Date()
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

    const todayVisits = await db
      .select({
        id: visitRegistrations.id,
        registrationNumber: visitRegistrations.registrationNumber,
        elderName: elderInfo.name,
        visitorName: visitRegistrations.visitorName,
        visitType: visitRegistrations.visitType,
        plannedStartTime: visitRegistrations.plannedStartTime,
        plannedEndTime: visitRegistrations.plannedEndTime,
        status: visitRegistrations.status,
      })
      .from(visitRegistrations)
      .innerJoin(elderInfo, eq(visitRegistrations.elderInfoId, elderInfo.id))
      .where(
        and(
          gte(visitRegistrations.visitDate, todayStart),
          lte(visitRegistrations.visitDate, todayEnd)
        )
      )
      .orderBy(visitRegistrations.plannedStartTime)

    // 计算床位使用率
    const occupancyRate = roomStats[0]?.totalCapacity 
      ? Math.round((roomStats[0].currentOccupancy / roomStats[0].totalCapacity) * 100)
      : 0

    // 构建响应数据
    const overviewData = {
      elderStats: elderStats[0] || {
        total: 0,
        inResidence: 0,
        onLeave: 0,
        discharged: 0,
      },
      careLevelStats: careLevelStats.map(stat => ({
        careLevel: stat.careLevel,
        count: stat.count,
        label: getCareLevelLabel(stat.careLevel),
      })),
      roomStats: {
        ...roomStats[0],
        occupancyRate,
      },
      recentLeaveApplications: recentLeaveApplications.map(app => ({
        ...app,
        leaveTypeLabel: getLeaveTypeLabel(app.leaveType),
        statusLabel: getLeaveStatusLabel(app.status),
      })),
      recentAccidentReports: recentAccidentReports.map(report => ({
        ...report,
        accidentTypeLabel: getAccidentTypeLabel(report.accidentType),
        severityLabel: getSeverityLabel(report.severity),
        statusLabel: getAccidentStatusLabel(report.status),
      })),
      todayVisits: todayVisits.map(visit => ({
        ...visit,
        visitTypeLabel: getVisitTypeLabel(visit.visitType),
        statusLabel: getVisitStatusLabel(visit.status),
      })),
    }

    return NextResponse.json({
      success: true,
      data: overviewData,
    })
  } catch (error) {
    console.error('Error fetching nursing overview:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 辅助函数：获取护理等级标签
function getCareLevelLabel(level: number): string {
  const labels = {
    1: '自理',
    2: '半自理',
    3: '不能自理',
    4: '特护',
  }
  return labels[level as keyof typeof labels] || '未知'
}

// 辅助函数：获取请假类型标签
function getLeaveTypeLabel(type: number): string {
  const labels = {
    1: '外出',
    2: '回家',
    3: '就医',
    4: '其他',
  }
  return labels[type as keyof typeof labels] || '未知'
}

// 辅助函数：获取请假状态标签
function getLeaveStatusLabel(status: number): string {
  const labels = {
    1: '待审核',
    2: '已批准',
    3: '已拒绝',
    4: '已销假',
    5: '逾期未归',
  }
  return labels[status as keyof typeof labels] || '未知'
}

// 辅助函数：获取事故类型标签
function getAccidentTypeLabel(type: number): string {
  const labels = {
    1: '跌倒',
    2: '烫伤',
    3: '走失',
    4: '误食',
    5: '其他意外',
  }
  return labels[type as keyof typeof labels] || '未知'
}

// 辅助函数：获取严重程度标签
function getSeverityLabel(severity: number): string {
  const labels = {
    1: '轻微',
    2: '一般',
    3: '严重',
    4: '危重',
  }
  return labels[severity as keyof typeof labels] || '未知'
}

// 辅助函数：获取事故状态标签
function getAccidentStatusLabel(status: number): string {
  const labels = {
    1: '待处理',
    2: '处理中',
    3: '已处理',
    4: '已结案',
  }
  return labels[status as keyof typeof labels] || '未知'
}

// 辅助函数：获取探访类型标签
function getVisitTypeLabel(type: number): string {
  const labels = {
    1: '家属探访',
    2: '朋友探访',
    3: '医疗探访',
    4: '法律事务',
    5: '其他',
  }
  return labels[type as keyof typeof labels] || '未知'
}

// 辅助函数：获取探访状态标签
function getVisitStatusLabel(status: number): string {
  const labels = {
    1: '预约',
    2: '已到达',
    3: '探访中',
    4: '已结束',
    5: '已取消',
    6: '逾期未到',
  }
  return labels[status as keyof typeof labels] || '未知'
}
