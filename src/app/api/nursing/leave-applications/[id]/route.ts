import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { leaveApplications, elderInfo } from '@/lib/db/schema'
import { eq } from 'drizzle-orm'
import { auth } from '@/lib/auth'
import { z } from 'zod'

// 审核请假申请验证模式
const reviewSchema = z.object({
  action: z.enum(['approve', 'reject']),
  reviewComments: z.string().optional(),
})

// 销假验证模式
const returnSchema = z.object({
  actualReturnDate: z.string(),
  returnCondition: z.string().optional(),
  returnNotes: z.string().optional(),
})

// 获取请假申请详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const application = await db
      .select({
        id: leaveApplications.id,
        applicationNumber: leaveApplications.applicationNumber,
        elderInfoId: leaveApplications.elderInfoId,
        elderName: elderInfo.name,
        elderAge: elderInfo.age,
        elderGender: elderInfo.gender,
        elderRoomNumber: elderInfo.roomNumber,
        elderBedNumber: elderInfo.bedNumber,
        applicantName: leaveApplications.applicantName,
        applicantPhone: leaveApplications.applicantPhone,
        applicantRelation: leaveApplications.applicantRelation,
        applicantIdCard: leaveApplications.applicantIdCard,
        leaveType: leaveApplications.leaveType,
        leaveReason: leaveApplications.leaveReason,
        startDate: leaveApplications.startDate,
        endDate: leaveApplications.endDate,
        expectedReturnDate: leaveApplications.expectedReturnDate,
        actualReturnDate: leaveApplications.actualReturnDate,
        accompaniedBy: leaveApplications.accompaniedBy,
        accompaniedPhone: leaveApplications.accompaniedPhone,
        accompaniedRelation: leaveApplications.accompaniedRelation,
        destination: leaveApplications.destination,
        destinationAddress: leaveApplications.destinationAddress,
        destinationContact: leaveApplications.destinationContact,
        healthCondition: leaveApplications.healthCondition,
        medicationNeeds: leaveApplications.medicationNeeds,
        specialCare: leaveApplications.specialCare,
        status: leaveApplications.status,
        reviewerId: leaveApplications.reviewerId,
        reviewDate: leaveApplications.reviewDate,
        reviewComments: leaveApplications.reviewComments,
        returnProcessedBy: leaveApplications.returnProcessedBy,
        returnProcessedDate: leaveApplications.returnProcessedDate,
        returnCondition: leaveApplications.returnCondition,
        returnNotes: leaveApplications.returnNotes,
        notes: leaveApplications.notes,
        attachments: leaveApplications.attachments,
        createdBy: leaveApplications.createdBy,
        createdAt: leaveApplications.createdAt,
        updatedAt: leaveApplications.updatedAt,
      })
      .from(leaveApplications)
      .innerJoin(elderInfo, eq(leaveApplications.elderInfoId, elderInfo.id))
      .where(eq(leaveApplications.id, params.id))

    if (!application.length) {
      return NextResponse.json(
        { success: false, error: 'Leave application not found' },
        { status: 404 }
      )
    }

    const applicationData = {
      ...application[0],
      leaveTypeLabel: getLeaveTypeLabel(application[0].leaveType),
      statusLabel: getLeaveStatusLabel(application[0].status),
      genderLabel: application[0].elderGender === 1 ? '男' : '女',
    }

    return NextResponse.json({
      success: true,
      data: applicationData,
    })
  } catch (error) {
    console.error('Error fetching leave application:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 更新请假申请
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action } = body

    if (action === 'review') {
      // 审核请假申请
      const validatedData = reviewSchema.parse(body)
      
      const newStatus = validatedData.action === 'approve' ? 2 : 3
      
      const updatedApplication = await db
        .update(leaveApplications)
        .set({
          status: newStatus,
          reviewerId: session.user.id,
          reviewDate: new Date().toISOString().split('T')[0],
          reviewComments: validatedData.reviewComments,
          updatedAt: new Date(),
        })
        .where(eq(leaveApplications.id, params.id))
        .returning()

      return NextResponse.json({
        success: true,
        data: updatedApplication[0],
        message: validatedData.action === 'approve' ? '请假申请已批准' : '请假申请已拒绝',
      })
    } else if (action === 'return') {
      // 销假处理
      const validatedData = returnSchema.parse(body)
      
      const updatedApplication = await db
        .update(leaveApplications)
        .set({
          status: 4, // 已销假
          actualReturnDate: validatedData.actualReturnDate,
          returnProcessedBy: session.user.id,
          returnProcessedDate: new Date().toISOString().split('T')[0],
          returnCondition: validatedData.returnCondition,
          returnNotes: validatedData.returnNotes,
          updatedAt: new Date(),
        })
        .where(eq(leaveApplications.id, params.id))
        .returning()

      return NextResponse.json({
        success: true,
        data: updatedApplication[0],
        message: '销假处理完成',
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action' },
        { status: 400 }
      )
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating leave application:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 删除请假申请
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // 检查申请状态，只有待审核状态才能删除
    const application = await db
      .select({ status: leaveApplications.status })
      .from(leaveApplications)
      .where(eq(leaveApplications.id, params.id))

    if (!application.length) {
      return NextResponse.json(
        { success: false, error: 'Leave application not found' },
        { status: 404 }
      )
    }

    if (application[0].status !== 1) {
      return NextResponse.json(
        { success: false, error: 'Only pending applications can be deleted' },
        { status: 400 }
      )
    }

    await db
      .delete(leaveApplications)
      .where(eq(leaveApplications.id, params.id))

    return NextResponse.json({
      success: true,
      message: '请假申请已删除',
    })
  } catch (error) {
    console.error('Error deleting leave application:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 辅助函数
function getLeaveTypeLabel(type: number): string {
  const labels = {
    1: '外出',
    2: '回家',
    3: '就医',
    4: '其他',
  }
  return labels[type as keyof typeof labels] || '未知'
}

function getLeaveStatusLabel(status: number): string {
  const labels = {
    1: '待审核',
    2: '已批准',
    3: '已拒绝',
    4: '已销假',
    5: '逾期未归',
  }
  return labels[status as keyof typeof labels] || '未知'
}
