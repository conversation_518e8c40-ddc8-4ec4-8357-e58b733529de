import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { leaveApplications, elderInfo } from '@/lib/db/schema'
import { eq, and, gte, lte, ilike, desc, asc, sql } from 'drizzle-orm'
import { auth } from '@/lib/auth'
import { z } from 'zod'

// 请假申请验证模式
const leaveApplicationSchema = z.object({
  elderInfoId: z.string().uuid(),
  applicantName: z.string().min(1, '申请人姓名不能为空'),
  applicantPhone: z.string().min(1, '申请人电话不能为空'),
  applicantRelation: z.string().min(1, '与老人关系不能为空'),
  applicantIdCard: z.string().optional(),
  leaveType: z.number().int().min(1).max(4),
  leaveReason: z.string().min(1, '请假原因不能为空'),
  startDate: z.string(),
  endDate: z.string(),
  expectedReturnDate: z.string(),
  accompaniedBy: z.string().optional(),
  accompaniedPhone: z.string().optional(),
  accompaniedRelation: z.string().optional(),
  destination: z.string().optional(),
  destinationAddress: z.string().optional(),
  destinationContact: z.string().optional(),
  healthCondition: z.string().optional(),
  medicationNeeds: z.string().optional(),
  specialCare: z.string().optional(),
  notes: z.string().optional(),
  attachments: z.string().optional(),
})

// 获取请假申请列表
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status')
    const leaveType = searchParams.get('leaveType')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const offset = (page - 1) * limit

    // 构建查询条件
    let whereConditions = []

    if (search) {
      whereConditions.push(ilike(elderInfo.name, `%${search}%`))
    }

    if (status) {
      whereConditions.push(eq(leaveApplications.status, parseInt(status)))
    }

    if (leaveType) {
      whereConditions.push(eq(leaveApplications.leaveType, parseInt(leaveType)))
    }

    if (startDate && endDate) {
      whereConditions.push(
        and(
          gte(leaveApplications.startDate, startDate),
          lte(leaveApplications.endDate, endDate)
        )
      )
    }

    // 构建排序
    const orderBy =
      sortOrder === 'desc'
        ? desc(leaveApplications[sortBy as keyof typeof leaveApplications])
        : asc(leaveApplications[sortBy as keyof typeof leaveApplications])

    // 查询数据
    const applications = await db
      .select({
        id: leaveApplications.id,
        applicationNumber: leaveApplications.applicationNumber,
        elderName: elderInfo.name,
        elderAge: elderInfo.age,
        elderGender: elderInfo.gender,
        applicantName: leaveApplications.applicantName,
        applicantPhone: leaveApplications.applicantPhone,
        applicantRelation: leaveApplications.applicantRelation,
        leaveType: leaveApplications.leaveType,
        leaveReason: leaveApplications.leaveReason,
        startDate: leaveApplications.startDate,
        endDate: leaveApplications.endDate,
        expectedReturnDate: leaveApplications.expectedReturnDate,
        actualReturnDate: leaveApplications.actualReturnDate,
        status: leaveApplications.status,
        reviewComments: leaveApplications.reviewComments,
        createdAt: leaveApplications.createdAt,
        updatedAt: leaveApplications.updatedAt,
      })
      .from(leaveApplications)
      .innerJoin(elderInfo, eq(leaveApplications.elderInfoId, elderInfo.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset)

    // 获取总数
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(leaveApplications)
      .innerJoin(elderInfo, eq(leaveApplications.elderInfoId, elderInfo.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 添加标签
    const applicationsWithLabels = applications.map(app => ({
      ...app,
      leaveTypeLabel: getLeaveTypeLabel(app.leaveType),
      statusLabel: getLeaveStatusLabel(app.status),
      genderLabel: app.elderGender === 1 ? '男' : '女',
    }))

    return NextResponse.json({
      success: true,
      data: {
        applications: applicationsWithLabels,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error('Error fetching leave applications:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 创建请假申请
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = leaveApplicationSchema.parse(body)

    // 生成申请编号
    const today = new Date()
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '')
    const randomSuffix = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0')
    const applicationNumber = `LA${dateStr}${randomSuffix}`

    // 创建请假申请
    const newApplication = await db
      .insert(leaveApplications)
      .values({
        applicationNumber,
        ...validatedData,
        createdBy: session.user.id,
      })
      .returning()

    return NextResponse.json({
      success: true,
      data: newApplication[0],
      message: '请假申请创建成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating leave application:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 辅助函数
function getLeaveTypeLabel(type: number): string {
  const labels = {
    1: '外出',
    2: '回家',
    3: '就医',
    4: '其他',
  }
  return labels[type as keyof typeof labels] || '未知'
}

function getLeaveStatusLabel(status: number): string {
  const labels = {
    1: '待审核',
    2: '已批准',
    3: '已拒绝',
    4: '已销假',
    5: '逾期未归',
  }
  return labels[status as keyof typeof labels] || '未知'
}
