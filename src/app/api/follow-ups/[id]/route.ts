import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { consultationFollowUps, consultations, betterAuthUsers } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq } from 'drizzle-orm'

// 跟进记录更新验证 schema
const updateFollowUpSchema = z.object({
  content: z.string().min(1, '跟进内容不能为空').max(2000, '跟进内容不能超过2000字').optional(),
  nextFollowUpDate: z.string().optional().nullable(),
})

/**
 * GET /api/follow-ups/[id]
 * 获取单个跟进记录详情
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户身份
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const { id } = params

    // 验证 ID 格式
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { success: false, error: '跟进记录ID格式不正确' },
        { status: 400 }
      )
    }

    // 查询跟进记录详情
    const [followUp] = await db
      .select({
        id: consultationFollowUps.id,
        consultationId: consultationFollowUps.consultationId,
        content: consultationFollowUps.content,
        nextFollowUpDate: consultationFollowUps.nextFollowUpDate,
        createdAt: consultationFollowUps.createdAt,
        user: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
        consultation: {
          id: consultations.id,
          consultantName: consultations.consultantName,
          consultantPhone: consultations.consultantPhone,
          purpose: consultations.purpose,
          status: consultations.status,
          mediaChannel: consultations.mediaChannel,
          notes: consultations.notes,
        },
      })
      .from(consultationFollowUps)
      .leftJoin(betterAuthUsers, eq(consultationFollowUps.userId, betterAuthUsers.id))
      .leftJoin(consultations, eq(consultationFollowUps.consultationId, consultations.id))
      .where(eq(consultationFollowUps.id, id))

    if (!followUp) {
      return NextResponse.json(
        { success: false, error: '跟进记录不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: followUp,
    })
  } catch (error) {
    console.error('获取跟进记录详情失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/follow-ups/[id]
 * 更新跟进记录
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户身份
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const { id } = params

    // 验证 ID 格式
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { success: false, error: '跟进记录ID格式不正确' },
        { status: 400 }
      )
    }

    // 解析请求体
    const body = await request.json()
    const updateData = updateFollowUpSchema.parse(body)

    // 检查跟进记录是否存在
    const [existingFollowUp] = await db
      .select({
        id: consultationFollowUps.id,
        userId: consultationFollowUps.userId,
        consultationId: consultationFollowUps.consultationId,
      })
      .from(consultationFollowUps)
      .where(eq(consultationFollowUps.id, id))

    if (!existingFollowUp) {
      return NextResponse.json(
        { success: false, error: '跟进记录不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有创建者可以修改
    if (existingFollowUp.userId !== user.id) {
      return NextResponse.json(
        { success: false, error: '无权限修改此跟进记录' },
        { status: 403 }
      )
    }

    // 准备更新数据
    const updateValues: any = {}
    
    if (updateData.content !== undefined) {
      updateValues.content = updateData.content
    }
    
    if (updateData.nextFollowUpDate !== undefined) {
      updateValues.nextFollowUpDate = updateData.nextFollowUpDate 
        ? new Date(updateData.nextFollowUpDate) 
        : null
    }

    // 如果没有要更新的字段
    if (Object.keys(updateValues).length === 0) {
      return NextResponse.json(
        { success: false, error: '没有要更新的数据' },
        { status: 400 }
      )
    }

    // 更新跟进记录
    await db
      .update(consultationFollowUps)
      .set(updateValues)
      .where(eq(consultationFollowUps.id, id))

    // 如果更新了下次跟进日期，同时更新咨询记录
    if (updateData.nextFollowUpDate !== undefined) {
      await db
        .update(consultations)
        .set({
          followUpDate: updateData.nextFollowUpDate 
            ? new Date(updateData.nextFollowUpDate) 
            : null,
          updatedAt: new Date(),
        })
        .where(eq(consultations.id, existingFollowUp.consultationId))
    }

    // 查询更新后的跟进记录
    const [updatedFollowUp] = await db
      .select({
        id: consultationFollowUps.id,
        consultationId: consultationFollowUps.consultationId,
        content: consultationFollowUps.content,
        nextFollowUpDate: consultationFollowUps.nextFollowUpDate,
        createdAt: consultationFollowUps.createdAt,
        user: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
        consultation: {
          id: consultations.id,
          consultantName: consultations.consultantName,
          consultantPhone: consultations.consultantPhone,
          purpose: consultations.purpose,
          status: consultations.status,
        },
      })
      .from(consultationFollowUps)
      .leftJoin(betterAuthUsers, eq(consultationFollowUps.userId, betterAuthUsers.id))
      .leftJoin(consultations, eq(consultationFollowUps.consultationId, consultations.id))
      .where(eq(consultationFollowUps.id, id))

    return NextResponse.json({
      success: true,
      data: updatedFollowUp,
      message: '跟进记录更新成功',
    })
  } catch (error) {
    console.error('更新跟进记录失败:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: '请求数据格式错误', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/follow-ups/[id]
 * 删除跟进记录
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户身份
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const { id } = params

    // 验证 ID 格式
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { success: false, error: '跟进记录ID格式不正确' },
        { status: 400 }
      )
    }

    // 检查跟进记录是否存在
    const [existingFollowUp] = await db
      .select({
        id: consultationFollowUps.id,
        userId: consultationFollowUps.userId,
      })
      .from(consultationFollowUps)
      .where(eq(consultationFollowUps.id, id))

    if (!existingFollowUp) {
      return NextResponse.json(
        { success: false, error: '跟进记录不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有创建者可以删除
    if (existingFollowUp.userId !== user.id) {
      return NextResponse.json(
        { success: false, error: '无权限删除此跟进记录' },
        { status: 403 }
      )
    }

    // 删除跟进记录
    await db
      .delete(consultationFollowUps)
      .where(eq(consultationFollowUps.id, id))

    return NextResponse.json({
      success: true,
      message: '跟进记录删除成功',
    })
  } catch (error) {
    console.error('删除跟进记录失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
