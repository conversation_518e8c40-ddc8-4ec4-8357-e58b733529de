import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { consultationFollowUps, consultations, betterAuthUsers } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, desc, and, like, or, count } from 'drizzle-orm'

// 跟进记录创建验证 schema
const createFollowUpSchema = z.object({
  consultationId: z.string().uuid('咨询记录ID格式不正确'),
  content: z.string().min(1, '跟进内容不能为空').max(2000, '跟进内容不能超过2000字'),
  nextFollowUpDate: z.string().optional().nullable(),
})

// 跟进记录查询参数验证 schema
const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('20'),
  consultationId: z.string().uuid().optional(),
  search: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

/**
 * GET /api/follow-ups
 * 获取跟进记录列表
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    const {
      page,
      limit,
      consultationId,
      search,
      startDate,
      endDate,
    } = querySchema.parse(queryParams)

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // 构建查询条件
    const conditions = []
    
    if (consultationId) {
      conditions.push(eq(consultationFollowUps.consultationId, consultationId))
    }

    if (search) {
      conditions.push(
        like(consultationFollowUps.content, `%${search}%`)
      )
    }

    if (startDate) {
      conditions.push(
        eq(consultationFollowUps.createdAt, new Date(startDate))
      )
    }

    if (endDate) {
      conditions.push(
        eq(consultationFollowUps.createdAt, new Date(endDate))
      )
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined

    // 查询跟进记录列表
    const followUps = await db
      .select({
        id: consultationFollowUps.id,
        consultationId: consultationFollowUps.consultationId,
        content: consultationFollowUps.content,
        nextFollowUpDate: consultationFollowUps.nextFollowUpDate,
        createdAt: consultationFollowUps.createdAt,
        user: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
        consultation: {
          id: consultations.id,
          consultantName: consultations.consultantName,
          consultantPhone: consultations.consultantPhone,
          purpose: consultations.purpose,
          status: consultations.status,
        },
      })
      .from(consultationFollowUps)
      .leftJoin(betterAuthUsers, eq(consultationFollowUps.userId, betterAuthUsers.id))
      .leftJoin(consultations, eq(consultationFollowUps.consultationId, consultations.id))
      .where(whereClause)
      .orderBy(desc(consultationFollowUps.createdAt))
      .limit(limitNum)
      .offset(offset)

    // 查询总数
    const [{ count: total }] = await db
      .select({ count: count() })
      .from(consultationFollowUps)
      .where(whereClause)

    return NextResponse.json({
      success: true,
      data: {
        items: followUps,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      },
    })
  } catch (error) {
    console.error('获取跟进记录列表失败:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: '请求参数格式错误', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/follow-ups
 * 创建跟进记录
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    // 解析请求体
    const body = await request.json()
    const { consultationId, content, nextFollowUpDate } = createFollowUpSchema.parse(body)

    // 验证咨询记录是否存在
    const consultation = await db
      .select({ id: consultations.id })
      .from(consultations)
      .where(eq(consultations.id, consultationId))
      .limit(1)

    if (consultation.length === 0) {
      return NextResponse.json(
        { success: false, error: '咨询记录不存在' },
        { status: 404 }
      )
    }

    // 创建跟进记录
    const [newFollowUp] = await db
      .insert(consultationFollowUps)
      .values({
        consultationId,
        userId: user.id,
        content,
        nextFollowUpDate: nextFollowUpDate ? new Date(nextFollowUpDate) : null,
      })
      .returning()

    // 更新咨询记录的跟进日期和状态
    await db
      .update(consultations)
      .set({
        followUpDate: nextFollowUpDate ? new Date(nextFollowUpDate) : null,
        status: 2, // 已跟进
        updatedAt: new Date(),
      })
      .where(eq(consultations.id, consultationId))

    // 查询完整的跟进记录信息
    const [followUpWithDetails] = await db
      .select({
        id: consultationFollowUps.id,
        consultationId: consultationFollowUps.consultationId,
        content: consultationFollowUps.content,
        nextFollowUpDate: consultationFollowUps.nextFollowUpDate,
        createdAt: consultationFollowUps.createdAt,
        user: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
        consultation: {
          id: consultations.id,
          consultantName: consultations.consultantName,
          consultantPhone: consultations.consultantPhone,
          purpose: consultations.purpose,
          status: consultations.status,
        },
      })
      .from(consultationFollowUps)
      .leftJoin(betterAuthUsers, eq(consultationFollowUps.userId, betterAuthUsers.id))
      .leftJoin(consultations, eq(consultationFollowUps.consultationId, consultations.id))
      .where(eq(consultationFollowUps.id, newFollowUp.id))

    return NextResponse.json(
      {
        success: true,
        data: followUpWithDetails,
        message: '跟进记录创建成功',
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('创建跟进记录失败:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: '请求数据格式错误', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
