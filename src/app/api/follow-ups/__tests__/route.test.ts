import { NextRequest } from 'next/server'
import { vi } from 'vitest'
import { GET, POST } from '../route'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    leftJoin: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    offset: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    values: vi.fn().mockReturnThis(),
    returning: vi.fn(),
    update: vi.fn().mockReturnThis(),
    set: vi.fn().mockReturnThis(),
  },
}))

vi.mock('@/lib/db/schema', () => ({
  consultationFollowUps: {
    id: 'id',
    consultationId: 'consultationId',
    userId: 'userId',
    content: 'content',
    nextFollowUpDate: 'nextFollowUpDate',
    createdAt: 'createdAt',
  },
  consultations: {
    id: 'id',
    consultantName: 'consultantName',
    consultantPhone: 'consultantPhone',
    purpose: 'purpose',
    status: 'status',
  },
  betterAuthUsers: {
    id: 'id',
    name: 'name',
    email: 'email',
  },
}))

vi.mock('@/lib/auth-utils', () => ({
  getCurrentUser: vi.fn(),
}))

vi.mock('drizzle-orm', () => ({
  eq: vi.fn(),
  desc: vi.fn(),
  and: vi.fn(),
  like: vi.fn(),
  or: vi.fn(),
  count: vi.fn(),
}))

const { getCurrentUser } = await import('@/lib/auth-utils')
const { db } = await import('@/lib/db')

describe('/api/follow-ups', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('GET', () => {
    it('should return 401 when user is not authenticated', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue(null)

      const request = new NextRequest('http://localhost/api/follow-ups')
      const response = await GET(request)

      expect(response.status).toBe(401)
      const data = await response.json()
      expect(data.success).toBe(false)
      expect(data.error).toBe('未授权访问')
    })

    it('should return follow-ups list when authenticated', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue({
        id: 'user-1',
        name: '张三',
        email: '<EMAIL>',
      })

      const mockFollowUps = [
        {
          id: '1',
          consultationId: 'consultation-1',
          content: 'Test follow-up',
          nextFollowUpDate: '2024-01-20',
          createdAt: new Date('2024-01-15T10:30:00Z'),
          user: { id: 'user-1', name: '张三', email: '<EMAIL>' },
          consultation: { id: 'consultation-1', consultantName: '李四', consultantPhone: '13800138000', purpose: '咨询', status: 1 },
        },
      ]

      const mockCount = [{ count: 1 }]

      vi.mocked(db.select).mockReturnValue({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({
                orderBy: vi.fn().mockReturnValue({
                  limit: vi.fn().mockReturnValue({
                    offset: vi.fn().mockResolvedValue(mockFollowUps),
                  }),
                }),
              }),
            }),
          }),
        }),
      } as any)

      // Mock count query
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockResolvedValue(mockCount),
        }),
      } as any)

      const request = new NextRequest('http://localhost/api/follow-ups?page=1&limit=10')
      const response = await GET(request)

      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.success).toBe(true)
      expect(data.data.items).toEqual(mockFollowUps)
      expect(data.data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
      })
    })

    it('should handle query parameters correctly', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue({
        id: 'user-1',
        name: '张三',
        email: '<EMAIL>',
      })

      vi.mocked(db.select).mockReturnValue({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({
                orderBy: vi.fn().mockReturnValue({
                  limit: vi.fn().mockReturnValue({
                    offset: vi.fn().mockResolvedValue([]),
                  }),
                }),
              }),
            }),
          }),
        }),
      } as any)

      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockResolvedValue([{ count: 0 }]),
        }),
      } as any)

      const request = new NextRequest('http://localhost/api/follow-ups?consultationId=consultation-1&search=test')
      const response = await GET(request)

      expect(response.status).toBe(200)
    })

    it('should handle invalid query parameters', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue({
        id: 'user-1',
        name: '张三',
        email: '<EMAIL>',
      })

      const request = new NextRequest('http://localhost/api/follow-ups?page=invalid')
      const response = await GET(request)

      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.success).toBe(false)
      expect(data.error).toBe('请求参数格式错误')
    })
  })

  describe('POST', () => {
    it('should return 401 when user is not authenticated', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue(null)

      const request = new NextRequest('http://localhost/api/follow-ups', {
        method: 'POST',
        body: JSON.stringify({
          consultationId: 'consultation-1',
          content: 'Test follow-up',
        }),
      })

      const response = await POST(request)

      expect(response.status).toBe(401)
      const data = await response.json()
      expect(data.success).toBe(false)
      expect(data.error).toBe('未授权访问')
    })

    it('should create follow-up when authenticated', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue({
        id: 'user-1',
        name: '张三',
        email: '<EMAIL>',
      })

      // Mock consultation exists check
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([{ id: 'consultation-1' }]),
          }),
        }),
      } as any)

      // Mock insert follow-up
      const mockNewFollowUp = {
        id: '1',
        consultationId: 'consultation-1',
        userId: 'user-1',
        content: 'Test follow-up',
        nextFollowUpDate: null,
      }

      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([mockNewFollowUp]),
        }),
      } as any)

      // Mock update consultation
      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockResolvedValue(undefined),
        }),
      } as any)

      // Mock select follow-up with details
      const mockFollowUpWithDetails = {
        id: '1',
        consultationId: 'consultation-1',
        content: 'Test follow-up',
        nextFollowUpDate: null,
        createdAt: new Date('2024-01-15T10:30:00Z'),
        user: { id: 'user-1', name: '张三', email: '<EMAIL>' },
        consultation: { id: 'consultation-1', consultantName: '李四', consultantPhone: '13800138000', purpose: '咨询', status: 2 },
      }

      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              where: vi.fn().mockResolvedValue([mockFollowUpWithDetails]),
            }),
          }),
        }),
      } as any)

      const request = new NextRequest('http://localhost/api/follow-ups', {
        method: 'POST',
        body: JSON.stringify({
          consultationId: 'consultation-1',
          content: 'Test follow-up',
        }),
      })

      const response = await POST(request)

      expect(response.status).toBe(201)
      const data = await response.json()
      expect(data.success).toBe(true)
      expect(data.data).toEqual(mockFollowUpWithDetails)
      expect(data.message).toBe('跟进记录创建成功')
    })

    it('should return 404 when consultation does not exist', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue({
        id: 'user-1',
        name: '张三',
        email: '<EMAIL>',
      })

      // Mock consultation not found
      vi.mocked(db.select).mockReturnValue({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([]),
          }),
        }),
      } as any)

      const request = new NextRequest('http://localhost/api/follow-ups', {
        method: 'POST',
        body: JSON.stringify({
          consultationId: 'non-existent',
          content: 'Test follow-up',
        }),
      })

      const response = await POST(request)

      expect(response.status).toBe(404)
      const data = await response.json()
      expect(data.success).toBe(false)
      expect(data.error).toBe('咨询记录不存在')
    })

    it('should validate request data', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue({
        id: 'user-1',
        name: '张三',
        email: '<EMAIL>',
      })

      const request = new NextRequest('http://localhost/api/follow-ups', {
        method: 'POST',
        body: JSON.stringify({
          consultationId: 'invalid-uuid',
          content: '',
        }),
      })

      const response = await POST(request)

      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.success).toBe(false)
      expect(data.error).toBe('请求数据格式错误')
    })
  })
})
