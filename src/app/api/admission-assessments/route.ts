import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  admissionAssessments, 
  admissionApplications,
  betterAuthUsers 
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and, or, desc, asc, count } from 'drizzle-orm'
import { format } from 'date-fns'

// 评估创建验证 schema
const createAssessmentSchema = z.object({
  applicationId: z.string().min(1, '请选择入住申请'),
  assessmentType: z.number().min(1).max(4, '请选择评估类型'),
  assessmentDate: z.string().min(1, '请选择评估日期'),
  
  // 健康评估
  physicalCondition: z.number().min(1).max(4).optional(),
  mentalCondition: z.number().min(1).max(4).optional(),
  mobilityLevel: z.number().min(1).max(4).optional(),
  
  // 护理评估
  careLevel: z.number().min(1).max(4).optional(),
  nursingNeeds: z.array(z.string()).optional(),
  medicalSupport: z.array(z.string()).optional(),
  
  // 风险评估
  fallRisk: z.number().min(1).max(3).optional(),
  cognitiveRisk: z.number().min(1).max(3).optional(),
  behaviorRisk: z.number().min(1).max(3).optional(),
  
  // 评估结果
  overallScore: z.number().min(0).max(100).optional(),
  recommendedCareLevel: z.number().min(1).max(4).optional(),
  suitabilityAssessment: z.number().min(1).max(3).optional(),
  recommendations: z.string().optional(),
  notes: z.string().optional(),
  attachments: z.array(z.string()).optional(),
})

// 评估更新验证 schema
const updateAssessmentSchema = createAssessmentSchema.partial().extend({
  status: z.number().min(1).max(3).optional(),
})

// 计算综合评分
function calculateOverallScore(assessment: any): number {
  let score = 0
  let factors = 0

  // 健康状况评分 (40%)
  if (assessment.physicalCondition) {
    score += (5 - assessment.physicalCondition) * 10 // 1=40分, 2=30分, 3=20分, 4=10分
    factors++
  }
  if (assessment.mentalCondition) {
    score += (5 - assessment.mentalCondition) * 10
    factors++
  }
  if (assessment.mobilityLevel) {
    score += (5 - assessment.mobilityLevel) * 10
    factors++
  }

  // 风险评估 (30%)
  if (assessment.fallRisk) {
    score += (4 - assessment.fallRisk) * 10 // 1=30分, 2=20分, 3=10分
    factors++
  }
  if (assessment.cognitiveRisk) {
    score += (4 - assessment.cognitiveRisk) * 10
    factors++
  }
  if (assessment.behaviorRisk) {
    score += (4 - assessment.behaviorRisk) * 10
    factors++
  }

  return factors > 0 ? Math.round(score / factors) : 0
}

// 推荐护理等级
function getRecommendedCareLevel(assessment: any): number {
  const scores = []
  
  if (assessment.physicalCondition) scores.push(assessment.physicalCondition)
  if (assessment.mentalCondition) scores.push(assessment.mentalCondition)
  if (assessment.mobilityLevel) scores.push(assessment.mobilityLevel)
  
  if (scores.length === 0) return 1
  
  const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length
  
  if (avgScore <= 1.5) return 1 // 自理
  if (avgScore <= 2.5) return 2 // 半自理
  if (avgScore <= 3.5) return 3 // 不能自理
  return 4 // 特护
}

// 适宜性评估
function getSuitabilityAssessment(overallScore: number): number {
  if (overallScore >= 70) return 1 // 适合
  if (overallScore >= 50) return 2 // 有条件适合
  return 3 // 不适合
}

// GET - 获取评估列表
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const applicationId = searchParams.get('applicationId')
    const assessmentType = searchParams.get('assessmentType')
    const status = searchParams.get('status')
    const assessorId = searchParams.get('assessorId')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const offset = (page - 1) * limit
    const limitNum = Math.min(limit, 100)

    // 构建查询条件
    const whereConditions = []

    if (applicationId) {
      whereConditions.push(eq(admissionAssessments.applicationId, applicationId))
    }

    if (assessmentType) {
      whereConditions.push(eq(admissionAssessments.assessmentType, parseInt(assessmentType)))
    }

    if (status) {
      whereConditions.push(eq(admissionAssessments.status, parseInt(status)))
    }

    if (assessorId) {
      whereConditions.push(eq(admissionAssessments.assessorId, assessorId))
    }

    // 排序
    const orderBy = sortOrder === 'asc' 
      ? asc(admissionAssessments[sortBy as keyof typeof admissionAssessments] || admissionAssessments.createdAt)
      : desc(admissionAssessments[sortBy as keyof typeof admissionAssessments] || admissionAssessments.createdAt)

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(admissionAssessments)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    const total = totalResult[0]?.count || 0

    // 获取评估列表
    const assessmentList = await db
      .select({
        id: admissionAssessments.id,
        applicationId: admissionAssessments.applicationId,
        assessmentType: admissionAssessments.assessmentType,
        assessmentDate: admissionAssessments.assessmentDate,
        physicalCondition: admissionAssessments.physicalCondition,
        mentalCondition: admissionAssessments.mentalCondition,
        mobilityLevel: admissionAssessments.mobilityLevel,
        careLevel: admissionAssessments.careLevel,
        fallRisk: admissionAssessments.fallRisk,
        cognitiveRisk: admissionAssessments.cognitiveRisk,
        behaviorRisk: admissionAssessments.behaviorRisk,
        overallScore: admissionAssessments.overallScore,
        recommendedCareLevel: admissionAssessments.recommendedCareLevel,
        suitabilityAssessment: admissionAssessments.suitabilityAssessment,
        status: admissionAssessments.status,
        createdAt: admissionAssessments.createdAt,
        updatedAt: admissionAssessments.updatedAt,
        // 关联数据
        application: {
          id: admissionApplications.id,
          applicationNumber: admissionApplications.applicationNumber,
          elderName: admissionApplications.elderName,
          applicantName: admissionApplications.applicantName,
          status: admissionApplications.status,
        },
        assessor: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(admissionAssessments)
      .leftJoin(admissionApplications, eq(admissionAssessments.applicationId, admissionApplications.id))
      .leftJoin(betterAuthUsers, eq(admissionAssessments.assessorId, betterAuthUsers.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(orderBy)
      .limit(limitNum)
      .offset(offset)

    return NextResponse.json({
      success: true,
      data: {
        list: assessmentList,
        pagination: {
          page,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      },
    })
  } catch (error) {
    console.error('获取评估列表失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// POST - 创建评估记录
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createAssessmentSchema.parse(body)

    // 检查申请是否存在
    const application = await db
      .select()
      .from(admissionApplications)
      .where(eq(admissionApplications.id, validatedData.applicationId))
      .limit(1)

    if (application.length === 0) {
      return NextResponse.json(
        { success: false, code: 'APPLICATION_NOT_FOUND', message: '入住申请不存在' },
        { status: 404 }
      )
    }

    // 检查申请状态是否允许评估
    if (![1, 2].includes(application[0].status)) { // 待评估或评估中
      return NextResponse.json(
        { 
          success: false, 
          code: 'INVALID_APPLICATION_STATUS', 
          message: '申请状态不允许进行评估' 
        },
        { status: 400 }
      )
    }

    // 检查是否已存在相同类型的进行中评估
    const existingAssessment = await db
      .select()
      .from(admissionAssessments)
      .where(
        and(
          eq(admissionAssessments.applicationId, validatedData.applicationId),
          eq(admissionAssessments.assessmentType, validatedData.assessmentType),
          eq(admissionAssessments.status, 1) // 进行中
        )
      )
      .limit(1)

    if (existingAssessment.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          code: 'ASSESSMENT_EXISTS', 
          message: '该类型的评估已在进行中' 
        },
        { status: 400 }
      )
    }

    // 计算评估结果
    const overallScore = calculateOverallScore(validatedData)
    const recommendedCareLevel = getRecommendedCareLevel(validatedData)
    const suitabilityAssessment = getSuitabilityAssessment(overallScore)

    // 创建评估记录
    const assessmentData = {
      ...validatedData,
      assessorId: user.id,
      overallScore,
      recommendedCareLevel,
      suitabilityAssessment,
      nursingNeeds: validatedData.nursingNeeds ? JSON.stringify(validatedData.nursingNeeds) : null,
      medicalSupport: validatedData.medicalSupport ? JSON.stringify(validatedData.medicalSupport) : null,
      attachments: validatedData.attachments ? JSON.stringify(validatedData.attachments) : null,
    }

    const newAssessment = await db
      .insert(admissionAssessments)
      .values(assessmentData)
      .returning()

    // 如果申请状态是待评估，更新为评估中
    if (application[0].status === 1) {
      await db
        .update(admissionApplications)
        .set({
          status: 2, // 评估中
          updatedAt: new Date(),
        })
        .where(eq(admissionApplications.id, validatedData.applicationId))
    }

    return NextResponse.json({
      success: true,
      data: newAssessment[0],
      message: '评估记录创建成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('创建评估记录失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
