import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { 
  admissionAssessments, 
  admissionApplications,
  betterAuthUsers 
} from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, and } from 'drizzle-orm'

// 评估更新验证 schema
const updateAssessmentSchema = z.object({
  assessmentDate: z.string().optional(),
  
  // 健康评估
  physicalCondition: z.number().min(1).max(4).optional(),
  mentalCondition: z.number().min(1).max(4).optional(),
  mobilityLevel: z.number().min(1).max(4).optional(),
  
  // 护理评估
  careLevel: z.number().min(1).max(4).optional(),
  nursingNeeds: z.array(z.string()).optional(),
  medicalSupport: z.array(z.string()).optional(),
  
  // 风险评估
  fallRisk: z.number().min(1).max(3).optional(),
  cognitiveRisk: z.number().min(1).max(3).optional(),
  behaviorRisk: z.number().min(1).max(3).optional(),
  
  // 评估结果
  recommendations: z.string().optional(),
  notes: z.string().optional(),
  attachments: z.array(z.string()).optional(),
  status: z.number().min(1).max(3).optional(),
})

// 计算综合评分
function calculateOverallScore(assessment: any): number {
  let score = 0
  let factors = 0

  // 健康状况评分 (40%)
  if (assessment.physicalCondition) {
    score += (5 - assessment.physicalCondition) * 10
    factors++
  }
  if (assessment.mentalCondition) {
    score += (5 - assessment.mentalCondition) * 10
    factors++
  }
  if (assessment.mobilityLevel) {
    score += (5 - assessment.mobilityLevel) * 10
    factors++
  }

  // 风险评估 (30%)
  if (assessment.fallRisk) {
    score += (4 - assessment.fallRisk) * 10
    factors++
  }
  if (assessment.cognitiveRisk) {
    score += (4 - assessment.cognitiveRisk) * 10
    factors++
  }
  if (assessment.behaviorRisk) {
    score += (4 - assessment.behaviorRisk) * 10
    factors++
  }

  return factors > 0 ? Math.round(score / factors) : 0
}

// 推荐护理等级
function getRecommendedCareLevel(assessment: any): number {
  const scores = []
  
  if (assessment.physicalCondition) scores.push(assessment.physicalCondition)
  if (assessment.mentalCondition) scores.push(assessment.mentalCondition)
  if (assessment.mobilityLevel) scores.push(assessment.mobilityLevel)
  
  if (scores.length === 0) return 1
  
  const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length
  
  if (avgScore <= 1.5) return 1 // 自理
  if (avgScore <= 2.5) return 2 // 半自理
  if (avgScore <= 3.5) return 3 // 不能自理
  return 4 // 特护
}

// 适宜性评估
function getSuitabilityAssessment(overallScore: number): number {
  if (overallScore >= 70) return 1 // 适合
  if (overallScore >= 50) return 2 // 有条件适合
  return 3 // 不适合
}

// GET - 获取单个评估详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const assessmentId = params.id

    // 获取评估详情
    const assessmentResult = await db
      .select({
        id: admissionAssessments.id,
        applicationId: admissionAssessments.applicationId,
        assessmentType: admissionAssessments.assessmentType,
        assessmentDate: admissionAssessments.assessmentDate,
        physicalCondition: admissionAssessments.physicalCondition,
        mentalCondition: admissionAssessments.mentalCondition,
        mobilityLevel: admissionAssessments.mobilityLevel,
        careLevel: admissionAssessments.careLevel,
        nursingNeeds: admissionAssessments.nursingNeeds,
        medicalSupport: admissionAssessments.medicalSupport,
        fallRisk: admissionAssessments.fallRisk,
        cognitiveRisk: admissionAssessments.cognitiveRisk,
        behaviorRisk: admissionAssessments.behaviorRisk,
        overallScore: admissionAssessments.overallScore,
        recommendedCareLevel: admissionAssessments.recommendedCareLevel,
        suitabilityAssessment: admissionAssessments.suitabilityAssessment,
        recommendations: admissionAssessments.recommendations,
        notes: admissionAssessments.notes,
        attachments: admissionAssessments.attachments,
        status: admissionAssessments.status,
        createdAt: admissionAssessments.createdAt,
        updatedAt: admissionAssessments.updatedAt,
        // 关联数据
        application: {
          id: admissionApplications.id,
          applicationNumber: admissionApplications.applicationNumber,
          elderName: admissionApplications.elderName,
          elderAge: admissionApplications.elderAge,
          elderGender: admissionApplications.elderGender,
          applicantName: admissionApplications.applicantName,
          status: admissionApplications.status,
        },
        assessor: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(admissionAssessments)
      .leftJoin(admissionApplications, eq(admissionAssessments.applicationId, admissionApplications.id))
      .leftJoin(betterAuthUsers, eq(admissionAssessments.assessorId, betterAuthUsers.id))
      .where(eq(admissionAssessments.id, assessmentId))
      .limit(1)

    if (assessmentResult.length === 0) {
      return NextResponse.json(
        { success: false, code: 'ASSESSMENT_NOT_FOUND', message: '评估记录不存在' },
        { status: 404 }
      )
    }

    const assessment = assessmentResult[0]

    // 解析 JSON 字段
    if (assessment.nursingNeeds) {
      try {
        assessment.nursingNeeds = JSON.parse(assessment.nursingNeeds)
      } catch {
        assessment.nursingNeeds = []
      }
    }

    if (assessment.medicalSupport) {
      try {
        assessment.medicalSupport = JSON.parse(assessment.medicalSupport)
      } catch {
        assessment.medicalSupport = []
      }
    }

    if (assessment.attachments) {
      try {
        assessment.attachments = JSON.parse(assessment.attachments)
      } catch {
        assessment.attachments = []
      }
    }

    return NextResponse.json({
      success: true,
      data: assessment,
    })
  } catch (error) {
    console.error('获取评估详情失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// PUT - 更新评估记录
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const assessmentId = params.id
    const body = await request.json()
    const validatedData = updateAssessmentSchema.parse(body)

    // 检查评估是否存在
    const existingAssessment = await db
      .select()
      .from(admissionAssessments)
      .where(eq(admissionAssessments.id, assessmentId))
      .limit(1)

    if (existingAssessment.length === 0) {
      return NextResponse.json(
        { success: false, code: 'ASSESSMENT_NOT_FOUND', message: '评估记录不存在' },
        { status: 404 }
      )
    }

    // 检查是否可以更新
    const currentStatus = existingAssessment[0].status
    if (currentStatus === 2 && validatedData.status !== 3) { // 已完成的评估只能重新评估
      return NextResponse.json(
        { 
          success: false, 
          code: 'CANNOT_UPDATE', 
          message: '已完成的评估无法修改，只能重新评估' 
        },
        { status: 400 }
      )
    }

    // 合并现有数据和更新数据
    const mergedData = { ...existingAssessment[0], ...validatedData }

    // 重新计算评估结果
    const overallScore = calculateOverallScore(mergedData)
    const recommendedCareLevel = getRecommendedCareLevel(mergedData)
    const suitabilityAssessment = getSuitabilityAssessment(overallScore)

    // 更新评估记录
    const updateData: any = {
      ...validatedData,
      overallScore,
      recommendedCareLevel,
      suitabilityAssessment,
      updatedAt: new Date(),
    }

    if (validatedData.nursingNeeds) {
      updateData.nursingNeeds = JSON.stringify(validatedData.nursingNeeds)
    }

    if (validatedData.medicalSupport) {
      updateData.medicalSupport = JSON.stringify(validatedData.medicalSupport)
    }

    if (validatedData.attachments) {
      updateData.attachments = JSON.stringify(validatedData.attachments)
    }

    const updatedAssessment = await db
      .update(admissionAssessments)
      .set(updateData)
      .where(eq(admissionAssessments.id, assessmentId))
      .returning()

    // 如果评估完成，检查是否所有必要的评估都已完成
    if (validatedData.status === 2) { // 已完成
      const applicationId = existingAssessment[0].applicationId

      // 检查是否所有必要的评估都已完成
      const completedAssessments = await db
        .select()
        .from(admissionAssessments)
        .where(
          and(
            eq(admissionAssessments.applicationId, applicationId),
            eq(admissionAssessments.status, 2) // 已完成
          )
        )

      // 如果健康评估和护理评估都已完成，更新申请状态为待审核
      const hasHealthAssessment = completedAssessments.some(a => a.assessmentType === 1)
      const hasNursingAssessment = completedAssessments.some(a => a.assessmentType === 2)

      if (hasHealthAssessment && hasNursingAssessment) {
        await db
          .update(admissionApplications)
          .set({
            status: 3, // 待审核
            updatedAt: new Date(),
          })
          .where(eq(admissionApplications.id, applicationId))
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedAssessment[0],
      message: '评估记录更新成功',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          code: 'INVALID_DATA',
          message: '数据验证失败',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('更新评估记录失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// DELETE - 删除评估记录
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { success: false, code: 'UNAUTHORIZED', message: '未授权访问' },
        { status: 401 }
      )
    }

    const assessmentId = params.id

    // 检查评估是否存在
    const existingAssessment = await db
      .select()
      .from(admissionAssessments)
      .where(eq(admissionAssessments.id, assessmentId))
      .limit(1)

    if (existingAssessment.length === 0) {
      return NextResponse.json(
        { success: false, code: 'ASSESSMENT_NOT_FOUND', message: '评估记录不存在' },
        { status: 404 }
      )
    }

    // 检查是否可以删除
    const currentStatus = existingAssessment[0].status
    if (currentStatus === 2) { // 已完成
      return NextResponse.json(
        { 
          success: false, 
          code: 'CANNOT_DELETE', 
          message: '已完成的评估无法删除' 
        },
        { status: 400 }
      )
    }

    // 删除评估记录
    await db
      .delete(admissionAssessments)
      .where(eq(admissionAssessments.id, assessmentId))

    return NextResponse.json({
      success: true,
      message: '评估记录删除成功',
    })
  } catch (error) {
    console.error('删除评估记录失败:', error)
    return NextResponse.json(
      { success: false, code: 'INTERNAL_ERROR', message: '服务器内部错误' },
      { status: 500 }
    )
  }
}
