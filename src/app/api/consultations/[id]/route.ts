/**
 * 单个咨询记录 API 路由
 * 
 * 处理单个咨询记录的获取、更新和删除操作
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { consultations, elderInfo, betterAuthUsers, consultationFollowUps } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, desc } from 'drizzle-orm'

// 更新咨询记录验证 Schema
const UpdateConsultationSchema = z.object({
  // 老人信息
  elderInfo: z.object({
    name: z.string().min(1, '老人姓名不能为空').optional(),
    age: z.number().min(0).max(150, '年龄必须在0-150之间').optional(),
    gender: z.number().min(1).max(2, '性别必须为1(男)或2(女)').optional(),
    phone: z.string().optional(),
    idCard: z.string().optional(),
    address: z.string().optional(),
    emergencyContactName: z.string().optional(),
    emergencyContactPhone: z.string().optional(),
    emergencyContactRelation: z.string().optional(),
    healthCondition: z.string().optional(),
    medications: z.string().optional(),
    allergies: z.string().optional(),
    specialNeeds: z.string().optional(),
  }).optional(),
  // 咨询人信息
  consultantName: z.string().min(1, '咨询人姓名不能为空').optional(),
  consultantPhone: z.string().min(1, '咨询人电话不能为空').optional(),
  consultantRelation: z.string().optional(),
  // 咨询详情
  purpose: z.string().min(1, '咨询目的不能为空').optional(),
  expectedCheckInDate: z.string().optional(),
  mediaChannel: z.number().min(1).max(4, '媒介渠道必须在1-4之间').optional(),
  status: z.number().min(1).max(4, '状态必须在1-4之间').optional(),
  notes: z.string().optional(),
  followUpDate: z.string().optional(),
  assignedUserId: z.string().optional(),
})

/**
 * GET /api/consultations/[id] - 获取咨询记录详情
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户权限
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问', code: 'UNAUTHORIZED' },
        { status: 401 }
      )
    }

    const consultationId = params.id

    // 查询咨询记录详情
    const [consultation] = await db
      .select({
        id: consultations.id,
        consultantName: consultations.consultantName,
        consultantPhone: consultations.consultantPhone,
        consultantRelation: consultations.consultantRelation,
        purpose: consultations.purpose,
        expectedCheckInDate: consultations.expectedCheckInDate,
        mediaChannel: consultations.mediaChannel,
        status: consultations.status,
        notes: consultations.notes,
        followUpDate: consultations.followUpDate,
        createdAt: consultations.createdAt,
        updatedAt: consultations.updatedAt,
        elderInfo: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
          phone: elderInfo.phone,
          idCard: elderInfo.idCard,
          address: elderInfo.address,
          emergencyContactName: elderInfo.emergencyContactName,
          emergencyContactPhone: elderInfo.emergencyContactPhone,
          emergencyContactRelation: elderInfo.emergencyContactRelation,
          healthCondition: elderInfo.healthCondition,
          medications: elderInfo.medications,
          allergies: elderInfo.allergies,
          specialNeeds: elderInfo.specialNeeds,
        },
        assignedUser: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(consultations)
      .leftJoin(elderInfo, eq(consultations.elderInfoId, elderInfo.id))
      .leftJoin(betterAuthUsers, eq(consultations.assignedUserId, betterAuthUsers.id))
      .where(eq(consultations.id, consultationId))

    if (!consultation) {
      return NextResponse.json(
        { success: false, error: '咨询记录不存在', code: 'NOT_FOUND' },
        { status: 404 }
      )
    }

    // 查询跟进记录
    const followUps = await db
      .select({
        id: consultationFollowUps.id,
        content: consultationFollowUps.content,
        nextFollowUpDate: consultationFollowUps.nextFollowUpDate,
        createdAt: consultationFollowUps.createdAt,
        user: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(consultationFollowUps)
      .leftJoin(betterAuthUsers, eq(consultationFollowUps.userId, betterAuthUsers.id))
      .where(eq(consultationFollowUps.consultationId, consultationId))
      .orderBy(desc(consultationFollowUps.createdAt))

    return NextResponse.json({
      success: true,
      data: {
        ...consultation,
        followUps,
      },
    })

  } catch (error) {
    console.error('获取咨询记录详情失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器错误', code: 'SERVER_ERROR' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/consultations/[id] - 更新咨询记录
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户权限
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问', code: 'UNAUTHORIZED' },
        { status: 401 }
      )
    }

    const consultationId = params.id

    // 解析请求体
    const body = await request.json()
    const validatedData = UpdateConsultationSchema.parse(body)

    // 检查咨询记录是否存在
    const [existingConsultation] = await db
      .select({
        id: consultations.id,
        elderInfoId: consultations.elderInfoId,
      })
      .from(consultations)
      .where(eq(consultations.id, consultationId))

    if (!existingConsultation) {
      return NextResponse.json(
        { success: false, error: '咨询记录不存在', code: 'NOT_FOUND' },
        { status: 404 }
      )
    }

    // 开始数据库事务
    const result = await db.transaction(async (tx) => {
      // 1. 更新老人信息（如果提供）
      if (validatedData.elderInfo && existingConsultation.elderInfoId) {
        const elderInfoUpdates: any = {}
        
        Object.entries(validatedData.elderInfo).forEach(([key, value]) => {
          if (value !== undefined) {
            elderInfoUpdates[key] = value
          }
        })

        if (Object.keys(elderInfoUpdates).length > 0) {
          elderInfoUpdates.updatedAt = new Date()
          
          await tx
            .update(elderInfo)
            .set(elderInfoUpdates)
            .where(eq(elderInfo.id, existingConsultation.elderInfoId))
        }
      }

      // 2. 更新咨询记录
      const consultationUpdates: any = {}
      
      Object.entries(validatedData).forEach(([key, value]) => {
        if (key !== 'elderInfo' && value !== undefined) {
          if (key === 'expectedCheckInDate' || key === 'followUpDate') {
            consultationUpdates[key] = value ? new Date(value) : null
          } else {
            consultationUpdates[key] = value
          }
        }
      })

      if (Object.keys(consultationUpdates).length > 0) {
        consultationUpdates.updatedAt = new Date()
        
        const [updatedConsultation] = await tx
          .update(consultations)
          .set(consultationUpdates)
          .where(eq(consultations.id, consultationId))
          .returning()

        return updatedConsultation
      }

      return existingConsultation
    })

    return NextResponse.json({
      success: true,
      data: result,
      message: '咨询记录更新成功',
    })

  } catch (error) {
    console.error('更新咨询记录失败:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: '数据验证失败', 
          details: error.errors,
          code: 'VALIDATION_ERROR' 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: '服务器错误', code: 'SERVER_ERROR' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/consultations/[id] - 删除咨询记录
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户权限
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问', code: 'UNAUTHORIZED' },
        { status: 401 }
      )
    }

    const consultationId = params.id

    // 检查咨询记录是否存在
    const [existingConsultation] = await db
      .select({
        id: consultations.id,
        elderInfoId: consultations.elderInfoId,
      })
      .from(consultations)
      .where(eq(consultations.id, consultationId))

    if (!existingConsultation) {
      return NextResponse.json(
        { success: false, error: '咨询记录不存在', code: 'NOT_FOUND' },
        { status: 404 }
      )
    }

    // 删除咨询记录（级联删除跟进记录）
    await db
      .delete(consultations)
      .where(eq(consultations.id, consultationId))

    return NextResponse.json({
      success: true,
      message: '咨询记录删除成功',
    })

  } catch (error) {
    console.error('删除咨询记录失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器错误', code: 'SERVER_ERROR' },
      { status: 500 }
    )
  }
}
