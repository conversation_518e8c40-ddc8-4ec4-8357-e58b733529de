/**
 * 咨询记录 API 路由
 * 
 * 处理咨询记录的列表查询和创建操作
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/db'
import { consultations, elderInfo, betterAuthUsers } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'
import { eq, desc, and, or, like, count, sql } from 'drizzle-orm'

// 查询参数验证 Schema
const ConsultationQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(10),
  search: z.string().optional(),
  status: z.coerce.number().optional(),
  mediaChannel: z.coerce.number().optional(),
  assignedUserId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'consultantName', 'status']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

// 创建咨询记录验证 Schema
const CreateConsultationSchema = z.object({
  // 老人信息
  elderInfo: z.object({
    name: z.string().min(1, '老人姓名不能为空'),
    age: z.number().min(0).max(150, '年龄必须在0-150之间'),
    gender: z.number().min(1).max(2, '性别必须为1(男)或2(女)'),
    phone: z.string().optional(),
    idCard: z.string().optional(),
    address: z.string().optional(),
    emergencyContactName: z.string().optional(),
    emergencyContactPhone: z.string().optional(),
    emergencyContactRelation: z.string().optional(),
    healthCondition: z.string().optional(),
    medications: z.string().optional(),
    allergies: z.string().optional(),
    specialNeeds: z.string().optional(),
  }),
  // 咨询人信息
  consultantName: z.string().min(1, '咨询人姓名不能为空'),
  consultantPhone: z.string().min(1, '咨询人电话不能为空'),
  consultantRelation: z.string().optional(),
  // 咨询详情
  purpose: z.string().min(1, '咨询目的不能为空'),
  expectedCheckInDate: z.string().optional(),
  mediaChannel: z.number().min(1).max(4, '媒介渠道必须在1-4之间'),
  notes: z.string().optional(),
  followUpDate: z.string().optional(),
  assignedUserId: z.string().optional(),
})

/**
 * GET /api/consultations - 获取咨询记录列表
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问', code: 'UNAUTHORIZED' },
        { status: 401 }
      )
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    const validatedParams = ConsultationQuerySchema.parse(queryParams)
    const { 
      page, 
      pageSize, 
      search, 
      status, 
      mediaChannel, 
      assignedUserId,
      startDate,
      endDate,
      sortBy, 
      sortOrder 
    } = validatedParams

    // 构建查询条件
    const conditions = []
    
    if (search) {
      conditions.push(
        or(
          like(consultations.consultantName, `%${search}%`),
          like(consultations.consultantPhone, `%${search}%`),
          like(elderInfo.name, `%${search}%`)
        )
      )
    }
    
    if (status !== undefined) {
      conditions.push(eq(consultations.status, status))
    }
    
    if (mediaChannel !== undefined) {
      conditions.push(eq(consultations.mediaChannel, mediaChannel))
    }
    
    if (assignedUserId) {
      conditions.push(eq(consultations.assignedUserId, assignedUserId))
    }
    
    if (startDate) {
      conditions.push(sql`${consultations.createdAt} >= ${new Date(startDate)}`)
    }
    
    if (endDate) {
      conditions.push(sql`${consultations.createdAt} <= ${new Date(endDate)}`)
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined

    // 查询总数
    const [totalResult] = await db
      .select({ count: count() })
      .from(consultations)
      .leftJoin(elderInfo, eq(consultations.elderInfoId, elderInfo.id))
      .where(whereClause)

    const total = totalResult.count

    // 查询数据
    const offset = (page - 1) * pageSize
    const orderColumn = consultations[sortBy as keyof typeof consultations]
    const orderDirection = sortOrder === 'asc' ? orderColumn : desc(orderColumn)

    const results = await db
      .select({
        id: consultations.id,
        consultantName: consultations.consultantName,
        consultantPhone: consultations.consultantPhone,
        consultantRelation: consultations.consultantRelation,
        purpose: consultations.purpose,
        expectedCheckInDate: consultations.expectedCheckInDate,
        mediaChannel: consultations.mediaChannel,
        status: consultations.status,
        notes: consultations.notes,
        followUpDate: consultations.followUpDate,
        createdAt: consultations.createdAt,
        updatedAt: consultations.updatedAt,
        elderInfo: {
          id: elderInfo.id,
          name: elderInfo.name,
          age: elderInfo.age,
          gender: elderInfo.gender,
          phone: elderInfo.phone,
        },
        assignedUser: {
          id: betterAuthUsers.id,
          name: betterAuthUsers.name,
          email: betterAuthUsers.email,
        },
      })
      .from(consultations)
      .leftJoin(elderInfo, eq(consultations.elderInfoId, elderInfo.id))
      .leftJoin(betterAuthUsers, eq(consultations.assignedUserId, betterAuthUsers.id))
      .where(whereClause)
      .orderBy(orderDirection)
      .limit(pageSize)
      .offset(offset)

    return NextResponse.json({
      success: true,
      data: {
        items: results,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      },
    })

  } catch (error) {
    console.error('获取咨询记录列表失败:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: '参数验证失败', 
          details: error.errors,
          code: 'VALIDATION_ERROR' 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: '服务器错误', code: 'SERVER_ERROR' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/consultations - 创建咨询记录
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问', code: 'UNAUTHORIZED' },
        { status: 401 }
      )
    }

    // 解析请求体
    const body = await request.json()
    const validatedData = CreateConsultationSchema.parse(body)

    // 开始数据库事务
    const result = await db.transaction(async (tx) => {
      // 1. 创建老人信息记录
      const [elderInfoRecord] = await tx
        .insert(elderInfo)
        .values({
          name: validatedData.elderInfo.name,
          age: validatedData.elderInfo.age,
          gender: validatedData.elderInfo.gender,
          phone: validatedData.elderInfo.phone,
          idCard: validatedData.elderInfo.idCard,
          address: validatedData.elderInfo.address,
          emergencyContactName: validatedData.elderInfo.emergencyContactName,
          emergencyContactPhone: validatedData.elderInfo.emergencyContactPhone,
          emergencyContactRelation: validatedData.elderInfo.emergencyContactRelation,
          healthCondition: validatedData.elderInfo.healthCondition,
          medications: validatedData.elderInfo.medications,
          allergies: validatedData.elderInfo.allergies,
          specialNeeds: validatedData.elderInfo.specialNeeds,
        })
        .returning()

      // 2. 创建咨询记录
      const [consultationRecord] = await tx
        .insert(consultations)
        .values({
          elderInfoId: elderInfoRecord.id,
          consultantName: validatedData.consultantName,
          consultantPhone: validatedData.consultantPhone,
          consultantRelation: validatedData.consultantRelation,
          purpose: validatedData.purpose,
          expectedCheckInDate: validatedData.expectedCheckInDate ? new Date(validatedData.expectedCheckInDate) : null,
          mediaChannel: validatedData.mediaChannel,
          notes: validatedData.notes,
          followUpDate: validatedData.followUpDate ? new Date(validatedData.followUpDate) : null,
          assignedUserId: validatedData.assignedUserId || user.id,
          status: 1, // 默认状态：待跟进
        })
        .returning()

      return {
        consultation: consultationRecord,
        elderInfo: elderInfoRecord,
      }
    })

    return NextResponse.json({
      success: true,
      data: result,
      message: '咨询记录创建成功',
    })

  } catch (error) {
    console.error('创建咨询记录失败:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: '数据验证失败', 
          details: error.errors,
          code: 'VALIDATION_ERROR' 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: '服务器错误', code: 'SERVER_ERROR' },
      { status: 500 }
    )
  }
}
