/**
 * @vitest-environment node
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { GET, POST } from '../route'
import { NextRequest } from 'next/server'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn(),
    insert: vi.fn(),
    transaction: vi.fn(),
  },
}))

vi.mock('@/lib/auth-utils', () => ({
  getCurrentUser: vi.fn(),
}))

vi.mock('drizzle-orm', () => ({
  eq: vi.fn(),
  and: vi.fn(),
  or: vi.fn(),
  like: vi.fn(),
  desc: vi.fn(),
  count: vi.fn(),
}))

describe('/api/consultations', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('GET', () => {
    it('returns consultations list with pagination', async () => {
      const { db } = await import('@/lib/db')
      const { getCurrentUser } = await import('@/lib/auth-utils')

      // Mock user authentication
      ;(getCurrentUser as any).mockResolvedValue({
        id: 'user-1',
        name: 'Test User',
      })

      // Mock database query
      const mockSelect = {
        from: vi.fn().mockReturnThis(),
        leftJoin: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        offset: vi.fn().mockResolvedValue([
          {
            id: 'consultation-1',
            consultantName: '张三',
            consultantPhone: '13800138000',
            purpose: '了解养老院服务',
            status: 1,
            mediaChannel: 1,
            createdAt: new Date('2024-01-01'),
            elderInfo: {
              name: '李四',
              age: 75,
              gender: 1,
            },
          },
        ]),
      }

      const mockCountSelect = {
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockResolvedValue([{ count: 1 }]),
      }

      ;(db.select as any)
        .mockReturnValueOnce(mockSelect)
        .mockReturnValueOnce(mockCountSelect)

      const request = new NextRequest('http://localhost:3000/api/consultations?page=1&limit=10')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveProperty('items')
      expect(data.data).toHaveProperty('pagination')
      expect(data.data.items).toHaveLength(1)
      expect(data.data.pagination.total).toBe(1)
    })

    it('handles search query', async () => {
      const { db } = await import('@/lib/db')
      const { getCurrentUser } = await import('@/lib/auth-utils')

      ;(getCurrentUser as any).mockResolvedValue({ id: 'user-1' })

      const mockSelect = {
        from: vi.fn().mockReturnThis(),
        leftJoin: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        offset: vi.fn().mockResolvedValue([]),
      }

      const mockCountSelect = {
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockResolvedValue([{ count: 0 }]),
      }

      ;(db.select as any)
        .mockReturnValueOnce(mockSelect)
        .mockReturnValueOnce(mockCountSelect)

      const request = new NextRequest('http://localhost:3000/api/consultations?search=张三')
      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(mockSelect.where).toHaveBeenCalled()
    })

    it('returns 401 when user is not authenticated', async () => {
      const { getCurrentUser } = await import('@/lib/auth-utils')

      ;(getCurrentUser as any).mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/consultations')
      const response = await GET(request)

      expect(response.status).toBe(401)
    })

    it('handles database errors', async () => {
      const { db } = await import('@/lib/db')
      const { getCurrentUser } = await import('@/lib/auth-utils')

      ;(getCurrentUser as any).mockResolvedValue({ id: 'user-1' })

      const mockSelect = {
        from: vi.fn().mockReturnThis(),
        leftJoin: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        offset: vi.fn().mockRejectedValue(new Error('Database error')),
      }

      ;(db.select as any).mockReturnValue(mockSelect)

      const request = new NextRequest('http://localhost:3000/api/consultations')
      const response = await GET(request)

      expect(response.status).toBe(500)
    })
  })

  describe('POST', () => {
    it('creates consultation successfully', async () => {
      const { db } = await import('@/lib/db')
      const { getCurrentUser } = await import('@/lib/auth-utils')

      ;(getCurrentUser as any).mockResolvedValue({
        id: 'user-1',
        name: 'Test User',
      })

      // Mock transaction
      const mockTransaction = vi.fn().mockImplementation(async (callback) => {
        const mockTx = {
          insert: vi.fn().mockReturnThis(),
          values: vi.fn().mockReturnThis(),
          returning: vi.fn().mockResolvedValue([
            { id: 'elder-1' },
            { id: 'consultation-1' },
          ]),
        }
        return callback(mockTx)
      })

      ;(db.transaction as any).mockImplementation(mockTransaction)

      const requestBody = {
        elderInfo: {
          name: '张三',
          age: 75,
          gender: 1,
        },
        consultantName: '李四',
        consultantPhone: '13800138000',
        purpose: '了解养老院服务',
        mediaChannel: 1,
      }

      const request = new NextRequest('http://localhost:3000/api/consultations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data).toHaveProperty('id')
      expect(mockTransaction).toHaveBeenCalled()
    })

    it('validates request body', async () => {
      const { getCurrentUser } = await import('@/lib/auth-utils')

      ;(getCurrentUser as any).mockResolvedValue({ id: 'user-1' })

      const invalidRequestBody = {
        // Missing required fields
        consultantName: '李四',
      }

      const request = new NextRequest('http://localhost:3000/api/consultations', {
        method: 'POST',
        body: JSON.stringify(invalidRequestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)

      expect(response.status).toBe(400)
    })

    it('returns 401 when user is not authenticated', async () => {
      const { getCurrentUser } = await import('@/lib/auth-utils')

      ;(getCurrentUser as any).mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/consultations', {
        method: 'POST',
        body: JSON.stringify({}),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)

      expect(response.status).toBe(401)
    })

    it('handles database transaction errors', async () => {
      const { db } = await import('@/lib/db')
      const { getCurrentUser } = await import('@/lib/auth-utils')

      ;(getCurrentUser as any).mockResolvedValue({ id: 'user-1' })

      const mockTransaction = vi.fn().mockRejectedValue(new Error('Transaction failed'))
      ;(db.transaction as any).mockImplementation(mockTransaction)

      const requestBody = {
        elderInfo: {
          name: '张三',
          age: 75,
          gender: 1,
        },
        consultantName: '李四',
        consultantPhone: '13800138000',
        purpose: '了解养老院服务',
        mediaChannel: 1,
      }

      const request = new NextRequest('http://localhost:3000/api/consultations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)

      expect(response.status).toBe(500)
    })

    it('handles file attachments', async () => {
      const { db } = await import('@/lib/db')
      const { getCurrentUser } = await import('@/lib/auth-utils')

      ;(getCurrentUser as any).mockResolvedValue({ id: 'user-1' })

      const mockTransaction = vi.fn().mockImplementation(async (callback) => {
        const mockTx = {
          insert: vi.fn().mockReturnThis(),
          values: vi.fn().mockReturnThis(),
          returning: vi.fn().mockResolvedValue([
            { id: 'elder-1' },
            { id: 'consultation-1' },
          ]),
        }
        return callback(mockTx)
      })

      ;(db.transaction as any).mockImplementation(mockTransaction)

      const requestBody = {
        elderInfo: {
          name: '张三',
          age: 75,
          gender: 1,
        },
        consultantName: '李四',
        consultantPhone: '13800138000',
        purpose: '了解养老院服务',
        mediaChannel: 1,
        attachments: [
          {
            fileName: 'document.pdf',
            originalName: 'document.pdf',
            fileSize: 1024,
            mimeType: 'application/pdf',
            url: 'http://example.com/document.pdf',
            path: 'consultation/document.pdf',
          },
        ],
      }

      const request = new NextRequest('http://localhost:3000/api/consultations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
    })
  })
})
