'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { useResidenceOverview } from '@/hooks/use-residence-overview'
import { ResidenceOverviewDashboard } from '@/components/residence/ResidenceOverviewDashboard'
import { ResidenceOverviewCharts } from '@/components/residence/ResidenceOverviewCharts'
import { 
  Home, 
  Calendar as CalendarIcon, 
  Download, 
  Refresh<PERSON><PERSON>,
  Filter,
  <PERSON><PERSON><PERSON>3,
  <PERSON><PERSON><PERSON>
} from 'lucide-react'
import { format, addDays } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { DateRange } from 'react-day-picker'

export default function ResidenceOverviewPage() {
  const { toast } = useToast()
  const { getResidenceOverview, isLoading } = useResidenceOverview()

  // 状态管理
  const [data, setData] = useState<any>(null)
  const [period, setPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month')
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const [activeTab, setActiveTab] = useState('dashboard')

  // 加载数据
  const loadData = async () => {
    try {
      const params = {
        period,
        startDate: dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : undefined,
        endDate: dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined,
      }

      const result = await getResidenceOverview(params)
      setData(result)
    } catch (error) {
      toast({
        title: '加载失败',
        description: '获取入住统计数据失败，请重试',
        variant: 'destructive',
      })
    }
  }

  // 初始化加载
  useEffect(() => {
    loadData()
  }, [period, dateRange])

  // 导出数据
  const handleExport = async () => {
    try {
      // 这里可以实现数据导出功能
      toast({
        title: '导出成功',
        description: '入住统计数据已导出',
      })
    } catch (error) {
      toast({
        title: '导出失败',
        description: '导出数据时发生错误，请重试',
        variant: 'destructive',
      })
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题和操作栏 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <Home className="h-8 w-8 text-blue-600" />
            入住总览
          </h1>
          <p className="text-gray-600 mt-2">
            查看床位使用率、入住统计和房间分布情况
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={loadData}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            导出
          </Button>
        </div>
      </motion.div>

      {/* 筛选条件 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              筛选条件
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 items-end">
              {/* 时间周期选择 */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">时间周期</label>
                <Select value={period} onValueChange={(value: any) => setPeriod(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="week">最近一周</SelectItem>
                    <SelectItem value="month">本月</SelectItem>
                    <SelectItem value="quarter">本季度</SelectItem>
                    <SelectItem value="year">本年度</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 自定义日期范围 */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">自定义日期</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-64 justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange?.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, 'yyyy年MM月dd日', { locale: zhCN })} -{' '}
                            {format(dateRange.to, 'yyyy年MM月dd日', { locale: zhCN })}
                          </>
                        ) : (
                          format(dateRange.from, 'yyyy年MM月dd日', { locale: zhCN })
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange?.from}
                      selected={dateRange}
                      onSelect={setDateRange}
                      numberOfMonths={2}
                      locale={zhCN}
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* 清除筛选 */}
              {dateRange && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setDateRange(undefined)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  清除日期
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 数据展示区域 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              数据概览
            </TabsTrigger>
            <TabsTrigger value="charts" className="flex items-center gap-2">
              <PieChart className="h-4 w-4" />
              图表分析
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            <ResidenceOverviewDashboard
              data={data}
              loading={isLoading}
            />
          </TabsContent>

          <TabsContent value="charts" className="space-y-6">
            <ResidenceOverviewCharts
              data={data}
              loading={isLoading}
            />
          </TabsContent>
        </Tabs>
      </motion.div>

      {/* 数据时间范围显示 */}
      {data?.dateRange && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center"
        >
          <Badge variant="outline" className="text-sm">
            数据时间范围: {data.dateRange.start} 至 {data.dateRange.end}
          </Badge>
        </motion.div>
      )}
    </div>
  )
}
