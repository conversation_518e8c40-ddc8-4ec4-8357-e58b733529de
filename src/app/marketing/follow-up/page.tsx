'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Plus, Users, Clock, TrendingUp } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { FollowUpTimeline, FollowUpRecord } from '@/components/follow-up/FollowUpTimeline'
import { FollowUpForm } from '@/components/follow-up/FollowUpForm'
import { FollowUpReminderPanel, FollowUpReminder } from '@/components/follow-up/FollowUpReminder'
import { 
  SuccessAnimation, 
  LoadingAnimation,
  FloatingActionButton,
  CountUpAnimation,
  fadeInUp,
  staggerContainer 
} from '@/components/follow-up/FollowUpAnimations'
import { useFollowUp, useFollowUpReminder } from '@/hooks/use-follow-up'
import { useToast } from '@/hooks/use-toast'

export default function FollowUpPage() {
  const { toast } = useToast()
  const { 
    getFollowUps, 
    createFollowUp, 
    updateFollowUp, 
    deleteFollowUp,
    isLoading: followUpLoading 
  } = useFollowUp()
  const { 
    getReminders, 
    markReminderCompleted, 
    snoozeReminder,
    getReminderStats,
    isLoading: reminderLoading 
  } = useFollowUpReminder()

  // 状态管理
  const [followUps, setFollowUps] = useState<FollowUpRecord[]>([])
  const [reminders, setReminders] = useState<FollowUpReminder[]>([])
  const [stats, setStats] = useState({
    total: 0,
    overdue: 0,
    today: 0,
    tomorrow: 0,
    upcoming: 0,
    completed: 0,
  })
  const [showForm, setShowForm] = useState(false)
  const [editingFollowUp, setEditingFollowUp] = useState<FollowUpRecord | null>(null)
  const [showSuccess, setShowSuccess] = useState(false)
  const [showLoading, setShowLoading] = useState(false)
  const [selectedConsultationId, setSelectedConsultationId] = useState<string>('')

  // 加载数据
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setShowLoading(true)
      
      // 并行加载数据
      const [followUpsData, remindersData, statsData] = await Promise.all([
        getFollowUps({ limit: 50 }),
        getReminders(),
        getReminderStats(),
      ])

      setFollowUps(followUpsData.items)
      setReminders(remindersData)
      setStats(statsData)
    } catch (error) {
      toast({
        title: '加载失败',
        description: '获取跟进数据时发生错误',
        variant: 'destructive',
      })
    } finally {
      setShowLoading(false)
    }
  }

  // 处理创建跟进记录
  const handleCreateFollowUp = async (data: { content: string; nextFollowUpDate?: string }) => {
    if (!selectedConsultationId) {
      toast({
        title: '请选择咨询记录',
        description: '需要先选择要跟进的咨询记录',
        variant: 'destructive',
      })
      return
    }

    try {
      const newFollowUp = await createFollowUp({
        consultationId: selectedConsultationId,
        content: data.content,
        nextFollowUpDate: data.nextFollowUpDate,
      })

      setFollowUps(prev => [newFollowUp, ...prev])
      setShowSuccess(true)
      await loadData() // 重新加载数据以更新统计
    } catch (error) {
      throw error
    }
  }

  // 处理编辑跟进记录
  const handleEditFollowUp = async (data: { content?: string; nextFollowUpDate?: string }) => {
    if (!editingFollowUp) return

    try {
      const updatedFollowUp = await updateFollowUp(editingFollowUp.id, data)
      
      setFollowUps(prev => 
        prev.map(item => 
          item.id === editingFollowUp.id ? updatedFollowUp : item
        )
      )
      
      setEditingFollowUp(null)
      setShowSuccess(true)
      await loadData()
    } catch (error) {
      throw error
    }
  }

  // 处理删除跟进记录
  const handleDeleteFollowUp = async (followUpId: string) => {
    try {
      await deleteFollowUp(followUpId)
      setFollowUps(prev => prev.filter(item => item.id !== followUpId))
      await loadData()
    } catch (error) {
      toast({
        title: '删除失败',
        description: '删除跟进记录时发生错误',
        variant: 'destructive',
      })
    }
  }

  // 处理标记提醒完成
  const handleMarkReminderCompleted = async (reminderId: string) => {
    try {
      await markReminderCompleted(reminderId)
      await loadData()
    } catch (error) {
      throw error
    }
  }

  // 处理延期提醒
  const handleSnoozeReminder = async (reminderId: string, newDate: string) => {
    try {
      await snoozeReminder(reminderId, newDate)
      await loadData()
    } catch (error) {
      throw error
    }
  }

  // 处理查看咨询详情
  const handleViewConsultationDetails = (consultationId: string) => {
    // 跳转到咨询详情页面
    window.open(`/marketing/consultation/${consultationId}`, '_blank')
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和统计 */}
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6"
      >
        <motion.div variants={fadeInUp}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">跟进记录管理</h1>
              <p className="text-gray-600 mt-2">管理客户跟进记录和提醒</p>
            </div>
            <Button onClick={() => setShowForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              添加跟进记录
            </Button>
          </div>
        </motion.div>

        {/* 统计卡片 */}
        <motion.div variants={fadeInUp} className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总跟进记录</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <CountUpAnimation from={0} to={stats.total} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">逾期提醒</CardTitle>
              <Clock className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                <CountUpAnimation from={0} to={stats.overdue} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">今日待跟进</CardTitle>
              <Clock className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                <CountUpAnimation from={0} to={stats.today} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">已完成</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                <CountUpAnimation from={0} to={stats.completed} />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* 主要内容区域 */}
        <motion.div variants={fadeInUp}>
          <Tabs defaultValue="reminders" className="space-y-6">
            <TabsList>
              <TabsTrigger value="reminders">跟进提醒</TabsTrigger>
              <TabsTrigger value="timeline">跟进时间线</TabsTrigger>
            </TabsList>

            <TabsContent value="reminders" className="space-y-6">
              <FollowUpReminderPanel
                reminders={reminders}
                onMarkCompleted={handleMarkReminderCompleted}
                onSnoozeReminder={handleSnoozeReminder}
                onViewDetails={handleViewConsultationDetails}
                isLoading={reminderLoading}
              />
            </TabsContent>

            <TabsContent value="timeline" className="space-y-6">
              <FollowUpTimeline
                consultationId={selectedConsultationId}
                followUps={followUps}
                onAddFollowUp={() => setShowForm(true)}
                onEditFollowUp={setEditingFollowUp}
                onDeleteFollowUp={handleDeleteFollowUp}
                isLoading={followUpLoading}
              />
            </TabsContent>
          </Tabs>
        </motion.div>
      </motion.div>

      {/* 跟进记录表单 */}
      <FollowUpForm
        open={showForm || !!editingFollowUp}
        onOpenChange={(open) => {
          setShowForm(open)
          if (!open) setEditingFollowUp(null)
        }}
        consultationId={selectedConsultationId}
        initialData={editingFollowUp}
        onSubmit={editingFollowUp ? handleEditFollowUp : handleCreateFollowUp}
        isLoading={followUpLoading}
      />

      {/* 悬浮操作按钮 */}
      <FloatingActionButton
        onClick={() => setShowForm(true)}
        icon={<Plus className="h-5 w-5" />}
        label="快速跟进"
      />

      {/* 动画效果 */}
      <SuccessAnimation
        show={showSuccess}
        message="操作成功完成"
        onComplete={() => setShowSuccess(false)}
      />

      <LoadingAnimation
        show={showLoading}
        message="加载数据中..."
      />
    </div>
  )
}
