'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ColumnDef } from '@tanstack/react-table'
import {
  Plus,
  Eye,
  Edit,
  Trash2,
  Phone,
  Calendar,
  User,
  Filter,
  Download,
  RefreshCw,
} from 'lucide-react'

import { AppLayout } from '@/components/layout'
import { DataTable } from '@/components/ui/data-table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { useApi } from '@/hooks/use-api'
import { apiClient } from '@/lib/api-client'
import { formatDate } from '@/lib/utils'
import { ConsultationForm } from '@/components/consultation/ConsultationForm'

// 咨询记录类型定义
interface Consultation {
  id: string
  consultantName: string
  consultantPhone: string
  consultantRelation?: string
  purpose: string
  expectedCheckInDate?: string
  mediaChannel: number
  status: number
  notes?: string
  followUpDate?: string
  createdAt: string
  updatedAt: string
  elderInfo?: {
    id: string
    name: string
    age: number
    gender: number
    phone?: string
  }
  assignedUser?: {
    id: string
    name: string
    email: string
  }
}

// 状态映射
const STATUS_MAP = {
  1: { label: '待跟进', color: 'bg-yellow-100 text-yellow-800' },
  2: { label: '已跟进', color: 'bg-blue-100 text-blue-800' },
  3: { label: '已入住', color: 'bg-green-100 text-green-800' },
  4: { label: '已放弃', color: 'bg-gray-100 text-gray-800' },
}

// 媒介渠道映射
const MEDIA_CHANNEL_MAP = {
  1: '电话',
  2: '微信',
  3: '现场',
  4: '其他',
}

/**
 * 咨询接待列表页面
 */
export default function ConsultationListPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [mediaChannelFilter, setMediaChannelFilter] = useState<string>('')
  const [selectedConsultation, setSelectedConsultation] =
    useState<Consultation | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  const { toast } = useToast()

  // 构建查询参数
  const queryParams = new URLSearchParams()
  if (searchTerm) queryParams.set('search', searchTerm)
  if (statusFilter) queryParams.set('status', statusFilter)
  if (mediaChannelFilter) queryParams.set('mediaChannel', mediaChannelFilter)

  // 获取咨询记录列表
  const {
    data: consultationsData,
    isLoading,
    error,
    mutate: refetchConsultations,
  } = useApi<{
    items: Consultation[]
    pagination: {
      page: number
      pageSize: number
      total: number
      totalPages: number
    }
  }>(`/api/consultations?${queryParams.toString()}`, {
    refreshInterval: 30000, // 30秒自动刷新
    revalidateOnFocus: true,
  })

  const consultations = consultationsData?.items || []

  // 表格列定义
  const columns: ColumnDef<Consultation>[] = [
    {
      accessorKey: 'consultantName',
      header: '咨询人信息',
      cell: ({ row }) => {
        const consultation = row.original
        return (
          <div className="space-y-1">
            <div className="font-medium">{consultation.consultantName}</div>
            <div className="text-sm text-muted-foreground flex items-center gap-1">
              <Phone className="h-3 w-3" />
              {consultation.consultantPhone}
            </div>
            {consultation.consultantRelation && (
              <div className="text-sm text-muted-foreground">
                关系：{consultation.consultantRelation}
              </div>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: 'elderInfo',
      header: '老人信息',
      cell: ({ row }) => {
        const elderInfo = row.original.elderInfo
        if (!elderInfo) return <span className="text-muted-foreground">-</span>

        return (
          <div className="space-y-1">
            <div className="font-medium">{elderInfo.name}</div>
            <div className="text-sm text-muted-foreground">
              {elderInfo.age}岁 · {elderInfo.gender === 1 ? '男' : '女'}
            </div>
            {elderInfo.phone && (
              <div className="text-sm text-muted-foreground flex items-center gap-1">
                <Phone className="h-3 w-3" />
                {elderInfo.phone}
              </div>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: 'purpose',
      header: '咨询目的',
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate" title={row.original.purpose}>
          {row.original.purpose}
        </div>
      ),
    },
    {
      accessorKey: 'mediaChannel',
      header: '媒介渠道',
      cell: ({ row }) => (
        <Badge variant="outline">
          {
            MEDIA_CHANNEL_MAP[
              row.original.mediaChannel as keyof typeof MEDIA_CHANNEL_MAP
            ]
          }
        </Badge>
      ),
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => {
        const status =
          STATUS_MAP[row.original.status as keyof typeof STATUS_MAP]
        return <Badge className={status.color}>{status.label}</Badge>
      },
    },
    {
      accessorKey: 'expectedCheckInDate',
      header: '预期入住',
      cell: ({ row }) => {
        const date = row.original.expectedCheckInDate
        return date ? (
          <div className="flex items-center gap-1 text-sm">
            <Calendar className="h-3 w-3" />
            {formatDate(date)}
          </div>
        ) : (
          <span className="text-muted-foreground">-</span>
        )
      },
    },
    {
      accessorKey: 'assignedUser',
      header: '负责人',
      cell: ({ row }) => {
        const user = row.original.assignedUser
        return user ? (
          <div className="flex items-center gap-1 text-sm">
            <User className="h-3 w-3" />
            {user.name}
          </div>
        ) : (
          <span className="text-muted-foreground">-</span>
        )
      },
    },
    {
      accessorKey: 'createdAt',
      header: '创建时间',
      cell: ({ row }) => (
        <div className="text-sm text-muted-foreground">
          {formatDate(row.original.createdAt)}
        </div>
      ),
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const consultation = row.original
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleViewDetail(consultation)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEdit(consultation)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(consultation)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        )
      },
    },
  ]

  // 处理查看详情
  const handleViewDetail = (consultation: Consultation) => {
    setSelectedConsultation(consultation)
    setIsDetailDialogOpen(true)
  }

  // 处理编辑
  const handleEdit = (consultation: Consultation) => {
    // TODO: 实现编辑功能
    toast({
      title: '功能开发中',
      description: '编辑功能正在开发中...',
    })
  }

  // 处理删除
  const handleDelete = async (consultation: Consultation) => {
    if (!confirm('确定要删除这条咨询记录吗？')) return

    try {
      await apiClient.delete(`/api/consultations/${consultation.id}`)
      toast({
        title: '删除成功',
        description: '咨询记录已删除',
      })
      refetchConsultations()
    } catch (error) {
      toast({
        title: '删除失败',
        description: '删除咨询记录时发生错误',
        variant: 'destructive',
      })
    }
  }

  // 处理创建
  const handleCreate = () => {
    setIsCreateDialogOpen(true)
  }

  // 处理表单提交
  const handleFormSubmit = async (data: any, files?: any[]) => {
    try {
      // 将文件信息添加到提交数据中
      const submitData = {
        ...data,
        attachments:
          files?.map(file => ({
            fileName: file.fileName,
            originalName: file.originalName,
            fileSize: file.fileSize,
            mimeType: file.mimeType,
            url: file.url,
            path: file.path,
          })) || [],
      }

      await apiClient.post('/api/consultations', submitData)
      toast({
        title: '创建成功',
        description: '咨询记录已创建',
      })
      setIsCreateDialogOpen(false)
      refetchConsultations()
    } catch (error) {
      throw error // 让表单组件处理错误显示
    }
  }

  // 处理导出
  const handleExport = () => {
    // TODO: 实现导出功能
    toast({
      title: '功能开发中',
      description: '导出功能正在开发中...',
    })
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* 页面头部 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">咨询接待</h1>
              <p className="text-gray-600 mt-2">管理客户咨询记录和跟进情况</p>
            </div>
            <Button onClick={handleCreate}>
              <Plus className="mr-2 h-4 w-4" />
              新增咨询
            </Button>
          </div>
        </motion.div>

        {/* 筛选器 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                筛选条件
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-[200px]">
                  <Input
                    placeholder="搜索咨询人、老人姓名或电话..."
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="状态筛选" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部状态</SelectItem>
                    <SelectItem value="1">待跟进</SelectItem>
                    <SelectItem value="2">已跟进</SelectItem>
                    <SelectItem value="3">已入住</SelectItem>
                    <SelectItem value="4">已放弃</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={mediaChannelFilter}
                  onValueChange={setMediaChannelFilter}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="渠道筛选" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部渠道</SelectItem>
                    <SelectItem value="1">电话</SelectItem>
                    <SelectItem value="2">微信</SelectItem>
                    <SelectItem value="3">现场</SelectItem>
                    <SelectItem value="4">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* 数据表格 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>咨询记录列表</CardTitle>
              <CardDescription>
                共 {consultationsData?.pagination.total || 0} 条记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={columns}
                data={consultations}
                loading={isLoading}
                onRefresh={refetchConsultations}
                onExport={handleExport}
                searchPlaceholder="搜索咨询记录..."
              />
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* 详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>咨询记录详情</DialogTitle>
          </DialogHeader>
          {selectedConsultation && (
            <div className="space-y-4">
              {/* TODO: 实现详情内容 */}
              <p>详情内容开发中...</p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 创建对话框 */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>新增咨询记录</DialogTitle>
          </DialogHeader>
          <ConsultationForm
            onSubmit={handleFormSubmit}
            onCancel={() => setIsCreateDialogOpen(false)}
            userId="current-user-id" // TODO: 从认证系统获取当前用户ID
          />
        </DialogContent>
      </Dialog>
    </AppLayout>
  )
}
