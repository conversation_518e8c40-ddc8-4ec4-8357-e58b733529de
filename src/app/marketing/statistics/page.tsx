'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DatePickerWithRange } from '@/components/ui/date-range-picker'
import { Badge } from '@/components/ui/badge'
import { SalesPerformanceCharts } from '@/components/statistics/SalesPerformanceCharts'
import { ChannelAnalysisCharts } from '@/components/statistics/ChannelAnalysisCharts'
import { useStatistics } from '@/hooks/use-statistics'
import { useToast } from '@/hooks/use-toast'
import { 
  BarChart3, 
  Download, 
  Calendar, 
  TrendingUp, 
  Users, 
  Target,
  RefreshCw,
  FileText,
  FileSpreadsheet
} from 'lucide-react'
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { DateRange } from 'react-day-picker'

export default function StatisticsPage() {
  const { toast } = useToast()
  const {
    getSalesPerformance,
    getChannelAnalysis,
    exportSalesPerformance,
    exportChannelAnalysis,
    isLoading,
    isExporting,
  } = useStatistics()

  // 状态管理
  const [activeTab, setActiveTab] = useState('sales')
  const [period, setPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month')
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: startOfMonth(new Date()),
    to: endOfMonth(new Date()),
  })
  const [salesData, setSalesData] = useState<any>(null)
  const [channelData, setChannelData] = useState<any>(null)

  // 加载数据
  const loadData = async () => {
    try {
      const params = {
        period,
        startDate: dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : undefined,
        endDate: dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined,
      }

      const [salesResult, channelResult] = await Promise.all([
        getSalesPerformance(params),
        getChannelAnalysis(params),
      ])

      setSalesData(salesResult)
      setChannelData(channelResult)
    } catch (error) {
      toast({
        title: '加载失败',
        description: '获取统计数据失败，请重试',
        variant: 'destructive',
      })
    }
  }

  // 初始加载
  useEffect(() => {
    loadData()
  }, [period, dateRange])

  // 快速时间选择
  const handleQuickDateSelect = (type: 'week' | 'month' | 'quarter' | 'year') => {
    const now = new Date()
    let from: Date
    let to: Date = now

    switch (type) {
      case 'week':
        from = subDays(now, 7)
        break
      case 'month':
        from = startOfMonth(now)
        to = endOfMonth(now)
        break
      case 'quarter':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3
        from = new Date(now.getFullYear(), quarterStart, 1)
        to = new Date(now.getFullYear(), quarterStart + 3, 0)
        break
      case 'year':
        from = new Date(now.getFullYear(), 0, 1)
        to = new Date(now.getFullYear(), 11, 31)
        break
      default:
        from = startOfMonth(now)
    }

    setDateRange({ from, to })
    setPeriod(type)
  }

  // 导出数据
  const handleExport = async (format: 'csv' | 'json') => {
    try {
      if (activeTab === 'sales' && salesData) {
        await exportSalesPerformance(salesData, {
          format,
          title: '销售业绩统计报表',
        })
        toast({
          title: '导出成功',
          description: '销售业绩数据已导出',
        })
      } else if (activeTab === 'channels' && channelData) {
        await exportChannelAnalysis(channelData, {
          format,
          title: '媒介渠道分析报表',
        })
        toast({
          title: '导出成功',
          description: '渠道分析数据已导出',
        })
      }
    } catch (error) {
      toast({
        title: '导出失败',
        description: '数据导出失败，请重试',
        variant: 'destructive',
      })
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">业绩统计与报表</h1>
          <p className="text-gray-600 mt-2">查看销售业绩和渠道分析数据</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-sm">
            {dateRange?.from && dateRange?.to
              ? `${format(dateRange.from, 'MM/dd', { locale: zhCN })} - ${format(dateRange.to, 'MM/dd', { locale: zhCN })}`
              : '选择时间范围'
            }
          </Badge>
        </div>
      </motion.div>

      {/* 控制面板 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-wrap items-center gap-4">
              {/* 时间范围选择 */}
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <DatePickerWithRange
                  date={dateRange}
                  onDateChange={setDateRange}
                  placeholder="选择时间范围"
                />
              </div>

              {/* 快速时间选择 */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">快速选择:</span>
                {(['week', 'month', 'quarter', 'year'] as const).map((type) => (
                  <Button
                    key={type}
                    variant={period === type ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleQuickDateSelect(type)}
                  >
                    {type === 'week' && '本周'}
                    {type === 'month' && '本月'}
                    {type === 'quarter' && '本季度'}
                    {type === 'year' && '本年'}
                  </Button>
                ))}
              </div>

              {/* 刷新按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={loadData}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                刷新
              </Button>

              {/* 导出按钮 */}
              <div className="flex items-center gap-2 ml-auto">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport('csv')}
                  disabled={isExporting || (!salesData && !channelData)}
                >
                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                  导出 CSV
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport('json')}
                  disabled={isExporting || (!salesData && !channelData)}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  导出 JSON
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 统计内容 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="sales" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              销售业绩
            </TabsTrigger>
            <TabsTrigger value="channels" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              渠道分析
            </TabsTrigger>
          </TabsList>

          <TabsContent value="sales" className="mt-6">
            {salesData ? (
              <SalesPerformanceCharts
                data={salesData}
                loading={isLoading}
              />
            ) : (
              <Card>
                <CardContent className="p-12 text-center">
                  <div className="text-gray-500">
                    {isLoading ? '正在加载销售业绩数据...' : '暂无销售业绩数据'}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="channels" className="mt-6">
            {channelData ? (
              <ChannelAnalysisCharts
                data={channelData}
                loading={isLoading}
              />
            ) : (
              <Card>
                <CardContent className="p-12 text-center">
                  <div className="text-gray-500">
                    {isLoading ? '正在加载渠道分析数据...' : '暂无渠道分析数据'}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  )
}
