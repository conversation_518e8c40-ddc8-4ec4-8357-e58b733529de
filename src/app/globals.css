@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.56 0.18 214);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.96 0.02 210);
  --secondary-foreground: oklch(0.56 0.18 214);
  --muted: oklch(0.96 0.02 210);
  --muted-foreground: oklch(0.47 0.08 215);
  --accent: oklch(0.96 0.02 210);
  --accent-foreground: oklch(0.56 0.18 214);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.91 0.04 214);
  --input: oklch(0.91 0.04 214);
  --ring: oklch(0.56 0.18 214);
  --chart-1: oklch(0.56 0.18 214);
  --chart-2: oklch(0.48 0.22 199);
  --chart-3: oklch(0.53 0.2 187);
  --chart-4: oklch(0.39 0.15 173);
  --chart-5: oklch(0.52 0.16 158);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.56 0.18 214);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.96 0.02 210);
  --sidebar-accent-foreground: oklch(0.56 0.18 214);
  --sidebar-border: oklch(0.91 0.04 214);
  --sidebar-ring: oklch(0.56 0.18 214);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.92 0.004 286.32);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.552 0.016 285.938);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.552 0.016 285.938);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for nursing home management system */
@layer components {
  .sidebar-nav {
    @apply space-y-1;
  }

  .sidebar-nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors;
  }

  .sidebar-nav-item:hover {
    @apply bg-accent text-accent-foreground;
  }

  .sidebar-nav-item.active {
    @apply bg-primary text-primary-foreground;
  }

  .dashboard-card {
    @apply bg-card border border-border rounded-lg p-6 shadow-sm;
  }

  .stat-card {
    @apply bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-lg p-4;
  }
}

/* Theme transition and user preferences */
@layer utilities {
  /* 主题切换动画 */
  .theme-transition {
    transition:
      background-color 0.3s ease,
      color 0.3s ease,
      border-color 0.3s ease;
  }

  .theme-transition * {
    transition:
      background-color 0.3s ease,
      color 0.3s ease,
      border-color 0.3s ease;
  }

  /* 紧凑模式样式 */
  .compact-mode {
    --spacing-scale: 0.75;
  }

  .compact-mode .compact-layout {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .compact-mode .dashboard-card {
    @apply p-4;
  }

  .compact-mode .stat-card {
    @apply p-3;
  }

  /* 减少动画效果 */
  .reduce-motion,
  .reduce-motion *,
  .reduce-motion *::before,
  .reduce-motion *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* 响应式布局增强 */
  @media (max-width: 768px) {
    .mobile-optimized {
      padding: 0.75rem;
    }

    .mobile-optimized .dashboard-card {
      @apply p-4;
    }

    .mobile-optimized .stat-card {
      @apply p-3;
    }
  }

  @media (max-width: 640px) {
    .mobile-optimized {
      padding: 0.5rem;
    }

    .mobile-optimized .dashboard-card {
      @apply p-3;
    }

    .mobile-optimized .stat-card {
      @apply p-2;
    }
  }

  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .dashboard-card {
      @apply border-2;
    }

    .stat-card {
      @apply border-2;
    }
  }

  /* 减少动画偏好支持 */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}
