import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'

// 公开路径，不需要认证
const publicPaths = [
  '/api/auth',
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
]

// API路径，需要特殊处理
const apiPaths = ['/api']

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 检查是否为公开路径
  const isPublicPath = publicPaths.some(path => pathname.startsWith(path))

  // 如果是公开路径，直接通过
  if (isPublicPath) {
    return NextResponse.next()
  }

  try {
    // 获取会话信息
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    // 如果是API路径
    if (apiPaths.some(path => pathname.startsWith(path))) {
      // API路径需要认证
      if (!session) {
        return NextResponse.json(
          { error: '未授权访问', code: 'UNAUTHORIZED' },
          { status: 401 }
        )
      }
      return NextResponse.next()
    }

    // 页面路径处理
    if (!session) {
      // 未登录，重定向到登录页
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }

    // 检查用户是否激活
    if (!session.user.isActive) {
      return NextResponse.json(
        { error: '账户已被禁用', code: 'ACCOUNT_DISABLED' },
        { status: 403 }
      )
    }

    return NextResponse.next()
  } catch (error) {
    console.error('认证中间件错误:', error)

    // 如果是API路径，返回错误响应
    if (apiPaths.some(path => pathname.startsWith(path))) {
      return NextResponse.json(
        { error: '认证服务异常', code: 'AUTH_ERROR' },
        { status: 500 }
      )
    }

    // 页面路径，重定向到登录页
    return NextResponse.redirect(new URL('/login', request.url))
  }
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico (网站图标)
     * - public文件夹中的文件
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
