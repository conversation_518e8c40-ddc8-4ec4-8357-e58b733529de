'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { CalendarIcon, Upload, X } from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

// 退住申请表单验证 schema
const checkoutApplicationSchema = z.object({
  elderInfoId: z.string().uuid('请选择老人'),
  applicantName: z.string().min(1, '请输入申请人姓名'),
  applicantPhone: z.string().min(1, '请输入申请人电话'),
  applicantRelation: z.string().min(1, '请输入与老人关系'),
  checkoutReason: z.string().min(1, '请输入退住原因'),
  checkoutType: z.number().min(1).max(3, '请选择退住类型'),
  expectedCheckoutDate: z.date({
    required_error: '请选择预期退住日期',
  }),
  notes: z.string().optional(),
  attachments: z
    .array(
      z.object({
        name: z.string(),
        url: z.string(),
        type: z.string(),
        size: z.number(),
      })
    )
    .optional(),
})

type CheckoutApplicationFormData = z.infer<typeof checkoutApplicationSchema>

interface CheckoutApplicationFormProps {
  elderOptions: Array<{
    id: string
    name: string
    idCard: string
    roomNumber?: string
  }>
  onSubmit: (data: CheckoutApplicationFormData) => Promise<void>
  onCancel: () => void
  initialData?: Partial<CheckoutApplicationFormData>
  isLoading?: boolean
}

const checkoutTypes = [
  { value: 1, label: '正常退住' },
  { value: 2, label: '转院退住' },
  { value: 3, label: '其他原因' },
]

const relationOptions = [
  '子女',
  '配偶',
  '父母',
  '兄弟姐妹',
  '其他亲属',
  '朋友',
  '代理人',
  '其他',
]

export function CheckoutApplicationForm({
  elderOptions,
  onSubmit,
  onCancel,
  initialData,
  isLoading = false,
}: CheckoutApplicationFormProps) {
  const [attachments, setAttachments] = useState<
    Array<{
      name: string
      url: string
      type: string
      size: number
    }>
  >(initialData?.attachments || [])

  const form = useForm<CheckoutApplicationFormData>({
    resolver: zodResolver(checkoutApplicationSchema),
    defaultValues: {
      elderInfoId: initialData?.elderInfoId || '',
      applicantName: initialData?.applicantName || '',
      applicantPhone: initialData?.applicantPhone || '',
      applicantRelation: initialData?.applicantRelation || '',
      checkoutReason: initialData?.checkoutReason || '',
      checkoutType: initialData?.checkoutType || 1,
      expectedCheckoutDate: initialData?.expectedCheckoutDate,
      notes: initialData?.notes || '',
      attachments: attachments,
    },
  })

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    Array.from(files).forEach(file => {
      // 这里应该实现文件上传逻辑
      // 暂时模拟文件上传
      const mockUrl = URL.createObjectURL(file)
      const newAttachment = {
        name: file.name,
        url: mockUrl,
        type: file.type,
        size: file.size,
      }

      setAttachments(prev => [...prev, newAttachment])
      form.setValue('attachments', [...attachments, newAttachment])
    })

    // 清空文件输入
    event.target.value = ''
  }

  const removeAttachment = (index: number) => {
    const newAttachments = attachments.filter((_, i) => i !== index)
    setAttachments(newAttachments)
    form.setValue('attachments', newAttachments)
  }

  const handleSubmit = async (data: CheckoutApplicationFormData) => {
    try {
      await onSubmit(data)
      toast.success('退住申请提交成功')
    } catch (error) {
      toast.error('提交失败，请重试')
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>退住申请</CardTitle>
        <CardDescription>
          请填写完整的退住申请信息，所有标记为必填的字段都需要填写
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* 老人信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="elderInfoId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>选择老人 *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择老人" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {elderOptions.map(elder => (
                          <SelectItem key={elder.id} value={elder.id}>
                            {elder.name} - {elder.idCard}{' '}
                            {elder.roomNumber && `(${elder.roomNumber})`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="checkoutType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>退住类型 *</FormLabel>
                    <Select
                      onValueChange={value => field.onChange(parseInt(value))}
                      defaultValue={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择退住类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {checkoutTypes.map(type => (
                          <SelectItem
                            key={type.value}
                            value={type.value.toString()}
                          >
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 申请人信息 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="applicantName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>申请人姓名 *</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入申请人姓名" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="applicantPhone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>申请人电话 *</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入申请人电话" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="applicantRelation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>与老人关系 *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择关系" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {relationOptions.map(relation => (
                          <SelectItem key={relation} value={relation}>
                            {relation}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 退住信息 */}
            <FormField
              control={form.control}
              name="expectedCheckoutDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>预期退住日期 *</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full pl-3 text-left font-normal',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          {field.value ? (
                            format(field.value, 'yyyy-MM-dd')
                          ) : (
                            <span>请选择日期</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={date =>
                          date < new Date() || date < new Date('1900-01-01')
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="checkoutReason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>退住原因 *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请详细说明退住原因"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="其他需要说明的事项"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 附件上传 */}
            <div className="space-y-4">
              <Label>相关附件</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                <div className="text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <Label htmlFor="file-upload" className="cursor-pointer">
                      <span className="mt-2 block text-sm font-medium text-gray-900">
                        点击上传文件
                      </span>
                      <span className="mt-1 block text-xs text-gray-500">
                        支持 PDF、图片等格式，单个文件不超过 10MB
                      </span>
                    </Label>
                    <Input
                      id="file-upload"
                      type="file"
                      className="hidden"
                      multiple
                      accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                      onChange={handleFileUpload}
                    />
                  </div>
                </div>
              </div>

              {/* 已上传文件列表 */}
              {attachments.length > 0 && (
                <div className="space-y-2">
                  <Label>已上传文件</Label>
                  {attachments.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded"
                    >
                      <span className="text-sm">{file.name}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeAttachment(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                取消
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? '提交中...' : '提交申请'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
