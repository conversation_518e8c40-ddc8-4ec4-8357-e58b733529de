'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { CheckCircle, XCircle, AlertCircle, Plus, Trash2 } from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

// 审核表单验证 schema
const reviewSchema = z.object({
  reviewLevel: z.number().min(1).max(3, '请选择审核级别'),
  reviewType: z.number().min(1).max(4, '请选择审核类型'),
  reviewResult: z.number().min(1).max(3, '请选择审核结果'),
  reviewNotes: z.string().optional(),
  requirements: z.array(z.object({
    requirement: z.string().min(1, '请输入要求内容'),
    completed: z.boolean().optional().default(false),
    notes: z.string().optional(),
  })).optional(),
  suggestions: z.string().optional(),
  nextReviewLevel: z.number().min(1).max(3).optional(),
  nextReviewer: z.string().uuid().optional(),
})

type ReviewFormData = z.infer<typeof reviewSchema>

interface CheckoutApplication {
  id: string
  applicationNumber: string
  applicantName: string
  applicantPhone: string
  applicantRelation: string
  checkoutReason: string
  checkoutType: number
  expectedCheckoutDate: string
  status: number
  applicationDate: string
  notes?: string
  elder: {
    id: string
    name: string
    age: number
    gender: string
    idCard: string
    careLevel: string
  }
  room?: {
    id: string
    roomNumber: string
    roomType: string
  }
}

interface CheckoutReviewFormProps {
  application: CheckoutApplication
  reviewerOptions: Array<{
    id: string
    name: string
    email: string
  }>
  onSubmit: (data: ReviewFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

const reviewLevels = [
  { value: 1, label: '初审' },
  { value: 2, label: '复审' },
  { value: 3, label: '终审' },
]

const reviewTypes = [
  { value: 1, label: '常规审核' },
  { value: 2, label: '特殊审核' },
  { value: 3, label: '紧急审核' },
  { value: 4, label: '补充审核' },
]

const reviewResults = [
  { value: 1, label: '通过', color: 'text-green-600', icon: CheckCircle },
  { value: 2, label: '拒绝', color: 'text-red-600', icon: XCircle },
  { value: 3, label: '需要补充材料', color: 'text-yellow-600', icon: AlertCircle },
]

const checkoutTypeConfig = {
  1: '正常退住',
  2: '转院退住',
  3: '其他原因',
}

export function CheckoutReviewForm({
  application,
  reviewerOptions,
  onSubmit,
  onCancel,
  isLoading = false,
}: CheckoutReviewFormProps) {
  const [requirements, setRequirements] = useState<Array<{
    requirement: string
    completed: boolean
    notes: string
  }>>([])

  const form = useForm<ReviewFormData>({
    resolver: zodResolver(reviewSchema),
    defaultValues: {
      reviewLevel: 1,
      reviewType: 1,
      reviewResult: 1,
      reviewNotes: '',
      requirements: [],
      suggestions: '',
    },
  })

  const watchReviewResult = form.watch('reviewResult')
  const watchReviewLevel = form.watch('reviewLevel')

  const addRequirement = () => {
    const newRequirement = {
      requirement: '',
      completed: false,
      notes: '',
    }
    setRequirements([...requirements, newRequirement])
  }

  const removeRequirement = (index: number) => {
    const newRequirements = requirements.filter((_, i) => i !== index)
    setRequirements(newRequirements)
    form.setValue('requirements', newRequirements)
  }

  const updateRequirement = (index: number, field: string, value: any) => {
    const newRequirements = [...requirements]
    newRequirements[index] = { ...newRequirements[index], [field]: value }
    setRequirements(newRequirements)
    form.setValue('requirements', newRequirements)
  }

  const handleSubmit = async (data: ReviewFormData) => {
    try {
      await onSubmit({ ...data, requirements })
      toast.success('审核提交成功')
    } catch (error) {
      toast.error('审核提交失败，请重试')
    }
  }

  return (
    <div className="space-y-6">
      {/* 申请信息概览 */}
      <Card>
        <CardHeader>
          <CardTitle>申请信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label className="text-sm font-medium text-gray-500">申请编号</Label>
              <div className="mt-1">{application.applicationNumber}</div>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-500">老人信息</Label>
              <div className="mt-1">
                {application.elder.name} ({application.elder.gender}, {application.elder.age}岁)
              </div>
              <div className="text-sm text-gray-500">{application.elder.idCard}</div>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-500">申请人</Label>
              <div className="mt-1">{application.applicantName}</div>
              <div className="text-sm text-gray-500">
                {application.applicantRelation} · {application.applicantPhone}
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-500">退住类型</Label>
              <div className="mt-1">
                {checkoutTypeConfig[application.checkoutType as keyof typeof checkoutTypeConfig]}
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-500">预期退住日期</Label>
              <div className="mt-1">
                {format(new Date(application.expectedCheckoutDate), 'yyyy-MM-dd')}
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-500">申请日期</Label>
              <div className="mt-1">
                {format(new Date(application.applicationDate), 'yyyy-MM-dd')}
              </div>
            </div>
          </div>
          
          {application.room && (
            <div className="mt-4">
              <Label className="text-sm font-medium text-gray-500">房间信息</Label>
              <div className="mt-1">
                {application.room.roomNumber} ({application.room.roomType})
              </div>
            </div>
          )}

          <div className="mt-4">
            <Label className="text-sm font-medium text-gray-500">退住原因</Label>
            <div className="mt-1 p-3 bg-gray-50 rounded-md">
              {application.checkoutReason}
            </div>
          </div>

          {application.notes && (
            <div className="mt-4">
              <Label className="text-sm font-medium text-gray-500">备注</Label>
              <div className="mt-1 p-3 bg-gray-50 rounded-md">
                {application.notes}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 审核表单 */}
      <Card>
        <CardHeader>
          <CardTitle>审核意见</CardTitle>
          <CardDescription>
            请仔细审核申请信息，并填写审核意见
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              {/* 审核基本信息 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="reviewLevel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>审核级别 *</FormLabel>
                      <Select onValueChange={(value) => field.onChange(parseInt(value))} defaultValue={field.value?.toString()}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="请选择审核级别" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {reviewLevels.map((level) => (
                            <SelectItem key={level.value} value={level.value.toString()}>
                              {level.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="reviewType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>审核类型 *</FormLabel>
                      <Select onValueChange={(value) => field.onChange(parseInt(value))} defaultValue={field.value?.toString()}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="请选择审核类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {reviewTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value.toString()}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="reviewResult"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>审核结果 *</FormLabel>
                      <Select onValueChange={(value) => field.onChange(parseInt(value))} defaultValue={field.value?.toString()}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="请选择审核结果" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {reviewResults.map((result) => {
                            const Icon = result.icon
                            return (
                              <SelectItem key={result.value} value={result.value.toString()}>
                                <div className={cn('flex items-center gap-2', result.color)}>
                                  <Icon className="h-4 w-4" />
                                  {result.label}
                                </div>
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 审核意见 */}
              <FormField
                control={form.control}
                name="reviewNotes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>审核意见</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请填写详细的审核意见"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 审核要求 */}
              {watchReviewResult === 3 && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>补充要求</Label>
                    <Button type="button" variant="outline" size="sm" onClick={addRequirement}>
                      <Plus className="h-4 w-4 mr-2" />
                      添加要求
                    </Button>
                  </div>
                  
                  {requirements.map((requirement, index) => (
                    <div key={index} className="p-4 border rounded-lg space-y-3">
                      <div className="flex items-center justify-between">
                        <Label>要求 {index + 1}</Label>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeRequirement(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <Input
                        placeholder="请输入具体要求"
                        value={requirement.requirement}
                        onChange={(e) => updateRequirement(index, 'requirement', e.target.value)}
                      />
                      <Textarea
                        placeholder="补充说明"
                        value={requirement.notes}
                        onChange={(e) => updateRequirement(index, 'notes', e.target.value)}
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* 下一级审核 */}
              {watchReviewResult === 1 && watchReviewLevel < 3 && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="nextReviewLevel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>下一审核级别</FormLabel>
                        <Select onValueChange={(value) => field.onChange(parseInt(value))} defaultValue={field.value?.toString()}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="请选择下一审核级别" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {reviewLevels
                              .filter(level => level.value > watchReviewLevel)
                              .map((level) => (
                                <SelectItem key={level.value} value={level.value.toString()}>
                                  {level.label}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="nextReviewer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>下一审核人</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="请选择下一审核人" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {reviewerOptions.map((reviewer) => (
                              <SelectItem key={reviewer.id} value={reviewer.id}>
                                {reviewer.name} ({reviewer.email})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* 建议 */}
              <FormField
                control={form.control}
                name="suggestions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>建议</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="其他建议或意见"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 操作按钮 */}
              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={onCancel}>
                  取消
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? '提交中...' : '提交审核'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
