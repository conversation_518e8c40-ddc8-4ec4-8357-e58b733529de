'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Search, Filter, MoreHorizontal, Eye, Edit, Trash2, FileText, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'

interface CheckoutApplication {
  id: string
  applicationNumber: string
  applicantName: string
  applicantPhone: string
  applicantRelation: string
  checkoutReason: string
  checkoutType: number
  expectedCheckoutDate: string
  actualCheckoutDate?: string
  status: number
  applicationDate: string
  notes?: string
  elder: {
    id: string
    name: string
    age: number
    gender: string
    idCard: string
    careLevel: string
  }
  room?: {
    id: string
    roomNumber: string
    roomType: string
  }
  createdBy: {
    id: string
    name: string
  }
}

interface CheckoutApplicationListProps {
  applications: CheckoutApplication[]
  onView: (application: CheckoutApplication) => void
  onEdit: (application: CheckoutApplication) => void
  onDelete: (applicationId: string) => void
  onReview: (applicationId: string) => void
  isLoading?: boolean
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  onPageChange?: (page: number) => void
  onSearch?: (search: string) => void
  onFilter?: (filters: any) => void
}

const statusConfig = {
  1: { label: '待审核', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  2: { label: '审核中', color: 'bg-blue-100 text-blue-800', icon: AlertCircle },
  3: { label: '已通过', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  4: { label: '已拒绝', color: 'bg-red-100 text-red-800', icon: XCircle },
  5: { label: '费用结算中', color: 'bg-purple-100 text-purple-800', icon: FileText },
  6: { label: '房间清理中', color: 'bg-orange-100 text-orange-800', icon: AlertCircle },
  7: { label: '已完成', color: 'bg-gray-100 text-gray-800', icon: CheckCircle },
}

const checkoutTypeConfig = {
  1: '正常退住',
  2: '转院退住',
  3: '其他原因',
}

export function CheckoutApplicationList({
  applications,
  onView,
  onEdit,
  onDelete,
  onReview,
  isLoading = false,
  pagination,
  onPageChange,
  onSearch,
  onFilter,
}: CheckoutApplicationListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')

  const handleSearch = () => {
    onSearch?.(searchTerm)
  }

  const handleFilter = () => {
    onFilter?.({
      status: statusFilter || undefined,
      checkoutType: typeFilter || undefined,
    })
  }

  const clearFilters = () => {
    setSearchTerm('')
    setStatusFilter('')
    setTypeFilter('')
    onSearch?.('')
    onFilter?.({})
  }

  const getStatusBadge = (status: number) => {
    const config = statusConfig[status as keyof typeof statusConfig]
    if (!config) return null

    const Icon = config.icon
    return (
      <Badge className={cn('flex items-center gap-1', config.color)}>
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>退住申请管理</CardTitle>
        <CardDescription>
          管理所有退住申请，包括审核、费用结算和房间清理等流程
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* 搜索和筛选 */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索申请编号、老人姓名、申请人..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="状态筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部状态</SelectItem>
                {Object.entries(statusConfig).map(([value, config]) => (
                  <SelectItem key={value} value={value}>
                    {config.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="类型筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部类型</SelectItem>
                {Object.entries(checkoutTypeConfig).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button onClick={handleSearch} variant="outline">
              <Search className="h-4 w-4 mr-2" />
              搜索
            </Button>
            
            <Button onClick={handleFilter} variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              筛选
            </Button>

            <Button onClick={clearFilters} variant="ghost">
              清空
            </Button>
          </div>
        </div>

        {/* 申请列表 */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>申请编号</TableHead>
                <TableHead>老人信息</TableHead>
                <TableHead>申请人</TableHead>
                <TableHead>退住类型</TableHead>
                <TableHead>预期退住日期</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>申请日期</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    加载中...
                  </TableCell>
                </TableRow>
              ) : applications.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                    暂无退住申请
                  </TableCell>
                </TableRow>
              ) : (
                applications.map((application) => (
                  <TableRow key={application.id}>
                    <TableCell className="font-medium">
                      {application.applicationNumber}
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{application.elder.name}</div>
                        <div className="text-sm text-gray-500">
                          {application.elder.gender} · {application.elder.age}岁
                        </div>
                        {application.room && (
                          <div className="text-sm text-gray-500">
                            {application.room.roomNumber}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{application.applicantName}</div>
                        <div className="text-sm text-gray-500">
                          {application.applicantRelation}
                        </div>
                        <div className="text-sm text-gray-500">
                          {application.applicantPhone}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {checkoutTypeConfig[application.checkoutType as keyof typeof checkoutTypeConfig]}
                    </TableCell>
                    <TableCell>
                      {format(new Date(application.expectedCheckoutDate), 'yyyy-MM-dd')}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(application.status)}
                    </TableCell>
                    <TableCell>
                      {format(new Date(application.applicationDate), 'yyyy-MM-dd')}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onView(application)}>
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </DropdownMenuItem>
                          {[1, 2].includes(application.status) && (
                            <>
                              <DropdownMenuItem onClick={() => onEdit(application)}>
                                <Edit className="mr-2 h-4 w-4" />
                                编辑
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onReview(application.id)}>
                                <FileText className="mr-2 h-4 w-4" />
                                审核
                              </DropdownMenuItem>
                            </>
                          )}
                          {application.status === 1 && (
                            <DropdownMenuItem 
                              onClick={() => onDelete(application.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* 分页 */}
        {pagination && pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-gray-500">
              共 {pagination.total} 条记录，第 {pagination.page} / {pagination.totalPages} 页
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange?.(pagination.page - 1)}
                disabled={pagination.page <= 1}
              >
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange?.(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
