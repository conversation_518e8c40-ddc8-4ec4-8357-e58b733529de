'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import {
  CheckCircle,
  Circle,
  Clock,
  XCircle,
  FileText,
  ClipboardCheck,
  UserCheck,
  FileSignature,
  CreditCard,
  Home,
} from 'lucide-react'

// 入住流程步骤定义
export const ADMISSION_STEPS = [
  {
    id: 'application',
    title: '提交申请',
    description: '填写入住申请表',
    icon: FileText,
    status: [1], // 对应的申请状态
  },
  {
    id: 'assessment',
    title: '健康评估',
    description: '进行健康和护理评估',
    icon: ClipboardCheck,
    status: [2], // 评估中
  },
  {
    id: 'review',
    title: '审核流程',
    description: '多级审核和批准',
    icon: UserCheck,
    status: [3, 4], // 待审核、审核中
  },
  {
    id: 'contract',
    title: '签订合同',
    description: '签署入住服务合同',
    icon: FileSignature,
    status: [5], // 已通过
  },
  {
    id: 'payment',
    title: '缴费处理',
    description: '缴纳押金和费用',
    icon: CreditCard,
    status: [5], // 已通过（与合同并行）
  },
  {
    id: 'checkin',
    title: '入住安排',
    description: '房间分配和入住',
    icon: Home,
    status: [7], // 已入住
  },
]

// 步骤状态类型
export type StepStatus = 'pending' | 'current' | 'completed' | 'error'

interface AdmissionStepsProps {
  currentStatus: number // 当前申请状态
  className?: string
  showDescription?: boolean
  orientation?: 'horizontal' | 'vertical'
}

export function AdmissionSteps({
  currentStatus,
  className,
  showDescription = true,
  orientation = 'horizontal',
}: AdmissionStepsProps) {
  // 根据申请状态计算每个步骤的状态
  const getStepStatus = (
    step: (typeof ADMISSION_STEPS)[0],
    index: number
  ): StepStatus => {
    // 被拒绝的状态
    if (currentStatus === 6) {
      if (index <= 2) return 'error' // 前三步显示错误
      return 'pending'
    }

    // 正常流程
    if (step.status.includes(currentStatus)) {
      return 'current'
    }

    // 已完成的步骤
    const stepMaxStatus = Math.max(...step.status)
    if (currentStatus > stepMaxStatus) {
      return 'completed'
    }

    // 特殊处理：合同和缴费是并行的
    if (step.id === 'contract' || step.id === 'payment') {
      if (currentStatus >= 5) {
        return currentStatus === 5 ? 'current' : 'completed'
      }
    }

    return 'pending'
  }

  // 获取步骤图标
  const getStepIcon = (
    step: (typeof ADMISSION_STEPS)[0],
    status: StepStatus
  ) => {
    const IconComponent = step.icon

    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'current':
        return <Clock className="w-5 h-5 text-blue-600" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600" />
      default:
        return <Circle className="w-5 h-5 text-gray-400" />
    }
  }

  // 获取步骤样式
  const getStepStyles = (status: StepStatus) => {
    switch (status) {
      case 'completed':
        return {
          container: 'text-green-600',
          title: 'text-green-900 font-medium',
          description: 'text-green-700',
          connector: 'bg-green-600',
        }
      case 'current':
        return {
          container: 'text-blue-600',
          title: 'text-blue-900 font-medium',
          description: 'text-blue-700',
          connector: 'bg-blue-600',
        }
      case 'error':
        return {
          container: 'text-red-600',
          title: 'text-red-900 font-medium',
          description: 'text-red-700',
          connector: 'bg-red-600',
        }
      default:
        return {
          container: 'text-gray-400',
          title: 'text-gray-600',
          description: 'text-gray-500',
          connector: 'bg-gray-300',
        }
    }
  }

  if (orientation === 'vertical') {
    return (
      <div className={cn('space-y-4', className)}>
        {ADMISSION_STEPS.map((step, index) => {
          const status = getStepStatus(step, index)
          const styles = getStepStyles(status)
          const isLast = index === ADMISSION_STEPS.length - 1

          return (
            <div key={step.id} className="relative">
              <div className="flex items-start space-x-3">
                {/* 图标 */}
                <div
                  className={cn(
                    'flex items-center justify-center w-8 h-8 rounded-full border-2',
                    status === 'completed'
                      ? 'bg-green-50 border-green-600'
                      : status === 'current'
                        ? 'bg-blue-50 border-blue-600'
                        : status === 'error'
                          ? 'bg-red-50 border-red-600'
                          : 'bg-gray-50 border-gray-300'
                  )}
                >
                  {getStepIcon(step, status)}
                </div>

                {/* 内容 */}
                <div className="flex-1 min-w-0">
                  <h3 className={cn('text-sm font-medium', styles.title)}>
                    {step.title}
                  </h3>
                  {showDescription && (
                    <p className={cn('text-xs mt-1', styles.description)}>
                      {step.description}
                    </p>
                  )}
                </div>
              </div>

              {/* 连接线 */}
              {!isLast && (
                <div
                  className={cn(
                    'absolute left-4 top-8 w-0.5 h-4',
                    status === 'completed' ? styles.connector : 'bg-gray-300'
                  )}
                />
              )}
            </div>
          )
        })}
      </div>
    )
  }

  // 水平布局
  return (
    <div className={cn('w-full', className)}>
      <div className="flex items-center justify-between">
        {ADMISSION_STEPS.map((step, index) => {
          const status = getStepStatus(step, index)
          const styles = getStepStyles(status)
          const isLast = index === ADMISSION_STEPS.length - 1

          return (
            <React.Fragment key={step.id}>
              <div className="flex flex-col items-center space-y-2">
                {/* 图标 */}
                <div
                  className={cn(
                    'flex items-center justify-center w-10 h-10 rounded-full border-2',
                    status === 'completed'
                      ? 'bg-green-50 border-green-600'
                      : status === 'current'
                        ? 'bg-blue-50 border-blue-600'
                        : status === 'error'
                          ? 'bg-red-50 border-red-600'
                          : 'bg-gray-50 border-gray-300'
                  )}
                >
                  {getStepIcon(step, status)}
                </div>

                {/* 标题 */}
                <div className="text-center">
                  <h3 className={cn('text-sm font-medium', styles.title)}>
                    {step.title}
                  </h3>
                  {showDescription && (
                    <p
                      className={cn(
                        'text-xs mt-1 max-w-20',
                        styles.description
                      )}
                    >
                      {step.description}
                    </p>
                  )}
                </div>
              </div>

              {/* 连接线 */}
              {!isLast && (
                <div
                  className={cn(
                    'flex-1 h-0.5 mx-2',
                    status === 'completed' ? styles.connector : 'bg-gray-300'
                  )}
                />
              )}
            </React.Fragment>
          )
        })}
      </div>
    </div>
  )
}

// 简化版步骤条（只显示图标）
interface SimpleAdmissionStepsProps {
  currentStatus: number
  className?: string
}

export function SimpleAdmissionSteps({
  currentStatus,
  className,
}: SimpleAdmissionStepsProps) {
  // 内部函数：获取步骤状态
  const getStepStatus = (
    step: (typeof ADMISSION_STEPS)[0],
    index: number
  ): StepStatus => {
    if (currentStatus === 6) {
      if (index <= 2) return 'error'
      return 'pending'
    }

    if (step.status.includes(currentStatus)) {
      return 'current'
    }

    const stepMaxStatus = Math.max(...step.status)
    if (currentStatus > stepMaxStatus) {
      return 'completed'
    }

    if (step.id === 'contract' || step.id === 'payment') {
      if (currentStatus >= 5) {
        return currentStatus === 5 ? 'current' : 'completed'
      }
    }

    return 'pending'
  }

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      {ADMISSION_STEPS.map((step, index) => {
        const status = getStepStatus(step, index)

        return (
          <div
            key={step.id}
            className={cn(
              'flex items-center justify-center w-6 h-6 rounded-full border',
              status === 'completed'
                ? 'bg-green-100 border-green-600 text-green-600'
                : status === 'current'
                  ? 'bg-blue-100 border-blue-600 text-blue-600'
                  : status === 'error'
                    ? 'bg-red-100 border-red-600 text-red-600'
                    : 'bg-gray-100 border-gray-300 text-gray-400'
            )}
            title={step.title}
          >
            {status === 'completed' ? (
              <CheckCircle className="w-3 h-3" />
            ) : status === 'error' ? (
              <XCircle className="w-3 h-3" />
            ) : (
              <span className="text-xs font-medium">{index + 1}</span>
            )}
          </div>
        )
      })}
    </div>
  )
}

// 状态描述映射
export const STATUS_DESCRIPTIONS = {
  1: '申请已提交，等待评估',
  2: '正在进行健康评估',
  3: '等待初审',
  4: '审核中',
  5: '审核通过，准备签约',
  6: '申请被拒绝',
  7: '已入住',
} as const

// 获取状态描述
export function getStatusDescription(status: number): string {
  return (
    STATUS_DESCRIPTIONS[status as keyof typeof STATUS_DESCRIPTIONS] ||
    '未知状态'
  )
}
