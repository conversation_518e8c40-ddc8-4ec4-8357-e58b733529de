'use client'

import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { CalendarIcon, Loader2, Upload } from 'lucide-react'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

// 表单验证 schema
const admissionApplicationSchema = z.object({
  // 老人基本信息
  elderName: z.string().min(1, '请输入老人姓名'),
  elderAge: z.number().min(1, '请输入有效年龄').max(120, '年龄不能超过120岁'),
  elderGender: z.number().min(1).max(2),
  elderIdCard: z.string().min(15, '请输入有效身份证号').max(18),
  elderPhone: z.string().min(11, '请输入有效手机号').max(11),

  // 申请人信息
  applicantName: z.string().min(1, '请输入申请人姓名'),
  applicantRelation: z.string().min(1, '请选择与老人关系'),
  applicantPhone: z.string().min(11, '请输入有效手机号').max(11),
  applicantIdCard: z.string().min(15, '请输入有效身份证号').max(18),
  applicantAddress: z.string().min(1, '请输入申请人地址'),

  // 紧急联系人
  emergencyContact: z.string().min(1, '请输入紧急联系人姓名'),
  emergencyPhone: z.string().min(11, '请输入有效手机号').max(11),
  emergencyRelation: z.string().min(1, '请输入与老人关系'),

  // 健康和护理信息
  healthCondition: z.string().min(1, '请描述健康状况'),
  medicalHistory: z.string().optional(),
  currentMedications: z.string().optional(),
  careNeeds: z.string().min(1, '请描述护理需求'),
  specialRequirements: z.string().optional(),

  // 入住信息
  expectedAdmissionDate: z.date({
    required_error: '请选择预期入住日期',
  }),
  notes: z.string().optional(),
})

type AdmissionApplicationFormData = z.infer<typeof admissionApplicationSchema>

interface AdmissionApplicationFormProps {
  onSubmit: (data: AdmissionApplicationFormData) => Promise<void>
  initialData?: Partial<AdmissionApplicationFormData>
  isLoading?: boolean
  className?: string
}

export function AdmissionApplicationForm({
  onSubmit,
  initialData,
  isLoading = false,
  className,
}: AdmissionApplicationFormProps) {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])

  const form = useForm<AdmissionApplicationFormData>({
    resolver: zodResolver(admissionApplicationSchema),
    defaultValues: {
      elderGender: 1,
      ...initialData,
    },
  })

  const handleSubmit = async (data: AdmissionApplicationFormData) => {
    try {
      await onSubmit(data)
      toast.success('申请提交成功')
      form.reset()
    } catch (error) {
      toast.error('提交失败，请重试')
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setUploadedFiles(prev => [...prev, ...files])
  }

  return (
    <div className={cn('space-y-6', className)}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* 老人基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>老人基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="elderName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>老人姓名 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入老人姓名" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="elderAge"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>年龄 *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="请输入年龄"
                          {...field}
                          onChange={e =>
                            field.onChange(parseInt(e.target.value) || 0)
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="elderGender"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>性别 *</FormLabel>
                      <Select
                        onValueChange={value => field.onChange(parseInt(value))}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="请选择性别" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="1">男</SelectItem>
                          <SelectItem value="2">女</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="elderIdCard"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>身份证号 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入身份证号" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="elderPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系电话 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入联系电话" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* 申请人信息 */}
          <Card>
            <CardHeader>
              <CardTitle>申请人信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="applicantName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>申请人姓名 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入申请人姓名" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="applicantRelation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>与老人关系 *</FormLabel>
                      <Select onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="请选择关系" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="儿子">儿子</SelectItem>
                          <SelectItem value="女儿">女儿</SelectItem>
                          <SelectItem value="配偶">配偶</SelectItem>
                          <SelectItem value="其他亲属">其他亲属</SelectItem>
                          <SelectItem value="朋友">朋友</SelectItem>
                          <SelectItem value="其他">其他</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="applicantPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系电话 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入联系电话" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="applicantIdCard"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>身份证号 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入身份证号" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="applicantAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>联系地址 *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请输入详细地址"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* 紧急联系人 */}
          <Card>
            <CardHeader>
              <CardTitle>紧急联系人</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="emergencyContact"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系人姓名 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入联系人姓名" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="emergencyPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系电话 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入联系电话" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="emergencyRelation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>与老人关系 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入关系" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* 健康和护理信息 */}
          <Card>
            <CardHeader>
              <CardTitle>健康和护理信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="healthCondition"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>健康状况 *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请详细描述老人的健康状况"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="medicalHistory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>既往病史</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='请输入既往病史（如无请填写"无"）'
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="currentMedications"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>当前用药</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='请输入当前用药情况（如无请填写"无"）'
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="careNeeds"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>护理需求 *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请详细描述所需的护理服务"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="specialRequirements"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>特殊要求</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请输入特殊要求或备注"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* 入住信息 */}
          <Card>
            <CardHeader>
              <CardTitle>入住信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="expectedAdmissionDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>预期入住日期 *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? (
                                format(field.value, 'yyyy-MM-dd')
                              ) : (
                                <span>请选择日期</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={date =>
                              date < new Date() || date < new Date('1900-01-01')
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>备注</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="其他需要说明的情况"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* 文件上传 */}
          <Card>
            <CardHeader>
              <CardTitle>相关文件</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-center w-full">
                  <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className="w-8 h-8 mb-4 text-gray-500" />
                      <p className="mb-2 text-sm text-gray-500">
                        <span className="font-semibold">点击上传</span>{' '}
                        或拖拽文件到此处
                      </p>
                      <p className="text-xs text-gray-500">
                        支持 PDF、JPG、PNG 格式，单个文件不超过 10MB
                      </p>
                    </div>
                    <input
                      type="file"
                      className="hidden"
                      multiple
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={handleFileUpload}
                    />
                  </label>
                </div>

                {uploadedFiles.length > 0 && (
                  <div className="space-y-2">
                    <Label>已上传文件：</Label>
                    <div className="space-y-1">
                      {uploadedFiles.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded"
                        >
                          <span className="text-sm">{file.name}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              setUploadedFiles(prev =>
                                prev.filter((_, i) => i !== index)
                              )
                            }
                          >
                            删除
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => form.reset()}
            >
              重置
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              提交申请
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
