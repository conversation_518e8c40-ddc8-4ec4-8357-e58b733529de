'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ChevronRight, Home } from 'lucide-react'
import { motion } from 'framer-motion'
import { useTheme } from '@/hooks/use-theme'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ComponentType<{ className?: string }>
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[]
  className?: string
  showHome?: boolean
}

// 路由映射配置
const ROUTE_LABELS: Record<string, string> = {
  '/': '首页',
  '/marketing': '营销管理',
  '/marketing/consultation': '咨询接待',
  '/marketing/follow-up': '跟进记录',
  '/marketing/performance': '业绩统计',
  '/residence': '居住管理',
  '/residence/overview': '入住总览',
  '/residence/reservation': '预订管理',
  '/residence/checkin': '入住流程',
  '/residence/checkout': '退住管理',
  '/nursing': '护理服务',
  '/nursing/service': '在住服务',
  '/nursing/plan': '护理计划',
  '/nursing/record': '护理记录',
  '/nursing/dashboard': '照护看板',
  '/finance': '财务管理',
  '/finance/bill': '费用账单',
  '/finance/payment': '缴费管理',
  '/finance/query': '费用查询',
  '/finance/report': '财务报表',
  '/inventory': '库存管理',
  '/inventory/query': '库存查询',
  '/inventory/inbound': '入库管理',
  '/inventory/outbound': '出库管理',
  '/inventory/transfer': '调拨管理',
  '/system': '系统管理',
  '/system/users': '用户管理',
  '/system/roles': '角色管理',
  '/system/logs': '系统日志',
  '/system/config': '基础配置',
}

/**
 * 面包屑导航组件
 * 自动根据当前路径生成面包屑，支持自定义项目
 */
export function Breadcrumb({
  items,
  className,
  showHome = true,
}: BreadcrumbProps) {
  const pathname = usePathname()
  const { userPreferences } = useTheme()

  // 生成面包屑项目
  const generateBreadcrumbItems = (): BreadcrumbItem[] => {
    if (items) return items

    const pathSegments = pathname.split('/').filter(Boolean)
    const breadcrumbItems: BreadcrumbItem[] = []

    // 添加首页
    if (showHome && pathname !== '/') {
      breadcrumbItems.push({
        label: '首页',
        href: '/',
        icon: Home,
      })
    }

    // 生成路径面包屑
    let currentPath = ''
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`
      const label = ROUTE_LABELS[currentPath] || segment
      const isLast = index === pathSegments.length - 1

      breadcrumbItems.push({
        label,
        href: isLast ? undefined : currentPath,
      })
    })

    return breadcrumbItems
  }

  const breadcrumbItems = generateBreadcrumbItems()

  const itemVariants = {
    initial: { opacity: 0, x: -10 },
    animate: {
      opacity: 1,
      x: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.3 : 0,
        ease: [0.4, 0, 0.2, 1] as const,
      },
    },
  }

  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.2 : 0,
        staggerChildren: userPreferences.animationsEnabled ? 0.1 : 0,
      },
    },
  }

  if (breadcrumbItems.length === 0) return null

  return (
    <motion.nav
      variants={containerVariants}
      initial="initial"
      animate="animate"
      className={cn(
        'flex items-center space-x-1 text-sm text-muted-foreground',
        userPreferences.compactMode && 'text-xs',
        className
      )}
      aria-label="面包屑导航"
    >
      {breadcrumbItems.map((item, index) => (
        <motion.div
          key={`${item.href || item.label}-${index}`}
          variants={itemVariants}
          className="flex items-center"
        >
          {index > 0 && <ChevronRight className="mx-1 h-3 w-3 flex-shrink-0" />}

          {item.href ? (
            <Link
              href={item.href}
              className={cn(
                'flex items-center gap-1 hover:text-foreground transition-colors',
                'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-1 py-0.5'
              )}
            >
              {item.icon && <item.icon className="h-3 w-3 flex-shrink-0" />}
              <span className="truncate max-w-[150px] sm:max-w-[200px]">
                {item.label}
              </span>
            </Link>
          ) : (
            <span
              className={cn(
                'flex items-center gap-1 text-foreground font-medium',
                userPreferences.compactMode && 'font-normal'
              )}
            >
              {item.icon && <item.icon className="h-3 w-3 flex-shrink-0" />}
              <span className="truncate max-w-[150px] sm:max-w-[200px]">
                {item.label}
              </span>
            </span>
          )}
        </motion.div>
      ))}
    </motion.nav>
  )
}
