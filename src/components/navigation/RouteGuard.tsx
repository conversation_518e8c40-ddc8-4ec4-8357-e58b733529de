'use client'

import { useEffect, ReactNode } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/hooks/use-auth'
import { checkRoutePermissions } from '@/lib/routes'
import { motion } from 'framer-motion'
import { useTheme } from '@/hooks/use-theme'
import { AlertCircle } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'

interface RouteGuardProps {
  children: ReactNode
}

/**
 * 路由守卫组件
 * 检查用户权限并控制页面访问
 */
export function RouteGuard({ children }: RouteGuardProps) {
  const { user, isLoading, isAuthenticated } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const { userPreferences } = useTheme()

  useEffect(() => {
    // 如果正在加载，不做任何处理
    if (isLoading) return

    // 如果未认证且不在登录页面，跳转到登录页
    if (!isAuthenticated && pathname !== '/login') {
      router.push('/login')
      return
    }

    // 如果已认证但在登录页面，跳转到首页
    if (isAuthenticated && pathname === '/login') {
      router.push('/')
      return
    }

    // 检查路由权限
    if (isAuthenticated && user) {
      const hasPermission = checkRoutePermissions(
        pathname,
        user.permissions || []
      )
      if (!hasPermission) {
        // 可以跳转到无权限页面或显示错误信息
        console.warn(`用户无权限访问路径: ${pathname}`)
      }
    }
  }, [isLoading, isAuthenticated, pathname, router, user])

  // 加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'linear',
            repeatDelay: 0,
          }}
          className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full"
        />
      </div>
    )
  }

  // 未认证状态
  if (!isAuthenticated && pathname !== '/login') {
    return null // 将会被重定向到登录页
  }

  // 权限检查失败
  if (isAuthenticated && user) {
    const hasPermission = checkRoutePermissions(
      pathname,
      user.permissions || []
    )
    if (!hasPermission) {
      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: 1,
              y: 0,
              transition: {
                duration: userPreferences.animationsEnabled ? 0.4 : 0,
              },
            }}
            className="max-w-md w-full"
          >
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>访问被拒绝</AlertTitle>
              <AlertDescription className="mt-2">
                您没有权限访问此页面。请联系管理员获取相应权限。
              </AlertDescription>
              <div className="mt-4 flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.back()}
                >
                  返回上页
                </Button>
                <Button size="sm" onClick={() => router.push('/')}>
                  回到首页
                </Button>
              </div>
            </Alert>
          </motion.div>
        </div>
      )
    }
  }

  return <>{children}</>
}
