'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Search, X, Clock, ArrowRight } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useTheme } from '@/hooks/use-theme'
import { cn } from '@/lib/utils'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

interface SearchResult {
  id: string
  title: string
  description?: string
  category: string
  url: string
  icon?: React.ComponentType<{ className?: string }>
}

interface GlobalSearchProps {
  placeholder?: string
  className?: string
}

// 模拟搜索数据
const SEARCH_DATA: SearchResult[] = [
  {
    id: '1',
    title: '咨询接待',
    description: '管理客户咨询和接待记录',
    category: '营销管理',
    url: '/marketing/consultation',
  },
  {
    id: '2',
    title: '入住总览',
    description: '查看当前入住情况和统计信息',
    category: '居住管理',
    url: '/residence/overview',
  },
  {
    id: '3',
    title: '护理计划',
    description: '制定和管理老人护理计划',
    category: '护理服务',
    url: '/nursing/plan',
  },
  {
    id: '4',
    title: '费用账单',
    description: '管理老人费用账单和结算',
    category: '财务管理',
    url: '/finance/bill',
  },
  {
    id: '5',
    title: '库存查询',
    description: '查询物品库存情况',
    category: '库存管理',
    url: '/inventory/query',
  },
  {
    id: '6',
    title: '用户管理',
    description: '管理系统用户账号和权限',
    category: '系统管理',
    url: '/system/users',
  },
]

/**
 * 全局搜索组件
 * 支持快捷键调用、实时搜索和历史记录
 */
export function GlobalSearch({
  placeholder = '搜索功能、数据...',
  className,
}: GlobalSearchProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [selectedIndex, setSelectedIndex] = useState(-1)

  const router = useRouter()
  const { userPreferences } = useTheme()
  const inputRef = useRef<HTMLInputElement>(null)

  // 加载历史搜索记录
  useEffect(() => {
    const saved = localStorage.getItem('recent-searches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }
  }, [])

  // 保存搜索记录
  const saveSearch = (searchQuery: string) => {
    if (!searchQuery.trim()) return

    const updated = [
      searchQuery,
      ...recentSearches.filter(s => s !== searchQuery),
    ].slice(0, 5)
    setRecentSearches(updated)
    localStorage.setItem('recent-searches', JSON.stringify(updated))
  }

  // 搜索功能
  const performSearch = (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    const filtered = SEARCH_DATA.filter(
      item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.category.toLowerCase().includes(searchQuery.toLowerCase())
    )

    setResults(filtered)
  }

  // 处理搜索输入
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(query)
    }, 200)

    return () => clearTimeout(timeoutId)
  }, [query])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K 打开搜索
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setIsOpen(true)
      }

      // ESC 关闭搜索
      if (e.key === 'Escape') {
        setIsOpen(false)
      }

      // 在搜索框内的键盘导航
      if (isOpen) {
        if (e.key === 'ArrowDown') {
          e.preventDefault()
          setSelectedIndex(prev =>
            prev < results.length - 1 ? prev + 1 : prev
          )
        } else if (e.key === 'ArrowUp') {
          e.preventDefault()
          setSelectedIndex(prev => (prev > -1 ? prev - 1 : prev))
        } else if (e.key === 'Enter' && selectedIndex >= 0) {
          e.preventDefault()
          handleResultClick(results[selectedIndex])
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, results, selectedIndex])

  // 处理结果点击
  const handleResultClick = (result: SearchResult) => {
    saveSearch(query)
    setIsOpen(false)
    setQuery('')
    setSelectedIndex(-1)
    router.push(result.url)
  }

  // 处理历史搜索点击
  const handleRecentSearchClick = (searchQuery: string) => {
    setQuery(searchQuery)
    performSearch(searchQuery)
  }

  // 清除历史记录
  const clearRecentSearches = () => {
    setRecentSearches([])
    localStorage.removeItem('recent-searches')
  }

  const dialogVariants = {
    initial: { opacity: 0, scale: 0.95, y: -20 },
    animate: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.2 : 0,
        ease: [0.4, 0, 0.2, 1] as const,
      },
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: -20,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.15 : 0,
      },
    },
  }

  const resultVariants = {
    initial: { opacity: 0, y: 10 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.2 : 0,
      },
    },
  }

  return (
    <>
      {/* 搜索触发器 */}
      <div className={cn('relative', className)}>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder={placeholder}
            className="pl-10 pr-16 cursor-pointer"
            onClick={() => setIsOpen(true)}
            readOnly
          />
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
              <span className="text-xs">⌘</span>K
            </kbd>
          </div>
        </div>
      </div>

      {/* 搜索对话框 */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-2xl p-0">
          <motion.div
            variants={dialogVariants}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <DialogHeader className="px-4 py-3 border-b">
              <DialogTitle className="sr-only">全局搜索</DialogTitle>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  ref={inputRef}
                  placeholder={placeholder}
                  value={query}
                  onChange={e => setQuery(e.target.value)}
                  className="pl-10 pr-10 border-0 focus-visible:ring-0 text-base"
                  autoFocus
                />
                {query && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
                    onClick={() => setQuery('')}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </DialogHeader>

            <div className="max-h-96 overflow-y-auto">
              <AnimatePresence mode="wait">
                {query ? (
                  // 搜索结果
                  <motion.div
                    key="results"
                    variants={resultVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    className="p-2"
                  >
                    {results.length > 0 ? (
                      <div className="space-y-1">
                        {results.map((result, index) => (
                          <motion.div
                            key={result.id}
                            variants={resultVariants}
                            className={cn(
                              'flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors',
                              'hover:bg-accent hover:text-accent-foreground',
                              selectedIndex === index &&
                                'bg-accent text-accent-foreground'
                            )}
                            onClick={() => handleResultClick(result)}
                          >
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2">
                                <span className="font-medium truncate">
                                  {result.title}
                                </span>
                                <span className="text-xs text-muted-foreground bg-muted px-2 py-0.5 rounded">
                                  {result.category}
                                </span>
                              </div>
                              {result.description && (
                                <p className="text-sm text-muted-foreground truncate mt-1">
                                  {result.description}
                                </p>
                              )}
                            </div>
                            <ArrowRight className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          </motion.div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-8 text-center text-muted-foreground">
                        <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>未找到相关结果</p>
                      </div>
                    )}
                  </motion.div>
                ) : (
                  // 历史搜索
                  <motion.div
                    key="recent"
                    variants={resultVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    className="p-2"
                  >
                    {recentSearches.length > 0 ? (
                      <div className="space-y-1">
                        <div className="flex items-center justify-between px-3 py-2">
                          <span className="text-sm font-medium text-muted-foreground">
                            最近搜索
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 text-xs"
                            onClick={clearRecentSearches}
                          >
                            清除
                          </Button>
                        </div>
                        {recentSearches.map((search, index) => (
                          <div
                            key={index}
                            className="flex items-center gap-3 p-3 rounded-lg cursor-pointer hover:bg-accent hover:text-accent-foreground transition-colors"
                            onClick={() => handleRecentSearchClick(search)}
                          >
                            <Clock className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            <span className="flex-1 truncate">{search}</span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-8 text-center text-muted-foreground">
                        <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>开始搜索功能和数据</p>
                        <p className="text-xs mt-1">使用 ⌘K 快速打开搜索</p>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        </DialogContent>
      </Dialog>
    </>
  )
}
