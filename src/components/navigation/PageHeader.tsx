'use client'

import React, { ReactNode } from 'react'
import { motion } from 'framer-motion'
import { useTheme } from '@/hooks/use-theme'
import { cn } from '@/lib/utils'
import { Breadcrumb } from './Breadcrumb'

interface PageHeaderProps {
  title: string
  description?: string
  children?: ReactNode
  className?: string
  showBreadcrumb?: boolean
  breadcrumbItems?: Array<{
    label: string
    href?: string
    icon?: React.ComponentType<{ className?: string }>
  }>
}

/**
 * 页面头部组件
 * 包含页面标题、描述、面包屑导航和操作按钮区域
 */
export function PageHeader({
  title,
  description,
  children,
  className,
  showBreadcrumb = true,
  breadcrumbItems,
}: PageHeaderProps) {
  const { userPreferences } = useTheme()

  const headerVariants = {
    initial: { opacity: 0, y: -20 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.4 : 0,
        ease: [0.4, 0, 0.2, 1] as const,
      },
    },
  }

  const contentVariants = {
    initial: { opacity: 0, y: 10 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.3 : 0,
        ease: [0.4, 0, 0.2, 1] as const,
        delay: userPreferences.animationsEnabled ? 0.1 : 0,
      },
    },
  }

  return (
    <motion.div
      variants={headerVariants}
      initial="initial"
      animate="animate"
      className={cn(
        'space-y-4 pb-4 border-b border-border',
        userPreferences.compactMode && 'space-y-2 pb-2',
        className
      )}
    >
      {/* 面包屑导航 */}
      {showBreadcrumb && (
        <motion.div variants={contentVariants}>
          <Breadcrumb items={breadcrumbItems} />
        </motion.div>
      )}

      {/* 页面标题和操作区域 */}
      <motion.div
        variants={contentVariants}
        className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between"
      >
        <div className="space-y-1">
          <h1
            className={cn(
              'text-2xl font-bold tracking-tight text-foreground',
              userPreferences.compactMode && 'text-xl'
            )}
          >
            {title}
          </h1>
          {description && (
            <p
              className={cn(
                'text-muted-foreground',
                userPreferences.compactMode && 'text-sm'
              )}
            >
              {description}
            </p>
          )}
        </div>

        {/* 操作按钮区域 */}
        {children && (
          <div className="flex items-center gap-2 flex-shrink-0">
            {children}
          </div>
        )}
      </motion.div>
    </motion.div>
  )
}
