'use client'

import React, { useRef, useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { useTheme } from '@/hooks/use-theme'
import { cn } from '@/lib/utils'

// 动态导入 ECharts 以避免 SSR 问题
let echarts: any = null
if (typeof window !== 'undefined') {
  import('echarts').then(module => {
    echarts = module.default || module
  })
}

export interface ChartProps {
  option: any
  width?: string | number
  height?: string | number
  className?: string
  loading?: boolean
  onChartReady?: (chart: any) => void
  theme?: 'light' | 'dark'
}

/**
 * ECharts 图表组件
 * 支持主题切换、响应式和动画效果
 */
export function Chart({
  option,
  width = '100%',
  height = 400,
  className,
  loading = false,
  onChartReady,
  theme,
}: ChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<any>(null)
  const [isReady, setIsReady] = useState(false)
  const { resolvedTheme, userPreferences } = useTheme()

  const currentTheme = theme || resolvedTheme

  // 初始化图表
  useEffect(() => {
    if (!echarts || !chartRef.current) return

    // 创建图表实例
    chartInstance.current = echarts.init(
      chartRef.current,
      currentTheme === 'dark' ? 'dark' : undefined
    )

    setIsReady(true)
    onChartReady?.(chartInstance.current)

    // 监听窗口大小变化
    const handleResize = () => {
      chartInstance.current?.resize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [currentTheme, onChartReady])

  // 更新图表配置
  useEffect(() => {
    if (!chartInstance.current || !option) return

    const finalOption = {
      ...option,
      animation: userPreferences.animationsEnabled,
      animationDuration: userPreferences.animationsEnabled ? 1000 : 0,
      animationEasing: 'cubicOut',
    }

    chartInstance.current.setOption(finalOption, true)
  }, [option, userPreferences.animationsEnabled])

  // 处理加载状态
  useEffect(() => {
    if (!chartInstance.current) return

    if (loading) {
      chartInstance.current.showLoading('default', {
        text: '加载中...',
        color: 'hsl(var(--primary))',
        textColor: 'hsl(var(--foreground))',
        maskColor: 'hsl(var(--background) / 0.8)',
        zlevel: 0,
      })
    } else {
      chartInstance.current.hideLoading()
    }
  }, [loading])

  const chartVariants = {
    initial: { opacity: 0, scale: 0.95 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.4 : 0,
        ease: [0.4, 0, 0.2, 1] as const,
      },
    },
  }

  return (
    <motion.div
      variants={chartVariants}
      initial="initial"
      animate="animate"
      className={cn('relative', className)}
      style={{ width, height }}
    >
      <div ref={chartRef} className="w-full h-full" style={{ width, height }} />
      {!isReady && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80">
          <div className="text-sm text-muted-foreground">图表加载中...</div>
        </div>
      )}
    </motion.div>
  )
}

/**
 * 预定义的图表配置
 */
export const ChartConfigs = {
  // 柱状图配置
  bar: (data: any[], xAxisData: string[], title?: string) => ({
    title: title ? { text: title, left: 'center' } : undefined,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        type: 'bar',
        data,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
        },
      },
    ],
  }),

  // 折线图配置
  line: (data: any[], xAxisData: string[], title?: string) => ({
    title: title ? { text: title, left: 'center' } : undefined,
    tooltip: {
      trigger: 'axis',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        type: 'line',
        data,
        smooth: true,
        areaStyle: {
          opacity: 0.3,
        },
      },
    ],
  }),

  // 饼图配置
  pie: (data: Array<{ name: string; value: number }>, title?: string) => ({
    title: title ? { text: title, left: 'center' } : undefined,
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
    },
    series: [
      {
        name: '数据',
        type: 'pie',
        radius: '50%',
        data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }),

  // 仪表盘配置
  gauge: (value: number, title?: string, max: number = 100) => ({
    title: title ? { text: title, left: 'center' } : undefined,
    series: [
      {
        name: '指标',
        type: 'gauge',
        progress: {
          show: true,
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}%',
        },
        data: [
          {
            value,
            name: '完成率',
          },
        ],
        max,
      },
    ],
  }),
}

/**
 * 快速图表组件
 */
interface QuickChartProps extends Omit<ChartProps, 'option'> {
  type: 'bar' | 'line' | 'pie' | 'gauge'
  data: any
  title?: string
  xAxisData?: string[]
  max?: number
}

export function QuickChart({
  type,
  data,
  title,
  xAxisData = [],
  max,
  ...props
}: QuickChartProps) {
  const getOption = () => {
    switch (type) {
      case 'bar':
        return ChartConfigs.bar(data, xAxisData, title)
      case 'line':
        return ChartConfigs.line(data, xAxisData, title)
      case 'pie':
        return ChartConfigs.pie(data, title)
      case 'gauge':
        return ChartConfigs.gauge(data, title, max)
      default:
        return {}
    }
  }

  return <Chart option={getOption()} {...props} />
}
