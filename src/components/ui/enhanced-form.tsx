'use client'

import React, { ReactNode } from 'react'
import { motion } from 'framer-motion'
import {
  useForm,
  FormProvider,
  UseFormReturn,
  FieldValues,
} from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Loader2, Save, X } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useTheme } from '@/hooks/use-theme'
import { cn } from '@/lib/utils'

interface FormSection {
  title: string
  description?: string
  children: ReactNode
  className?: string
}

interface EnhancedFormProps<T extends FieldValues> {
  title?: string
  description?: string
  schema?: z.ZodSchema<T>
  defaultValues?: Partial<T>
  onSubmit: (data: T) => Promise<void> | void
  onCancel?: () => void
  children: ReactNode | ((form: UseFormReturn<T>) => ReactNode)
  submitText?: string
  cancelText?: string
  loading?: boolean
  className?: string
  showCard?: boolean
  sections?: FormSection[]
}

/**
 * 增强的表单组件
 * 支持分区、验证、加载状态和动画效果
 */
export function EnhancedForm<T extends FieldValues>({
  title,
  description,
  schema,
  defaultValues,
  onSubmit,
  onCancel,
  children,
  submitText = '保存',
  cancelText = '取消',
  loading = false,
  className,
  showCard = true,
  sections,
}: EnhancedFormProps<T>) {
  const { userPreferences } = useTheme()

  const form = useForm<T>({
    resolver: schema ? zodResolver(schema) : undefined,
    defaultValues,
  })

  const handleSubmit = async (data: T) => {
    try {
      await onSubmit(data)
    } catch (error) {
      console.error('表单提交失败:', error)
    }
  }

  const formVariants = {
    initial: { opacity: 0, y: 20 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.4 : 0,
        ease: [0.4, 0, 0.2, 1] as const,
      },
    },
  }

  const sectionVariants = {
    initial: { opacity: 0, x: -20 },
    animate: {
      opacity: 1,
      x: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.3 : 0,
      },
    },
  }

  const FormContent = () => (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* 表单头部 */}
        {(title || description) && (
          <motion.div variants={sectionVariants} className="space-y-2">
            {title && (
              <h2
                className={cn(
                  'text-2xl font-bold tracking-tight',
                  userPreferences.compactMode && 'text-xl'
                )}
              >
                {title}
              </h2>
            )}
            {description && (
              <p className="text-muted-foreground">{description}</p>
            )}
          </motion.div>
        )}

        {/* 表单内容 */}
        <div className="space-y-6">
          {sections ? (
            // 分区表单
            sections.map((section, index) => (
              <motion.div
                key={section.title}
                variants={sectionVariants}
                transition={{
                  delay: userPreferences.animationsEnabled ? index * 0.1 : 0,
                }}
              >
                <FormSection
                  title={section.title}
                  description={section.description}
                  className={section.className}
                >
                  {section.children}
                </FormSection>
              </motion.div>
            ))
          ) : (
            // 普通表单内容
            <motion.div variants={sectionVariants}>
              {typeof children === 'function' ? children(form) : children}
            </motion.div>
          )}
        </div>

        {/* 表单操作按钮 */}
        <motion.div
          variants={sectionVariants}
          className="flex items-center justify-end space-x-2 pt-4 border-t"
        >
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              <X className="mr-2 h-4 w-4" />
              {cancelText}
            </Button>
          )}
          <Button type="submit" disabled={loading}>
            {loading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Save className="mr-2 h-4 w-4" />
            )}
            {submitText}
          </Button>
        </motion.div>
      </form>
    </FormProvider>
  )

  return (
    <motion.div
      variants={formVariants}
      initial="initial"
      animate="animate"
      className={className}
    >
      {showCard ? (
        <Card>
          <CardContent className="p-6">
            <FormContent />
          </CardContent>
        </Card>
      ) : (
        <FormContent />
      )}
    </motion.div>
  )
}

/**
 * 表单分区组件
 */
export function FormSection({
  title,
  description,
  children,
  className,
}: FormSection) {
  const { userPreferences } = useTheme()

  return (
    <div className={cn('space-y-4', className)}>
      <div className="space-y-1">
        <h3
          className={cn(
            'text-lg font-semibold',
            userPreferences.compactMode && 'text-base'
          )}
        >
          {title}
        </h3>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      <div className="space-y-4 pl-4 border-l-2 border-muted">{children}</div>
    </div>
  )
}
