'use client'

import React, { ReactNode } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, AlertTriangle, CheckCircle, Info, AlertCircle } from 'lucide-react'

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { useTheme } from '@/hooks/use-theme'
import { cn } from '@/lib/utils'

interface EnhancedDialogProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  trigger?: ReactNode
  title: string
  description?: string
  children: ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  type?: 'default' | 'success' | 'warning' | 'error' | 'info'
  showCloseButton?: boolean
  closeOnOverlayClick?: boolean
  className?: string
}

/**
 * 增强的对话框组件
 * 支持不同尺寸、类型和动画效果
 */
export function EnhancedDialog({
  open,
  onOpenChange,
  trigger,
  title,
  description,
  children,
  size = 'md',
  type = 'default',
  showCloseButton = true,
  closeOnOverlayClick = true,
  className,
}: EnhancedDialogProps) {
  const { userPreferences } = useTheme()

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-[95vw] max-h-[95vh]',
  }

  const typeIcons = {
    default: null,
    success: CheckCircle,
    warning: AlertTriangle,
    error: AlertCircle,
    info: Info,
  }

  const typeColors = {
    default: '',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    error: 'text-red-600',
    info: 'text-blue-600',
  }

  const TypeIcon = typeIcons[type]

  const contentVariants = {
    initial: {
      opacity: 0,
      scale: 0.95,
      y: -20,
    },
    animate: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.2 : 0,
        ease: [0.4, 0, 0.2, 1] as const,
      },
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: -20,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.15 : 0,
      },
    },
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={closeOnOverlayClick}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}

      <AnimatePresence>
        {open && (
          <DialogContent
            className={cn(sizeClasses[size], 'p-0 overflow-hidden', className)}
            onPointerDownOutside={
              closeOnOverlayClick ? undefined : e => e.preventDefault()
            }
          >
            <motion.div
              variants={contentVariants}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              <DialogHeader className="px-6 py-4 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {TypeIcon && (
                      <TypeIcon className={cn('h-5 w-5', typeColors[type])} />
                    )}
                    <div>
                      <DialogTitle className="text-lg font-semibold">
                        {title}
                      </DialogTitle>
                      {description && (
                        <DialogDescription className="mt-1">
                          {description}
                        </DialogDescription>
                      )}
                    </div>
                  </div>

                  {showCloseButton && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => onOpenChange?.(false)}
                    >
                      <X className="h-4 w-4" />
                      <span className="sr-only">关闭</span>
                    </Button>
                  )}
                </div>
              </DialogHeader>

              <div className="px-6 py-4">{children}</div>
            </motion.div>
          </DialogContent>
        )}
      </AnimatePresence>
    </Dialog>
  )
}

/**
 * 确认对话框组件
 */
interface ConfirmDialogProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  trigger?: ReactNode
  title: string
  description?: string
  confirmText?: string
  cancelText?: string
  onConfirm: () => void | Promise<void>
  onCancel?: () => void
  type?: 'default' | 'warning' | 'error'
  loading?: boolean
}

export function ConfirmDialog({
  open,
  onOpenChange,
  trigger,
  title,
  description,
  confirmText = '确认',
  cancelText = '取消',
  onConfirm,
  onCancel,
  type = 'default',
  loading = false,
}: ConfirmDialogProps) {
  const handleConfirm = async () => {
    try {
      await onConfirm()
      onOpenChange?.(false)
    } catch (error) {
      console.error('确认操作失败:', error)
    }
  }

  const handleCancel = () => {
    onCancel?.()
    onOpenChange?.(false)
  }

  return (
    <EnhancedDialog
      open={open}
      onOpenChange={onOpenChange}
      trigger={trigger}
      title={title}
      description={description}
      type={type}
      size="sm"
    >
      <div className="flex items-center justify-end space-x-2 pt-4">
        <Button variant="outline" onClick={handleCancel} disabled={loading}>
          {cancelText}
        </Button>
        <Button
          variant={type === 'error' ? 'destructive' : 'default'}
          onClick={handleConfirm}
          disabled={loading}
        >
          {loading ? '处理中...' : confirmText}
        </Button>
      </div>
    </EnhancedDialog>
  )
}

/**
 * 信息对话框组件
 */
interface InfoDialogProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  trigger?: ReactNode
  title: string
  message: string
  type?: 'success' | 'warning' | 'error' | 'info'
  buttonText?: string
}

export function InfoDialog({
  open,
  onOpenChange,
  trigger,
  title,
  message,
  type = 'info',
  buttonText = '知道了',
}: InfoDialogProps) {
  return (
    <EnhancedDialog
      open={open}
      onOpenChange={onOpenChange}
      trigger={trigger}
      title={title}
      type={type}
      size="sm"
    >
      <div className="space-y-4">
        <p className="text-sm text-muted-foreground">{message}</p>
        <div className="flex justify-end">
          <Button onClick={() => onOpenChange?.(false)}>{buttonText}</Button>
        </div>
      </div>
    </EnhancedDialog>
  )
}
