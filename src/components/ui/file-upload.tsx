'use client'

import React, { useState, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Upload,
  X,
  File,
  Image as ImageIcon,
  FileText,
  Download,
  Eye,
  Trash2,
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useTheme } from '@/hooks/use-theme'
import { cn } from '@/lib/utils'

export interface FileItem {
  id: string
  file: File
  url?: string
  progress?: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

interface FileUploadProps {
  accept?: string
  multiple?: boolean
  maxSize?: number // MB
  maxFiles?: number
  onFilesChange?: (files: FileItem[]) => void
  onUpload?: (files: FileItem[]) => Promise<void>
  className?: string
  disabled?: boolean
  showPreview?: boolean
  dragDropText?: string
  browseText?: string
}

/**
 * 文件上传组件
 * 支持拖拽上传、预览、进度显示和多文件管理
 */
export function FileUpload({
  accept = '*/*',
  multiple = true,
  maxSize = 10,
  maxFiles = 5,
  onFilesChange,
  onUpload,
  className,
  disabled = false,
  showPreview = true,
  dragDropText = '拖拽文件到此处或点击选择',
  browseText = '选择文件',
}: FileUploadProps) {
  const [files, setFiles] = useState<FileItem[]>([])
  const [isDragOver, setIsDragOver] = useState(false)
  const [previewFile, setPreviewFile] = useState<FileItem | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { userPreferences } = useTheme()

  // 生成文件ID
  const generateFileId = () => Math.random().toString(36).substr(2, 9)

  // 验证文件
  const validateFile = (file: File): string | null => {
    if (file.size > maxSize * 1024 * 1024) {
      return `文件大小不能超过 ${maxSize}MB`
    }
    return null
  }

  // 获取文件图标
  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return ImageIcon
    } else if (file.type.includes('pdf') || file.type.includes('document')) {
      return FileText
    }
    return File
  }

  // 处理文件选择
  const handleFiles = useCallback(
    (selectedFiles: FileList | null) => {
      if (!selectedFiles || disabled) return

      const newFiles: FileItem[] = []
      const currentFileCount = files.length

      Array.from(selectedFiles).forEach((file, index) => {
        if (currentFileCount + newFiles.length >= maxFiles) {
          return
        }

        const error = validateFile(file)
        const fileItem: FileItem = {
          id: generateFileId(),
          file,
          url: URL.createObjectURL(file),
          status: error ? 'error' : 'pending',
          error,
        }

        newFiles.push(fileItem)
      })

      const updatedFiles = [...files, ...newFiles]
      setFiles(updatedFiles)
      onFilesChange?.(updatedFiles)
    },
    [files, maxFiles, maxSize, disabled, onFilesChange]
  )

  // 拖拽处理
  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      if (!disabled) {
        setIsDragOver(true)
      }
    },
    [disabled]
  )

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      setIsDragOver(false)
      if (!disabled) {
        handleFiles(e.dataTransfer.files)
      }
    },
    [handleFiles, disabled]
  )

  // 移除文件
  const removeFile = (id: string) => {
    const updatedFiles = files.filter(file => {
      if (file.id === id && file.url) {
        URL.revokeObjectURL(file.url)
      }
      return file.id !== id
    })
    setFiles(updatedFiles)
    onFilesChange?.(updatedFiles)
  }

  // 上传文件
  const handleUpload = async () => {
    if (!onUpload || files.length === 0) return

    const filesToUpload = files.filter(file => file.status === 'pending')
    if (filesToUpload.length === 0) return

    // 更新状态为上传中
    const updatedFiles = files.map(file =>
      filesToUpload.some(f => f.id === file.id)
        ? { ...file, status: 'uploading' as const, progress: 0 }
        : file
    )
    setFiles(updatedFiles)

    try {
      await onUpload(filesToUpload)

      // 更新状态为成功
      setFiles(prev =>
        prev.map(file =>
          filesToUpload.some(f => f.id === file.id)
            ? { ...file, status: 'success' as const, progress: 100 }
            : file
        )
      )
    } catch (error) {
      // 更新状态为失败
      setFiles(prev =>
        prev.map(file =>
          filesToUpload.some(f => f.id === file.id)
            ? {
                ...file,
                status: 'error' as const,
                error: error instanceof Error ? error.message : '上传失败',
              }
            : file
        )
      )
    }
  }

  const containerVariants = {
    initial: { opacity: 0, y: 20 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.3 : 0,
      },
    },
  }

  const fileItemVariants = {
    initial: { opacity: 0, x: -20 },
    animate: {
      opacity: 1,
      x: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.2 : 0,
      },
    },
    exit: {
      opacity: 0,
      x: 20,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.2 : 0,
      },
    },
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="initial"
      animate="animate"
      className={cn('space-y-4', className)}
    >
      {/* 上传区域 */}
      <div
        className={cn(
          'relative border-2 border-dashed rounded-lg p-6 transition-colors',
          isDragOver
            ? 'border-primary bg-primary/5'
            : 'border-muted-foreground/25 hover:border-muted-foreground/50',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <Upload className="h-10 w-10 text-muted-foreground" />
          <div className="space-y-2">
            <p className="text-sm font-medium">{dragDropText}</p>
            <p className="text-xs text-muted-foreground">
              支持 {accept === '*/*' ? '所有格式' : accept}，单个文件最大{' '}
              {maxSize}MB
              {multiple && `，最多 ${maxFiles} 个文件`}
            </p>
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled}
          >
            {browseText}
          </Button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={e => handleFiles(e.target.files)}
          className="hidden"
          disabled={disabled}
        />
      </div>

      {/* 文件列表 */}
      {files.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">
              已选择文件 ({files.length}/{maxFiles})
            </h4>
            {onUpload && files.some(f => f.status === 'pending') && (
              <Button size="sm" onClick={handleUpload}>
                <Upload className="mr-2 h-4 w-4" />
                上传文件
              </Button>
            )}
          </div>

          <div className="space-y-2 max-h-60 overflow-y-auto">
            <AnimatePresence>
              {files.map(fileItem => {
                const FileIcon = getFileIcon(fileItem.file)
                return (
                  <motion.div
                    key={fileItem.id}
                    variants={fileItemVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg"
                  >
                    <FileIcon className="h-5 w-5 text-muted-foreground flex-shrink-0" />

                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {fileItem.file.name}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {(fileItem.file.size / 1024 / 1024).toFixed(2)} MB
                      </p>

                      {fileItem.status === 'uploading' &&
                        fileItem.progress !== undefined && (
                          <Progress
                            value={fileItem.progress}
                            className="mt-2"
                          />
                        )}

                      {fileItem.error && (
                        <p className="text-xs text-destructive mt-1">
                          {fileItem.error}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center space-x-1">
                      {/* 预览按钮 */}
                      {showPreview &&
                        fileItem.file.type.startsWith('image/') && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setPreviewFile(fileItem)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}

                      {/* 下载按钮 */}
                      {fileItem.url && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const a = document.createElement('a')
                            a.href = fileItem.url!
                            a.download = fileItem.file.name
                            a.click()
                          }}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      )}

                      {/* 删除按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(fileItem.id)}
                        disabled={fileItem.status === 'uploading'}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* 状态指示器 */}
                    <div className="flex-shrink-0">
                      {fileItem.status === 'success' && (
                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                      )}
                      {fileItem.status === 'error' && (
                        <div className="w-2 h-2 bg-red-500 rounded-full" />
                      )}
                      {fileItem.status === 'uploading' && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                      )}
                    </div>
                  </motion.div>
                )
              })}
            </AnimatePresence>
          </div>
        </div>
      )}

      {/* 图片预览对话框 */}
      <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>{previewFile?.file.name}</DialogTitle>
          </DialogHeader>
          {previewFile?.url && (
            <div className="flex justify-center">
              <img
                src={previewFile.url}
                alt={previewFile.file.name}
                className="max-w-full max-h-[70vh] object-contain rounded-lg"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </motion.div>
  )
}
