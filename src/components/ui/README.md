# 通用组件库文档

本文档介绍了养老院管理系统中的通用组件库，基于 shadcn/ui 构建，提供了丰富的业务组件和增强功能。

## 组件概览

### 1. 数据表格 (DataTable)

增强的数据表格组件，支持排序、筛选、分页、搜索等功能。

```tsx
import { DataTable } from '@/components/ui/data-table'

const columns = [
  {
    accessorKey: 'name',
    header: '姓名',
  },
  {
    accessorKey: 'age',
    header: '年龄',
  },
]

<DataTable
  columns={columns}
  data={data}
  searchPlaceholder="搜索用户..."
  onRefresh={() => fetchData()}
  onExport={() => exportData()}
/>
```

**特性：**

- 全局搜索和列筛选
- 可配置的分页
- 列显示控制
- 导出功能
- 加载状态
- 响应式设计

### 2. 增强表单 (EnhancedForm)

支持分区、验证和动画效果的表单组件。

```tsx
import { EnhancedForm, FormSection } from '@/components/ui/enhanced-form'
import { z } from 'zod'

const schema = z.object({
  name: z.string().min(1, '姓名不能为空'),
  email: z.string().email('邮箱格式不正确'),
})

<EnhancedForm
  title="用户信息"
  schema={schema}
  onSubmit={handleSubmit}
  sections={[
    {
      title: '基本信息',
      children: <FormFields />
    }
  ]}
/>
```

**特性：**

- Zod 模式验证
- 分区表单布局
- 加载状态管理
- 动画效果
- 自动错误处理

### 3. 图表组件 (Chart)

基于 ECharts 的图表组件，支持主题切换和响应式。

```tsx
import { Chart, QuickChart, ChartConfigs } from '@/components/ui/chart'

// 使用预配置的快速图表
<QuickChart
  type="bar"
  data={[120, 200, 150, 80, 70]}
  xAxisData={['1月', '2月', '3月', '4月', '5月']}
  title="月度统计"
/>

// 使用自定义配置
<Chart
  option={ChartConfigs.pie(pieData, '数据分布')}
  height={400}
/>
```

**特性：**

- 多种图表类型（柱状图、折线图、饼图、仪表盘）
- 主题自动切换
- 响应式设计
- 加载状态
- 动画效果

### 4. 文件上传 (FileUpload)

支持拖拽、预览和进度显示的文件上传组件。

```tsx
import { FileUpload } from '@/components/ui/file-upload'

;<FileUpload
  accept="image/*"
  multiple={true}
  maxSize={10}
  maxFiles={5}
  onFilesChange={handleFilesChange}
  onUpload={handleUpload}
  showPreview={true}
/>
```

**特性：**

- 拖拽上传
- 文件预览
- 上传进度
- 文件验证
- 多文件管理
- 图片预览对话框

### 5. 增强对话框 (EnhancedDialog)

支持多种类型和尺寸的对话框组件。

```tsx
import {
  EnhancedDialog,
  ConfirmDialog,
  InfoDialog
} from '@/components/ui/enhanced-dialog'

// 基础对话框
<EnhancedDialog
  title="编辑用户"
  description="修改用户基本信息"
  size="lg"
  type="default"
>
  <UserForm />
</EnhancedDialog>

// 确认对话框
<ConfirmDialog
  title="删除确认"
  description="确定要删除这个用户吗？此操作不可撤销。"
  type="error"
  onConfirm={handleDelete}
/>

// 信息对话框
<InfoDialog
  title="操作成功"
  message="用户信息已成功更新"
  type="success"
/>
```

**特性：**

- 多种尺寸（sm, md, lg, xl, full）
- 多种类型（default, success, warning, error, info）
- 动画效果
- 可配置的关闭行为

## 布局组件

### 1. 响应式容器 (ResponsiveContainer)

提供一致的响应式布局容器。

```tsx
import { ResponsiveContainer } from '@/components/layout'

;<ResponsiveContainer maxWidth="7xl" padding="md" animate={true}>
  <YourContent />
</ResponsiveContainer>
```

### 2. 响应式网格 (ResponsiveGrid)

灵活的响应式网格布局组件。

```tsx
import { ResponsiveGrid } from '@/components/layout'

;<ResponsiveGrid
  cols={{ default: 1, sm: 2, md: 3, lg: 4 }}
  gap="md"
  staggerChildren={true}
>
  {items.map(item => (
    <GridItem key={item.id} {...item} />
  ))}
</ResponsiveGrid>
```

## 导航组件

### 1. 面包屑导航 (Breadcrumb)

自动生成的面包屑导航组件。

```tsx
import { Breadcrumb } from '@/components/navigation'

;<Breadcrumb showHome={true} />
```

### 2. 页面头部 (PageHeader)

包含标题、描述和面包屑的页面头部组件。

```tsx
import { PageHeader } from '@/components/navigation'

;<PageHeader
  title="用户管理"
  description="管理系统用户账号和权限"
  showBreadcrumb={true}
>
  <Button>添加用户</Button>
</PageHeader>
```

### 3. 全局搜索 (GlobalSearch)

支持快捷键和历史记录的全局搜索组件。

```tsx
import { GlobalSearch } from '@/components/navigation'

;<GlobalSearch placeholder="搜索功能、数据..." />
```

## 主题和用户偏好

所有组件都支持以下用户偏好设置：

- **主题切换**：明亮模式、暗黑模式、跟随系统
- **紧凑模式**：减少间距和字体大小
- **动画控制**：启用/禁用动画效果
- **语言设置**：中文/英文界面

## 使用指南

### 1. 导入组件

```tsx
// 从对应的模块导入组件
import { DataTable } from '@/components/ui/data-table'
import { EnhancedForm } from '@/components/ui/enhanced-form'
import { Chart } from '@/components/ui/chart'
```

### 2. 主题集成

组件会自动读取用户的主题偏好设置：

```tsx
import { useTheme } from '@/hooks/use-theme'

const { userPreferences, resolvedTheme } = useTheme()
```

### 3. 响应式设计

所有组件都内置了响应式设计，会根据屏幕尺寸自动调整：

- **移动端**：简化布局，优化触摸操作
- **平板端**：平衡的布局和交互
- **桌面端**：完整功能和最佳体验

### 4. 无障碍支持

组件遵循 WCAG 2.1 无障碍标准：

- 键盘导航支持
- 屏幕阅读器友好
- 高对比度模式支持
- 焦点管理

## 最佳实践

### 1. 性能优化

- 使用 `React.memo` 包装重复渲染的组件
- 合理使用 `useMemo` 和 `useCallback`
- 大数据集使用虚拟滚动

### 2. 用户体验

- 提供加载状态反馈
- 使用适当的动画效果
- 保持一致的交互模式

### 3. 错误处理

- 提供友好的错误信息
- 实现错误边界
- 支持重试机制

## 扩展开发

### 1. 创建新组件

```tsx
'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useTheme } from '@/hooks/use-theme'
import { cn } from '@/lib/utils'

interface MyComponentProps {
  // 定义属性
}

export function MyComponent({ ...props }: MyComponentProps) {
  const { userPreferences } = useTheme()

  // 组件逻辑

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className={cn('base-styles', className)}
    >
      {/* 组件内容 */}
    </motion.div>
  )
}
```

### 2. 主题适配

确保新组件支持主题切换和用户偏好：

```tsx
// 使用 CSS 变量
className = 'bg-background text-foreground'

// 响应用户偏好
{
  userPreferences.compactMode && 'compact-styles'
}
{
  userPreferences.animationsEnabled && 'animate-in'
}
```

### 3. 类型安全

使用 TypeScript 确保类型安全：

```tsx
interface ComponentProps {
  required: string
  optional?: number
  callback: (value: string) => void
}
```

这个组件库为养老院管理系统提供了完整的 UI 组件解决方案，支持现代化的用户体验和无障碍访问。
