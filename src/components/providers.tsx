'use client'

import { ReactNode, useEffect } from 'react'
import { useTheme } from '@/hooks/use-theme'
import { StoreProvider, useStoreInitialization } from '@/store/store-provider'

interface ProvidersProps {
  children: ReactNode
}

/**
 * 主题和用户偏好初始化组件
 */
function ThemeInitializer() {
  const { theme, setTheme, userPreferences } = useTheme()

  useEffect(() => {
    // 初始化主题
    setTheme(theme)

    // 初始化用户偏好设置
    const root = document.documentElement

    if (userPreferences.compactMode) {
      root.classList.add('compact-mode')
    }

    if (!userPreferences.animationsEnabled) {
      root.classList.add('reduce-motion')
    }

    root.setAttribute(
      'lang',
      userPreferences.language === 'zh-CN' ? 'zh-CN' : 'en-US'
    )
  }, [theme, setTheme, userPreferences])

  return null
}

/**
 * Store 初始化组件
 */
function StoreInitializer() {
  useStoreInitialization()
  return null
}

/**
 * 应用程序提供者组件
 * 包装所有全局状态管理和上下文提供者
 */
export function Providers({ children }: ProvidersProps) {
  return (
    <StoreProvider>
      <ThemeInitializer />
      <StoreInitializer />
      {children}
    </StoreProvider>
  )
}
