'use client'

import { ReactNode } from 'react'
import { useRole } from '@/hooks/use-auth'

interface RoleGuardProps {
  children: ReactNode
  role?: string
  roles?: string[]
  fallback?: ReactNode
  loading?: ReactNode
}

/**
 * 角色守卫组件
 * 根据用户角色决定是否渲染子组件
 */
export function RoleGuard({
  children,
  role,
  roles = [],
  fallback = null,
  loading = null,
}: RoleGuardProps) {
  const { hasRole, hasAnyRole, loading: isLoading } = useRole()

  if (isLoading) {
    return <>{loading}</>
  }

  // 单个角色检查
  if (role) {
    return hasRole(role) ? <>{children}</> : <>{fallback}</>
  }

  // 多个角色检查
  if (roles.length > 0) {
    return hasAnyRole(roles) ? <>{children}</> : <>{fallback}</>
  }

  // 没有指定角色要求，直接渲染
  return <>{children}</>
}
