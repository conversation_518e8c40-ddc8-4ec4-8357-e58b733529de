'use client'

import { ReactNode, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/hooks/use-auth'
import { Skeleton } from '@/components/ui/skeleton'

interface RouteGuardProps {
  children: ReactNode
  fallback?: ReactNode
  loading?: ReactNode
}

/**
 * 路由守卫组件
 * 根据用户权限决定是否允许访问当前路由
 */
export function RouteGuard({
  children,
  fallback = (
    <div className="p-8 text-center text-gray-500">您没有权限访问此页面</div>
  ),
  loading = <RouteGuardSkeleton />,
}: RouteGuardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated, canAccessRoute, isLoading } = useAuth()

  useEffect(() => {
    // 如果用户未登录，重定向到登录页
    if (!isLoading && !isAuthenticated) {
      const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`
      router.push(loginUrl)
      return
    }

    // 如果用户已登录但没有权限访问当前路由，显示无权限页面
    if (!isLoading && isAuthenticated && !canAccessRoute(pathname)) {
      // 可以选择重定向到无权限页面或显示错误信息
      console.warn(`用户没有权限访问路由: ${pathname}`)
    }
  }, [isLoading, isAuthenticated, canAccessRoute, pathname, router])

  // 加载中状态
  if (isLoading) {
    return <>{loading}</>
  }

  // 未登录状态
  if (!isAuthenticated) {
    return null // 将重定向到登录页
  }

  // 没有权限访问当前路由
  if (!canAccessRoute(pathname)) {
    return <>{fallback}</>
  }

  // 有权限访问，渲染子组件
  return <>{children}</>
}

/**
 * 路由守卫加载骨架屏
 */
function RouteGuardSkeleton() {
  return (
    <div className="p-6 space-y-4">
      <Skeleton className="h-8 w-48" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    </div>
  )
}
