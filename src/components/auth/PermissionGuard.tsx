'use client'

import { ReactNode } from 'react'
import { usePermissions } from '@/hooks/use-auth'

interface PermissionGuardProps {
  children: ReactNode
  permission?: string
  permissions?: string[]
  requireAll?: boolean
  fallback?: ReactNode
  loading?: ReactNode
}

/**
 * 权限守卫组件
 * 根据用户权限决定是否渲染子组件
 */
export function PermissionGuard({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback = null,
  loading = null,
}: PermissionGuardProps) {
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    loading: isLoading,
  } = usePermissions()

  if (isLoading) {
    return <>{loading}</>
  }

  // 单个权限检查
  if (permission) {
    return hasPermission(permission) ? <>{children}</> : <>{fallback}</>
  }

  // 多个权限检查
  if (permissions.length > 0) {
    const hasRequiredPermissions = requireAll
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions)

    return hasRequiredPermissions ? <>{children}</> : <>{fallback}</>
  }

  // 没有指定权限要求，直接渲染
  return <>{children}</>
}
