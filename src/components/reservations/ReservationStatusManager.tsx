'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { useReservations } from '@/hooks/use-reservations'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Calendar as CalendarIcon,
  User,
  Phone,
  Home,
  ArrowRight,
  Bell,
  Settings,
  Loader2,
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface ReservationStatusManagerProps {
  reservation: any
  onStatusChange?: () => void
}

export function ReservationStatusManager({
  reservation,
  onStatusChange,
}: ReservationStatusManagerProps) {
  const { toast } = useToast()
  const { updateReservation, isLoading } = useReservations()

  const [showStatusDialog, setShowStatusDialog] = useState(false)
  const [newStatus, setNewStatus] = useState<number>(reservation.status)
  const [checkInDate, setCheckInDate] = useState<Date | undefined>()
  const [checkOutDate, setCheckOutDate] = useState<Date | undefined>()

  // 状态配置
  const statusConfig = {
    1: {
      name: '待确认',
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      icon: Clock,
      description: '预订已提交，等待确认',
      nextActions: [
        { status: 2, name: '确认预订', color: 'blue' },
        { status: 4, name: '取消预订', color: 'red' },
      ],
    },
    2: {
      name: '已确认',
      color: 'bg-blue-100 text-blue-800 border-blue-200',
      icon: CheckCircle,
      description: '预订已确认，等待入住',
      nextActions: [
        { status: 3, name: '办理入住', color: 'green' },
        { status: 4, name: '取消预订', color: 'red' },
      ],
    },
    3: {
      name: '已入住',
      color: 'bg-green-100 text-green-800 border-green-200',
      icon: CheckCircle,
      description: '已成功入住',
      nextActions: [],
    },
    4: {
      name: '已取消',
      color: 'bg-red-100 text-red-800 border-red-200',
      icon: XCircle,
      description: '预订已取消',
      nextActions: [],
    },
    5: {
      name: '已过期',
      color: 'bg-gray-100 text-gray-800 border-gray-200',
      icon: AlertTriangle,
      description: '预订已过期',
      nextActions: [{ status: 4, name: '标记取消', color: 'red' }],
    },
  }

  const currentConfig =
    statusConfig[reservation.status as keyof typeof statusConfig]
  const IconComponent = currentConfig.icon

  // 更新状态
  const handleStatusUpdate = async () => {
    try {
      const updateData: any = { status: newStatus }

      // 如果是办理入住，需要设置实际入住日期
      if (newStatus === 3) {
        if (!checkInDate) {
          toast({
            title: '请选择入住日期',
            description: '办理入住时必须选择实际入住日期',
            variant: 'destructive',
          })
          return
        }
        updateData.actualCheckInDate = format(checkInDate, 'yyyy-MM-dd')
      }

      // 如果设置了退房日期
      if (checkOutDate) {
        updateData.actualCheckOutDate = format(checkOutDate, 'yyyy-MM-dd')
      }

      await updateReservation(reservation.id, updateData)

      setShowStatusDialog(false)
      onStatusChange?.()

      toast({
        title: '状态更新成功',
        description: `预订状态已更新为：${statusConfig[newStatus as keyof typeof statusConfig].name}`,
      })
    } catch (error) {
      toast({
        title: '更新失败',
        description: '状态更新失败，请重试',
        variant: 'destructive',
      })
    }
  }

  // 获取状态进度
  const getStatusProgress = () => {
    const statusOrder = [1, 2, 3] // 正常流程：待确认 -> 已确认 -> 已入住
    const currentIndex = statusOrder.indexOf(reservation.status)
    return currentIndex >= 0
      ? ((currentIndex + 1) / statusOrder.length) * 100
      : 0
  }

  // 检查是否需要提醒
  const needsReminder = () => {
    const today = new Date()
    const checkInDate = new Date(reservation.expectedCheckInDate)
    const daysDiff = Math.ceil(
      (checkInDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
    )

    return reservation.status === 2 && daysDiff <= 3 && daysDiff >= 0
  }

  return (
    <div className="space-y-4">
      {/* 当前状态卡片 */}
      <Card className={`border-2 ${currentConfig.color}`}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <IconComponent className="h-6 w-6" />
              <span>当前状态：{currentConfig.name}</span>
            </div>
            {needsReminder() && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <Bell className="h-3 w-3" />
                即将入住
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600 mb-4">
            {currentConfig.description}
          </p>

          {/* 状态进度条 */}
          {reservation.status <= 3 && (
            <div className="mb-4">
              <div className="flex justify-between text-xs text-gray-500 mb-1">
                <span>待确认</span>
                <span>已确认</span>
                <span>已入住</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <motion.div
                  className="bg-blue-600 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${getStatusProgress()}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
          )}

          {/* 预订信息摘要 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-400" />
              <span>{reservation.contactName}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-gray-400" />
              <span>{reservation.contactPhone}</span>
            </div>
            <div className="flex items-center gap-2">
              <Home className="h-4 w-4 text-gray-400" />
              <span>{reservation.room?.roomNumber || '未分配'}</span>
            </div>
          </div>

          {/* 日期信息 */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-gray-500">预期入住：</span>
                <span className="font-medium">
                  {format(
                    new Date(reservation.expectedCheckInDate),
                    'yyyy年MM月dd日',
                    { locale: zhCN }
                  )}
                </span>
              </div>
              {reservation.expectedCheckOutDate && (
                <div>
                  <span className="text-gray-500">预期退房：</span>
                  <span className="font-medium">
                    {format(
                      new Date(reservation.expectedCheckOutDate),
                      'yyyy年MM月dd日',
                      { locale: zhCN }
                    )}
                  </span>
                </div>
              )}
              {reservation.actualCheckInDate && (
                <div>
                  <span className="text-gray-500">实际入住：</span>
                  <span className="font-medium text-green-600">
                    {format(
                      new Date(reservation.actualCheckInDate),
                      'yyyy年MM月dd日',
                      { locale: zhCN }
                    )}
                  </span>
                </div>
              )}
              {reservation.actualCheckOutDate && (
                <div>
                  <span className="text-gray-500">实际退房：</span>
                  <span className="font-medium text-red-600">
                    {format(
                      new Date(reservation.actualCheckOutDate),
                      'yyyy年MM月dd日',
                      { locale: zhCN }
                    )}
                  </span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 状态操作 */}
      {currentConfig.nextActions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              状态操作
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {currentConfig.nextActions.map(action => (
                <Dialog
                  key={action.status}
                  open={showStatusDialog && newStatus === action.status}
                  onOpenChange={open => {
                    setShowStatusDialog(open)
                    if (open) setNewStatus(action.status)
                  }}
                >
                  <DialogTrigger asChild>
                    <Button
                      variant={
                        action.color === 'red'
                          ? 'destructive'
                          : action.color === 'green'
                            ? 'default'
                            : 'outline'
                      }
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <ArrowRight className="h-4 w-4" />
                      {action.name}
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>{action.name}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          确定要将预订状态更改为"
                          {
                            statusConfig[
                              action.status as keyof typeof statusConfig
                            ].name
                          }
                          "吗？
                        </AlertDescription>
                      </Alert>

                      {/* 办理入住时需要选择实际入住日期 */}
                      {action.status === 3 && (
                        <div className="space-y-4">
                          <div>
                            <label className="text-sm font-medium">
                              实际入住日期 *
                            </label>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="w-full justify-start text-left font-normal mt-1"
                                >
                                  {checkInDate ? (
                                    format(checkInDate, 'yyyy年MM月dd日', {
                                      locale: zhCN,
                                    })
                                  ) : (
                                    <span>选择入住日期</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <Calendar
                                  mode="single"
                                  selected={checkInDate}
                                  onSelect={setCheckInDate}
                                  disabled={date => date > new Date()}
                                  initialFocus
                                  locale={zhCN}
                                />
                              </PopoverContent>
                            </Popover>
                          </div>

                          <div>
                            <label className="text-sm font-medium">
                              实际退房日期（可选）
                            </label>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="w-full justify-start text-left font-normal mt-1"
                                >
                                  {checkOutDate ? (
                                    format(checkOutDate, 'yyyy年MM月dd日', {
                                      locale: zhCN,
                                    })
                                  ) : (
                                    <span>选择退房日期（可选）</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <Calendar
                                  mode="single"
                                  selected={checkOutDate}
                                  onSelect={setCheckOutDate}
                                  disabled={date =>
                                    date > new Date() ||
                                    (checkInDate && date <= checkInDate)
                                  }
                                  initialFocus
                                  locale={zhCN}
                                />
                              </PopoverContent>
                            </Popover>
                          </div>
                        </div>
                      )}

                      <div className="flex justify-end gap-3">
                        <Button
                          variant="outline"
                          onClick={() => setShowStatusDialog(false)}
                        >
                          取消
                        </Button>
                        <Button
                          onClick={handleStatusUpdate}
                          disabled={
                            isLoading || (action.status === 3 && !checkInDate)
                          }
                          variant={
                            action.color === 'red' ? 'destructive' : 'default'
                          }
                        >
                          {isLoading && (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          )}
                          确认{action.name}
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 提醒信息 */}
      <AnimatePresence>
        {needsReminder() && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <Alert className="border-orange-200 bg-orange-50">
              <Bell className="h-4 w-4" />
              <AlertDescription>
                <strong>入住提醒：</strong>
                该预订的预期入住日期为{' '}
                {format(new Date(reservation.expectedCheckInDate), 'MM月dd日', {
                  locale: zhCN,
                })}
                ， 距离现在还有{' '}
                {Math.ceil(
                  (new Date(reservation.expectedCheckInDate).getTime() -
                    new Date().getTime()) /
                    (1000 * 60 * 60 * 24)
                )}{' '}
                天， 请及时联系客户确认入住安排。
              </AlertDescription>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
