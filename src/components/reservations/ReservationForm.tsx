'use client'

import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useReservations } from '@/hooks/use-reservations'
import { useToast } from '@/hooks/use-toast'
import { 
  Calendar as CalendarIcon,
  User,
  Phone,
  Home,
  AlertCircle,
  CheckCircle,
  Search,
  Loader2
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 表单验证 schema
const reservationFormSchema = z.object({
  elderInfoId: z.string().optional(),
  roomId: z.string().min(1, '请选择房间'),
  expectedCheckInDate: z.date({
    required_error: '请选择预期入住日期',
  }),
  expectedCheckOutDate: z.date().optional(),
  reservationType: z.number().min(1).max(3),
  contactName: z.string().min(1, '请输入联系人姓名'),
  contactPhone: z.string().min(1, '请输入联系电话').regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码'),
  contactRelation: z.string().optional(),
  specialRequirements: z.string().optional(),
  depositAmount: z.number().min(0, '押金金额不能为负数').optional(),
  depositPaid: z.boolean().default(false),
  notes: z.string().optional(),
})

type ReservationFormData = z.infer<typeof reservationFormSchema>

interface ReservationFormProps {
  initialData?: Partial<ReservationFormData>
  reservationId?: string
  onSuccess?: () => void
  onCancel?: () => void
}

export function ReservationForm({
  initialData,
  reservationId,
  onSuccess,
  onCancel,
}: ReservationFormProps) {
  const { toast } = useToast()
  const { createReservation, updateReservation, checkRoomAvailability, isLoading } = useReservations()

  // 状态管理
  const [availableRooms, setAvailableRooms] = useState<any[]>([])
  const [selectedRoom, setSelectedRoom] = useState<any>(null)
  const [checkingAvailability, setCheckingAvailability] = useState(false)
  const [availabilityChecked, setAvailabilityChecked] = useState(false)

  // 表单初始化
  const form = useForm<ReservationFormData>({
    resolver: zodResolver(reservationFormSchema),
    defaultValues: {
      reservationType: 1,
      depositPaid: false,
      ...initialData,
    },
  })

  const { watch, setValue, getValues } = form

  // 监听日期变化，自动检查房间可用性
  const watchedCheckInDate = watch('expectedCheckInDate')
  const watchedCheckOutDate = watch('expectedCheckOutDate')

  useEffect(() => {
    if (watchedCheckInDate) {
      checkAvailability()
    }
  }, [watchedCheckInDate, watchedCheckOutDate])

  // 检查房间可用性
  const checkAvailability = async () => {
    if (!watchedCheckInDate) return

    setCheckingAvailability(true)
    setAvailabilityChecked(false)

    try {
      const params = {
        checkInDate: format(watchedCheckInDate, 'yyyy-MM-dd'),
        checkOutDate: watchedCheckOutDate ? format(watchedCheckOutDate, 'yyyy-MM-dd') : undefined,
        excludeReservationId: reservationId,
      }

      const result = await checkRoomAvailability(params)
      setAvailableRooms(result.rooms || [])
      setAvailabilityChecked(true)

      // 如果当前选择的房间不可用，清除选择
      const currentRoomId = getValues('roomId')
      if (currentRoomId) {
        const currentRoom = result.rooms?.find((room: any) => room.id === currentRoomId)
        if (!currentRoom?.isAvailable) {
          setValue('roomId', '')
          setSelectedRoom(null)
          toast({
            title: '房间不可用',
            description: '当前选择的房间在指定日期不可用，请重新选择',
            variant: 'destructive',
          })
        }
      }
    } catch (error) {
      toast({
        title: '检查失败',
        description: '检查房间可用性失败，请重试',
        variant: 'destructive',
      })
    } finally {
      setCheckingAvailability(false)
    }
  }

  // 选择房间
  const handleRoomSelect = (roomId: string) => {
    const room = availableRooms.find(r => r.id === roomId)
    if (room && room.isAvailable) {
      setValue('roomId', roomId)
      setSelectedRoom(room)
    }
  }

  // 提交表单
  const onSubmit = async (data: ReservationFormData) => {
    try {
      const submitData = {
        ...data,
        expectedCheckInDate: format(data.expectedCheckInDate, 'yyyy-MM-dd'),
        expectedCheckOutDate: data.expectedCheckOutDate 
          ? format(data.expectedCheckOutDate, 'yyyy-MM-dd') 
          : undefined,
      }

      if (reservationId) {
        await updateReservation(reservationId, submitData)
      } else {
        await createReservation(submitData)
      }

      onSuccess?.()
    } catch (error) {
      toast({
        title: '提交失败',
        description: reservationId ? '更新预订失败，请重试' : '创建预订失败，请重试',
        variant: 'destructive',
      })
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              基本信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="contactName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>联系人姓名 *</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入联系人姓名" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contactPhone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>联系电话 *</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入联系电话" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contactRelation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>与老人关系</FormLabel>
                    <FormControl>
                      <Input placeholder="如：子女、配偶等" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reservationType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>预订类型</FormLabel>
                    <Select value={field.value?.toString()} onValueChange={(value) => field.onChange(parseInt(value))}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择预订类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="1">长期入住</SelectItem>
                        <SelectItem value="2">短期体验</SelectItem>
                        <SelectItem value="3">临时住宿</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* 入住信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CalendarIcon className="h-5 w-5" />
              入住信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="expectedCheckInDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>预期入住日期 *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className="w-full pl-3 text-left font-normal"
                          >
                            {field.value ? (
                              format(field.value, 'yyyy年MM月dd日', { locale: zhCN })
                            ) : (
                              <span>选择入住日期</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date()}
                          initialFocus
                          locale={zhCN}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expectedCheckOutDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>预期退房日期</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className="w-full pl-3 text-left font-normal"
                          >
                            {field.value ? (
                              format(field.value, 'yyyy年MM月dd日', { locale: zhCN })
                            ) : (
                              <span>选择退房日期（可选）</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => 
                            date < new Date() || 
                            (watchedCheckInDate && date <= watchedCheckInDate)
                          }
                          initialFocus
                          locale={zhCN}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 房间可用性检查状态 */}
            {watchedCheckInDate && (
              <div className="mt-4">
                {checkingAvailability ? (
                  <Alert>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <AlertDescription>
                      正在检查房间可用性...
                    </AlertDescription>
                  </Alert>
                ) : availabilityChecked ? (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      房间可用性检查完成，共找到 {availableRooms.filter(r => r.isAvailable).length} 个可用房间
                    </AlertDescription>
                  </Alert>
                ) : null}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 房间选择 */}
        {availabilityChecked && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Home className="h-5 w-5" />
                房间选择
              </CardTitle>
            </CardHeader>
            <CardContent>
              {availableRooms.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {availableRooms.map((room) => (
                    <motion.div
                      key={room.id}
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className={`border rounded-lg p-4 cursor-pointer transition-all ${
                        room.isAvailable
                          ? selectedRoom?.id === room.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                          : 'border-red-200 bg-red-50 cursor-not-allowed opacity-60'
                      }`}
                      onClick={() => room.isAvailable && handleRoomSelect(room.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{room.roomNumber}</h4>
                        <Badge variant={room.isAvailable ? 'default' : 'destructive'}>
                          {room.isAvailable ? '可用' : '不可用'}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 space-y-1">
                        <div>类型：{room.roomTypeName}</div>
                        <div>楼层：{room.floor}楼</div>
                        <div>容量：{room.capacity}人</div>
                        <div>可用床位：{room.availableBeds}个</div>
                        <div>月费：¥{room.monthlyRate}</div>
                      </div>
                      {!room.isAvailable && (
                        <div className="text-xs text-red-600 mt-2">
                          {room.hasReservationConflict && '时间冲突'}
                          {room.isRoomFull && '房间已满'}
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Home className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>指定日期没有可用房间</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* 其他信息 */}
        <Card>
          <CardHeader>
            <CardTitle>其他信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="specialRequirements"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>特殊要求</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="请描述特殊要求或注意事项"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="depositAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>押金金额</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="depositPaid"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        押金已支付
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="其他备注信息"
                      className="min-h-[60px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* 操作按钮 */}
        <div className="flex justify-end gap-3">
          <Button type="button" variant="outline" onClick={onCancel}>
            取消
          </Button>
          <Button type="submit" disabled={isLoading || !form.getValues('roomId')}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {reservationId ? '更新预订' : '创建预订'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
