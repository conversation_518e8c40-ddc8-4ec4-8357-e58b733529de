'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useReservations } from '@/hooks/use-reservations'
import { useToast } from '@/hooks/use-toast'
import { 
  Bell,
  Clock,
  Calendar,
  User,
  Phone,
  Home,
  AlertTriangle,
  CheckCircle,
  X,
  RefreshCw
} from 'lucide-react'
import { format, differenceInDays, isToday, isTomorrow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface ReservationReminderProps {
  className?: string
}

interface ReminderItem {
  id: string
  type: 'check_in_soon' | 'check_in_today' | 'overdue' | 'deposit_pending'
  priority: 'high' | 'medium' | 'low'
  reservation: any
  message: string
  actionRequired: boolean
}

export function ReservationReminder({ className = '' }: ReservationReminderProps) {
  const { toast } = useToast()
  const { getReservations, isLoading } = useReservations()
  
  const [reminders, setReminders] = useState<ReminderItem[]>([])
  const [dismissedReminders, setDismissedReminders] = useState<Set<string>>(new Set())

  // 加载提醒数据
  const loadReminders = async () => {
    try {
      // 获取需要提醒的预订（已确认状态）
      const result = await getReservations({
        status: '2', // 已确认
        limit: 100,
      })

      const today = new Date()
      const reminderItems: ReminderItem[] = []

      result.list?.forEach((reservation: any) => {
        const checkInDate = new Date(reservation.expectedCheckInDate)
        const daysDiff = differenceInDays(checkInDate, today)

        // 今天入住
        if (isToday(checkInDate)) {
          reminderItems.push({
            id: `check_in_today_${reservation.id}`,
            type: 'check_in_today',
            priority: 'high',
            reservation,
            message: `${reservation.contactName} 今天入住`,
            actionRequired: true,
          })
        }
        // 明天入住
        else if (isTomorrow(checkInDate)) {
          reminderItems.push({
            id: `check_in_tomorrow_${reservation.id}`,
            type: 'check_in_soon',
            priority: 'high',
            reservation,
            message: `${reservation.contactName} 明天入住`,
            actionRequired: true,
          })
        }
        // 3天内入住
        else if (daysDiff >= 0 && daysDiff <= 3) {
          reminderItems.push({
            id: `check_in_soon_${reservation.id}`,
            type: 'check_in_soon',
            priority: 'medium',
            reservation,
            message: `${reservation.contactName} ${daysDiff}天后入住`,
            actionRequired: false,
          })
        }
        // 已过期
        else if (daysDiff < 0) {
          reminderItems.push({
            id: `overdue_${reservation.id}`,
            type: 'overdue',
            priority: 'high',
            reservation,
            message: `${reservation.contactName} 预订已过期${Math.abs(daysDiff)}天`,
            actionRequired: true,
          })
        }

        // 押金未支付
        if (reservation.depositAmount > 0 && !reservation.depositPaid) {
          reminderItems.push({
            id: `deposit_pending_${reservation.id}`,
            type: 'deposit_pending',
            priority: 'medium',
            reservation,
            message: `${reservation.contactName} 押金未支付`,
            actionRequired: true,
          })
        }
      })

      // 按优先级排序
      reminderItems.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 }
        return priorityOrder[b.priority] - priorityOrder[a.priority]
      })

      setReminders(reminderItems)
    } catch (error) {
      console.error('加载提醒失败:', error)
    }
  }

  // 初始化加载
  useEffect(() => {
    loadReminders()
    
    // 每5分钟刷新一次
    const interval = setInterval(loadReminders, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  // 忽略提醒
  const dismissReminder = (reminderId: string) => {
    setDismissedReminders(prev => new Set([...prev, reminderId]))
  }

  // 获取优先级样式
  const getPriorityStyle = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-red-200 bg-red-50'
      case 'medium':
        return 'border-orange-200 bg-orange-50'
      case 'low':
        return 'border-blue-200 bg-blue-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  // 获取类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'check_in_today':
      case 'check_in_soon':
        return Calendar
      case 'overdue':
        return AlertTriangle
      case 'deposit_pending':
        return Clock
      default:
        return Bell
    }
  }

  // 获取类型颜色
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'check_in_today':
        return 'text-green-600'
      case 'check_in_soon':
        return 'text-blue-600'
      case 'overdue':
        return 'text-red-600'
      case 'deposit_pending':
        return 'text-orange-600'
      default:
        return 'text-gray-600'
    }
  }

  // 过滤未忽略的提醒
  const activeReminders = reminders.filter(reminder => !dismissedReminders.has(reminder.id))

  if (activeReminders.length === 0) {
    return null
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-orange-600" />
              <span>预订提醒</span>
              <Badge variant="destructive" className="ml-2">
                {activeReminders.length}
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={loadReminders}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <AnimatePresence>
              {activeReminders.map((reminder, index) => {
                const IconComponent = getTypeIcon(reminder.type)
                const iconColor = getTypeColor(reminder.type)
                
                return (
                  <motion.div
                    key={reminder.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ delay: index * 0.1 }}
                    className={`border rounded-lg p-4 ${getPriorityStyle(reminder.priority)}`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <IconComponent className={`h-5 w-5 mt-0.5 ${iconColor}`} />
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-gray-900">
                              {reminder.message}
                            </span>
                            {reminder.actionRequired && (
                              <Badge variant="destructive" className="text-xs">
                                需要处理
                              </Badge>
                            )}
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span>{reminder.reservation.contactName}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              <span>{reminder.reservation.contactPhone}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Home className="h-3 w-3" />
                              <span>{reminder.reservation.room?.roomNumber || '未分配'}</span>
                            </div>
                          </div>

                          <div className="text-sm text-gray-600">
                            <span>预期入住：</span>
                            <span className="font-medium">
                              {format(new Date(reminder.reservation.expectedCheckInDate), 'MM月dd日 EEEE', { locale: zhCN })}
                            </span>
                          </div>

                          {reminder.type === 'deposit_pending' && (
                            <div className="text-sm text-orange-600">
                              <span>押金金额：¥{reminder.reservation.depositAmount}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        {reminder.actionRequired && (
                          <Button size="sm" variant="outline">
                            处理
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => dismissReminder(reminder.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </AnimatePresence>
          </div>

          {/* 统计信息 */}
          <div className="mt-4 pt-4 border-t">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {activeReminders.filter(r => r.priority === 'high').length}
                </div>
                <div className="text-xs text-gray-500">高优先级</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {activeReminders.filter(r => r.priority === 'medium').length}
                </div>
                <div className="text-xs text-gray-500">中优先级</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {activeReminders.filter(r => r.actionRequired).length}
                </div>
                <div className="text-xs text-gray-500">需要处理</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {activeReminders.filter(r => r.type === 'check_in_today').length}
                </div>
                <div className="text-xs text-gray-500">今日入住</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
