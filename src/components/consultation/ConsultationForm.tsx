'use client'

import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion } from 'framer-motion'
import {
  User,
  Phone,
  Calendar,
  FileText,
  Save,
  X,
  AlertCircle,
  CheckCircle,
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { DatePickerField } from '@/components/forms/DatePickerField'
import { ConsultationFileUpload } from './ConsultationFileUpload'
import { useToast } from '@/hooks/use-toast'
import { apiClient } from '@/lib/api-client'
import { type FileUploadResult } from '@/lib/file-upload'

// 表单验证 Schema
const ConsultationFormSchema = z.object({
  // 老人信息
  elderInfo: z.object({
    name: z.string().min(1, '老人姓名不能为空'),
    age: z.coerce
      .number()
      .min(0, '年龄不能为负数')
      .max(150, '年龄不能超过150岁'),
    gender: z.coerce.number().min(1).max(2, '请选择性别'),
    phone: z.string().optional(),
    idCard: z.string().optional(),
    address: z.string().optional(),
    emergencyContactName: z.string().optional(),
    emergencyContactPhone: z.string().optional(),
    emergencyContactRelation: z.string().optional(),
    healthCondition: z.string().optional(),
    medications: z.string().optional(),
    allergies: z.string().optional(),
    specialNeeds: z.string().optional(),
  }),
  // 咨询人信息
  consultantName: z.string().min(1, '咨询人姓名不能为空'),
  consultantPhone: z.string().min(1, '咨询人电话不能为空'),
  consultantRelation: z.string().optional(),
  // 咨询详情
  purpose: z.string().min(1, '咨询目的不能为空'),
  expectedCheckInDate: z.string().optional(),
  mediaChannel: z.coerce.number().min(1).max(4, '请选择媒介渠道'),
  notes: z.string().optional(),
  followUpDate: z.string().optional(),
})

type ConsultationFormData = z.infer<typeof ConsultationFormSchema>

interface ConsultationFormProps {
  onSubmit: (
    data: ConsultationFormData,
    files?: FileUploadResult[]
  ) => Promise<void>
  onCancel: () => void
  initialData?: Partial<ConsultationFormData>
  initialFiles?: FileUploadResult[]
  isLoading?: boolean
  userId?: string
}

/**
 * 咨询信息录入表单组件
 */
export function ConsultationForm({
  onSubmit,
  onCancel,
  initialData,
  initialFiles = [],
  isLoading = false,
  userId,
}: ConsultationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [uploadedFiles, setUploadedFiles] =
    useState<FileUploadResult[]>(initialFiles)
  const { toast } = useToast()

  const form = useForm<ConsultationFormData>({
    resolver: zodResolver(ConsultationFormSchema),
    defaultValues: {
      elderInfo: {
        name: '',
        age: 0,
        gender: 1,
        phone: '',
        idCard: '',
        address: '',
        emergencyContactName: '',
        emergencyContactPhone: '',
        emergencyContactRelation: '',
        healthCondition: '',
        medications: '',
        allergies: '',
        specialNeeds: '',
      },
      consultantName: '',
      consultantPhone: '',
      consultantRelation: '',
      purpose: '',
      expectedCheckInDate: '',
      mediaChannel: 1,
      notes: '',
      followUpDate: '',
      ...initialData,
    },
  })

  const handleSubmit = async (data: ConsultationFormData) => {
    try {
      setIsSubmitting(true)
      await onSubmit(data, uploadedFiles)
      toast({
        title: '提交成功',
        description: '咨询记录已保存',
        variant: 'default',
      })
    } catch (error) {
      toast({
        title: '提交失败',
        description:
          error instanceof Error ? error.message : '保存咨询记录时发生错误',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* 老人信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                老人信息
              </CardTitle>
              <CardDescription>请填写需要入住的老人基本信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="elderInfo.name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>姓名 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入老人姓名" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-2">
                  <FormField
                    control={form.control}
                    name="elderInfo.age"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>年龄 *</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="年龄"
                            {...field}
                            onChange={e =>
                              field.onChange(Number(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="elderInfo.gender"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>性别 *</FormLabel>
                        <Select
                          onValueChange={value => field.onChange(Number(value))}
                          defaultValue={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择性别" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="1">男</SelectItem>
                            <SelectItem value="2">女</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="elderInfo.phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系电话</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入联系电话" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="elderInfo.idCard"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>身份证号</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入身份证号" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="elderInfo.address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>家庭住址</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入家庭住址" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="elderInfo.emergencyContactName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>紧急联系人</FormLabel>
                      <FormControl>
                        <Input placeholder="联系人姓名" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="elderInfo.emergencyContactPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系人电话</FormLabel>
                      <FormControl>
                        <Input placeholder="联系人电话" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="elderInfo.emergencyContactRelation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>关系</FormLabel>
                      <FormControl>
                        <Input placeholder="与老人关系" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="elderInfo.healthCondition"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>健康状况</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="请描述老人的健康状况"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="elderInfo.medications"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>用药情况</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="请描述老人的用药情况"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="elderInfo.allergies"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>过敏史</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="请描述老人的过敏史"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="elderInfo.specialNeeds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>特殊需求</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="请描述老人的特殊需求"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* 咨询人信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                咨询人信息
              </CardTitle>
              <CardDescription>请填写咨询人的联系信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="consultantName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>咨询人姓名 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入咨询人姓名" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="consultantPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>咨询人电话 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入咨询人电话" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="consultantRelation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>与老人关系</FormLabel>
                      <FormControl>
                        <Input placeholder="如：子女、亲属等" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* 咨询详情 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                咨询详情
              </CardTitle>
              <CardDescription>请填写咨询的具体信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="purpose"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>咨询目的 *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请描述咨询的目的和需求"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="mediaChannel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>媒介渠道 *</FormLabel>
                      <Select
                        onValueChange={value => field.onChange(Number(value))}
                        defaultValue={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择媒介渠道" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="1">电话</SelectItem>
                          <SelectItem value="2">微信</SelectItem>
                          <SelectItem value="3">现场</SelectItem>
                          <SelectItem value="4">其他</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="expectedCheckInDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>预期入住时间</FormLabel>
                      <FormControl>
                        <DatePickerField
                          value={
                            field.value ? new Date(field.value) : undefined
                          }
                          onChange={date => field.onChange(date?.toISOString())}
                          placeholder="选择预期入住时间"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="followUpDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>跟进时间</FormLabel>
                      <FormControl>
                        <DatePickerField
                          value={
                            field.value ? new Date(field.value) : undefined
                          }
                          onChange={date => field.onChange(date?.toISOString())}
                          placeholder="选择跟进时间"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>备注</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请输入其他备注信息"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* 文件上传 */}
          <ConsultationFileUpload
            userId={userId}
            initialFiles={uploadedFiles}
            onFilesChange={setUploadedFiles}
            disabled={isSubmitting}
          />

          {/* 操作按钮 */}
          <div className="flex items-center justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              <X className="mr-2 h-4 w-4" />
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting || isLoading}>
              {isSubmitting ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      ease: 'linear',
                    }}
                    className="mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"
                  />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  保存
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </motion.div>
  )
}
