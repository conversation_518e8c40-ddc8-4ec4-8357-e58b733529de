'use client'

import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { FileText, Image as ImageIcon, Trash2, Download, Eye } from 'lucide-react'

import { FileUpload, type FileItem } from '@/components/ui/file-upload'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { 
  uploadFiles, 
  deleteFile, 
  getFileDownloadUrl, 
  getFileCategory,
  formatFileSize,
  type FileUploadResult 
} from '@/lib/file-upload'

interface ConsultationFileUploadProps {
  consultationId?: string
  userId?: string
  initialFiles?: FileUploadResult[]
  onFilesChange?: (files: FileUploadResult[]) => void
  disabled?: boolean
}

/**
 * 咨询记录文件上传组件
 */
export function ConsultationFileUpload({
  consultationId,
  userId,
  initialFiles = [],
  onFilesChange,
  disabled = false,
}: ConsultationFileUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<FileUploadResult[]>(initialFiles)
  const [isUploading, setIsUploading] = useState(false)
  const { toast } = useToast()

  // 处理文件上传
  const handleUpload = useCallback(async (fileItems: FileItem[]) => {
    if (!fileItems.length || disabled) return

    setIsUploading(true)
    try {
      const files = fileItems.map(item => item.file)
      const results = await uploadFiles(
        files,
        'consultation',
        userId,
        (fileIndex, progress) => {
          // 可以在这里更新单个文件的上传进度
          console.log(`File ${fileIndex} progress: ${progress}%`)
        }
      )

      const newUploadedFiles = [...uploadedFiles, ...results]
      setUploadedFiles(newUploadedFiles)
      onFilesChange?.(newUploadedFiles)

      toast({
        title: '上传成功',
        description: `成功上传 ${results.length} 个文件`,
      })
    } catch (error) {
      toast({
        title: '上传失败',
        description: error instanceof Error ? error.message : '文件上传失败',
        variant: 'destructive',
      })
      throw error
    } finally {
      setIsUploading(false)
    }
  }, [uploadedFiles, userId, onFilesChange, disabled, toast])

  // 处理文件删除
  const handleDeleteFile = useCallback(async (file: FileUploadResult) => {
    if (disabled) return

    try {
      await deleteFile(file.path)
      
      const newUploadedFiles = uploadedFiles.filter(f => f.id !== file.id)
      setUploadedFiles(newUploadedFiles)
      onFilesChange?.(newUploadedFiles)

      toast({
        title: '删除成功',
        description: '文件已删除',
      })
    } catch (error) {
      toast({
        title: '删除失败',
        description: error instanceof Error ? error.message : '文件删除失败',
        variant: 'destructive',
      })
    }
  }, [uploadedFiles, onFilesChange, disabled, toast])

  // 处理文件下载
  const handleDownloadFile = useCallback(async (file: FileUploadResult) => {
    try {
      const downloadUrl = await getFileDownloadUrl(file.path)
      
      // 创建下载链接
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = file.originalName
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: '下载开始',
        description: '文件下载已开始',
      })
    } catch (error) {
      toast({
        title: '下载失败',
        description: error instanceof Error ? error.message : '文件下载失败',
        variant: 'destructive',
      })
    }
  }, [toast])

  // 处理文件预览
  const handlePreviewFile = useCallback((file: FileUploadResult) => {
    // 在新窗口中打开文件
    window.open(file.url, '_blank')
  }, [])

  // 获取文件图标
  const getFileIcon = (mimeType: string) => {
    const category = getFileCategory(mimeType)
    switch (category) {
      case 'image':
        return <ImageIcon className="h-5 w-5 text-blue-500" />
      case 'document':
        return <FileText className="h-5 w-5 text-green-500" />
      default:
        return <FileText className="h-5 w-5 text-gray-500" />
    }
  }

  // 获取文件类型标签
  const getFileTypeBadge = (mimeType: string) => {
    const category = getFileCategory(mimeType)
    switch (category) {
      case 'image':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">图片</Badge>
      case 'document':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">文档</Badge>
      default:
        return <Badge variant="secondary">其他</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* 文件上传区域 */}
      <Card>
        <CardHeader>
          <CardTitle>文件上传</CardTitle>
          <CardDescription>
            支持上传图片和文档文件，单个文件最大 10MB
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FileUpload
            accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
            multiple={true}
            maxSize={10}
            maxFiles={10}
            onUpload={handleUpload}
            disabled={disabled || isUploading}
            showPreview={true}
            dragDropText="拖拽文件到此处或点击选择"
            browseText="选择文件"
          />
        </CardContent>
      </Card>

      {/* 已上传文件列表 */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>已上传文件 ({uploadedFiles.length})</CardTitle>
            <CardDescription>
              管理已上传的文件
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {uploadedFiles.map((file) => (
                <motion.div
                  key={file.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {/* 文件图标 */}
                    <div className="flex-shrink-0">
                      {getFileIcon(file.mimeType)}
                    </div>

                    {/* 文件信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium truncate">
                          {file.originalName}
                        </p>
                        {getFileTypeBadge(file.mimeType)}
                      </div>
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                        <span>{formatFileSize(file.fileSize)}</span>
                        <span>
                          {new Date(file.uploadedAt).toLocaleDateString('zh-CN', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-2">
                    {/* 预览按钮 */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePreviewFile(file)}
                      title="预览文件"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>

                    {/* 下载按钮 */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDownloadFile(file)}
                      title="下载文件"
                    >
                      <Download className="h-4 w-4" />
                    </Button>

                    {/* 删除按钮 */}
                    {!disabled && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteFile(file)}
                        className="text-red-600 hover:text-red-700"
                        title="删除文件"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
