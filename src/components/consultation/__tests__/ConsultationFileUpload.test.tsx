/**
 * @vitest-environment jsdom
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ConsultationFileUpload } from '../ConsultationFileUpload'

// Mock dependencies
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

vi.mock('@/lib/file-upload', () => ({
  uploadFiles: vi.fn(),
  deleteFile: vi.fn(),
  getFileDownloadUrl: vi.fn(),
  getFileCategory: vi.fn((mimeType: string) => {
    if (mimeType.startsWith('image/')) return 'image'
    if (mimeType === 'application/pdf') return 'document'
    return 'other'
  }),
  formatFileSize: vi.fn((bytes: number) => `${bytes} bytes`),
}))

vi.mock('@/components/ui/file-upload', () => ({
  FileUpload: ({ onUpload, disabled }: any) => (
    <div data-testid="file-upload">
      <button
        onClick={() => {
          const mockFiles = [
            {
              id: 'test-1',
              file: new File(['test'], 'test.pdf', { type: 'application/pdf' }),
              status: 'pending' as const,
            },
          ]
          onUpload?.(mockFiles)
        }}
        disabled={disabled}
      >
        Upload Files
      </button>
    </div>
  ),
}))

describe('ConsultationFileUpload', () => {
  const mockOnFilesChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  const defaultProps = {
    onFilesChange: mockOnFilesChange,
  }

  it('renders file upload component', () => {
    render(<ConsultationFileUpload {...defaultProps} />)

    expect(screen.getByText('文件上传')).toBeInTheDocument()
    expect(screen.getByText('支持上传图片和文档文件，单个文件最大 10MB')).toBeInTheDocument()
    expect(screen.getByTestId('file-upload')).toBeInTheDocument()
  })

  it('handles file upload successfully', async () => {
    const { uploadFiles } = await import('@/lib/file-upload')
    const mockUploadFiles = uploadFiles as any
    
    mockUploadFiles.mockResolvedValue([
      {
        id: 'uploaded-1',
        fileName: 'test.pdf',
        originalName: 'test.pdf',
        fileSize: 1024,
        mimeType: 'application/pdf',
        url: 'http://example.com/test.pdf',
        path: 'consultation/test.pdf',
        uploadedAt: new Date(),
      },
    ])

    const user = userEvent.setup()
    render(<ConsultationFileUpload {...defaultProps} />)

    const uploadButton = screen.getByText('Upload Files')
    await user.click(uploadButton)

    await waitFor(() => {
      expect(mockUploadFiles).toHaveBeenCalled()
      expect(mockOnFilesChange).toHaveBeenCalledWith([
        expect.objectContaining({
          id: 'uploaded-1',
          fileName: 'test.pdf',
          originalName: 'test.pdf',
        }),
      ])
    })
  })

  it('handles file upload error', async () => {
    const { uploadFiles } = await import('@/lib/file-upload')
    const mockUploadFiles = uploadFiles as any
    
    mockUploadFiles.mockRejectedValue(new Error('Upload failed'))

    const user = userEvent.setup()
    render(<ConsultationFileUpload {...defaultProps} />)

    const uploadButton = screen.getByText('Upload Files')
    await user.click(uploadButton)

    await waitFor(() => {
      expect(mockUploadFiles).toHaveBeenCalled()
    })
  })

  it('displays uploaded files', () => {
    const initialFiles = [
      {
        id: 'file-1',
        fileName: 'document.pdf',
        originalName: 'document.pdf',
        fileSize: 2048,
        mimeType: 'application/pdf',
        url: 'http://example.com/document.pdf',
        path: 'consultation/document.pdf',
        uploadedAt: new Date('2024-01-01'),
      },
      {
        id: 'file-2',
        fileName: 'image.jpg',
        originalName: 'image.jpg',
        fileSize: 1024,
        mimeType: 'image/jpeg',
        url: 'http://example.com/image.jpg',
        path: 'consultation/image.jpg',
        uploadedAt: new Date('2024-01-02'),
      },
    ]

    render(<ConsultationFileUpload {...defaultProps} initialFiles={initialFiles} />)

    expect(screen.getByText('已上传文件 (2)')).toBeInTheDocument()
    expect(screen.getByText('document.pdf')).toBeInTheDocument()
    expect(screen.getByText('image.jpg')).toBeInTheDocument()
  })

  it('handles file deletion', async () => {
    const { deleteFile } = await import('@/lib/file-upload')
    const mockDeleteFile = deleteFile as any
    
    mockDeleteFile.mockResolvedValue(undefined)

    const initialFiles = [
      {
        id: 'file-1',
        fileName: 'document.pdf',
        originalName: 'document.pdf',
        fileSize: 2048,
        mimeType: 'application/pdf',
        url: 'http://example.com/document.pdf',
        path: 'consultation/document.pdf',
        uploadedAt: new Date(),
      },
    ]

    const user = userEvent.setup()
    render(<ConsultationFileUpload {...defaultProps} initialFiles={initialFiles} />)

    const deleteButton = screen.getByTitle('删除文件')
    await user.click(deleteButton)

    await waitFor(() => {
      expect(mockDeleteFile).toHaveBeenCalledWith('consultation/document.pdf')
      expect(mockOnFilesChange).toHaveBeenCalledWith([])
    })
  })

  it('handles file download', async () => {
    const { getFileDownloadUrl } = await import('@/lib/file-upload')
    const mockGetFileDownloadUrl = getFileDownloadUrl as any
    
    mockGetFileDownloadUrl.mockResolvedValue('http://example.com/download/document.pdf')

    // Mock document.createElement and related methods
    const mockLink = {
      href: '',
      download: '',
      target: '',
      click: vi.fn(),
    }
    const mockCreateElement = vi.spyOn(document, 'createElement').mockReturnValue(mockLink as any)
    const mockAppendChild = vi.spyOn(document.body, 'appendChild').mockImplementation(() => mockLink as any)
    const mockRemoveChild = vi.spyOn(document.body, 'removeChild').mockImplementation(() => mockLink as any)

    const initialFiles = [
      {
        id: 'file-1',
        fileName: 'document.pdf',
        originalName: 'document.pdf',
        fileSize: 2048,
        mimeType: 'application/pdf',
        url: 'http://example.com/document.pdf',
        path: 'consultation/document.pdf',
        uploadedAt: new Date(),
      },
    ]

    const user = userEvent.setup()
    render(<ConsultationFileUpload {...defaultProps} initialFiles={initialFiles} />)

    const downloadButton = screen.getByTitle('下载文件')
    await user.click(downloadButton)

    await waitFor(() => {
      expect(mockGetFileDownloadUrl).toHaveBeenCalledWith('consultation/document.pdf')
      expect(mockCreateElement).toHaveBeenCalledWith('a')
      expect(mockLink.href).toBe('http://example.com/download/document.pdf')
      expect(mockLink.download).toBe('document.pdf')
      expect(mockLink.click).toHaveBeenCalled()
    })

    // Cleanup mocks
    mockCreateElement.mockRestore()
    mockAppendChild.mockRestore()
    mockRemoveChild.mockRestore()
  })

  it('handles file preview', async () => {
    const mockWindowOpen = vi.spyOn(window, 'open').mockImplementation(() => null)

    const initialFiles = [
      {
        id: 'file-1',
        fileName: 'image.jpg',
        originalName: 'image.jpg',
        fileSize: 1024,
        mimeType: 'image/jpeg',
        url: 'http://example.com/image.jpg',
        path: 'consultation/image.jpg',
        uploadedAt: new Date(),
      },
    ]

    const user = userEvent.setup()
    render(<ConsultationFileUpload {...defaultProps} initialFiles={initialFiles} />)

    const previewButton = screen.getByTitle('预览文件')
    await user.click(previewButton)

    expect(mockWindowOpen).toHaveBeenCalledWith('http://example.com/image.jpg', '_blank')

    mockWindowOpen.mockRestore()
  })

  it('disables upload when disabled prop is true', () => {
    render(<ConsultationFileUpload {...defaultProps} disabled={true} />)

    const uploadButton = screen.getByText('Upload Files')
    expect(uploadButton).toBeDisabled()
  })

  it('does not show delete buttons when disabled', () => {
    const initialFiles = [
      {
        id: 'file-1',
        fileName: 'document.pdf',
        originalName: 'document.pdf',
        fileSize: 2048,
        mimeType: 'application/pdf',
        url: 'http://example.com/document.pdf',
        path: 'consultation/document.pdf',
        uploadedAt: new Date(),
      },
    ]

    render(<ConsultationFileUpload {...defaultProps} initialFiles={initialFiles} disabled={true} />)

    expect(screen.queryByTitle('删除文件')).not.toBeInTheDocument()
  })

  it('displays correct file type badges', () => {
    const initialFiles = [
      {
        id: 'file-1',
        fileName: 'document.pdf',
        originalName: 'document.pdf',
        fileSize: 2048,
        mimeType: 'application/pdf',
        url: 'http://example.com/document.pdf',
        path: 'consultation/document.pdf',
        uploadedAt: new Date(),
      },
      {
        id: 'file-2',
        fileName: 'image.jpg',
        originalName: 'image.jpg',
        fileSize: 1024,
        mimeType: 'image/jpeg',
        url: 'http://example.com/image.jpg',
        path: 'consultation/image.jpg',
        uploadedAt: new Date(),
      },
    ]

    render(<ConsultationFileUpload {...defaultProps} initialFiles={initialFiles} />)

    expect(screen.getByText('文档')).toBeInTheDocument()
    expect(screen.getByText('图片')).toBeInTheDocument()
  })
})
