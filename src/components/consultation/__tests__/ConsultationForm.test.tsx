/**
 * @vitest-environment jsdom
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ConsultationForm } from '../ConsultationForm'

// Mock dependencies
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

vi.mock('@/lib/api-client', () => ({
  apiClient: {
    post: vi.fn(),
  },
}))

vi.mock('../ConsultationFileUpload', () => ({
  ConsultationFileUpload: ({ onFilesChange }: any) => (
    <div data-testid="file-upload">
      <button
        onClick={() => onFilesChange?.([
          {
            id: 'test-file-1',
            fileName: 'test.pdf',
            originalName: 'test.pdf',
            fileSize: 1024,
            mimeType: 'application/pdf',
            url: 'http://example.com/test.pdf',
            path: 'consultation/test.pdf',
            uploadedAt: new Date(),
          },
        ])}
      >
        Upload File
      </button>
    </div>
  ),
}))

describe('ConsultationForm', () => {
  const mockOnSubmit = vi.fn()
  const mockOnCancel = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  const defaultProps = {
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
  }

  it('renders all form sections', () => {
    render(<ConsultationForm {...defaultProps} />)

    expect(screen.getByText('老人信息')).toBeInTheDocument()
    expect(screen.getByText('咨询人信息')).toBeInTheDocument()
    expect(screen.getByText('咨询详情')).toBeInTheDocument()
    expect(screen.getByTestId('file-upload')).toBeInTheDocument()
  })

  it('renders required fields with asterisks', () => {
    render(<ConsultationForm {...defaultProps} />)

    expect(screen.getByText('姓名 *')).toBeInTheDocument()
    expect(screen.getByText('年龄 *')).toBeInTheDocument()
    expect(screen.getByText('性别 *')).toBeInTheDocument()
    expect(screen.getByText('咨询人姓名 *')).toBeInTheDocument()
    expect(screen.getByText('咨询人电话 *')).toBeInTheDocument()
    expect(screen.getByText('咨询目的 *')).toBeInTheDocument()
    expect(screen.getByText('媒介渠道 *')).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    render(<ConsultationForm {...defaultProps} />)

    const submitButton = screen.getByRole('button', { name: /保存/ })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('老人姓名不能为空')).toBeInTheDocument()
      expect(screen.getByText('咨询人姓名不能为空')).toBeInTheDocument()
      expect(screen.getByText('咨询人电话不能为空')).toBeInTheDocument()
      expect(screen.getByText('咨询目的不能为空')).toBeInTheDocument()
    })

    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('validates age field', async () => {
    const user = userEvent.setup()
    render(<ConsultationForm {...defaultProps} />)

    const ageInput = screen.getByLabelText('年龄 *')
    await user.type(ageInput, '-5')

    const submitButton = screen.getByRole('button', { name: /保存/ })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('年龄不能为负数')).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    const user = userEvent.setup()
    mockOnSubmit.mockResolvedValue(undefined)

    render(<ConsultationForm {...defaultProps} />)

    // Fill required fields
    await user.type(screen.getByLabelText('姓名 *'), '张三')
    await user.type(screen.getByLabelText('年龄 *'), '75')
    await user.type(screen.getByLabelText('咨询人姓名 *'), '李四')
    await user.type(screen.getByLabelText('咨询人电话 *'), '13800138000')
    await user.type(screen.getByLabelText('咨询目的 *'), '了解养老院服务')

    const submitButton = screen.getByRole('button', { name: /保存/ })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          elderInfo: expect.objectContaining({
            name: '张三',
            age: 75,
            gender: 1,
          }),
          consultantName: '李四',
          consultantPhone: '13800138000',
          purpose: '了解养老院服务',
          mediaChannel: 1,
        }),
        []
      )
    })
  })

  it('submits form with file attachments', async () => {
    const user = userEvent.setup()
    mockOnSubmit.mockResolvedValue(undefined)

    render(<ConsultationForm {...defaultProps} />)

    // Fill required fields
    await user.type(screen.getByLabelText('姓名 *'), '张三')
    await user.type(screen.getByLabelText('年龄 *'), '75')
    await user.type(screen.getByLabelText('咨询人姓名 *'), '李四')
    await user.type(screen.getByLabelText('咨询人电话 *'), '13800138000')
    await user.type(screen.getByLabelText('咨询目的 *'), '了解养老院服务')

    // Upload a file
    const uploadButton = screen.getByText('Upload File')
    await user.click(uploadButton)

    const submitButton = screen.getByRole('button', { name: /保存/ })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.any(Object),
        expect.arrayContaining([
          expect.objectContaining({
            id: 'test-file-1',
            fileName: 'test.pdf',
            originalName: 'test.pdf',
          }),
        ])
      )
    })
  })

  it('handles form submission error', async () => {
    const user = userEvent.setup()
    const error = new Error('提交失败')
    mockOnSubmit.mockRejectedValue(error)

    render(<ConsultationForm {...defaultProps} />)

    // Fill required fields
    await user.type(screen.getByLabelText('姓名 *'), '张三')
    await user.type(screen.getByLabelText('年龄 *'), '75')
    await user.type(screen.getByLabelText('咨询人姓名 *'), '李四')
    await user.type(screen.getByLabelText('咨询人电话 *'), '13800138000')
    await user.type(screen.getByLabelText('咨询目的 *'), '了解养老院服务')

    const submitButton = screen.getByRole('button', { name: /保存/ })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalled()
    })
  })

  it('calls onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup()
    render(<ConsultationForm {...defaultProps} />)

    const cancelButton = screen.getByRole('button', { name: /取消/ })
    await user.click(cancelButton)

    expect(mockOnCancel).toHaveBeenCalled()
  })

  it('disables form when isLoading is true', () => {
    render(<ConsultationForm {...defaultProps} isLoading={true} />)

    const submitButton = screen.getByRole('button', { name: /保存/ })
    expect(submitButton).toBeDisabled()
  })

  it('populates form with initial data', () => {
    const initialData = {
      elderInfo: {
        name: '王五',
        age: 80,
        gender: 2,
        phone: '13900139000',
      },
      consultantName: '赵六',
      consultantPhone: '13700137000',
      purpose: '咨询入住事宜',
    }

    render(<ConsultationForm {...defaultProps} initialData={initialData} />)

    expect(screen.getByDisplayValue('王五')).toBeInTheDocument()
    expect(screen.getByDisplayValue('80')).toBeInTheDocument()
    expect(screen.getByDisplayValue('13900139000')).toBeInTheDocument()
    expect(screen.getByDisplayValue('赵六')).toBeInTheDocument()
    expect(screen.getByDisplayValue('13700137000')).toBeInTheDocument()
    expect(screen.getByDisplayValue('咨询入住事宜')).toBeInTheDocument()
  })

  it('handles gender selection', async () => {
    const user = userEvent.setup()
    render(<ConsultationForm {...defaultProps} />)

    // Find and click the gender select trigger
    const genderSelect = screen.getByRole('combobox', { name: /性别/ })
    await user.click(genderSelect)

    // Select female option
    const femaleOption = screen.getByRole('option', { name: '女' })
    await user.click(femaleOption)

    // Fill other required fields and submit
    await user.type(screen.getByLabelText('姓名 *'), '张三')
    await user.type(screen.getByLabelText('年龄 *'), '75')
    await user.type(screen.getByLabelText('咨询人姓名 *'), '李四')
    await user.type(screen.getByLabelText('咨询人电话 *'), '13800138000')
    await user.type(screen.getByLabelText('咨询目的 *'), '了解养老院服务')

    const submitButton = screen.getByRole('button', { name: /保存/ })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          elderInfo: expect.objectContaining({
            gender: 2,
          }),
        }),
        expect.any(Array)
      )
    })
  })

  it('handles media channel selection', async () => {
    const user = userEvent.setup()
    render(<ConsultationForm {...defaultProps} />)

    // Find and click the media channel select trigger
    const mediaChannelSelect = screen.getByRole('combobox', { name: /媒介渠道/ })
    await user.click(mediaChannelSelect)

    // Select WeChat option
    const wechatOption = screen.getByRole('option', { name: '微信' })
    await user.click(wechatOption)

    // Fill other required fields and submit
    await user.type(screen.getByLabelText('姓名 *'), '张三')
    await user.type(screen.getByLabelText('年龄 *'), '75')
    await user.type(screen.getByLabelText('咨询人姓名 *'), '李四')
    await user.type(screen.getByLabelText('咨询人电话 *'), '13800138000')
    await user.type(screen.getByLabelText('咨询目的 *'), '了解养老院服务')

    const submitButton = screen.getByRole('button', { name: /保存/ })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          mediaChannel: 2,
        }),
        expect.any(Array)
      )
    })
  })
})
