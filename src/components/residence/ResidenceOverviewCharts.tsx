'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Chart } from '@/components/ui/chart'
import { TrendingUp, <PERSON><PERSON>hart, BarChart3, Calendar } from 'lucide-react'
import type { ResidenceOverviewData } from './ResidenceOverviewDashboard'

interface ResidenceOverviewChartsProps {
  data: ResidenceOverviewData
  loading?: boolean
  className?: string
}

export function ResidenceOverviewCharts({
  data,
  loading = false,
  className = '',
}: ResidenceOverviewChartsProps) {
  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
                  <div className="h-64 bg-gray-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  // 床位使用率饼图配置
  const occupancyPieOption = {
    title: {
      text: '床位使用情况',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `
          <div style="font-weight: bold; margin-bottom: 8px;">${params.name}</div>
          <div>床位数: ${params.value}</div>
          <div>占比: ${params.percent}%</div>
        `
      },
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
    },
    series: [
      {
        name: '床位使用情况',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: [
          {
            value: data.overview.occupiedBeds,
            name: '已使用床位',
            itemStyle: { color: '#3b82f6' },
          },
          {
            value: data.overview.totalBeds - data.overview.occupiedBeds,
            name: '空闲床位',
            itemStyle: { color: '#e5e7eb' },
          },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }

  // 房间类型分布柱状图配置
  const roomTypeBarOption = {
    title: {
      text: '房间类型分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const data = params[0]
        const roomType = data.name
        const roomData = data.data
        return `
          <div style="font-weight: bold; margin-bottom: 8px;">${roomType}</div>
          <div>房间数量: ${roomData.roomCount}</div>
          <div>总床位: ${roomData.totalBeds}</div>
          <div>已占用: ${roomData.occupiedBeds}</div>
          <div>使用率: ${roomData.occupancyRate}%</div>
        `
      },
    },
    xAxis: {
      type: 'category',
      data: data.roomTypes.map(item => item.roomTypeName),
      axisLabel: {
        rotate: 0,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '房间数量',
        position: 'left',
      },
      {
        type: 'value',
        name: '使用率(%)',
        position: 'right',
        max: 100,
      },
    ],
    series: [
      {
        name: '房间数量',
        type: 'bar',
        yAxisIndex: 0,
        data: data.roomTypes.map(item => ({
          value: item.roomCount,
          roomCount: item.roomCount,
          totalBeds: item.totalBeds,
          occupiedBeds: item.occupiedBeds,
          occupancyRate: item.occupancyRate,
        })),
        itemStyle: {
          color: '#10b981',
        },
      },
      {
        name: '使用率',
        type: 'line',
        yAxisIndex: 1,
        data: data.roomTypes.map(item => item.occupancyRate),
        itemStyle: {
          color: '#f59e0b',
        },
        lineStyle: {
          width: 3,
        },
        symbol: 'circle',
        symbolSize: 6,
      },
    ],
  }

  // 入住趋势图配置
  const trendLineOption = {
    title: {
      text: '入住趋势分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter: (params: any) => {
        const date = params[0].name
        let content = `<div style="font-weight: bold; margin-bottom: 8px;">${date}</div>`
        params.forEach((param: any) => {
          content += `<div>${param.seriesName}: ${param.value}</div>`
        })
        return content
      },
    },
    legend: {
      data: ['新入住', '新预订'],
      top: 30,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.trend.map(item => {
        const date = new Date(item.date)
        return `${date.getMonth() + 1}/${date.getDate()}`
      }),
    },
    yAxis: {
      type: 'value',
      name: '人数',
    },
    series: [
      {
        name: '新入住',
        type: 'line',
        stack: 'Total',
        smooth: true,
        data: data.trend.map(item => item.newAdmissions),
        itemStyle: {
          color: '#3b82f6',
        },
        areaStyle: {
          color: 'rgba(59, 130, 246, 0.1)',
        },
      },
      {
        name: '新预订',
        type: 'line',
        stack: 'Total',
        smooth: true,
        data: data.trend.map(item => item.newReservations),
        itemStyle: {
          color: '#10b981',
        },
        areaStyle: {
          color: 'rgba(16, 185, 129, 0.1)',
        },
      },
    ],
  }

  // 预订状态分布饼图配置
  const reservationStatusPieOption = {
    title: {
      text: '预订状态分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `
          <div style="font-weight: bold; margin-bottom: 8px;">${params.name}</div>
          <div>数量: ${params.value}</div>
          <div>占比: ${params.percent}%</div>
        `
      },
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
    },
    series: [
      {
        name: '预订状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: data.reservations.statusDistribution.map((item, index) => {
          const colors = ['#fbbf24', '#3b82f6', '#10b981', '#ef4444', '#6b7280']
          return {
            value: item.count,
            name: item.statusName,
            itemStyle: { color: colors[index % colors.length] },
          }
        }),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 第一行图表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="grid grid-cols-1 lg:grid-cols-2 gap-6"
      >
        {/* 床位使用情况饼图 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              床位使用情况
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Chart option={occupancyPieOption} height="300px" loading={loading} />
          </CardContent>
        </Card>

        {/* 房间类型分布图 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              房间类型分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Chart option={roomTypeBarOption} height="300px" loading={loading} />
          </CardContent>
        </Card>
      </motion.div>

      {/* 第二行图表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 lg:grid-cols-2 gap-6"
      >
        {/* 入住趋势图 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              入住趋势分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Chart option={trendLineOption} height="300px" loading={loading} />
          </CardContent>
        </Card>

        {/* 预订状态分布图 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              预订状态分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Chart option={reservationStatusPieOption} height="300px" loading={loading} />
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
