'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Home, 
  Users, 
  Bed, 
  Calendar,
  TrendingUp,
  <PERSON>r<PERSON><PERSON><PERSON>,
  Clock,
  Building2,
  AlertCircle
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 入住统计数据类型
export interface ResidenceOverviewData {
  overview: {
    totalRooms: number
    availableRooms: number
    occupiedRooms: number
    maintenanceRooms: number
    totalBeds: number
    occupiedBeds: number
    occupancyRate: number
    totalResidents: number
    newAdmissions: number
    pendingReservations: number
  }
  roomTypes: Array<{
    roomType: number
    roomTypeName: string
    roomCount: number
    totalBeds: number
    occupiedBeds: number
    occupancyRate: number
  }>
  reservations: {
    pendingReservations: number
    statusDistribution: Array<{
      status: number
      statusName: string
      count: number
    }>
  }
  recentAdmissions: Array<{
    id: string
    name: string
    age: number
    gender: number
    genderName: string
    roomNumber: string | null
    admissionDate: string | null
    careLevel: number
    careLevelName: string
  }>
  trend: Array<{
    date: string
    newAdmissions: number
    newReservations: number
  }>
  dateRange: {
    start?: string
    end?: string
  }
}

interface ResidenceOverviewDashboardProps {
  data: ResidenceOverviewData
  loading?: boolean
  className?: string
}

export function ResidenceOverviewDashboard({
  data,
  loading = false,
  className = '',
}: ResidenceOverviewDashboardProps) {
  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* 加载骨架屏 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const { overview, roomTypes, reservations, recentAdmissions } = data

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 核心指标概览 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        {/* 总房间数 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总房间数</p>
                <p className="text-2xl font-bold text-gray-900">
                  {overview.totalRooms.toLocaleString()}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  可用 {overview.availableRooms} | 维修 {overview.maintenanceRooms}
                </p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Home className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 床位使用率 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">床位使用率</p>
                <p className="text-2xl font-bold text-gray-900">
                  {overview.occupancyRate}%
                </p>
                <div className="mt-2">
                  <Progress value={overview.occupancyRate} className="h-2" />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {overview.occupiedBeds}/{overview.totalBeds} 床位
                </p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center ml-4">
                <Bed className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 在院人数 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">在院人数</p>
                <p className="text-2xl font-bold text-gray-900">
                  {overview.totalResidents.toLocaleString()}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  新入住 {overview.newAdmissions} 人
                </p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 待处理预订 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">待处理预订</p>
                <p className="text-2xl font-bold text-gray-900">
                  {overview.pendingReservations.toLocaleString()}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  需要及时处理
                </p>
              </div>
              <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 房间类型分布 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              房间类型分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {roomTypes.map((roomType, index) => (
                <motion.div
                  key={roomType.roomType}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.1 + index * 0.05 }}
                  className="p-4 border rounded-lg"
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{roomType.roomTypeName}</h4>
                    <Badge variant="outline">{roomType.roomCount} 间</Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">使用率</span>
                      <span className="font-medium">{roomType.occupancyRate}%</span>
                    </div>
                    <Progress value={roomType.occupancyRate} className="h-2" />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>已占用: {roomType.occupiedBeds}</span>
                      <span>总床位: {roomType.totalBeds}</span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 预订状态分布 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              预订状态分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              {reservations.statusDistribution.map((status, index) => {
                const getStatusColor = (statusCode: number) => {
                  switch (statusCode) {
                    case 1: return 'bg-yellow-100 text-yellow-800'
                    case 2: return 'bg-blue-100 text-blue-800'
                    case 3: return 'bg-green-100 text-green-800'
                    case 4: return 'bg-red-100 text-red-800'
                    case 5: return 'bg-gray-100 text-gray-800'
                    default: return 'bg-gray-100 text-gray-800'
                  }
                }

                return (
                  <motion.div
                    key={status.status}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.2 + index * 0.05 }}
                    className="text-center"
                  >
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status.status)}`}>
                      {status.statusName}
                    </div>
                    <p className="text-2xl font-bold text-gray-900 mt-2">
                      {status.count}
                    </p>
                  </motion.div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 最近入住记录 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserCheck className="h-5 w-5" />
              最近入住记录
            </CardTitle>
          </CardHeader>
          <CardContent>
            {recentAdmissions.length > 0 ? (
              <div className="space-y-4">
                {recentAdmissions.map((admission, index) => (
                  <motion.div
                    key={admission.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 + index * 0.05 }}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center gap-4">
                      <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{admission.name}</h4>
                        <p className="text-sm text-gray-600">
                          {admission.age}岁 · {admission.genderName} · {admission.careLevelName}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {admission.roomNumber || '未分配房间'}
                      </p>
                      <p className="text-xs text-gray-500">
                        {admission.admissionDate 
                          ? format(new Date(admission.admissionDate), 'MM月dd日', { locale: zhCN })
                          : '未知日期'
                        }
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>暂无最近入住记录</p>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
