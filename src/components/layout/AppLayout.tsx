'use client'

import { ReactNode, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Head<PERSON> } from './Header'
import { AppSidebar } from './AppSidebar'
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'
import { useTheme, useSystemThemeListener } from '@/hooks/use-theme'
import { usePageTitle } from '@/hooks/use-page-title'
import { cn } from '@/lib/utils'

interface AppLayoutProps {
  children: ReactNode
  className?: string
}

/**
 * 应用主布局组件
 * 包含响应式设计、动画效果、主题切换和用户偏好设置功能
 */
export function AppLayout({ children, className }: AppLayoutProps) {
  const { resolvedTheme, userPreferences } = useTheme()

  // 监听系统主题变化
  useSystemThemeListener()

  // 自动管理页面标题
  usePageTitle()

  // 应用用户偏好设置到 document
  useEffect(() => {
    const root = document.documentElement

    // 应用紧凑模式
    if (userPreferences.compactMode) {
      root.classList.add('compact-mode')
    } else {
      root.classList.remove('compact-mode')
    }

    // 应用动画设置
    if (!userPreferences.animationsEnabled) {
      root.classList.add('reduce-motion')
    } else {
      root.classList.remove('reduce-motion')
    }

    // 设置语言属性
    root.setAttribute(
      'lang',
      userPreferences.language === 'zh-CN' ? 'zh-CN' : 'en-US'
    )
  }, [userPreferences])

  const layoutVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.3 : 0,
        ease: [0.4, 0, 0.2, 1] as const,
      },
    },
    exit: {
      opacity: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.2 : 0,
      },
    },
  }

  const mainVariants = {
    initial: { opacity: 0, y: userPreferences.animationsEnabled ? 20 : 0 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.4 : 0,
        ease: [0.4, 0, 0.2, 1] as const,
        delay: userPreferences.animationsEnabled ? 0.1 : 0,
      },
    },
  }

  return (
    <div
      className={cn(
        'min-h-screen bg-background text-foreground transition-colors duration-300',
        resolvedTheme === 'dark' && 'dark',
        userPreferences.compactMode && 'compact-layout',
        className
      )}
    >
      <AnimatePresence mode="wait">
        <motion.div
          key={resolvedTheme}
          variants={layoutVariants}
          initial="initial"
          animate="animate"
          exit="exit"
          className="min-h-screen"
        >
          <SidebarProvider defaultOpen={!userPreferences.sidebarCollapsed}>
            <AppSidebar />
            <SidebarInset className="flex flex-col">
              <Header />
              <motion.main
                variants={mainVariants}
                initial="initial"
                animate="animate"
                className={cn(
                  'flex-1 overflow-auto',
                  // 响应式内边距
                  'p-3 sm:p-4 md:p-6 lg:p-8',
                  // 紧凑模式下减少内边距
                  userPreferences.compactMode && 'p-2 sm:p-3 md:p-4 lg:p-6'
                )}
              >
                <div
                  className={cn(
                    'mx-auto w-full',
                    // 响应式最大宽度
                    'max-w-full xl:max-w-7xl 2xl:max-w-8xl',
                    // 紧凑模式下使用更大的最大宽度
                    userPreferences.compactMode && 'max-w-full'
                  )}
                >
                  {children}
                </div>
              </motion.main>
            </SidebarInset>
          </SidebarProvider>
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
