'use client'

import React, { ReactNode } from 'react'
import { motion } from 'framer-motion'
import { useTheme } from '@/hooks/use-theme'
import { cn } from '@/lib/utils'

interface ResponsiveGridProps {
  children: ReactNode
  className?: string
  cols?: {
    default?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    '2xl'?: number
  }
  gap?: 'sm' | 'md' | 'lg' | 'xl'
  animate?: boolean
  staggerChildren?: boolean
}

/**
 * 响应式网格组件
 * 提供灵活的响应式网格布局和动画效果
 */
export function ResponsiveGrid({
  children,
  className,
  cols = { default: 1, sm: 2, md: 3, lg: 4 },
  gap = 'md',
  animate = true,
  staggerChildren = true,
}: ResponsiveGridProps) {
  const { userPreferences } = useTheme()

  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-4 md:gap-6',
    lg: 'gap-6 md:gap-8',
    xl: 'gap-8 md:gap-10',
  }

  const getGridCols = () => {
    const classes = []

    if (cols.default) classes.push(`grid-cols-${cols.default}`)
    if (cols.sm) classes.push(`sm:grid-cols-${cols.sm}`)
    if (cols.md) classes.push(`md:grid-cols-${cols.md}`)
    if (cols.lg) classes.push(`lg:grid-cols-${cols.lg}`)
    if (cols.xl) classes.push(`xl:grid-cols-${cols.xl}`)
    if (cols['2xl']) classes.push(`2xl:grid-cols-${cols['2xl']}`)

    return classes.join(' ')
  }

  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.3 : 0,
        staggerChildren:
          staggerChildren && userPreferences.animationsEnabled ? 0.1 : 0,
      },
    },
  }

  const itemVariants = {
    initial: {
      opacity: 0,
      y: userPreferences.animationsEnabled ? 20 : 0,
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.4 : 0,
        ease: [0.4, 0, 0.2, 1] as const,
      },
    },
  }

  const Container = animate ? motion.div : 'div'

  return (
    <Container
      {...(animate && {
        variants: containerVariants,
        initial: 'initial',
        animate: 'animate',
      })}
      className={cn(
        'grid',
        getGridCols(),
        gapClasses[gap],
        userPreferences.compactMode && 'gap-2 md:gap-4',
        className
      )}
    >
      {animate && staggerChildren
        ? React.Children.map(children, (child, index) => (
            <motion.div key={index} variants={itemVariants}>
              {child}
            </motion.div>
          ))
        : children}
    </Container>
  )
}
