'use client'

import { motion } from 'framer-motion'
import { <PERSON>, Setting<PERSON>, User, <PERSON>, <PERSON>, Monitor } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { GlobalSearch } from '@/components/navigation'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useTheme } from '@/hooks/use-theme'
import { useAuth } from '@/hooks/use-auth'
import { signOut } from '@/lib/auth-client'

/**
 * 应用顶部导航栏组件
 * 包含搜索框、通知中心、用户信息、主题切换和用户偏好设置
 */
export function Header() {
  const {
    theme,
    setTheme,
    userPreferences,
    toggleCompactMode,
    toggleAnimations,
  } = useTheme()
  const { user } = useAuth()

  const themeIcons = {
    light: Sun,
    dark: Moon,
    system: Monitor,
  }

  const ThemeIcon = themeIcons[theme]

  return (
    <motion.header
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
    >
      <div className="flex h-16 items-center gap-4 px-4 md:px-6">
        {/* 侧边栏触发器 */}
        <SidebarTrigger />

        {/* 系统标题 */}
        <div className="flex items-center gap-2">
          <h1 className="text-lg font-semibold text-primary md:text-xl">
            养老院管理系统
          </h1>
        </div>

        {/* 搜索框 */}
        <div className="flex-1 max-w-md">
          <GlobalSearch />
        </div>

        {/* 右侧操作区 */}
        <div className="flex items-center gap-1 sm:gap-2">
          {/* 通知中心 */}
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-4 w-4 sm:h-5 sm:w-5" />
            <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
              3
            </span>
            <span className="sr-only">通知</span>
          </Button>

          {/* 主题和偏好设置 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <ThemeIcon className="h-4 w-4 sm:h-5 sm:w-5" />
                <span className="sr-only">主题和偏好设置</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>主题设置</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setTheme('light')}>
                <Sun className="mr-2 h-4 w-4" />
                明亮模式
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('dark')}>
                <Moon className="mr-2 h-4 w-4" />
                暗黑模式
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('system')}>
                <Monitor className="mr-2 h-4 w-4" />
                跟随系统
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>偏好设置</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => toggleCompactMode()}>
                <Settings className="mr-2 h-4 w-4" />
                {userPreferences.compactMode ? '标准模式' : '紧凑模式'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => toggleAnimations()}>
                <Settings className="mr-2 h-4 w-4" />
                {userPreferences.animationsEnabled ? '禁用动画' : '启用动画'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* 用户菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative">
                <User className="h-4 w-4 sm:h-5 sm:w-5" />
                <span className="sr-only">用户菜单</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user?.name || '用户'}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user?.email || '<EMAIL>'}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                个人资料
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                系统设置
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={signOut}>退出登录</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </motion.header>
  )
}
