'use client'

import { ReactNode } from 'react'
import { motion } from 'framer-motion'
import { useTheme } from '@/hooks/use-theme'
import { cn } from '@/lib/utils'

interface ResponsiveContainerProps {
  children: ReactNode
  className?: string
  animate?: boolean
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '7xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
}

/**
 * 响应式容器组件
 * 提供一致的响应式布局和动画效果
 */
export function ResponsiveContainer({
  children,
  className,
  animate = true,
  maxWidth = '7xl',
  padding = 'md',
}: ResponsiveContainerProps) {
  const { userPreferences } = useTheme()

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full',
  }

  const paddingClasses = {
    none: '',
    sm: 'p-2 sm:p-3 md:p-4',
    md: 'p-3 sm:p-4 md:p-6 lg:p-8',
    lg: 'p-4 sm:p-6 md:p-8 lg:p-10',
    xl: 'p-6 sm:p-8 md:p-10 lg:p-12',
  }

  const containerVariants = {
    initial: {
      opacity: 0,
      y: animate && userPreferences.animationsEnabled ? 20 : 0,
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: userPreferences.animationsEnabled ? 0.4 : 0,
        ease: [0.4, 0, 0.2, 1] as const,
      },
    },
  }

  const Container = animate ? motion.div : 'div'

  return (
    <Container
      {...(animate && {
        variants: containerVariants,
        initial: 'initial',
        animate: 'animate',
      })}
      className={cn(
        'mx-auto w-full',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        userPreferences.compactMode && padding !== 'none' && 'compact-layout',
        className
      )}
    >
      {children}
    </Container>
  )
}
