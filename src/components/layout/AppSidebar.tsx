'use client'

import { motion } from 'framer-motion'
import {
  Home,
  Users,
  UserPlus,
  Heart,
  DollarSign,
  Package,
  Settings,
  ChevronRight,
  Building2,
  ClipboardList,
  Calendar,
  FileText,
  BarChart3,
  Shield,
  Database,
} from 'lucide-react'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { useState, useEffect } from 'react'

// 导航菜单数据结构
const navigationItems = [
  {
    title: '首页',
    icon: Home,
    url: '/',
  },
  {
    title: '营销管理',
    icon: UserPlus,
    items: [
      {
        title: '咨询接待',
        icon: Users,
        url: '/marketing/consultation',
      },
      {
        title: '跟进记录',
        icon: ClipboardList,
        url: '/marketing/follow-up',
      },
      {
        title: '业绩统计',
        icon: BarChart3,
        url: '/marketing/performance',
      },
    ],
  },
  {
    title: '居住管理',
    icon: Building2,
    items: [
      {
        title: '入住总览',
        icon: Home,
        url: '/residence/overview',
      },
      {
        title: '预订管理',
        icon: Calendar,
        url: '/residence/reservation',
      },
      {
        title: '入住流程',
        icon: FileText,
        url: '/residence/checkin',
      },
      {
        title: '退住管理',
        icon: Users,
        url: '/residence/checkout',
      },
    ],
  },
  {
    title: '护理服务',
    icon: Heart,
    items: [
      {
        title: '在住服务',
        icon: Users,
        url: '/nursing/service',
      },
      {
        title: '护理计划',
        icon: ClipboardList,
        url: '/nursing/plan',
      },
      {
        title: '护理记录',
        icon: FileText,
        url: '/nursing/record',
      },
      {
        title: '照护看板',
        icon: BarChart3,
        url: '/nursing/dashboard',
      },
    ],
  },
  {
    title: '财务管理',
    icon: DollarSign,
    items: [
      {
        title: '费用账单',
        icon: FileText,
        url: '/finance/bill',
      },
      {
        title: '缴费管理',
        icon: DollarSign,
        url: '/finance/payment',
      },
      {
        title: '费用查询',
        icon: BarChart3,
        url: '/finance/query',
      },
      {
        title: '财务报表',
        icon: ClipboardList,
        url: '/finance/report',
      },
    ],
  },
  {
    title: '库存管理',
    icon: Package,
    items: [
      {
        title: '库存查询',
        icon: Package,
        url: '/inventory/query',
      },
      {
        title: '入库管理',
        icon: Package,
        url: '/inventory/inbound',
      },
      {
        title: '出库管理',
        icon: Package,
        url: '/inventory/outbound',
      },
      {
        title: '调拨管理',
        icon: Package,
        url: '/inventory/transfer',
      },
    ],
  },
  {
    title: '系统管理',
    icon: Settings,
    items: [
      {
        title: '用户管理',
        icon: Users,
        url: '/system/users',
      },
      {
        title: '角色管理',
        icon: Shield,
        url: '/system/roles',
      },
      {
        title: '系统日志',
        icon: FileText,
        url: '/system/logs',
      },
      {
        title: '基础配置',
        icon: Database,
        url: '/system/config',
      },
    ],
  },
]

/**
 * 应用侧边栏组件
 * 包含多级导航菜单、动画效果和响应式设计
 */
export function AppSidebar() {
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev =>
      prev.includes(title)
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  const isActive = (url: string) => {
    return pathname === url
  }

  const isParentActive = (items: any[]) => {
    return items.some(
      item => pathname === item.url || pathname.startsWith(item.url + '/')
    )
  }

  // 自动展开包含当前路径的菜单项
  useEffect(() => {
    navigationItems.forEach(item => {
      if (item.items && isParentActive(item.items)) {
        setExpandedItems(prev =>
          prev.includes(item.title) ? prev : [...prev, item.title]
        )
      }
    })
  }, [pathname])

  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="flex items-center gap-2 px-4 py-2"
        >
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <Building2 className="h-4 w-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">养老院管理</span>
            <span className="truncate text-xs text-muted-foreground">
              专业管理系统
            </span>
          </div>
        </motion.div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>主要功能</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item, index) => (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <SidebarMenuItem>
                    {item.items ? (
                      <>
                        <SidebarMenuButton
                          onClick={() => toggleExpanded(item.title)}
                          isActive={isParentActive(item.items)}
                          className="w-full justify-between"
                        >
                          <div className="flex items-center gap-2">
                            <item.icon className="h-4 w-4" />
                            <span>{item.title}</span>
                          </div>
                          <ChevronRight
                            className={`h-4 w-4 transition-transform ${
                              expandedItems.includes(item.title)
                                ? 'rotate-90'
                                : ''
                            }`}
                          />
                        </SidebarMenuButton>
                        {expandedItems.includes(item.title) && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <SidebarMenuSub>
                              {item.items.map(subItem => (
                                <SidebarMenuSubItem key={subItem.title}>
                                  <SidebarMenuSubButton
                                    asChild
                                    isActive={isActive(subItem.url)}
                                  >
                                    <Link href={subItem.url}>
                                      <subItem.icon className="h-4 w-4" />
                                      <span>{subItem.title}</span>
                                    </Link>
                                  </SidebarMenuSubButton>
                                </SidebarMenuSubItem>
                              ))}
                            </SidebarMenuSub>
                          </motion.div>
                        )}
                      </>
                    ) : (
                      <SidebarMenuButton asChild isActive={isActive(item.url!)}>
                        <Link href={item.url!}>
                          <item.icon className="h-4 w-4" />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    )}
                  </SidebarMenuItem>
                </motion.div>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          className="px-4 py-2 text-xs text-muted-foreground"
        >
          <p>&copy; 2024 养老院管理系统</p>
          <p>版本 1.0.0</p>
        </motion.div>
      </SidebarFooter>
    </Sidebar>
  )
}
