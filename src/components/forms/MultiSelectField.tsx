/**
 * 多选表单字段组件
 * 
 * 支持多种多选模式：
 * - 下拉多选
 * - 复选框组
 * - 标签选择
 * - 搜索多选
 */

'use client'

import React from 'react'
import { Check, ChevronsUpDown, X, Search } from 'lucide-react'
import { Control, FieldPath, FieldValues } from 'react-hook-form'
import { motion, AnimatePresence } from 'framer-motion'

import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'

// 选项类型
export interface MultiSelectOption {
  value: string
  label: string
  description?: string
  disabled?: boolean
  group?: string
}

// 多选模式
export type MultiSelectMode = 'dropdown' | 'checkbox' | 'tags' | 'search'

// 多选字段属性
export interface MultiSelectFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  control: Control<TFieldValues>
  name: TName
  label?: string
  description?: string
  placeholder?: string
  options: MultiSelectOption[]
  mode?: MultiSelectMode
  maxSelections?: number
  searchable?: boolean
  clearable?: boolean
  required?: boolean
  disabled?: boolean
  className?: string
  optionClassName?: string
  badgeClassName?: string
  emptyMessage?: string
  searchPlaceholder?: string
}

// 下拉多选组件
const DropdownMultiSelect: React.FC<{
  value: string[]
  onChange: (value: string[]) => void
  options: MultiSelectOption[]
  placeholder: string
  searchable: boolean
  disabled: boolean
  maxSelections?: number
  emptyMessage: string
  searchPlaceholder: string
}> = ({
  value = [],
  onChange,
  options,
  placeholder,
  searchable,
  disabled,
  maxSelections,
  emptyMessage,
  searchPlaceholder,
}) => {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState('')

  const filteredOptions = React.useMemo(() => {
    if (!searchValue) return options
    return options.filter(option =>
      option.label.toLowerCase().includes(searchValue.toLowerCase()) ||
      option.description?.toLowerCase().includes(searchValue.toLowerCase())
    )
  }, [options, searchValue])

  const selectedOptions = React.useMemo(() => {
    return options.filter(option => value.includes(option.value))
  }, [options, value])

  const handleSelect = (optionValue: string) => {
    const newValue = value.includes(optionValue)
      ? value.filter(v => v !== optionValue)
      : maxSelections && value.length >= maxSelections
        ? value
        : [...value, optionValue]
    
    onChange(newValue)
  }

  const handleRemove = (optionValue: string) => {
    onChange(value.filter(v => v !== optionValue))
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          <div className="flex flex-wrap gap-1">
            {selectedOptions.length === 0 ? (
              <span className="text-muted-foreground">{placeholder}</span>
            ) : (
              selectedOptions.slice(0, 3).map(option => (
                <Badge
                  key={option.value}
                  variant="secondary"
                  className="text-xs"
                >
                  {option.label}
                  <X
                    className="ml-1 h-3 w-3 cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleRemove(option.value)
                    }}
                  />
                </Badge>
              ))
            )}
            {selectedOptions.length > 3 && (
              <Badge variant="secondary" className="text-xs">
                +{selectedOptions.length - 3}
              </Badge>
            )}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-full p-0">
        <Command>
          {searchable && (
            <CommandInput
              placeholder={searchPlaceholder}
              value={searchValue}
              onValueChange={setSearchValue}
            />
          )}
          <CommandEmpty>{emptyMessage}</CommandEmpty>
          <CommandGroup className="max-h-64 overflow-auto">
            {filteredOptions.map(option => (
              <CommandItem
                key={option.value}
                value={option.value}
                onSelect={() => handleSelect(option.value)}
                disabled={option.disabled}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    value.includes(option.value) ? 'opacity-100' : 'opacity-0'
                  )}
                />
                <div className="flex-1">
                  <div>{option.label}</div>
                  {option.description && (
                    <div className="text-xs text-muted-foreground">
                      {option.description}
                    </div>
                  )}
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

// 复选框组多选组件
const CheckboxMultiSelect: React.FC<{
  value: string[]
  onChange: (value: string[]) => void
  options: MultiSelectOption[]
  disabled: boolean
  maxSelections?: number
  optionClassName?: string
}> = ({ value = [], onChange, options, disabled, maxSelections, optionClassName }) => {
  const handleChange = (optionValue: string, checked: boolean) => {
    const newValue = checked
      ? maxSelections && value.length >= maxSelections
        ? value
        : [...value, optionValue]
      : value.filter(v => v !== optionValue)
    
    onChange(newValue)
  }

  // 按组分组选项
  const groupedOptions = React.useMemo(() => {
    const groups: Record<string, MultiSelectOption[]> = {}
    
    options.forEach(option => {
      const group = option.group || 'default'
      if (!groups[group]) groups[group] = []
      groups[group].push(option)
    })
    
    return groups
  }, [options])

  return (
    <div className="space-y-4">
      {Object.entries(groupedOptions).map(([group, groupOptions]) => (
        <div key={group} className="space-y-2">
          {group !== 'default' && (
            <Label className="text-sm font-medium text-muted-foreground">
              {group}
            </Label>
          )}
          <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
            {groupOptions.map(option => (
              <div
                key={option.value}
                className={cn(
                  'flex items-center space-x-2 rounded-md border p-3',
                  option.disabled && 'opacity-50',
                  optionClassName
                )}
              >
                <Checkbox
                  id={option.value}
                  checked={value.includes(option.value)}
                  onCheckedChange={(checked) =>
                    handleChange(option.value, checked as boolean)
                  }
                  disabled={disabled || option.disabled}
                />
                <div className="flex-1">
                  <Label
                    htmlFor={option.value}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {option.label}
                  </Label>
                  {option.description && (
                    <p className="text-xs text-muted-foreground">
                      {option.description}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

// 标签多选组件
const TagsMultiSelect: React.FC<{
  value: string[]
  onChange: (value: string[]) => void
  options: MultiSelectOption[]
  disabled: boolean
  maxSelections?: number
  badgeClassName?: string
}> = ({ value = [], onChange, options, disabled, maxSelections, badgeClassName }) => {
  const handleToggle = (optionValue: string) => {
    const newValue = value.includes(optionValue)
      ? value.filter(v => v !== optionValue)
      : maxSelections && value.length >= maxSelections
        ? value
        : [...value, optionValue]
    
    onChange(newValue)
  }

  return (
    <div className="flex flex-wrap gap-2">
      {options.map(option => {
        const isSelected = value.includes(option.value)
        return (
          <Badge
            key={option.value}
            variant={isSelected ? 'default' : 'outline'}
            className={cn(
              'cursor-pointer transition-colors',
              option.disabled && 'cursor-not-allowed opacity-50',
              badgeClassName
            )}
            onClick={() => !disabled && !option.disabled && handleToggle(option.value)}
          >
            {option.label}
            {isSelected && <Check className="ml-1 h-3 w-3" />}
          </Badge>
        )
      })}
    </div>
  )
}

// 多选字段组件
export function MultiSelectField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  control,
  name,
  label,
  description,
  placeholder = '请选择',
  options,
  mode = 'dropdown',
  maxSelections,
  searchable = true,
  clearable = true,
  required = false,
  disabled = false,
  className,
  optionClassName,
  badgeClassName,
  emptyMessage = '没有找到选项',
  searchPlaceholder = '搜索选项...',
}: MultiSelectFieldProps<TFieldValues, TName>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem className={cn('space-y-2', className)}>
          {label && (
            <FormLabel className={cn(required && 'after:content-["*"] after:text-red-500 after:ml-1')}>
              {label}
            </FormLabel>
          )}
          
          <FormControl>
            <div className="space-y-2">
              {mode === 'dropdown' && (
                <DropdownMultiSelect
                  value={field.value || []}
                  onChange={field.onChange}
                  options={options}
                  placeholder={placeholder}
                  searchable={searchable}
                  disabled={disabled}
                  maxSelections={maxSelections}
                  emptyMessage={emptyMessage}
                  searchPlaceholder={searchPlaceholder}
                />
              )}
              
              {mode === 'checkbox' && (
                <CheckboxMultiSelect
                  value={field.value || []}
                  onChange={field.onChange}
                  options={options}
                  disabled={disabled}
                  maxSelections={maxSelections}
                  optionClassName={optionClassName}
                />
              )}
              
              {mode === 'tags' && (
                <TagsMultiSelect
                  value={field.value || []}
                  onChange={field.onChange}
                  options={options}
                  disabled={disabled}
                  maxSelections={maxSelections}
                  badgeClassName={badgeClassName}
                />
              )}
              
              {clearable && field.value?.length > 0 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => field.onChange([])}
                  disabled={disabled}
                  className="h-auto p-1 text-xs"
                >
                  清除所有
                </Button>
              )}
            </div>
          </FormControl>

          {description && (
            <FormDescription>{description}</FormDescription>
          )}
          
          <AnimatePresence>
            {fieldState.error && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
              >
                <FormMessage />
              </motion.div>
            )}
          </AnimatePresence>
        </FormItem>
      )}
    />
  )
}

export default MultiSelectField
