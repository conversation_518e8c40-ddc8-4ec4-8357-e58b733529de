/**
 * DatePickerField 组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useForm } from 'react-hook-form'
import { format, addDays, subDays } from 'date-fns'

import { Form } from '@/components/ui/form'
import DatePickerField from '../DatePickerField'

// 测试表单组件
const TestForm: React.FC<{
  onSubmit: (data: any) => void
  defaultValues?: any
  children: React.ReactNode
}> = ({ onSubmit, defaultValues, children }) => {
  const form = useForm({ defaultValues })

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        {children}
        <button type="submit">提交</button>
      </form>
    </Form>
  )
}

describe('DatePickerField', () => {
  const mockSubmit = jest.fn()

  beforeEach(() => {
    mockSubmit.mockClear()
  })

  describe('基础功能', () => {
    it('应该渲染日期选择器', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="date"
            label="日期"
            placeholder="请选择日期"
          />
        </TestForm>
      )

      expect(screen.getByLabelText('日期')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('请选择日期')).toBeInTheDocument()
    })

    it('应该显示必填标记', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="date"
            label="日期"
            required
          />
        </TestForm>
      )

      expect(screen.getByText('*')).toBeInTheDocument()
    })

    it('应该显示描述信息', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="date"
            label="日期"
            description="请选择一个日期"
          />
        </TestForm>
      )

      expect(screen.getByText('请选择一个日期')).toBeInTheDocument()
    })

    it('应该支持禁用状态', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="date"
            label="日期"
            disabled
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      expect(trigger).toBeDisabled()
    })
  })

  describe('单日期选择', () => {
    it('应该打开日期选择器', async () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="date"
            label="日期"
            type="single"
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      await userEvent.click(trigger)

      // 检查是否显示日历
      expect(screen.getByRole('dialog')).toBeInTheDocument()
    })

    it('应该选择日期', async () => {
      const today = new Date()
      
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="date"
            label="日期"
            type="single"
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      await userEvent.click(trigger)

      // 选择今天的日期
      const todayButton = screen.getByText(today.getDate().toString())
      await userEvent.click(todayButton)

      // 检查日期是否被选中
      expect(trigger).toHaveTextContent(format(today, 'yyyy年M月d日'))
    })

    it('应该支持默认值', () => {
      const defaultDate = new Date('2023-12-25')
      
      render(
        <TestForm 
          onSubmit={mockSubmit}
          defaultValues={{ date: defaultDate }}
        >
          <DatePickerField
            name="date"
            label="日期"
            type="single"
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      expect(trigger).toHaveTextContent('2023年12月25日')
    })
  })

  describe('日期范围选择', () => {
    it('应该选择日期范围', async () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="dateRange"
            label="日期范围"
            type="range"
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      await userEvent.click(trigger)

      const today = new Date()
      const tomorrow = addDays(today, 1)

      // 选择开始日期
      const startDateButton = screen.getByText(today.getDate().toString())
      await userEvent.click(startDateButton)

      // 选择结束日期
      const endDateButton = screen.getByText(tomorrow.getDate().toString())
      await userEvent.click(endDateButton)

      // 检查日期范围是否被选中
      const expectedText = `${format(today, 'yyyy年M月d日')} - ${format(tomorrow, 'yyyy年M月d日')}`
      expect(trigger).toHaveTextContent(expectedText)
    })
  })

  describe('多日期选择', () => {
    it('应该选择多个日期', async () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="dates"
            label="多个日期"
            type="multiple"
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      await userEvent.click(trigger)

      const today = new Date()
      const tomorrow = addDays(today, 1)

      // 选择第一个日期
      const firstDateButton = screen.getByText(today.getDate().toString())
      await userEvent.click(firstDateButton)

      // 选择第二个日期
      const secondDateButton = screen.getByText(tomorrow.getDate().toString())
      await userEvent.click(secondDateButton)

      // 检查是否显示选中的日期数量
      expect(trigger).toHaveTextContent('已选择 2 个日期')
    })
  })

  describe('时间选择器', () => {
    it('应该显示时间选择器', async () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="datetime"
            label="日期时间"
            type="single"
            timePicker={{ enabled: true }}
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      await userEvent.click(trigger)

      // 检查是否显示时间选择器
      expect(screen.getByText('时间')).toBeInTheDocument()
    })

    it('应该支持12小时制', async () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="datetime"
            label="日期时间"
            type="single"
            timePicker={{ 
              enabled: true,
              format: '12h'
            }}
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      await userEvent.click(trigger)

      // 检查是否显示AM/PM选择器
      expect(screen.getByText('AM')).toBeInTheDocument()
      expect(screen.getByText('PM')).toBeInTheDocument()
    })
  })

  describe('日期限制', () => {
    it('应该限制最小日期', async () => {
      const minDate = new Date()
      
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="date"
            label="日期"
            type="single"
            minDate={minDate}
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      await userEvent.click(trigger)

      // 检查昨天的日期是否被禁用
      const yesterday = subDays(minDate, 1)
      const yesterdayButton = screen.queryByText(yesterday.getDate().toString())
      
      if (yesterdayButton) {
        expect(yesterdayButton).toHaveAttribute('aria-disabled', 'true')
      }
    })

    it('应该限制最大日期', async () => {
      const maxDate = new Date()
      
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="date"
            label="日期"
            type="single"
            maxDate={maxDate}
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      await userEvent.click(trigger)

      // 检查明天的日期是否被禁用
      const tomorrow = addDays(maxDate, 1)
      const tomorrowButton = screen.queryByText(tomorrow.getDate().toString())
      
      if (tomorrowButton) {
        expect(tomorrowButton).toHaveAttribute('aria-disabled', 'true')
      }
    })

    it('应该禁用指定日期', async () => {
      const today = new Date()
      const disabledDates = [today]
      
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="date"
            label="日期"
            type="single"
            disabledDates={disabledDates}
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      await userEvent.click(trigger)

      // 检查今天的日期是否被禁用
      const todayButton = screen.getByText(today.getDate().toString())
      expect(todayButton).toHaveAttribute('aria-disabled', 'true')
    })
  })

  describe('表单提交', () => {
    it('应该提交正确的日期数据', async () => {
      const testDate = new Date('2023-12-25')
      
      render(
        <TestForm 
          onSubmit={mockSubmit}
          defaultValues={{ date: testDate }}
        >
          <DatePickerField
            name="date"
            label="日期"
            type="single"
          />
        </TestForm>
      )

      const submitButton = screen.getByText('提交')
      await userEvent.click(submitButton)

      await waitFor(() => {
        expect(mockSubmit).toHaveBeenCalledWith({
          date: testDate,
        })
      })
    })

    it('应该提交日期范围数据', async () => {
      const startDate = new Date('2023-12-25')
      const endDate = new Date('2023-12-31')
      
      render(
        <TestForm 
          onSubmit={mockSubmit}
          defaultValues={{ 
            dateRange: { from: startDate, to: endDate }
          }}
        >
          <DatePickerField
            name="dateRange"
            label="日期范围"
            type="range"
          />
        </TestForm>
      )

      const submitButton = screen.getByText('提交')
      await userEvent.click(submitButton)

      await waitFor(() => {
        expect(mockSubmit).toHaveBeenCalledWith({
          dateRange: { from: startDate, to: endDate },
        })
      })
    })

    it('应该提交多个日期数据', async () => {
      const dates = [
        new Date('2023-12-25'),
        new Date('2023-12-26'),
        new Date('2023-12-27'),
      ]
      
      render(
        <TestForm 
          onSubmit={mockSubmit}
          defaultValues={{ dates }}
        >
          <DatePickerField
            name="dates"
            label="多个日期"
            type="multiple"
          />
        </TestForm>
      )

      const submitButton = screen.getByText('提交')
      await userEvent.click(submitButton)

      await waitFor(() => {
        expect(mockSubmit).toHaveBeenCalledWith({
          dates,
        })
      })
    })
  })

  describe('键盘导航', () => {
    it('应该支持键盘导航', async () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <DatePickerField
            name="date"
            label="日期"
            type="single"
          />
        </TestForm>
      )

      const trigger = screen.getByRole('button')
      
      // 使用 Enter 键打开日期选择器
      trigger.focus()
      fireEvent.keyDown(trigger, { key: 'Enter' })

      expect(screen.getByRole('dialog')).toBeInTheDocument()

      // 使用 Escape 键关闭日期选择器
      fireEvent.keyDown(document, { key: 'Escape' })

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
      })
    })
  })
})
