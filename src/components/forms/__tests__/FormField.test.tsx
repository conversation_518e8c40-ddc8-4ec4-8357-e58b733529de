/**
 * FormField 组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

import { Form } from '@/components/ui/form'
import { TextField } from '../FormField'

// 测试表单组件
const TestForm: React.FC<{
  onSubmit: (data: any) => void
  schema?: z.ZodSchema<any>
  defaultValues?: any
  children: React.ReactNode
}> = ({ onSubmit, schema, defaultValues, children }) => {
  const form = useForm({
    resolver: schema ? zodResolver(schema) : undefined,
    defaultValues,
  })

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        {children}
        <button type="submit">提交</button>
      </form>
    </Form>
  )
}

describe('TextField', () => {
  const mockSubmit = jest.fn()

  beforeEach(() => {
    mockSubmit.mockClear()
  })

  describe('基础功能', () => {
    it('应该渲染文本输入框', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="name"
            label="姓名"
            placeholder="请输入姓名"
          />
        </TestForm>
      )

      expect(screen.getByLabelText('姓名')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('请输入姓名')).toBeInTheDocument()
    })

    it('应该显示必填标记', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="name"
            label="姓名"
            required
          />
        </TestForm>
      )

      expect(screen.getByText('*')).toBeInTheDocument()
    })

    it('应该显示描述信息', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="name"
            label="姓名"
            description="请输入您的真实姓名"
          />
        </TestForm>
      )

      expect(screen.getByText('请输入您的真实姓名')).toBeInTheDocument()
    })

    it('应该支持禁用状态', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="name"
            label="姓名"
            disabled
          />
        </TestForm>
      )

      const input = screen.getByLabelText('姓名')
      expect(input).toBeDisabled()
    })
  })

  describe('输入类型', () => {
    it('应该渲染数字输入框', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="age"
            label="年龄"
            type="number"
          />
        </TestForm>
      )

      const input = screen.getByLabelText('年龄')
      expect(input).toHaveAttribute('type', 'number')
    })

    it('应该渲染邮箱输入框', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="email"
            label="邮箱"
            type="email"
          />
        </TestForm>
      )

      const input = screen.getByLabelText('邮箱')
      expect(input).toHaveAttribute('type', 'email')
    })

    it('应该渲染密码输入框', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="password"
            label="密码"
            type="password"
          />
        </TestForm>
      )

      const input = screen.getByLabelText('密码')
      expect(input).toHaveAttribute('type', 'password')
    })

    it('应该渲染文本域', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="description"
            label="描述"
            type="textarea"
          />
        </TestForm>
      )

      expect(screen.getByRole('textbox')).toBeInTheDocument()
    })
  })

  describe('选择类型', () => {
    const options = [
      { value: 'option1', label: '选项1' },
      { value: 'option2', label: '选项2' },
      { value: 'option3', label: '选项3' },
    ]

    it('应该渲染下拉选择框', async () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="select"
            label="选择"
            type="select"
            options={options}
          />
        </TestForm>
      )

      const trigger = screen.getByRole('combobox')
      expect(trigger).toBeInTheDocument()

      await userEvent.click(trigger)
      expect(screen.getByText('选项1')).toBeInTheDocument()
      expect(screen.getByText('选项2')).toBeInTheDocument()
      expect(screen.getByText('选项3')).toBeInTheDocument()
    })

    it('应该渲染单选按钮组', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="radio"
            label="单选"
            type="radio"
            options={options}
          />
        </TestForm>
      )

      expect(screen.getByDisplayValue('option1')).toBeInTheDocument()
      expect(screen.getByDisplayValue('option2')).toBeInTheDocument()
      expect(screen.getByDisplayValue('option3')).toBeInTheDocument()
    })

    it('应该渲染复选框', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="checkbox"
            label="复选框"
            type="checkbox"
          />
        </TestForm>
      )

      const checkbox = screen.getByRole('checkbox')
      expect(checkbox).toBeInTheDocument()
    })

    it('应该渲染开关', () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="switch"
            label="开关"
            type="switch"
          />
        </TestForm>
      )

      const switchElement = screen.getByRole('switch')
      expect(switchElement).toBeInTheDocument()
    })
  })

  describe('表单验证', () => {
    const schema = z.object({
      name: z.string().min(1, '姓名不能为空'),
      email: z.string().email('邮箱格式不正确'),
      age: z.number().min(0, '年龄不能为负数').max(150, '年龄不能超过150'),
    })

    it('应该显示验证错误', async () => {
      render(
        <TestForm onSubmit={mockSubmit} schema={schema}>
          <TextField name="name" label="姓名" />
          <TextField name="email" label="邮箱" type="email" />
          <TextField name="age" label="年龄" type="number" />
        </TestForm>
      )

      const submitButton = screen.getByText('提交')
      await userEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('姓名不能为空')).toBeInTheDocument()
      })
    })

    it('应该在输入有效数据后清除错误', async () => {
      render(
        <TestForm onSubmit={mockSubmit} schema={schema}>
          <TextField name="name" label="姓名" />
        </TestForm>
      )

      const submitButton = screen.getByText('提交')
      await userEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('姓名不能为空')).toBeInTheDocument()
      })

      const nameInput = screen.getByLabelText('姓名')
      await userEvent.type(nameInput, '张三')

      await waitFor(() => {
        expect(screen.queryByText('姓名不能为空')).not.toBeInTheDocument()
      })
    })
  })

  describe('表单提交', () => {
    it('应该提交正确的数据', async () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField name="name" label="姓名" />
          <TextField name="email" label="邮箱" type="email" />
          <TextField name="age" label="年龄" type="number" />
        </TestForm>
      )

      const nameInput = screen.getByLabelText('姓名')
      const emailInput = screen.getByLabelText('邮箱')
      const ageInput = screen.getByLabelText('年龄')

      await userEvent.type(nameInput, '张三')
      await userEvent.type(emailInput, '<EMAIL>')
      await userEvent.type(ageInput, '30')

      const submitButton = screen.getByText('提交')
      await userEvent.click(submitButton)

      await waitFor(() => {
        expect(mockSubmit).toHaveBeenCalledWith({
          name: '张三',
          email: '<EMAIL>',
          age: 30,
        })
      })
    })

    it('应该支持默认值', () => {
      const defaultValues = {
        name: '李四',
        email: '<EMAIL>',
        age: 25,
      }

      render(
        <TestForm onSubmit={mockSubmit} defaultValues={defaultValues}>
          <TextField name="name" label="姓名" />
          <TextField name="email" label="邮箱" type="email" />
          <TextField name="age" label="年龄" type="number" />
        </TestForm>
      )

      expect(screen.getByDisplayValue('李四')).toBeInTheDocument()
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
      expect(screen.getByDisplayValue('25')).toBeInTheDocument()
    })
  })

  describe('用户交互', () => {
    it('应该响应输入事件', async () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField name="name" label="姓名" />
        </TestForm>
      )

      const input = screen.getByLabelText('姓名')
      await userEvent.type(input, '测试输入')

      expect(input).toHaveValue('测试输入')
    })

    it('应该响应选择事件', async () => {
      const options = [
        { value: 'option1', label: '选项1' },
        { value: 'option2', label: '选项2' },
      ]

      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField
            name="select"
            label="选择"
            type="select"
            options={options}
          />
        </TestForm>
      )

      const trigger = screen.getByRole('combobox')
      await userEvent.click(trigger)

      const option = screen.getByText('选项1')
      await userEvent.click(option)

      expect(trigger).toHaveTextContent('选项1')
    })

    it('应该响应复选框切换', async () => {
      render(
        <TestForm onSubmit={mockSubmit}>
          <TextField name="checkbox" label="复选框" type="checkbox" />
        </TestForm>
      )

      const checkbox = screen.getByRole('checkbox')
      expect(checkbox).not.toBeChecked()

      await userEvent.click(checkbox)
      expect(checkbox).toBeChecked()

      await userEvent.click(checkbox)
      expect(checkbox).not.toBeChecked()
    })
  })
})
