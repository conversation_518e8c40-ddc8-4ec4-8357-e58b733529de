/**
 * 表单模板系统
 * 
 * 提供预定义的表单模板，包括：
 * - 老人信息表单
 * - 护理记录表单
 * - 房间管理表单
 * - 费用管理表单
 * - 库存管理表单
 */

'use client'

import React from 'react'
import { UseFormReturn } from 'react-hook-form'
import { z } from 'zod'

import { BusinessSchemas, BusinessValidationRules } from '@/lib/form-validation'
import { FormLayout, FormSection } from './FormLayout'
import { DynamicForm, DynamicFieldConfig } from './DynamicFormField'
import { TextField } from './FormField'
import DatePickerField from './DatePickerField'
import MultiSelectField from './MultiSelectField'

// 模板类型
export type FormTemplateType = 
  | 'elder-info'
  | 'nursing-record'
  | 'room-info'
  | 'fee-bill'
  | 'inventory-item'
  | 'supplier-info'
  | 'device-info'

// 表单模板属性
export interface FormTemplateProps {
  type: FormTemplateType
  form: UseFormReturn<any>
  layout?: 'grid' | 'columns' | 'cards' | 'steps'
  className?: string
  onSubmit?: (data: any) => void
}

// 老人信息表单模板
const ElderInfoTemplate: React.FC<{ form: UseFormReturn<any>; layout?: string }> = ({ 
  form, 
  layout = 'cards' 
}) => {
  const sections: FormSection[] = [
    {
      id: 'basic-info',
      title: '基本信息',
      description: '老人的基本个人信息',
      card: { title: '基本信息', collapsible: true },
      children: (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <TextField
            control={form.control}
            name="name"
            label="姓名"
            placeholder="请输入老人姓名"
            required
          />
          <TextField
            control={form.control}
            name="gender"
            label="性别"
            type="select"
            options={[
              { value: '男', label: '男' },
              { value: '女', label: '女' },
            ]}
            required
          />
          <TextField
            control={form.control}
            name="age"
            label="年龄"
            type="number"
            placeholder="请输入年龄"
            required
          />
          <TextField
            control={form.control}
            name="idCard"
            label="身份证号"
            placeholder="请输入身份证号"
            required
          />
          <TextField
            control={form.control}
            name="phone"
            label="联系电话"
            placeholder="请输入联系电话"
          />
          <TextField
            control={form.control}
            name="medicalCardNumber"
            label="医保卡号"
            placeholder="请输入医保卡号"
          />
        </div>
      ),
    },
    {
      id: 'care-info',
      title: '护理信息',
      description: '护理等级和入住信息',
      card: { title: '护理信息', collapsible: true },
      children: (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <TextField
            control={form.control}
            name="careLevel"
            label="护理等级"
            type="select"
            options={[
              { value: '1', label: '一级护理' },
              { value: '2', label: '二级护理' },
              { value: '3', label: '三级护理' },
              { value: '4', label: '四级护理' },
              { value: '5', label: '五级护理' },
            ]}
            required
          />
          <TextField
            control={form.control}
            name="checkInStatus"
            label="入住状态"
            type="select"
            options={[
              { value: '待入住', label: '待入住' },
              { value: '已入住', label: '已入住' },
              { value: '请假', label: '请假' },
              { value: '退住', label: '退住' },
              { value: '转院', label: '转院' },
            ]}
            required
          />
          <DatePickerField
            control={form.control}
            name="checkInDate"
            label="入住日期"
            placeholder="请选择入住日期"
            required
          />
          <TextField
            control={form.control}
            name="roomNumber"
            label="房间号"
            placeholder="如：A101"
          />
          <TextField
            control={form.control}
            name="bedNumber"
            label="床位号"
            placeholder="如：A101-1"
          />
        </div>
      ),
    },
    {
      id: 'notes',
      title: '备注信息',
      description: '其他需要说明的信息',
      card: { title: '备注信息', collapsible: true, defaultCollapsed: true },
      children: (
        <TextField
          control={form.control}
          name="notes"
          label="备注"
          type="textarea"
          placeholder="请输入备注信息"
          className="col-span-full"
        />
      ),
    },
  ]

  return <FormLayout type={layout as any} sections={sections} />
}

// 护理记录表单模板
const NursingRecordTemplate: React.FC<{ form: UseFormReturn<any>; layout?: string }> = ({ 
  form, 
  layout = 'grid' 
}) => {
  const sections: FormSection[] = [
    {
      id: 'record-basic',
      title: '记录基本信息',
      children: (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <TextField
            control={form.control}
            name="elderId"
            label="老人"
            type="select"
            placeholder="请选择老人"
            required
          />
          <TextField
            control={form.control}
            name="recordType"
            label="护理类型"
            type="select"
            options={[
              { value: '生活护理', label: '生活护理' },
              { value: '医疗护理', label: '医疗护理' },
              { value: '康复护理', label: '康复护理' },
              { value: '心理护理', label: '心理护理' },
              { value: '其他', label: '其他' },
            ]}
            required
          />
          <TextField
            control={form.control}
            name="nurseId"
            label="护理员"
            type="select"
            placeholder="请选择护理员"
            required
          />
          <DatePickerField
            control={form.control}
            name="recordTime"
            label="记录时间"
            timePicker={{ enabled: true }}
            required
          />
        </div>
      ),
    },
    {
      id: 'record-content',
      title: '护理内容',
      children: (
        <TextField
          control={form.control}
          name="content"
          label="护理内容"
          type="textarea"
          placeholder="请详细描述护理内容"
          required
        />
      ),
    },
    {
      id: 'vital-signs',
      title: '生命体征',
      children: (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <TextField
            control={form.control}
            name="vitalSigns.bloodPressure"
            label="血压"
            placeholder="如：120/80"
          />
          <TextField
            control={form.control}
            name="vitalSigns.bodyTemperature"
            label="体温 (°C)"
            type="number"
            placeholder="如：36.5"
          />
          <TextField
            control={form.control}
            name="vitalSigns.heartRate"
            label="心率 (次/分)"
            type="number"
            placeholder="如：72"
          />
          <TextField
            control={form.control}
            name="vitalSigns.weight"
            label="体重 (kg)"
            type="number"
            placeholder="如：65.5"
          />
        </div>
      ),
    },
  ]

  return <FormLayout type={layout as any} sections={sections} />
}

// 房间信息表单模板
const RoomInfoTemplate: React.FC<{ form: UseFormReturn<any>; layout?: string }> = ({ 
  form, 
  layout = 'cards' 
}) => {
  const sections: FormSection[] = [
    {
      id: 'room-basic',
      title: '房间基本信息',
      card: { title: '基本信息' },
      children: (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <TextField
            control={form.control}
            name="roomNumber"
            label="房间号"
            placeholder="如：A101"
            required
          />
          <TextField
            control={form.control}
            name="roomType"
            label="房间类型"
            type="select"
            options={[
              { value: '单人间', label: '单人间' },
              { value: '双人间', label: '双人间' },
              { value: '三人间', label: '三人间' },
              { value: '四人间', label: '四人间' },
            ]}
            required
          />
          <TextField
            control={form.control}
            name="floor"
            label="楼层"
            type="number"
            placeholder="请输入楼层"
            required
          />
          <TextField
            control={form.control}
            name="area"
            label="面积 (㎡)"
            type="number"
            placeholder="请输入面积"
            required
          />
        </div>
      ),
    },
    {
      id: 'room-pricing',
      title: '价格信息',
      card: { title: '价格信息' },
      children: (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <TextField
            control={form.control}
            name="dailyRate"
            label="日租金 (元)"
            type="number"
            placeholder="请输入日租金"
            required
          />
          <TextField
            control={form.control}
            name="monthlyRate"
            label="月租金 (元)"
            type="number"
            placeholder="请输入月租金"
            required
          />
        </div>
      ),
    },
    {
      id: 'room-status',
      title: '状态信息',
      card: { title: '状态信息' },
      children: (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <TextField
            control={form.control}
            name="status"
            label="房间状态"
            type="select"
            options={[
              { value: '可用', label: '可用' },
              { value: '已占用', label: '已占用' },
              { value: '维修中', label: '维修中' },
              { value: '停用', label: '停用' },
            ]}
            required
          />
          <MultiSelectField
            control={form.control}
            name="facilities"
            label="房间设施"
            options={[
              { value: '空调', label: '空调' },
              { value: '电视', label: '电视' },
              { value: '冰箱', label: '冰箱' },
              { value: '独立卫生间', label: '独立卫生间' },
              { value: '阳台', label: '阳台' },
              { value: '呼叫器', label: '呼叫器' },
              { value: '无障碍设施', label: '无障碍设施' },
            ]}
            mode="tags"
          />
        </div>
      ),
    },
  ]

  return <FormLayout type={layout as any} sections={sections} />
}

// 主表单模板组件
export function FormTemplate({
  type,
  form,
  layout = 'cards',
  className,
  onSubmit,
}: FormTemplateProps) {
  const handleSubmit = form.handleSubmit((data) => {
    onSubmit?.(data)
  })

  const renderTemplate = () => {
    switch (type) {
      case 'elder-info':
        return <ElderInfoTemplate form={form} layout={layout} />
      case 'nursing-record':
        return <NursingRecordTemplate form={form} layout={layout} />
      case 'room-info':
        return <RoomInfoTemplate form={form} layout={layout} />
      default:
        return <div>未知的表单模板类型: {type}</div>
    }
  }

  return (
    <form onSubmit={handleSubmit} className={className}>
      {renderTemplate()}
    </form>
  )
}

// 获取表单模板的验证 Schema
export function getTemplateSchema(type: FormTemplateType): z.ZodSchema<any> {
  switch (type) {
    case 'elder-info':
      return BusinessSchemas.elderInfo
    case 'nursing-record':
      return BusinessSchemas.nursingRecord
    case 'room-info':
      return BusinessSchemas.roomInfo
    case 'fee-bill':
      return BusinessSchemas.feeBill
    case 'inventory-item':
      return BusinessSchemas.inventoryItem
    case 'supplier-info':
      return BusinessSchemas.supplierInfo
    case 'device-info':
      return BusinessSchemas.deviceInfo
    default:
      return z.object({})
  }
}

// 获取表单模板的默认值
export function getTemplateDefaults(type: FormTemplateType): any {
  switch (type) {
    case 'elder-info':
      return {
        gender: '',
        careLevel: '',
        checkInStatus: '待入住',
        checkInDate: new Date(),
      }
    case 'nursing-record':
      return {
        recordTime: new Date(),
        recordType: '',
        vitalSigns: {},
      }
    case 'room-info':
      return {
        roomType: '',
        status: '可用',
        facilities: [],
      }
    default:
      return {}
  }
}

export default FormTemplate
