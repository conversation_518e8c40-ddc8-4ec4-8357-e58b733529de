/**
 * 日期选择器表单字段组件
 * 
 * 基于 shadcn/ui 的 Calendar 组件和 Popover 组件实现
 * 支持单日期选择、日期范围选择、时间选择等功能
 */

'use client'

import React from 'react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { Calendar as CalendarIcon, Clock } from 'lucide-react'
import { Control, FieldPath, FieldValues } from 'react-hook-form'
import { motion, AnimatePresence } from 'framer-motion'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'

// 日期选择器类型
export type DatePickerType = 'single' | 'range' | 'multiple'

// 时间选择器配置
export interface TimePickerConfig {
  enabled: boolean
  format?: '12h' | '24h'
  minuteStep?: number
  showSeconds?: boolean
}

// 日期选择器字段属性
export interface DatePickerFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  control: Control<TFieldValues>
  name: TName
  label?: string
  description?: string
  placeholder?: string
  type?: DatePickerType
  timePicker?: TimePickerConfig
  minDate?: Date
  maxDate?: Date
  disabledDates?: Date[]
  disabledDaysOfWeek?: number[]
  required?: boolean
  disabled?: boolean
  className?: string
  calendarClassName?: string
  buttonClassName?: string
  formatString?: string
  locale?: Locale
}

// 时间选择器组件
const TimePicker: React.FC<{
  value?: Date
  onChange: (date: Date) => void
  config: TimePickerConfig
}> = ({ value, onChange, config }) => {
  const [hours, setHours] = React.useState(value?.getHours() || 0)
  const [minutes, setMinutes] = React.useState(value?.getMinutes() || 0)
  const [seconds, setSeconds] = React.useState(value?.getSeconds() || 0)
  const [period, setPeriod] = React.useState<'AM' | 'PM'>(
    (value?.getHours() || 0) >= 12 ? 'PM' : 'AM'
  )

  const updateTime = React.useCallback(() => {
    const newDate = new Date(value || new Date())
    let finalHours = hours

    if (config.format === '12h') {
      finalHours = period === 'PM' && hours !== 12 ? hours + 12 : hours
      if (period === 'AM' && hours === 12) finalHours = 0
    }

    newDate.setHours(finalHours, minutes, config.showSeconds ? seconds : 0)
    onChange(newDate)
  }, [hours, minutes, seconds, period, config, value, onChange])

  React.useEffect(() => {
    updateTime()
  }, [updateTime])

  const hourOptions = config.format === '12h' 
    ? Array.from({ length: 12 }, (_, i) => i + 1)
    : Array.from({ length: 24 }, (_, i) => i)

  const minuteOptions = Array.from(
    { length: 60 / (config.minuteStep || 1) },
    (_, i) => i * (config.minuteStep || 1)
  )

  return (
    <div className="flex items-center space-x-2 p-3">
      <Clock className="h-4 w-4 text-muted-foreground" />
      
      {/* 小时选择 */}
      <select
        value={config.format === '12h' ? (hours === 0 ? 12 : hours > 12 ? hours - 12 : hours) : hours}
        onChange={(e) => setHours(parseInt(e.target.value))}
        className="w-16 rounded border px-2 py-1 text-sm"
      >
        {hourOptions.map((hour) => (
          <option key={hour} value={hour}>
            {hour.toString().padStart(2, '0')}
          </option>
        ))}
      </select>

      <span>:</span>

      {/* 分钟选择 */}
      <select
        value={minutes}
        onChange={(e) => setMinutes(parseInt(e.target.value))}
        className="w-16 rounded border px-2 py-1 text-sm"
      >
        {minuteOptions.map((minute) => (
          <option key={minute} value={minute}>
            {minute.toString().padStart(2, '0')}
          </option>
        ))}
      </select>

      {/* 秒选择 */}
      {config.showSeconds && (
        <>
          <span>:</span>
          <select
            value={seconds}
            onChange={(e) => setSeconds(parseInt(e.target.value))}
            className="w-16 rounded border px-2 py-1 text-sm"
          >
            {Array.from({ length: 60 }, (_, i) => (
              <option key={i} value={i}>
                {i.toString().padStart(2, '0')}
              </option>
            ))}
          </select>
        </>
      )}

      {/* AM/PM 选择 */}
      {config.format === '12h' && (
        <select
          value={period}
          onChange={(e) => setPeriod(e.target.value as 'AM' | 'PM')}
          className="w-16 rounded border px-2 py-1 text-sm"
        >
          <option value="AM">AM</option>
          <option value="PM">PM</option>
        </select>
      )}
    </div>
  )
}

// 日期选择器字段组件
export function DatePickerField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  control,
  name,
  label,
  description,
  placeholder = '选择日期',
  type = 'single',
  timePicker,
  minDate,
  maxDate,
  disabledDates = [],
  disabledDaysOfWeek = [],
  required = false,
  disabled = false,
  className,
  calendarClassName,
  buttonClassName,
  formatString,
  locale = zhCN,
}: DatePickerFieldProps<TFieldValues, TName>) {
  const [open, setOpen] = React.useState(false)

  // 默认格式化字符串
  const defaultFormat = React.useMemo(() => {
    if (formatString) return formatString
    if (timePicker?.enabled) {
      return timePicker.showSeconds ? 'yyyy-MM-dd HH:mm:ss' : 'yyyy-MM-dd HH:mm'
    }
    return 'yyyy-MM-dd'
  }, [formatString, timePicker])

  // 禁用日期判断
  const isDateDisabled = React.useCallback((date: Date) => {
    // 检查最小/最大日期
    if (minDate && date < minDate) return true
    if (maxDate && date > maxDate) return true
    
    // 检查禁用的具体日期
    if (disabledDates.some(disabledDate => 
      date.toDateString() === disabledDate.toDateString()
    )) return true
    
    // 检查禁用的星期几
    if (disabledDaysOfWeek.includes(date.getDay())) return true
    
    return false
  }, [minDate, maxDate, disabledDates, disabledDaysOfWeek])

  // 格式化显示值
  const formatDisplayValue = React.useCallback((value: any) => {
    if (!value) return placeholder

    if (type === 'single' && value instanceof Date) {
      return format(value, defaultFormat, { locale })
    }

    if (type === 'range' && Array.isArray(value) && value.length === 2) {
      const [from, to] = value
      if (from && to) {
        return `${format(from, defaultFormat, { locale })} - ${format(to, defaultFormat, { locale })}`
      }
      if (from) {
        return `${format(from, defaultFormat, { locale })} - ...`
      }
    }

    if (type === 'multiple' && Array.isArray(value)) {
      return `已选择 ${value.length} 个日期`
    }

    return placeholder
  }, [type, defaultFormat, locale, placeholder])

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem className={cn('space-y-2', className)}>
          {label && (
            <FormLabel className={cn(required && 'after:content-["*"] after:text-red-500 after:ml-1')}>
              {label}
            </FormLabel>
          )}
          
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !field.value && 'text-muted-foreground',
                    fieldState.error && 'border-red-500 focus:border-red-500',
                    buttonClassName
                  )}
                  disabled={disabled}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formatDisplayValue(field.value)}
                </Button>
              </FormControl>
            </PopoverTrigger>
            
            <PopoverContent className="w-auto p-0" align="start">
              <AnimatePresence>
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  <Calendar
                    mode={type}
                    selected={field.value}
                    onSelect={(date) => {
                      field.onChange(date)
                      if (type === 'single' && !timePicker?.enabled) {
                        setOpen(false)
                      }
                    }}
                    disabled={isDateDisabled}
                    initialFocus
                    className={calendarClassName}
                    locale={locale}
                  />
                  
                  {/* 时间选择器 */}
                  {timePicker?.enabled && field.value && (
                    <div className="border-t">
                      <TimePicker
                        value={field.value instanceof Date ? field.value : undefined}
                        onChange={(date) => {
                          field.onChange(date)
                        }}
                        config={timePicker}
                      />
                    </div>
                  )}
                  
                  {/* 确认按钮（当启用时间选择器时） */}
                  {timePicker?.enabled && (
                    <div className="border-t p-3">
                      <Button
                        onClick={() => setOpen(false)}
                        className="w-full"
                        size="sm"
                      >
                        确认
                      </Button>
                    </div>
                  )}
                </motion.div>
              </AnimatePresence>
            </PopoverContent>
          </Popover>

          {description && (
            <FormDescription>{description}</FormDescription>
          )}
          
          <AnimatePresence>
            {fieldState.error && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
              >
                <FormMessage />
              </motion.div>
            )}
          </AnimatePresence>
        </FormItem>
      )}
    />
  )
}

export default DatePickerField
