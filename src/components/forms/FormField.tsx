'use client'

import React, { forwardRef } from 'react'
import { useFormContext, Controller } from 'react-hook-form'
import { motion } from 'framer-motion'
import { Eye, EyeOff, Upload, X } from 'lucide-react'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Switch } from '@/components/ui/switch'
import { cn } from '@/lib/utils'
import { useTheme } from '@/hooks/use-theme'

// 基础字段属性
interface BaseFieldProps {
  name: string
  label?: string
  description?: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  className?: string
  error?: string
}

// 文本输入字段
interface TextFieldProps extends BaseFieldProps {
  type?: 'text' | 'email' | 'password' | 'tel' | 'url'
  maxLength?: number
  showPasswordToggle?: boolean
}

export const TextField = forwardRef<HTMLInputElement, TextFieldProps>(
  (
    { name, label, description, type = 'text', showPasswordToggle, ...props },
    ref
  ) => {
    const {
      control,
      formState: { errors },
    } = useFormContext()
    const [showPassword, setShowPassword] = React.useState(false)
    const { userPreferences } = useTheme()

    const error = errors[name]?.message as string
    const isPassword = type === 'password'
    const inputType = isPassword && showPassword ? 'text' : type

    return (
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: userPreferences.animationsEnabled ? 0.2 : 0,
            }}
            className={cn('space-y-2', props.className)}
          >
            {label && (
              <Label
                htmlFor={name}
                className={cn(
                  props.required &&
                    'after:content-["*"] after:text-red-500 after:ml-1'
                )}
              >
                {label}
              </Label>
            )}
            <div className="relative">
              <Input
                {...field}
                {...props}
                ref={ref}
                id={name}
                type={inputType}
                className={cn(error && 'border-red-500 focus:border-red-500')}
              />
              {isPassword && showPasswordToggle && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              )}
            </div>
            {description && !error && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
            {error && (
              <motion.p
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="text-sm text-red-500"
              >
                {error}
              </motion.p>
            )}
          </motion.div>
        )}
      />
    )
  }
)

TextField.displayName = 'TextField'

// 文本域字段
interface TextareaFieldProps extends BaseFieldProps {
  rows?: number
  maxLength?: number
  showCount?: boolean
}

export const TextareaField = forwardRef<
  HTMLTextAreaElement,
  TextareaFieldProps
>(
  (
    { name, label, description, rows = 4, maxLength, showCount, ...props },
    ref
  ) => {
    const {
      control,
      formState: { errors },
      watch,
    } = useFormContext()
    const { userPreferences } = useTheme()

    const error = errors[name]?.message as string
    const value = watch(name) || ''
    const currentLength = value.length

    return (
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: userPreferences.animationsEnabled ? 0.2 : 0,
            }}
            className={cn('space-y-2', props.className)}
          >
            {label && (
              <Label
                htmlFor={name}
                className={cn(
                  props.required &&
                    'after:content-["*"] after:text-red-500 after:ml-1'
                )}
              >
                {label}
              </Label>
            )}
            <div className="relative">
              <Textarea
                {...field}
                {...props}
                ref={ref}
                id={name}
                rows={rows}
                maxLength={maxLength}
                className={cn(error && 'border-red-500 focus:border-red-500')}
              />
              {showCount && maxLength && (
                <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
                  {currentLength}/{maxLength}
                </div>
              )}
            </div>
            {description && !error && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
            {error && (
              <motion.p
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="text-sm text-red-500"
              >
                {error}
              </motion.p>
            )}
          </motion.div>
        )}
      />
    )
  }
)

TextareaField.displayName = 'TextareaField'

// 选择字段
interface SelectFieldProps extends BaseFieldProps {
  options: { value: string; label: string; disabled?: boolean }[]
  allowClear?: boolean
}

export const SelectField: React.FC<SelectFieldProps> = ({
  name,
  label,
  description,
  options,
  allowClear,
  ...props
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext()
  const { userPreferences } = useTheme()

  const error = errors[name]?.message as string

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: userPreferences.animationsEnabled ? 0.2 : 0 }}
          className={cn('space-y-2', props.className)}
        >
          {label && (
            <Label
              htmlFor={name}
              className={cn(
                props.required &&
                  'after:content-["*"] after:text-red-500 after:ml-1'
              )}
            >
              {label}
            </Label>
          )}
          <div className="relative">
            <Select
              value={field.value}
              onValueChange={field.onChange}
              disabled={props.disabled}
            >
              <SelectTrigger
                className={cn(error && 'border-red-500 focus:border-red-500')}
              >
                <SelectValue placeholder={props.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {allowClear && field.value && (
                  <SelectItem value="" className="text-muted-foreground">
                    清除选择
                  </SelectItem>
                )}
                {options.map(option => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                    disabled={option.disabled}
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {description && !error && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
          {error && (
            <motion.p
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="text-sm text-red-500"
            >
              {error}
            </motion.p>
          )}
        </motion.div>
      )}
    />
  )
}

// 复选框字段
interface CheckboxFieldProps extends Omit<BaseFieldProps, 'placeholder'> {
  text?: string
}

export const CheckboxField: React.FC<CheckboxFieldProps> = ({
  name,
  label,
  text,
  description,
  ...props
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext()
  const { userPreferences } = useTheme()

  const error = errors[name]?.message as string

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: userPreferences.animationsEnabled ? 0.2 : 0 }}
          className={cn('space-y-2', props.className)}
        >
          {label && (
            <Label
              className={cn(
                props.required &&
                  'after:content-["*"] after:text-red-500 after:ml-1'
              )}
            >
              {label}
            </Label>
          )}
          <div className="flex items-center space-x-2">
            <Checkbox
              id={name}
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={props.disabled}
            />
            {text && (
              <Label
                htmlFor={name}
                className="text-sm font-normal cursor-pointer"
              >
                {text}
              </Label>
            )}
          </div>
          {description && !error && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
          {error && (
            <motion.p
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="text-sm text-red-500"
            >
              {error}
            </motion.p>
          )}
        </motion.div>
      )}
    />
  )
}

// 单选按钮组字段
interface RadioFieldProps extends BaseFieldProps {
  options: { value: string; label: string; disabled?: boolean }[]
  direction?: 'horizontal' | 'vertical'
}

export const RadioField: React.FC<RadioFieldProps> = ({
  name,
  label,
  description,
  options,
  direction = 'vertical',
  ...props
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext()
  const { userPreferences } = useTheme()

  const error = errors[name]?.message as string

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: userPreferences.animationsEnabled ? 0.2 : 0 }}
          className={cn('space-y-2', props.className)}
        >
          {label && (
            <Label
              className={cn(
                props.required &&
                  'after:content-["*"] after:text-red-500 after:ml-1'
              )}
            >
              {label}
            </Label>
          )}
          <RadioGroup
            value={field.value}
            onValueChange={field.onChange}
            disabled={props.disabled}
            className={cn(
              direction === 'horizontal' ? 'flex flex-wrap gap-4' : 'space-y-2'
            )}
          >
            {options.map(option => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem
                  value={option.value}
                  id={`${name}-${option.value}`}
                  disabled={option.disabled}
                />
                <Label
                  htmlFor={`${name}-${option.value}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
          {description && !error && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
          {error && (
            <motion.p
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="text-sm text-red-500"
            >
              {error}
            </motion.p>
          )}
        </motion.div>
      )}
    />
  )
}

// 开关字段
interface SwitchFieldProps extends Omit<BaseFieldProps, 'placeholder'> {
  text?: string
}

export const SwitchField: React.FC<SwitchFieldProps> = ({
  name,
  label,
  text,
  description,
  ...props
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext()
  const { userPreferences } = useTheme()

  const error = errors[name]?.message as string

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: userPreferences.animationsEnabled ? 0.2 : 0 }}
          className={cn('space-y-2', props.className)}
        >
          {label && (
            <Label
              className={cn(
                props.required &&
                  'after:content-["*"] after:text-red-500 after:ml-1'
              )}
            >
              {label}
            </Label>
          )}
          <div className="flex items-center space-x-2">
            <Switch
              id={name}
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={props.disabled}
            />
            {text && (
              <Label
                htmlFor={name}
                className="text-sm font-normal cursor-pointer"
              >
                {text}
              </Label>
            )}
          </div>
          {description && !error && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
          {error && (
            <motion.p
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="text-sm text-red-500"
            >
              {error}
            </motion.p>
          )}
        </motion.div>
      )}
    />
  )
}

// 文件上传字段
interface FileFieldProps extends BaseFieldProps {
  accept?: string
  multiple?: boolean
  maxSize?: number // MB
  maxFiles?: number
  showPreview?: boolean
}

export const FileField: React.FC<FileFieldProps> = ({
  name,
  label,
  description,
  accept,
  multiple,
  maxSize,
  maxFiles,
  showPreview,
  ...props
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext()
  const { userPreferences } = useTheme()
  const fileInputRef = React.useRef<HTMLInputElement>(null)

  const error = errors[name]?.message as string

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => {
        const files = field.value as FileList | File[] | null
        const fileArray = files ? Array.from(files) : []

        const handleFileSelect = (
          event: React.ChangeEvent<HTMLInputElement>
        ) => {
          const selectedFiles = event.target.files
          if (selectedFiles) {
            field.onChange(multiple ? selectedFiles : selectedFiles[0])
          }
        }

        const removeFile = (index: number) => {
          if (multiple && fileArray.length > 1) {
            const newFiles = fileArray.filter((_, i) => i !== index)
            field.onChange(newFiles)
          } else {
            field.onChange(null)
          }
        }

        return (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: userPreferences.animationsEnabled ? 0.2 : 0,
            }}
            className={cn('space-y-2', props.className)}
          >
            {label && (
              <Label
                className={cn(
                  props.required &&
                    'after:content-["*"] after:text-red-500 after:ml-1'
                )}
              >
                {label}
              </Label>
            )}

            <div className="space-y-2">
              <input
                ref={fileInputRef}
                type="file"
                accept={accept}
                multiple={multiple}
                onChange={handleFileSelect}
                className="hidden"
                disabled={props.disabled}
              />

              <Button
                type="button"
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={props.disabled}
                className={cn(
                  'w-full h-32 border-2 border-dashed',
                  error && 'border-red-500'
                )}
              >
                <div className="flex flex-col items-center space-y-2">
                  <Upload className="h-8 w-8 text-muted-foreground" />
                  <div className="text-sm text-muted-foreground">
                    点击选择文件或拖拽文件到此处
                  </div>
                  {maxSize && (
                    <div className="text-xs text-muted-foreground">
                      最大文件大小: {maxSize}MB
                    </div>
                  )}
                </div>
              </Button>

              {fileArray.length > 0 && (
                <div className="space-y-2">
                  {fileArray.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-muted rounded-md"
                    >
                      <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium">{file.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {description && !error && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
            {error && (
              <motion.p
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="text-sm text-red-500"
              >
                {error}
              </motion.p>
            )}
          </motion.div>
        )
      }}
    />
  )
}
