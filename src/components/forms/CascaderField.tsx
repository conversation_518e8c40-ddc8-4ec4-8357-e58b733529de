/**
 * 级联选择器表单字段组件
 * 
 * 支持多级联动选择，常用于地区选择、分类选择等场景
 * 支持异步加载数据和自定义渲染
 */

'use client'

import React from 'react'
import { ChevronRight, ChevronDown, Check } from 'lucide-react'
import { Control, FieldPath, FieldValues } from 'react-hook-form'
import { motion, AnimatePresence } from 'framer-motion'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'

// 级联选项类型
export interface CascaderOption {
  value: string
  label: string
  children?: CascaderOption[]
  disabled?: boolean
  loading?: boolean
  isLeaf?: boolean
}

// 级联选择器属性
export interface CascaderFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  control: Control<TFieldValues>
  name: TName
  label?: string
  description?: string
  placeholder?: string
  options: CascaderOption[]
  loadData?: (selectedOptions: CascaderOption[]) => Promise<CascaderOption[]>
  changeOnSelect?: boolean
  showSearch?: boolean
  searchPlaceholder?: string
  expandTrigger?: 'click' | 'hover'
  separator?: string
  required?: boolean
  disabled?: boolean
  className?: string
  popoverClassName?: string
  maxHeight?: number
}

// 级联面板组件
const CascaderPanel: React.FC<{
  options: CascaderOption[]
  value: string[]
  onChange: (value: string[], selectedOptions: CascaderOption[]) => void
  loadData?: (selectedOptions: CascaderOption[]) => Promise<CascaderOption[]>
  changeOnSelect: boolean
  expandTrigger: 'click' | 'hover'
  maxHeight: number
  onClose: () => void
}> = ({
  options,
  value,
  onChange,
  loadData,
  changeOnSelect,
  expandTrigger,
  maxHeight,
  onClose,
}) => {
  const [activeValue, setActiveValue] = React.useState<string[]>(value)
  const [optionsMap, setOptionsMap] = React.useState<Map<string, CascaderOption[]>>(
    new Map([['root', options]])
  )
  const [loadingMap, setLoadingMap] = React.useState<Map<string, boolean>>(new Map())

  // 获取选项路径
  const getOptionPath = React.useCallback((targetValue: string, currentOptions: CascaderOption[] = options, path: CascaderOption[] = []): CascaderOption[] | null => {
    for (const option of currentOptions) {
      const newPath = [...path, option]
      if (option.value === targetValue) {
        return newPath
      }
      if (option.children) {
        const result = getOptionPath(targetValue, option.children, newPath)
        if (result) return result
      }
    }
    return null
  }, [options])

  // 获取当前级别的选项
  const getCurrentLevelOptions = React.useCallback((level: number): CascaderOption[] => {
    if (level === 0) return options
    
    const parentValue = activeValue[level - 1]
    if (!parentValue) return []
    
    return optionsMap.get(parentValue) || []
  }, [options, activeValue, optionsMap])

  // 处理选项点击
  const handleOptionClick = React.useCallback(async (option: CascaderOption, level: number) => {
    const newActiveValue = [...activeValue.slice(0, level), option.value]
    setActiveValue(newActiveValue)

    // 如果是叶子节点或者设置了 changeOnSelect
    if (option.isLeaf || (!option.children && !loadData) || changeOnSelect) {
      const selectedPath = []
      let currentOptions = options
      
      for (const val of newActiveValue) {
        const found = currentOptions.find(opt => opt.value === val)
        if (found) {
          selectedPath.push(found)
          currentOptions = found.children || []
        }
      }
      
      onChange(newActiveValue, selectedPath)
      
      if (option.isLeaf || (!option.children && !loadData)) {
        onClose()
        return
      }
    }

    // 如果有子选项，直接使用
    if (option.children) {
      setOptionsMap(prev => new Map(prev).set(option.value, option.children!))
      return
    }

    // 如果需要异步加载数据
    if (loadData && !option.isLeaf) {
      setLoadingMap(prev => new Map(prev).set(option.value, true))
      
      try {
        const selectedPath = []
        let currentOptions = options
        
        for (const val of newActiveValue) {
          const found = currentOptions.find(opt => opt.value === val)
          if (found) {
            selectedPath.push(found)
            currentOptions = found.children || []
          }
        }
        
        const childOptions = await loadData(selectedPath)
        setOptionsMap(prev => new Map(prev).set(option.value, childOptions))
      } catch (error) {
        console.error('Failed to load cascader data:', error)
      } finally {
        setLoadingMap(prev => {
          const newMap = new Map(prev)
          newMap.delete(option.value)
          return newMap
        })
      }
    }
  }, [activeValue, options, loadData, changeOnSelect, onChange, onClose])

  // 计算需要显示的级别数
  const levels = React.useMemo(() => {
    let maxLevel = 1
    for (let i = 0; i < activeValue.length; i++) {
      const currentOptions = getCurrentLevelOptions(i + 1)
      if (currentOptions.length > 0) {
        maxLevel = i + 2
      }
    }
    return maxLevel
  }, [activeValue, getCurrentLevelOptions])

  return (
    <div className="flex">
      {Array.from({ length: levels }, (_, level) => {
        const currentOptions = getCurrentLevelOptions(level)
        const selectedValue = activeValue[level]
        
        return (
          <div key={level} className="border-r last:border-r-0">
            <ScrollArea style={{ height: maxHeight }} className="w-48">
              <div className="p-1">
                {currentOptions.map(option => {
                  const isSelected = option.value === selectedValue
                  const isLoading = loadingMap.get(option.value)
                  const hasChildren = option.children || (!option.isLeaf && loadData)
                  
                  return (
                    <div
                      key={option.value}
                      className={cn(
                        'flex cursor-pointer items-center justify-between rounded px-2 py-1.5 text-sm hover:bg-accent',
                        isSelected && 'bg-accent',
                        option.disabled && 'cursor-not-allowed opacity-50'
                      )}
                      onClick={() => !option.disabled && handleOptionClick(option, level)}
                    >
                      <span className="flex-1">{option.label}</span>
                      {isSelected && <Check className="h-4 w-4" />}
                      {hasChildren && !isSelected && <ChevronRight className="h-4 w-4" />}
                      {isLoading && (
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                      )}
                    </div>
                  )
                })}
              </div>
            </ScrollArea>
          </div>
        )
      })}
    </div>
  )
}

// 级联选择器字段组件
export function CascaderField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  control,
  name,
  label,
  description,
  placeholder = '请选择',
  options,
  loadData,
  changeOnSelect = false,
  showSearch = false,
  searchPlaceholder = '搜索...',
  expandTrigger = 'click',
  separator = ' / ',
  required = false,
  disabled = false,
  className,
  popoverClassName,
  maxHeight = 200,
}: CascaderFieldProps<TFieldValues, TName>) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState('')

  // 获取显示文本
  const getDisplayText = React.useCallback((value: string[]): string => {
    if (!value || value.length === 0) return placeholder

    const getOptionLabel = (val: string, currentOptions: CascaderOption[] = options): string => {
      for (const option of currentOptions) {
        if (option.value === val) return option.label
        if (option.children) {
          const result = getOptionLabel(val, option.children)
          if (result) return result
        }
      }
      return val
    }

    return value.map(val => getOptionLabel(val)).join(separator)
  }, [options, placeholder, separator])

  // 搜索过滤选项
  const getFilteredOptions = React.useCallback((searchTerm: string): CascaderOption[] => {
    if (!searchTerm) return options

    const filterOptions = (opts: CascaderOption[]): CascaderOption[] => {
      const filtered: CascaderOption[] = []
      
      for (const option of opts) {
        if (option.label.toLowerCase().includes(searchTerm.toLowerCase())) {
          filtered.push(option)
        } else if (option.children) {
          const filteredChildren = filterOptions(option.children)
          if (filteredChildren.length > 0) {
            filtered.push({
              ...option,
              children: filteredChildren,
            })
          }
        }
      }
      
      return filtered
    }

    return filterOptions(options)
  }, [options])

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem className={cn('space-y-2', className)}>
          {label && (
            <FormLabel className={cn(required && 'after:content-["*"] after:text-red-500 after:ml-1')}>
              {label}
            </FormLabel>
          )}
          
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className={cn(
                    'w-full justify-between',
                    !field.value?.length && 'text-muted-foreground',
                    fieldState.error && 'border-red-500 focus:border-red-500'
                  )}
                  disabled={disabled}
                >
                  <span className="truncate">
                    {getDisplayText(field.value || [])}
                  </span>
                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            
            <PopoverContent 
              className={cn('w-auto p-0', popoverClassName)} 
              align="start"
            >
              <AnimatePresence>
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  {showSearch && (
                    <div className="border-b p-2">
                      <Input
                        placeholder={searchPlaceholder}
                        value={searchValue}
                        onChange={(e) => setSearchValue(e.target.value)}
                        className="h-8"
                      />
                    </div>
                  )}
                  
                  <CascaderPanel
                    options={searchValue ? getFilteredOptions(searchValue) : options}
                    value={field.value || []}
                    onChange={(value, selectedOptions) => {
                      field.onChange(value)
                      if (!changeOnSelect) {
                        setOpen(false)
                      }
                    }}
                    loadData={loadData}
                    changeOnSelect={changeOnSelect}
                    expandTrigger={expandTrigger}
                    maxHeight={maxHeight}
                    onClose={() => setOpen(false)}
                  />
                </motion.div>
              </AnimatePresence>
            </PopoverContent>
          </Popover>

          {description && (
            <FormDescription>{description}</FormDescription>
          )}
          
          <AnimatePresence>
            {fieldState.error && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
              >
                <FormMessage />
              </motion.div>
            )}
          </AnimatePresence>
        </FormItem>
      )}
    />
  )
}

export default CascaderField
