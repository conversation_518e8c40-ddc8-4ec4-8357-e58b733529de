/**
 * 动态表单字段组件
 * 
 * 支持根据配置动态生成表单字段，包括：
 * - 字段类型动态切换
 * - 条件显示/隐藏
 * - 动态验证规则
 * - 字段依赖关系
 */

'use client'

import React from 'react'
import { Control, FieldPath, FieldValues, useWatch } from 'react-hook-form'
import { z } from 'zod'

import { cn } from '@/lib/utils'
import { TextField } from './FormField'
import DatePickerField from './DatePickerField'
import MultiSelectField from './MultiSelectField'
import CascaderField from './CascaderField'

// 字段类型枚举
export type DynamicFieldType = 
  | 'text' 
  | 'textarea' 
  | 'number' 
  | 'email' 
  | 'password' 
  | 'select' 
  | 'multiselect' 
  | 'checkbox' 
  | 'radio' 
  | 'switch' 
  | 'date' 
  | 'daterange' 
  | 'cascader' 
  | 'file'

// 选项配置
export interface FieldOption {
  value: string
  label: string
  description?: string
  disabled?: boolean
  group?: string
  children?: FieldOption[]
}

// 条件配置
export interface FieldCondition {
  field: string
  operator: 'eq' | 'ne' | 'in' | 'nin' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'exists'
  value: any
}

// 验证规则配置
export interface FieldValidation {
  required?: boolean | string
  min?: number | string
  max?: number | string
  pattern?: string | RegExp
  custom?: (value: any, formData: any) => string | boolean
}

// 动态字段配置
export interface DynamicFieldConfig {
  name: string
  type: DynamicFieldType
  label?: string
  description?: string
  placeholder?: string
  defaultValue?: any
  options?: FieldOption[]
  validation?: FieldValidation
  conditions?: FieldCondition[]
  dependencies?: string[]
  props?: Record<string, any>
  className?: string
  grid?: {
    span?: number
    offset?: number
  }
}

// 动态表单字段属性
export interface DynamicFormFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  control: Control<TFieldValues>
  config: DynamicFieldConfig
  formData?: TFieldValues
  disabled?: boolean
  className?: string
}

// 条件检查函数
const checkCondition = (condition: FieldCondition, formData: any): boolean => {
  const fieldValue = formData?.[condition.field]
  
  switch (condition.operator) {
    case 'eq':
      return fieldValue === condition.value
    case 'ne':
      return fieldValue !== condition.value
    case 'in':
      return Array.isArray(condition.value) && condition.value.includes(fieldValue)
    case 'nin':
      return Array.isArray(condition.value) && !condition.value.includes(fieldValue)
    case 'gt':
      return fieldValue > condition.value
    case 'gte':
      return fieldValue >= condition.value
    case 'lt':
      return fieldValue < condition.value
    case 'lte':
      return fieldValue <= condition.value
    case 'contains':
      return typeof fieldValue === 'string' && fieldValue.includes(condition.value)
    case 'exists':
      return fieldValue !== undefined && fieldValue !== null && fieldValue !== ''
    default:
      return true
  }
}

// 检查所有条件
const checkAllConditions = (conditions: FieldCondition[], formData: any): boolean => {
  return conditions.every(condition => checkCondition(condition, formData))
}

// 生成验证 Schema
const generateValidationSchema = (config: DynamicFieldConfig): z.ZodSchema<any> => {
  const { validation, type } = config
  if (!validation) return z.any()

  let schema: z.ZodSchema<any>

  // 根据字段类型设置基础 schema
  switch (type) {
    case 'number':
      schema = z.number()
      break
    case 'email':
      schema = z.string().email('请输入有效的邮箱地址')
      break
    case 'date':
    case 'daterange':
      schema = z.date()
      break
    case 'multiselect':
      schema = z.array(z.string())
      break
    case 'checkbox':
    case 'switch':
      schema = z.boolean()
      break
    default:
      schema = z.string()
  }

  // 应用验证规则
  if (validation.required) {
    const message = typeof validation.required === 'string' 
      ? validation.required 
      : '此字段为必填项'
    
    if (type === 'multiselect') {
      schema = (schema as z.ZodArray<any>).min(1, message)
    } else if (type === 'checkbox' || type === 'switch') {
      schema = (schema as z.ZodBoolean).refine(val => val === true, { message })
    } else {
      schema = (schema as z.ZodString).min(1, message)
    }
  } else {
    schema = schema.optional()
  }

  if (validation.min !== undefined) {
    if (type === 'number') {
      schema = (schema as z.ZodNumber).min(validation.min as number, `最小值为 ${validation.min}`)
    } else if (type === 'multiselect') {
      schema = (schema as z.ZodArray<any>).min(validation.min as number, `至少选择 ${validation.min} 项`)
    } else {
      schema = (schema as z.ZodString).min(validation.min as number, `最少 ${validation.min} 个字符`)
    }
  }

  if (validation.max !== undefined) {
    if (type === 'number') {
      schema = (schema as z.ZodNumber).max(validation.max as number, `最大值为 ${validation.max}`)
    } else if (type === 'multiselect') {
      schema = (schema as z.ZodArray<any>).max(validation.max as number, `最多选择 ${validation.max} 项`)
    } else {
      schema = (schema as z.ZodString).max(validation.max as number, `最多 ${validation.max} 个字符`)
    }
  }

  if (validation.pattern) {
    const pattern = typeof validation.pattern === 'string' 
      ? new RegExp(validation.pattern) 
      : validation.pattern
    schema = (schema as z.ZodString).regex(pattern, '格式不正确')
  }

  return schema
}

// 动态表单字段组件
export function DynamicFormField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  control,
  config,
  formData,
  disabled = false,
  className,
}: DynamicFormFieldProps<TFieldValues, TName>) {
  // 监听依赖字段的变化
  const watchedFields = useWatch({
    control,
    name: config.dependencies as any,
  })

  // 获取当前表单数据
  const currentFormData = useWatch({ control }) || formData

  // 检查显示条件
  const shouldShow = React.useMemo(() => {
    if (!config.conditions || config.conditions.length === 0) return true
    return checkAllConditions(config.conditions, currentFormData)
  }, [config.conditions, currentFormData])

  // 如果不满足显示条件，不渲染字段
  if (!shouldShow) return null

  // 合并属性
  const fieldProps = {
    control,
    name: config.name as TName,
    label: config.label,
    description: config.description,
    placeholder: config.placeholder,
    disabled,
    className: cn(className, config.className),
    ...config.props,
  }

  // 根据字段类型渲染对应组件
  switch (config.type) {
    case 'text':
    case 'email':
    case 'password':
      return (
        <TextField
          {...fieldProps}
          type={config.type}
        />
      )

    case 'textarea':
      return (
        <TextField
          {...fieldProps}
          type="textarea"
        />
      )

    case 'number':
      return (
        <TextField
          {...fieldProps}
          type="number"
        />
      )

    case 'select':
      return (
        <TextField
          {...fieldProps}
          type="select"
          options={config.options?.map(opt => ({
            value: opt.value,
            label: opt.label,
          }))}
        />
      )

    case 'multiselect':
      return (
        <MultiSelectField
          {...fieldProps}
          options={config.options || []}
        />
      )

    case 'checkbox':
      return (
        <TextField
          {...fieldProps}
          type="checkbox"
        />
      )

    case 'radio':
      return (
        <TextField
          {...fieldProps}
          type="radio"
          options={config.options?.map(opt => ({
            value: opt.value,
            label: opt.label,
          }))}
        />
      )

    case 'switch':
      return (
        <TextField
          {...fieldProps}
          type="switch"
        />
      )

    case 'date':
      return (
        <DatePickerField
          {...fieldProps}
          type="single"
        />
      )

    case 'daterange':
      return (
        <DatePickerField
          {...fieldProps}
          type="range"
        />
      )

    case 'cascader':
      return (
        <CascaderField
          {...fieldProps}
          options={config.options || []}
        />
      )

    case 'file':
      return (
        <TextField
          {...fieldProps}
          type="file"
        />
      )

    default:
      console.warn(`Unsupported field type: ${config.type}`)
      return null
  }
}

// 动态表单组件
export interface DynamicFormProps<TFieldValues extends FieldValues = FieldValues> {
  control: Control<TFieldValues>
  configs: DynamicFieldConfig[]
  formData?: TFieldValues
  disabled?: boolean
  className?: string
  gridClassName?: string
}

export function DynamicForm<TFieldValues extends FieldValues = FieldValues>({
  control,
  configs,
  formData,
  disabled = false,
  className,
  gridClassName = 'grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3',
}: DynamicFormProps<TFieldValues>) {
  return (
    <div className={cn('space-y-6', className)}>
      <div className={gridClassName}>
        {configs.map((config) => (
          <div
            key={config.name}
            className={cn(
              config.grid?.span && `col-span-${config.grid.span}`,
              config.grid?.offset && `col-start-${config.grid.offset + 1}`
            )}
          >
            <DynamicFormField
              control={control}
              config={config}
              formData={formData}
              disabled={disabled}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

export default DynamicFormField
