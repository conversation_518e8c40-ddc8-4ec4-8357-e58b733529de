/**
 * 表单布局组件
 * 
 * 提供多种表单布局模式：
 * - 网格布局
 * - 分栏布局
 * - 卡片布局
 * - 步骤布局
 * - 响应式布局
 */

'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'

import { cn } from '@/lib/utils'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'

// 布局类型
export type FormLayoutType = 'grid' | 'columns' | 'cards' | 'steps' | 'accordion'

// 网格配置
export interface GridConfig {
  cols?: number
  gap?: number
  responsive?: {
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
}

// 分栏配置
export interface ColumnConfig {
  count: number
  gap?: number
  ratios?: number[]
}

// 卡片配置
export interface CardConfig {
  title?: string
  description?: string
  collapsible?: boolean
  defaultCollapsed?: boolean
}

// 步骤配置
export interface StepConfig {
  title: string
  description?: string
  icon?: React.ReactNode
  optional?: boolean
}

// 表单区域配置
export interface FormSection {
  id: string
  title?: string
  description?: string
  children: React.ReactNode
  className?: string
  grid?: {
    span?: number
    start?: number
  }
  card?: CardConfig
  step?: StepConfig
  condition?: (formData: any) => boolean
}

// 表单布局属性
export interface FormLayoutProps {
  type?: FormLayoutType
  sections: FormSection[]
  grid?: GridConfig
  columns?: ColumnConfig
  className?: string
  formData?: any
  currentStep?: number
  onStepChange?: (step: number) => void
}

// 网格布局组件
const GridLayout: React.FC<{
  sections: FormSection[]
  grid: GridConfig
  className?: string
  formData?: any
}> = ({ sections, grid, className, formData }) => {
  const { cols = 1, gap = 4, responsive } = grid

  const gridClasses = cn(
    'grid gap-' + gap,
    `grid-cols-${cols}`,
    responsive?.sm && `sm:grid-cols-${responsive.sm}`,
    responsive?.md && `md:grid-cols-${responsive.md}`,
    responsive?.lg && `lg:grid-cols-${responsive.lg}`,
    responsive?.xl && `xl:grid-cols-${responsive.xl}`,
    className
  )

  return (
    <div className={gridClasses}>
      {sections.map((section) => {
        // 检查条件显示
        if (section.condition && !section.condition(formData)) {
          return null
        }

        return (
          <motion.div
            key={section.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className={cn(
              section.grid?.span && `col-span-${section.grid.span}`,
              section.grid?.start && `col-start-${section.grid.start}`,
              section.className
            )}
          >
            {section.title && (
              <div className="mb-4">
                <h3 className="text-lg font-semibold">{section.title}</h3>
                {section.description && (
                  <p className="text-sm text-muted-foreground">{section.description}</p>
                )}
              </div>
            )}
            {section.children}
          </motion.div>
        )
      })}
    </div>
  )
}

// 分栏布局组件
const ColumnLayout: React.FC<{
  sections: FormSection[]
  columns: ColumnConfig
  className?: string
  formData?: any
}> = ({ sections, columns, className, formData }) => {
  const { count, gap = 4, ratios } = columns

  // 将 sections 分配到各列
  const columnSections = React.useMemo(() => {
    const cols: FormSection[][] = Array.from({ length: count }, () => [])
    sections.forEach((section, index) => {
      if (!section.condition || section.condition(formData)) {
        cols[index % count].push(section)
      }
    })
    return cols
  }, [sections, count, formData])

  return (
    <div className={cn('flex gap-' + gap, className)}>
      {columnSections.map((colSections, colIndex) => (
        <div
          key={colIndex}
          className="flex-1 space-y-6"
          style={{
            flex: ratios?.[colIndex] || 1,
          }}
        >
          {colSections.map((section) => (
            <motion.div
              key={section.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: colIndex * 0.1 }}
              className={section.className}
            >
              {section.title && (
                <div className="mb-4">
                  <h3 className="text-lg font-semibold">{section.title}</h3>
                  {section.description && (
                    <p className="text-sm text-muted-foreground">{section.description}</p>
                  )}
                </div>
              )}
              {section.children}
            </motion.div>
          ))}
        </div>
      ))}
    </div>
  )
}

// 卡片布局组件
const CardLayout: React.FC<{
  sections: FormSection[]
  className?: string
  formData?: any
}> = ({ sections, className, formData }) => {
  const [collapsedCards, setCollapsedCards] = React.useState<Set<string>>(
    new Set(
      sections
        .filter(section => section.card?.defaultCollapsed)
        .map(section => section.id)
    )
  )

  const toggleCard = (sectionId: string) => {
    setCollapsedCards(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  return (
    <div className={cn('space-y-6', className)}>
      {sections.map((section) => {
        // 检查条件显示
        if (section.condition && !section.condition(formData)) {
          return null
        }

        const isCollapsed = collapsedCards.has(section.id)
        const cardConfig = section.card || {}

        return (
          <motion.div
            key={section.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className={section.className}>
              <CardHeader
                className={cn(
                  cardConfig.collapsible && 'cursor-pointer',
                  'pb-3'
                )}
                onClick={() => cardConfig.collapsible && toggleCard(section.id)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">
                      {cardConfig.title || section.title}
                    </CardTitle>
                    {(cardConfig.description || section.description) && (
                      <CardDescription className="mt-1">
                        {cardConfig.description || section.description}
                      </CardDescription>
                    )}
                  </div>
                  {cardConfig.collapsible && (
                    <motion.div
                      animate={{ rotate: isCollapsed ? 0 : 180 }}
                      transition={{ duration: 0.2 }}
                    >
                      <svg
                        className="h-4 w-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </motion.div>
                  )}
                </div>
              </CardHeader>
              
              <AnimatePresence>
                {!isCollapsed && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    style={{ overflow: 'hidden' }}
                  >
                    <CardContent className="pt-0">
                      {section.children}
                    </CardContent>
                  </motion.div>
                )}
              </AnimatePresence>
            </Card>
          </motion.div>
        )
      })}
    </div>
  )
}

// 步骤布局组件
const StepLayout: React.FC<{
  sections: FormSection[]
  currentStep: number
  onStepChange?: (step: number) => void
  className?: string
  formData?: any
}> = ({ sections, currentStep, onStepChange, className, formData }) => {
  const visibleSections = sections.filter(
    section => !section.condition || section.condition(formData)
  )

  return (
    <div className={cn('space-y-6', className)}>
      {/* 步骤指示器 */}
      <div className="flex items-center justify-between">
        {visibleSections.map((section, index) => {
          const stepConfig = section.step || { title: section.title || `步骤 ${index + 1}` }
          const isActive = index === currentStep
          const isCompleted = index < currentStep
          const isClickable = onStepChange && index <= currentStep

          return (
            <React.Fragment key={section.id}>
              <div
                className={cn(
                  'flex items-center space-x-2',
                  isClickable && 'cursor-pointer'
                )}
                onClick={() => isClickable && onStepChange(index)}
              >
                <div
                  className={cn(
                    'flex h-8 w-8 items-center justify-center rounded-full border-2 text-sm font-medium',
                    isActive && 'border-primary bg-primary text-primary-foreground',
                    isCompleted && 'border-primary bg-primary text-primary-foreground',
                    !isActive && !isCompleted && 'border-muted-foreground text-muted-foreground'
                  )}
                >
                  {stepConfig.icon || (index + 1)}
                </div>
                <div>
                  <div className={cn(
                    'text-sm font-medium',
                    isActive && 'text-primary',
                    !isActive && 'text-muted-foreground'
                  )}>
                    {stepConfig.title}
                  </div>
                  {stepConfig.description && (
                    <div className="text-xs text-muted-foreground">
                      {stepConfig.description}
                    </div>
                  )}
                </div>
              </div>
              
              {index < visibleSections.length - 1 && (
                <div className="flex-1">
                  <Separator className="mx-4" />
                </div>
              )}
            </React.Fragment>
          )
        })}
      </div>

      {/* 当前步骤内容 */}
      <AnimatePresence mode="wait">
        {visibleSections[currentStep] && (
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className={visibleSections[currentStep].className}
          >
            {visibleSections[currentStep].children}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// 主表单布局组件
export function FormLayout({
  type = 'grid',
  sections,
  grid = { cols: 1, gap: 4 },
  columns = { count: 2, gap: 4 },
  className,
  formData,
  currentStep = 0,
  onStepChange,
}: FormLayoutProps) {
  switch (type) {
    case 'grid':
      return (
        <GridLayout
          sections={sections}
          grid={grid}
          className={className}
          formData={formData}
        />
      )

    case 'columns':
      return (
        <ColumnLayout
          sections={sections}
          columns={columns}
          className={className}
          formData={formData}
        />
      )

    case 'cards':
      return (
        <CardLayout
          sections={sections}
          className={className}
          formData={formData}
        />
      )

    case 'steps':
      return (
        <StepLayout
          sections={sections}
          currentStep={currentStep}
          onStepChange={onStepChange}
          className={className}
          formData={formData}
        />
      )

    default:
      return (
        <GridLayout
          sections={sections}
          grid={grid}
          className={className}
          formData={formData}
        />
      )
  }
}

export default FormLayout
