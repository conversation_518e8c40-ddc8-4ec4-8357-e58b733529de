'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Chart } from '@/components/ui/chart'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, Filter } from 'lucide-react'

// 媒介渠道分析数据类型
export interface ChannelAnalysisData {
  analysis: Array<{
    channel: number
    channelName: string
    totalCount: number
    signedCount: number
    followingCount: number
    pendingCount: number
    abandonedCount: number
    conversionRate: string
    followUpRate: string
  }>
  comparison: Array<{
    channel: number
    channelName: string
    totalCount: number
    signedCount: number
    conversionRate: string
    percentage: string
  }>
  funnel: Array<{
    channel: number
    channelName: string
    stages: Array<{
      name: string
      count: number
      percentage: number | string
    }>
  }>
  channels: Record<number, string>
}

interface ChannelAnalysisChartsProps {
  data: ChannelAnalysisData
  loading?: boolean
  className?: string
}

export function ChannelAnalysisCharts({
  data,
  loading = false,
  className = '',
}: ChannelAnalysisChartsProps) {
  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {[...Array(3)].map((_, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="animate-pulse h-96 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // 渠道分布饼图配置
  const channelDistributionOption = {
    title: {
      text: '媒介渠道分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `
          <div style="font-weight: bold; margin-bottom: 8px;">${params.name}</div>
          <div>咨询数量: ${params.value}</div>
          <div>占比: ${params.percent}%</div>
        `
      },
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.analysis.map(item => item.channelName),
    },
    series: [
      {
        name: '渠道分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: data.analysis.map((item, index) => ({
          value: item.totalCount,
          name: item.channelName,
          itemStyle: {
            color: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'][index % 4],
          },
        })),
      },
    ],
  }

  // 渠道转化率对比图配置
  const conversionComparisonOption = {
    title: {
      text: '渠道转化率对比',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const data = params[0]
        return `
          <div style="font-weight: bold; margin-bottom: 8px;">${data.axisValue}</div>
          <div>咨询数量: ${data.data.totalCount}</div>
          <div>签约数量: ${data.data.signedCount}</div>
          <div>转化率: ${data.value}%</div>
        `
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.analysis.map(item => item.channelName),
      axisLabel: {
        rotate: 45,
      },
    },
    yAxis: {
      type: 'value',
      name: '转化率(%)',
      axisLabel: {
        formatter: '{value}%',
      },
    },
    series: [
      {
        name: '转化率',
        type: 'bar',
        data: data.analysis.map(item => ({
          value: parseFloat(item.conversionRate),
          totalCount: item.totalCount,
          signedCount: item.signedCount,
        })),
        itemStyle: {
          color: (params: any) => {
            const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444']
            return colors[params.dataIndex % colors.length]
          },
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
        },
      },
    ],
  }

  // 渠道漏斗图配置
  const funnelOption = {
    title: {
      text: '渠道转化漏斗',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `
          <div style="font-weight: bold; margin-bottom: 8px;">${params.name}</div>
          <div>数量: ${params.value}</div>
          <div>占比: ${params.data.percentage}%</div>
        `
      },
    },
    legend: {
      data: ['咨询接待', '跟进阶段', '成功签约'],
      bottom: 10,
    },
    series: data.funnel.map((channelData, index) => ({
      name: channelData.channelName,
      type: 'funnel',
      left: `${index * 25}%`,
      width: '20%',
      height: '60%',
      top: '15%',
      data: channelData.stages.map(stage => ({
        value: stage.count,
        name: stage.name,
        percentage: stage.percentage,
      })),
      itemStyle: {
        color: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'][index % 4],
      },
      label: {
        show: true,
        position: 'inside',
        formatter: '{b}: {c}',
        fontSize: 12,
      },
    })),
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 渠道概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {data.analysis.map((channel, index) => (
          <motion.div
            key={channel.channel}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-gray-900">{channel.channelName}</h3>
                  <Badge variant="outline">{channel.totalCount} 咨询</Badge>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">转化率</span>
                    <span className="font-medium">{channel.conversionRate}%</span>
                  </div>
                  <Progress 
                    value={parseFloat(channel.conversionRate)} 
                    className="h-2"
                  />
                  
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-gray-600">签约:</span>
                      <span className="font-medium ml-1">{channel.signedCount}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">跟进:</span>
                      <span className="font-medium ml-1">{channel.followingCount}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* 渠道分布饼图 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              媒介渠道分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Chart
              option={channelDistributionOption}
              height={400}
              loading={loading}
            />
          </CardContent>
        </Card>
      </motion.div>

      {/* 渠道转化率对比 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              渠道转化率对比
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Chart
              option={conversionComparisonOption}
              height={400}
              loading={loading}
            />
          </CardContent>
        </Card>
      </motion.div>

      {/* 转化漏斗分析 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              渠道转化漏斗分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Chart
              option={funnelOption}
              height={500}
              loading={loading}
            />
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
