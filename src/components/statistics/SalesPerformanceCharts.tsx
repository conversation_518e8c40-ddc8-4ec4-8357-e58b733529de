'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Chart } from '@/components/ui/chart'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  TrendingUp,
  TrendingDown,
  Users,
  Target,
  Phone,
  Award,
} from 'lucide-react'

// 销售业绩数据类型
export interface SalesPerformanceData {
  overview: {
    totalConsultations: number
    signedConsultations: number
    totalFollowUps: number
    activeSalesCount: number
    conversionRate: number
  }
  ranking: Array<{
    userId: string
    userName: string
    userEmail: string
    consultationCount: number
    signedCount: number
    followUpCount: number
    conversionRate: string
  }>
  trend: Array<{
    date: string
    consultationCount: number
    signedCount: number
    conversionRate: string
  }>
}

interface SalesPerformanceChartsProps {
  data: SalesPerformanceData
  loading?: boolean
  className?: string
}

export function SalesPerformanceCharts({
  data,
  loading = false,
  className = '',
}: SalesPerformanceChartsProps) {
  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* 加载骨架屏 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse h-96 bg-gray-200 rounded"></div>
          </CardContent>
        </Card>
      </div>
    )
  }
  // 业绩概览卡片配置
  const overviewCards = [
    {
      title: '总咨询数',
      value: data.overview.totalConsultations,
      icon: Phone,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: '签约数量',
      value: data.overview.signedConsultations,
      icon: Award,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: '跟进次数',
      value: data.overview.totalFollowUps,
      icon: Target,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: '活跃销售员',
      value: data.overview.activeSalesCount,
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ]

  // 销售趋势图表配置
  const trendChartOption = {
    title: {
      text: '销售趋势分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter: (params: any) => {
        const date = params[0].axisValue
        let content = `<div style="font-weight: bold; margin-bottom: 8px;">${date}</div>`

        params.forEach((param: any) => {
          const value =
            param.seriesName === '转化率' ? `${param.value}%` : param.value
          content += `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span>${param.seriesName}: ${value}</span>
            </div>
          `
        })

        return content
      },
    },
    legend: {
      data: ['咨询数量', '签约数量', '转化率'],
      bottom: 10,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.trend.map(item => item.date),
      axisLabel: {
        rotate: 45,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left',
        axisLabel: {
          formatter: '{value}',
        },
      },
      {
        type: 'value',
        name: '转化率(%)',
        position: 'right',
        axisLabel: {
          formatter: '{value}%',
        },
      },
    ],
    series: [
      {
        name: '咨询数量',
        type: 'line',
        smooth: true,
        data: data.trend.map(item => item.consultationCount),
        itemStyle: {
          color: '#3b82f6',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
              { offset: 1, color: 'rgba(59, 130, 246, 0.1)' },
            ],
          },
        },
      },
      {
        name: '签约数量',
        type: 'line',
        smooth: true,
        data: data.trend.map(item => item.signedCount),
        itemStyle: {
          color: '#10b981',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
              { offset: 1, color: 'rgba(16, 185, 129, 0.1)' },
            ],
          },
        },
      },
      {
        name: '转化率',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        data: data.trend.map(item => parseFloat(item.conversionRate)),
        itemStyle: {
          color: '#f59e0b',
        },
        lineStyle: {
          type: 'dashed',
        },
      },
    ],
  }

  // 销售员排行榜图表配置
  const rankingChartOption = {
    title: {
      text: '销售员业绩排行榜',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const data = params[0]
        const ranking = data.dataIndex + 1
        const userName = data.axisValue

        return `
          <div style="font-weight: bold; margin-bottom: 8px;">第${ranking}名: ${userName}</div>
          <div>咨询数量: ${data.value}</div>
          <div>签约数量: ${data.data.signedCount}</div>
          <div>跟进次数: ${data.data.followUpCount}</div>
          <div>转化率: ${data.data.conversionRate}%</div>
        `
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      name: '咨询数量',
    },
    yAxis: {
      type: 'category',
      data: data.ranking.slice(0, 10).map(item => item.userName),
      inverse: true,
    },
    series: [
      {
        name: '咨询数量',
        type: 'bar',
        data: data.ranking.slice(0, 10).map(item => ({
          value: item.consultationCount,
          signedCount: item.signedCount,
          followUpCount: item.followUpCount,
          conversionRate: item.conversionRate,
        })),
        itemStyle: {
          color: (params: any) => {
            const colors = [
              '#ef4444',
              '#f97316',
              '#eab308',
              '#22c55e',
              '#3b82f6',
            ]
            return colors[params.dataIndex % colors.length]
          },
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}',
        },
      },
    ],
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 业绩概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {overviewCards.map((card, index) => (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      {card.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {card.value.toLocaleString()}
                    </p>
                    {card.title === '总咨询数' && (
                      <div className="flex items-center mt-2">
                        <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                        <span className="text-sm text-green-600">
                          转化率 {data.overview.conversionRate}%
                        </span>
                      </div>
                    )}
                  </div>
                  <div className={`p-3 rounded-full ${card.bgColor}`}>
                    <card.icon className={`h-6 w-6 ${card.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* 销售趋势图表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              销售趋势分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Chart option={trendChartOption} height={400} loading={loading} />
          </CardContent>
        </Card>
      </motion.div>

      {/* 销售员排行榜 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              销售员业绩排行榜
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 图表 */}
              <div>
                <Chart
                  option={rankingChartOption}
                  height={400}
                  loading={loading}
                />
              </div>

              {/* 详细排行榜 */}
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">详细排行</h4>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {data.ranking.slice(0, 10).map((item, index) => (
                    <div
                      key={item.userId}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <Badge
                          variant={index < 3 ? 'default' : 'secondary'}
                          className={
                            index === 0
                              ? 'bg-yellow-500'
                              : index === 1
                                ? 'bg-gray-400'
                                : index === 2
                                  ? 'bg-orange-600'
                                  : ''
                          }
                        >
                          #{index + 1}
                        </Badge>
                        <div>
                          <p className="font-medium text-gray-900">
                            {item.userName}
                          </p>
                          <p className="text-sm text-gray-600">
                            {item.userEmail}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900">
                          {item.consultationCount} 咨询
                        </p>
                        <p className="text-sm text-gray-600">
                          {item.signedCount} 签约 ({item.conversionRate}%)
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
