'use client'

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion } from 'framer-motion'
import { Calendar, MessageSquare, Save, X } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { useToast } from '@/hooks/use-toast'
import { FollowUpRecord } from './FollowUpTimeline'

// 表单验证 schema
const followUpFormSchema = z.object({
  content: z
    .string()
    .min(1, '跟进内容不能为空')
    .max(2000, '跟进内容不能超过2000字'),
  nextFollowUpDate: z.string().optional(),
})

type FollowUpFormData = z.infer<typeof followUpFormSchema>

interface FollowUpFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  consultationId: string
  initialData?: FollowUpRecord | null
  onSubmit: (data: FollowUpFormData) => Promise<void>
  isLoading?: boolean
}

export function FollowUpForm({
  open,
  onOpenChange,
  consultationId,
  initialData,
  onSubmit,
  isLoading = false,
}: FollowUpFormProps) {
  const { toast } = useToast()
  const isEditing = !!initialData

  const form = useForm<FollowUpFormData>({
    resolver: zodResolver(followUpFormSchema),
    defaultValues: {
      content: initialData?.content || '',
      nextFollowUpDate: initialData?.nextFollowUpDate 
        ? new Date(initialData.nextFollowUpDate).toISOString().split('T')[0]
        : '',
    },
  })

  // 重置表单数据
  React.useEffect(() => {
    if (open) {
      form.reset({
        content: initialData?.content || '',
        nextFollowUpDate: initialData?.nextFollowUpDate 
          ? new Date(initialData.nextFollowUpDate).toISOString().split('T')[0]
          : '',
      })
    }
  }, [open, initialData, form])

  // 处理表单提交
  const handleSubmit = async (data: FollowUpFormData) => {
    try {
      await onSubmit(data)
      
      toast({
        title: isEditing ? '更新成功' : '创建成功',
        description: isEditing ? '跟进记录已更新' : '跟进记录已创建',
      })
      
      onOpenChange(false)
      form.reset()
    } catch (error) {
      toast({
        title: isEditing ? '更新失败' : '创建失败',
        description: error instanceof Error ? error.message : '操作失败，请重试',
        variant: 'destructive',
      })
    }
  }

  // 处理取消
  const handleCancel = () => {
    onOpenChange(false)
    form.reset()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            {isEditing ? '编辑跟进记录' : '添加跟进记录'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? '修改跟进记录的内容和下次跟进时间'
              : '记录本次跟进的详细内容，并设置下次跟进时间'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-4"
            >
              {/* 跟进内容 */}
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      跟进内容 *
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请详细描述本次跟进的内容，包括沟通情况、客户反馈、需要注意的事项等..."
                        className="min-h-[120px] resize-none"
                        {...field}
                      />
                    </FormControl>
                    <div className="flex justify-between text-sm text-gray-500">
                      <FormMessage />
                      <span>{field.value?.length || 0}/2000</span>
                    </div>
                  </FormItem>
                )}
              />

              {/* 下次跟进日期 */}
              <FormField
                control={form.control}
                name="nextFollowUpDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      下次跟进日期
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        min={new Date().toISOString().split('T')[0]}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </motion.div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isLoading}
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </Button>
              <Button type="submit" disabled={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                {isLoading 
                  ? (isEditing ? '更新中...' : '创建中...') 
                  : (isEditing ? '更新' : '创建')
                }
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

// 快速跟进按钮组件
interface QuickFollowUpProps {
  onQuickFollowUp: (content: string, nextDate?: string) => void
  disabled?: boolean
}

export function QuickFollowUpButtons({ onQuickFollowUp, disabled }: QuickFollowUpProps) {
  const quickTemplates = [
    {
      label: '电话已接通',
      content: '电话已接通，客户表示有兴趣了解更多详情。',
      nextDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 3天后
    },
    {
      label: '未接通',
      content: '电话未接通，稍后再次尝试联系。',
      nextDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1天后
    },
    {
      label: '已预约参观',
      content: '客户已同意预约参观，安排具体时间。',
      nextDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7天后
    },
    {
      label: '需要考虑',
      content: '客户表示需要时间考虑，已提供相关资料。',
      nextDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7天后
    },
  ]

  return (
    <div className="space-y-2">
      <p className="text-sm font-medium text-gray-700">快速跟进模板</p>
      <div className="grid grid-cols-2 gap-2">
        {quickTemplates.map((template, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            onClick={() => onQuickFollowUp(template.content, template.nextDate)}
            disabled={disabled}
            className="text-left justify-start h-auto py-2"
          >
            {template.label}
          </Button>
        ))}
      </div>
    </div>
  )
}
