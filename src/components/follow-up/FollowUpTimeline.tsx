'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  Clock,
  User,
  Calendar,
  MessageSquare,
  Edit,
  Trash2,
  Plus,
  ChevronDown,
  ChevronUp,
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarFallback, AvatarInitials } from '@/components/ui/avatar'
import { useToast } from '@/hooks/use-toast'

// 跟进记录类型定义
export interface FollowUpRecord {
  id: string
  consultationId: string
  content: string
  nextFollowUpDate: string | null
  createdAt: string
  user: {
    id: string
    name: string
    email: string
  }
  consultation: {
    id: string
    consultantName: string
    consultantPhone: string
    purpose: string
    status: number
  }
}

interface FollowUpTimelineProps {
  consultationId: string
  followUps: FollowUpRecord[]
  onAddFollowUp?: () => void
  onEditFollowUp?: (followUp: FollowUpRecord) => void
  onDeleteFollowUp?: (followUpId: string) => void
  isLoading?: boolean
  canEdit?: boolean
  className?: string
}

// 状态映射
const statusMap = {
  1: { label: '待跟进', color: 'bg-yellow-100 text-yellow-800' },
  2: { label: '已跟进', color: 'bg-blue-100 text-blue-800' },
  3: { label: '已入住', color: 'bg-green-100 text-green-800' },
  4: { label: '已放弃', color: 'bg-gray-100 text-gray-800' },
}

export function FollowUpTimeline({
  consultationId,
  followUps,
  onAddFollowUp,
  onEditFollowUp,
  onDeleteFollowUp,
  isLoading = false,
  canEdit = true,
  className = '',
}: FollowUpTimelineProps) {
  const { toast } = useToast()
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  // 切换展开/收起
  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(id)) {
      newExpanded.delete(id)
    } else {
      newExpanded.add(id)
    }
    setExpandedItems(newExpanded)
  }

  // 处理删除跟进记录
  const handleDelete = async (followUpId: string) => {
    if (!onDeleteFollowUp) return

    try {
      await onDeleteFollowUp(followUpId)
      toast({
        title: '删除成功',
        description: '跟进记录已删除',
      })
    } catch (error) {
      toast({
        title: '删除失败',
        description: '删除跟进记录时发生错误',
        variant: 'destructive',
      })
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })
  }

  // 格式化相对时间
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return '刚刚'
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`
    } else if (diffInHours < 24 * 7) {
      return `${Math.floor(diffInHours / 24)}天前`
    } else {
      return format(date, 'MM月dd日', { locale: zhCN })
    }
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            跟进时间线
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex gap-4 animate-pulse">
                <div className="w-10 h-10 bg-gray-200 rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4" />
                  <div className="h-3 bg-gray-200 rounded w-3/4" />
                  <div className="h-3 bg-gray-200 rounded w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            跟进时间线
            <Badge variant="secondary">{followUps.length}</Badge>
          </CardTitle>
          {canEdit && onAddFollowUp && (
            <Button onClick={onAddFollowUp} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              添加跟进
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {followUps.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>暂无跟进记录</p>
            {canEdit && onAddFollowUp && (
              <Button
                variant="outline"
                onClick={onAddFollowUp}
                className="mt-4"
              >
                <Plus className="h-4 w-4 mr-2" />
                添加第一条跟进记录
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            <AnimatePresence>
              {followUps.map((followUp, index) => {
                const isExpanded = expandedItems.has(followUp.id)
                const isLast = index === followUps.length - 1

                return (
                  <motion.div
                    key={followUp.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="relative"
                  >
                    {/* 时间线连接线 */}
                    {!isLast && (
                      <div className="absolute left-5 top-12 w-0.5 h-full bg-gray-200" />
                    )}

                    <div className="flex gap-4">
                      {/* 头像和时间线节点 */}
                      <div className="relative">
                        <Avatar className="h-10 w-10 border-2 border-white shadow-sm">
                          <AvatarFallback>
                            <AvatarInitials name={followUp.user.name} />
                          </AvatarFallback>
                        </Avatar>
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 rounded-full border-2 border-white" />
                      </div>

                      {/* 跟进内容 */}
                      <div className="flex-1 min-w-0">
                        <div className="bg-gray-50 rounded-lg p-4 border">
                          {/* 头部信息 */}
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <User className="h-4 w-4" />
                              <span className="font-medium">{followUp.user.name}</span>
                              <span>•</span>
                              <span>{formatRelativeTime(followUp.createdAt)}</span>
                            </div>
                            {canEdit && (
                              <div className="flex items-center gap-1">
                                {onEditFollowUp && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => onEditFollowUp(followUp)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                )}
                                {onDeleteFollowUp && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDelete(followUp.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                )}
                              </div>
                            )}
                          </div>

                          {/* 跟进内容 */}
                          <div className="mb-3">
                            <p className={`text-gray-800 ${!isExpanded && followUp.content.length > 100 ? 'line-clamp-3' : ''}`}>
                              {followUp.content}
                            </p>
                            {followUp.content.length > 100 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleExpanded(followUp.id)}
                                className="mt-2 p-0 h-auto text-blue-600 hover:text-blue-800"
                              >
                                {isExpanded ? (
                                  <>
                                    <ChevronUp className="h-4 w-4 mr-1" />
                                    收起
                                  </>
                                ) : (
                                  <>
                                    <ChevronDown className="h-4 w-4 mr-1" />
                                    展开
                                  </>
                                )}
                              </Button>
                            )}
                          </div>

                          {/* 下次跟进日期 */}
                          {followUp.nextFollowUpDate && (
                            <div className="flex items-center gap-2 text-sm text-orange-600 bg-orange-50 px-3 py-2 rounded-md">
                              <Calendar className="h-4 w-4" />
                              <span>下次跟进: {format(new Date(followUp.nextFollowUpDate), 'yyyy年MM月dd日', { locale: zhCN })}</span>
                            </div>
                          )}

                          {/* 详细时间 */}
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <p className="text-xs text-gray-500">
                              {formatDate(followUp.createdAt)}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </AnimatePresence>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
