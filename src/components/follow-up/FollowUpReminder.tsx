'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { format, isToday, isTomorrow, isPast, differenceInDays } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  Bell,
  Clock,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Phone,
  MessageCircle,
  User,
  ChevronRight,
  Filter,
  Search,
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'

// 跟进提醒类型定义
export interface FollowUpReminder {
  id: string
  consultationId: string
  consultantName: string
  consultantPhone: string
  purpose: string
  followUpDate: string
  lastFollowUpContent?: string
  priority: 'high' | 'medium' | 'low'
  status: 'pending' | 'completed' | 'overdue'
  assignedUser: {
    id: string
    name: string
  }
}

interface FollowUpReminderProps {
  reminders: FollowUpReminder[]
  onMarkCompleted: (reminderId: string) => void
  onSnoozeReminder: (reminderId: string, newDate: string) => void
  onViewDetails: (consultationId: string) => void
  isLoading?: boolean
  className?: string
}

export function FollowUpReminderPanel({
  reminders,
  onMarkCompleted,
  onSnoozeReminder,
  onViewDetails,
  isLoading = false,
  className = '',
}: FollowUpReminderProps) {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterPriority, setFilterPriority] = useState<string>('all')

  // 过滤和搜索提醒
  const filteredReminders = reminders.filter((reminder) => {
    const matchesSearch = 
      reminder.consultantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reminder.consultantPhone.includes(searchTerm) ||
      reminder.purpose.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = filterStatus === 'all' || reminder.status === filterStatus
    const matchesPriority = filterPriority === 'all' || reminder.priority === filterPriority

    return matchesSearch && matchesStatus && matchesPriority
  })

  // 按状态分组
  const groupedReminders = {
    overdue: filteredReminders.filter(r => r.status === 'overdue'),
    today: filteredReminders.filter(r => r.status === 'pending' && isToday(new Date(r.followUpDate))),
    tomorrow: filteredReminders.filter(r => r.status === 'pending' && isTomorrow(new Date(r.followUpDate))),
    upcoming: filteredReminders.filter(r => {
      const date = new Date(r.followUpDate)
      return r.status === 'pending' && !isToday(date) && !isTomorrow(date) && !isPast(date)
    }),
    completed: filteredReminders.filter(r => r.status === 'completed'),
  }

  // 获取优先级样式
  const getPriorityStyle = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 获取状态样式
  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'pending':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 格式化日期显示
  const formatDateDisplay = (dateString: string) => {
    const date = new Date(dateString)
    
    if (isToday(date)) {
      return '今天'
    } else if (isTomorrow(date)) {
      return '明天'
    } else if (isPast(date)) {
      const days = Math.abs(differenceInDays(new Date(), date))
      return `逾期 ${days} 天`
    } else {
      return format(date, 'MM月dd日', { locale: zhCN })
    }
  }

  // 处理标记完成
  const handleMarkCompleted = async (reminderId: string) => {
    try {
      await onMarkCompleted(reminderId)
      toast({
        title: '已标记完成',
        description: '跟进提醒已标记为完成',
      })
    } catch (error) {
      toast({
        title: '操作失败',
        description: '标记完成时发生错误',
        variant: 'destructive',
      })
    }
  }

  // 处理延期提醒
  const handleSnooze = async (reminderId: string, days: number) => {
    const newDate = new Date()
    newDate.setDate(newDate.getDate() + days)
    const newDateString = newDate.toISOString().split('T')[0]

    try {
      await onSnoozeReminder(reminderId, newDateString)
      toast({
        title: '提醒已延期',
        description: `提醒已延期到 ${format(newDate, 'yyyy年MM月dd日', { locale: zhCN })}`,
      })
    } catch (error) {
      toast({
        title: '延期失败',
        description: '延期提醒时发生错误',
        variant: 'destructive',
      })
    }
  }

  // 渲染提醒项
  const ReminderItem = ({ reminder, index }: { reminder: FollowUpReminder; index: number }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      className="bg-white border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          {/* 头部信息 */}
          <div className="flex items-center gap-2 mb-2">
            <Badge className={getPriorityStyle(reminder.priority)}>
              {reminder.priority === 'high' ? '高优先级' : 
               reminder.priority === 'medium' ? '中优先级' : '低优先级'}
            </Badge>
            <Badge className={getStatusStyle(reminder.status)}>
              {reminder.status === 'overdue' ? '已逾期' :
               reminder.status === 'pending' ? '待跟进' : '已完成'}
            </Badge>
            <span className="text-sm text-gray-500">
              {formatDateDisplay(reminder.followUpDate)}
            </span>
          </div>

          {/* 客户信息 */}
          <div className="mb-3">
            <h4 className="font-medium text-gray-900 mb-1">
              {reminder.consultantName}
            </h4>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Phone className="h-4 w-4" />
                {reminder.consultantPhone}
              </div>
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                {reminder.assignedUser.name}
              </div>
            </div>
          </div>

          {/* 咨询目的 */}
          <p className="text-sm text-gray-700 mb-3 line-clamp-2">
            {reminder.purpose}
          </p>

          {/* 上次跟进内容 */}
          {reminder.lastFollowUpContent && (
            <div className="bg-gray-50 rounded-md p-3 mb-3">
              <p className="text-xs text-gray-500 mb-1">上次跟进:</p>
              <p className="text-sm text-gray-700 line-clamp-2">
                {reminder.lastFollowUpContent}
              </p>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col gap-2 ml-4">
          <Button
            size="sm"
            onClick={() => onViewDetails(reminder.consultationId)}
          >
            查看详情
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
          
          {reminder.status === 'pending' && (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleMarkCompleted(reminder.id)}
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                标记完成
              </Button>
              
              <Select onValueChange={(value) => handleSnooze(reminder.id, parseInt(value))}>
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="延期" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">延期1天</SelectItem>
                  <SelectItem value="3">延期3天</SelectItem>
                  <SelectItem value="7">延期1周</SelectItem>
                </SelectContent>
              </Select>
            </>
          )}
        </div>
      </div>
    </motion.div>
  )

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>跟进提醒</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-24 bg-gray-200 rounded-lg" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            跟进提醒
            <Badge variant="secondary">{reminders.length}</Badge>
          </CardTitle>
        </div>

        {/* 搜索和筛选 */}
        <div className="flex gap-4 mt-4">
          <div className="flex-1">
            <Input
              placeholder="搜索客户姓名、电话或咨询目的..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="overdue">已逾期</SelectItem>
              <SelectItem value="pending">待跟进</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
            </SelectContent>
          </Select>
          <Select value={filterPriority} onValueChange={setFilterPriority}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部优先级</SelectItem>
              <SelectItem value="high">高优先级</SelectItem>
              <SelectItem value="medium">中优先级</SelectItem>
              <SelectItem value="low">低优先级</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="overdue" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overdue" className="flex items-center gap-1">
              <AlertTriangle className="h-4 w-4" />
              逾期 ({groupedReminders.overdue.length})
            </TabsTrigger>
            <TabsTrigger value="today" className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              今天 ({groupedReminders.today.length})
            </TabsTrigger>
            <TabsTrigger value="tomorrow" className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              明天 ({groupedReminders.tomorrow.length})
            </TabsTrigger>
            <TabsTrigger value="upcoming" className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              即将到来 ({groupedReminders.upcoming.length})
            </TabsTrigger>
            <TabsTrigger value="completed" className="flex items-center gap-1">
              <CheckCircle className="h-4 w-4" />
              已完成 ({groupedReminders.completed.length})
            </TabsTrigger>
          </TabsList>

          {Object.entries(groupedReminders).map(([key, reminders]) => (
            <TabsContent key={key} value={key} className="mt-6">
              {reminders.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Bell className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>暂无{key === 'overdue' ? '逾期' : key === 'today' ? '今天' : key === 'tomorrow' ? '明天' : key === 'upcoming' ? '即将到来' : '已完成'}的提醒</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <AnimatePresence>
                    {reminders.map((reminder, index) => (
                      <ReminderItem
                        key={reminder.id}
                        reminder={reminder}
                        index={index}
                      />
                    ))}
                  </AnimatePresence>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  )
}
