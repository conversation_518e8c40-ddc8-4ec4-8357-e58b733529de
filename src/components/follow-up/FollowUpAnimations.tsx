'use client'

import React from 'react'
import { motion, AnimatePresence, Variants } from 'framer-motion'
import { <PERSON><PERSON>ircle, Clock, AlertTriangle, Sparkles } from 'lucide-react'

// 动画变体定义
export const fadeInUp: Variants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: 'easeOut',
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.2,
      ease: 'easeIn',
    },
  },
}

export const slideInFromRight: Variants = {
  initial: {
    opacity: 0,
    x: 100,
  },
  animate: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.4,
      ease: 'easeOut',
    },
  },
  exit: {
    opacity: 0,
    x: 100,
    transition: {
      duration: 0.3,
      ease: 'easeIn',
    },
  },
}

export const scaleIn: Variants = {
  initial: {
    opacity: 0,
    scale: 0.8,
  },
  animate: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: 'easeOut',
    },
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    transition: {
      duration: 0.2,
      ease: 'easeIn',
    },
  },
}

export const staggerContainer: Variants = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
  exit: {
    transition: {
      staggerChildren: 0.05,
      staggerDirection: -1,
    },
  },
}

// 成功动画组件
interface SuccessAnimationProps {
  show: boolean
  message?: string
  onComplete?: () => void
}

export function SuccessAnimation({ show, message = '操作成功', onComplete }: SuccessAnimationProps) {
  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.5 }}
          transition={{ duration: 0.3 }}
          onAnimationComplete={() => {
            if (onComplete) {
              setTimeout(onComplete, 1500)
            }
          }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/20"
        >
          <motion.div
            initial={{ y: 20 }}
            animate={{ y: 0 }}
            className="bg-white rounded-lg p-6 shadow-xl flex flex-col items-center"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.1, type: 'spring', stiffness: 200 }}
            >
              <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
            </motion.div>
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
              className="text-lg font-medium text-gray-900"
            >
              {message}
            </motion.p>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// 加载动画组件
interface LoadingAnimationProps {
  show: boolean
  message?: string
}

export function LoadingAnimation({ show, message = '处理中...' }: LoadingAnimationProps) {
  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/20"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            className="bg-white rounded-lg p-6 shadow-xl flex flex-col items-center"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              className="mb-4"
            >
              <Clock className="h-8 w-8 text-blue-500" />
            </motion.div>
            <p className="text-gray-700">{message}</p>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// 提醒闪烁动画组件
interface ReminderPulseProps {
  children: React.ReactNode
  isUrgent?: boolean
  className?: string
}

export function ReminderPulse({ children, isUrgent = false, className = '' }: ReminderPulseProps) {
  return (
    <motion.div
      animate={isUrgent ? {
        scale: [1, 1.02, 1],
        boxShadow: [
          '0 0 0 0 rgba(239, 68, 68, 0)',
          '0 0 0 4px rgba(239, 68, 68, 0.1)',
          '0 0 0 0 rgba(239, 68, 68, 0)',
        ],
      } : {}}
      transition={{
        duration: 2,
        repeat: isUrgent ? Infinity : 0,
        ease: 'easeInOut',
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// 状态变化动画组件
interface StatusChangeAnimationProps {
  show: boolean
  fromStatus: string
  toStatus: string
  onComplete?: () => void
}

export function StatusChangeAnimation({ 
  show, 
  fromStatus, 
  toStatus, 
  onComplete 
}: StatusChangeAnimationProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-8 w-8 text-yellow-500" />
      case 'completed':
        return <CheckCircle className="h-8 w-8 text-green-500" />
      case 'overdue':
        return <AlertTriangle className="h-8 w-8 text-red-500" />
      default:
        return <Clock className="h-8 w-8 text-gray-500" />
    }
  }

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onAnimationComplete={onComplete}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/20"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            className="bg-white rounded-lg p-8 shadow-xl flex flex-col items-center"
          >
            <div className="flex items-center gap-4 mb-4">
              <motion.div
                initial={{ scale: 1 }}
                animate={{ scale: 0.8, opacity: 0.5 }}
                transition={{ duration: 0.3 }}
              >
                {getStatusIcon(fromStatus)}
              </motion.div>
              
              <motion.div
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.3 }}
              >
                <Sparkles className="h-6 w-6 text-blue-500" />
              </motion.div>
              
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.6, duration: 0.3 }}
              >
                {getStatusIcon(toStatus)}
              </motion.div>
            </div>
            
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
              className="text-lg font-medium text-gray-900"
            >
              状态已更新
            </motion.p>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// 悬浮操作按钮动画
interface FloatingActionButtonProps {
  onClick: () => void
  icon: React.ReactNode
  label: string
  position?: 'bottom-right' | 'bottom-left'
  className?: string
}

export function FloatingActionButton({
  onClick,
  icon,
  label,
  position = 'bottom-right',
  className = '',
}: FloatingActionButtonProps) {
  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
  }

  return (
    <motion.button
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
      transition={{ type: 'spring', stiffness: 200, damping: 10 }}
      onClick={onClick}
      className={`
        fixed ${positionClasses[position]} z-40
        bg-blue-600 hover:bg-blue-700 text-white
        rounded-full p-4 shadow-lg
        flex items-center gap-2
        group transition-all duration-200
        ${className}
      `}
    >
      <motion.div
        whileHover={{ rotate: 15 }}
        transition={{ type: 'spring', stiffness: 300 }}
      >
        {icon}
      </motion.div>
      
      <motion.span
        initial={{ width: 0, opacity: 0 }}
        whileHover={{ width: 'auto', opacity: 1 }}
        transition={{ duration: 0.2 }}
        className="overflow-hidden whitespace-nowrap text-sm font-medium"
      >
        {label}
      </motion.span>
    </motion.button>
  )
}

// 时间线项目动画包装器
interface TimelineItemWrapperProps {
  children: React.ReactNode
  index: number
  isVisible?: boolean
}

export function TimelineItemWrapper({ 
  children, 
  index, 
  isVisible = true 
}: TimelineItemWrapperProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: -50 }}
      animate={isVisible ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
      exit={{ opacity: 0, x: 50 }}
      transition={{
        duration: 0.4,
        delay: index * 0.1,
        ease: 'easeOut',
      }}
      whileHover={{
        scale: 1.02,
        transition: { duration: 0.2 },
      }}
    >
      {children}
    </motion.div>
  )
}

// 数字计数动画
interface CountUpAnimationProps {
  from: number
  to: number
  duration?: number
  className?: string
}

export function CountUpAnimation({ 
  from, 
  to, 
  duration = 1, 
  className = '' 
}: CountUpAnimationProps) {
  return (
    <motion.span
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className={className}
    >
      <motion.span
        initial={{ scale: 0.5 }}
        animate={{ scale: 1 }}
        transition={{ type: 'spring', stiffness: 100 }}
      >
        {to}
      </motion.span>
    </motion.span>
  )
}
