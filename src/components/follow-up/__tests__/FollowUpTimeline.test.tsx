import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { FollowUpTimeline, FollowUpRecord } from '../FollowUpTimeline'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => '2024年01月15日 10:30'),
  zhCN: {},
}))

// Mock hooks
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

// Mock UI components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children }: any) => <div data-testid="card">{children}</div>,
  CardContent: ({ children }: any) => <div data-testid="card-content">{children}</div>,
  CardHeader: ({ children }: any) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children }: any) => <h3 data-testid="card-title">{children}</h3>,
}))

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
}))

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children }: any) => <span data-testid="badge">{children}</span>,
}))

vi.mock('@/components/ui/avatar', () => ({
  Avatar: ({ children }: any) => <div data-testid="avatar">{children}</div>,
  AvatarFallback: ({ children }: any) => <div data-testid="avatar-fallback">{children}</div>,
  AvatarInitials: ({ name }: any) => <span>{name?.charAt(0)}</span>,
}))

describe('FollowUpTimeline', () => {
  const mockFollowUps: FollowUpRecord[] = [
    {
      id: '1',
      consultationId: 'consultation-1',
      content: '电话联系客户，了解需求',
      nextFollowUpDate: '2024-01-20',
      createdAt: '2024-01-15T10:30:00Z',
      user: {
        id: 'user-1',
        name: '张三',
        email: '<EMAIL>',
      },
      consultation: {
        id: 'consultation-1',
        consultantName: '李四',
        consultantPhone: '13800138000',
        purpose: '了解养老院服务',
        status: 1,
      },
    },
    {
      id: '2',
      consultationId: 'consultation-1',
      content: '客户表示有兴趣，安排参观',
      nextFollowUpDate: null,
      createdAt: '2024-01-10T14:20:00Z',
      user: {
        id: 'user-1',
        name: '张三',
        email: '<EMAIL>',
      },
      consultation: {
        id: 'consultation-1',
        consultantName: '李四',
        consultantPhone: '13800138000',
        purpose: '了解养老院服务',
        status: 2,
      },
    },
  ]

  const defaultProps = {
    consultationId: 'consultation-1',
    followUps: mockFollowUps,
    onAddFollowUp: vi.fn(),
    onEditFollowUp: vi.fn(),
    onDeleteFollowUp: vi.fn(),
    isLoading: false,
    canEdit: true,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders timeline with follow-up records', () => {
    render(<FollowUpTimeline {...defaultProps} />)

    expect(screen.getByText('跟进时间线')).toBeInTheDocument()
    expect(screen.getByText('2')).toBeInTheDocument() // Badge with count
    expect(screen.getByText('电话联系客户，了解需求')).toBeInTheDocument()
    expect(screen.getByText('客户表示有兴趣，安排参观')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    render(<FollowUpTimeline {...defaultProps} isLoading={true} />)

    expect(screen.getByText('跟进时间线')).toBeInTheDocument()
    // Should show loading skeletons
    expect(screen.getAllByTestId('card')).toHaveLength(1)
  })

  it('shows empty state when no follow-ups', () => {
    render(<FollowUpTimeline {...defaultProps} followUps={[]} />)

    expect(screen.getByText('暂无跟进记录')).toBeInTheDocument()
    expect(screen.getByText('添加第一条跟进记录')).toBeInTheDocument()
  })

  it('calls onAddFollowUp when add button is clicked', () => {
    render(<FollowUpTimeline {...defaultProps} />)

    const addButton = screen.getByText('添加跟进')
    fireEvent.click(addButton)

    expect(defaultProps.onAddFollowUp).toHaveBeenCalledTimes(1)
  })

  it('calls onEditFollowUp when edit button is clicked', () => {
    render(<FollowUpTimeline {...defaultProps} />)

    const editButtons = screen.getAllByRole('button')
    const editButton = editButtons.find(button => 
      button.querySelector('svg') // Edit icon
    )

    if (editButton) {
      fireEvent.click(editButton)
      expect(defaultProps.onEditFollowUp).toHaveBeenCalledWith(mockFollowUps[0])
    }
  })

  it('calls onDeleteFollowUp when delete button is clicked', async () => {
    render(<FollowUpTimeline {...defaultProps} />)

    const deleteButtons = screen.getAllByRole('button')
    const deleteButton = deleteButtons.find(button => 
      button.querySelector('svg') // Delete icon
    )

    if (deleteButton) {
      fireEvent.click(deleteButton)
      
      await waitFor(() => {
        expect(defaultProps.onDeleteFollowUp).toHaveBeenCalledWith('1')
      })
    }
  })

  it('expands and collapses long content', () => {
    const longContentFollowUp = {
      ...mockFollowUps[0],
      content: 'A'.repeat(200), // Long content
    }

    render(
      <FollowUpTimeline 
        {...defaultProps} 
        followUps={[longContentFollowUp]} 
      />
    )

    // Should show expand button for long content
    const expandButton = screen.getByText('展开')
    expect(expandButton).toBeInTheDocument()

    fireEvent.click(expandButton)
    expect(screen.getByText('收起')).toBeInTheDocument()
  })

  it('displays next follow-up date when available', () => {
    render(<FollowUpTimeline {...defaultProps} />)

    expect(screen.getByText(/下次跟进:/)).toBeInTheDocument()
  })

  it('displays user information correctly', () => {
    render(<FollowUpTimeline {...defaultProps} />)

    expect(screen.getAllByText('张三')).toHaveLength(2) // Two follow-ups by same user
  })

  it('disables edit actions when canEdit is false', () => {
    render(<FollowUpTimeline {...defaultProps} canEdit={false} />)

    // Should not show add button in header
    expect(screen.queryByText('添加跟进')).not.toBeInTheDocument()
    
    // Should not show edit/delete buttons in items
    const editButtons = screen.queryAllByRole('button').filter(button => 
      button.textContent?.includes('编辑') || button.textContent?.includes('删除')
    )
    expect(editButtons).toHaveLength(0)
  })

  it('formats dates correctly', () => {
    render(<FollowUpTimeline {...defaultProps} />)

    // Should display formatted dates
    expect(screen.getAllByText('2024年01月15日 10:30')).toHaveLength(2)
  })

  it('shows relative time for recent follow-ups', () => {
    // This would test the formatRelativeTime function
    // The actual implementation would depend on the current date
    render(<FollowUpTimeline {...defaultProps} />)

    // The component should show relative time like "2小时前", "1天前" etc.
    // This is mocked in our test setup
  })
})
