import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import { FollowUpForm } from '../FollowUpForm'
import { FollowUpRecord } from '../FollowUpTimeline'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}))

// Mock hooks
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

// Mock react-hook-form
vi.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: (fn: any) => (e: any) => {
      e.preventDefault()
      fn({ content: 'Test content', nextFollowUpDate: '2024-01-20' })
    },
    reset: vi.fn(),
    formState: { errors: {} },
  }),
}))

// Mock UI components
vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div data-testid="dialog-content">{children}</div>,
  DialogDescription: ({ children }: any) => <p data-testid="dialog-description">{children}</p>,
  DialogFooter: ({ children }: any) => <div data-testid="dialog-footer">{children}</div>,
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <h2 data-testid="dialog-title">{children}</h2>,
}))

vi.mock('@/components/ui/form', () => ({
  Form: ({ children }: any) => <form data-testid="form">{children}</form>,
  FormControl: ({ children }: any) => <div data-testid="form-control">{children}</div>,
  FormField: ({ render }: any) => render({ field: { value: '', onChange: vi.fn() } }),
  FormItem: ({ children }: any) => <div data-testid="form-item">{children}</div>,
  FormLabel: ({ children }: any) => <label data-testid="form-label">{children}</label>,
  FormMessage: () => <span data-testid="form-message"></span>,
}))

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, type, ...props }: any) => (
    <button onClick={onClick} type={type} {...props}>
      {children}
    </button>
  ),
}))

vi.mock('@/components/ui/textarea', () => ({
  Textarea: (props: any) => <textarea data-testid="textarea" {...props} />,
}))

vi.mock('@/components/ui/input', () => ({
  Input: (props: any) => <input data-testid="input" {...props} />,
}))

describe('FollowUpForm', () => {
  const mockOnSubmit = vi.fn()
  const mockOnOpenChange = vi.fn()

  const defaultProps = {
    open: true,
    onOpenChange: mockOnOpenChange,
    consultationId: 'consultation-1',
    onSubmit: mockOnSubmit,
    isLoading: false,
  }

  const mockInitialData: FollowUpRecord = {
    id: '1',
    consultationId: 'consultation-1',
    content: '初始跟进内容',
    nextFollowUpDate: '2024-01-20',
    createdAt: '2024-01-15T10:30:00Z',
    user: {
      id: 'user-1',
      name: '张三',
      email: '<EMAIL>',
    },
    consultation: {
      id: 'consultation-1',
      consultantName: '李四',
      consultantPhone: '13800138000',
      purpose: '了解养老院服务',
      status: 1,
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders create form when no initial data', () => {
    render(<FollowUpForm {...defaultProps} />)

    expect(screen.getByTestId('dialog')).toBeInTheDocument()
    expect(screen.getByText('添加跟进记录')).toBeInTheDocument()
    expect(screen.getByText('记录本次跟进的详细内容，并设置下次跟进时间')).toBeInTheDocument()
  })

  it('renders edit form when initial data provided', () => {
    render(<FollowUpForm {...defaultProps} initialData={mockInitialData} />)

    expect(screen.getByText('编辑跟进记录')).toBeInTheDocument()
    expect(screen.getByText('修改跟进记录的内容和下次跟进时间')).toBeInTheDocument()
  })

  it('does not render when open is false', () => {
    render(<FollowUpForm {...defaultProps} open={false} />)

    expect(screen.queryByTestId('dialog')).not.toBeInTheDocument()
  })

  it('shows loading state on submit button', () => {
    render(<FollowUpForm {...defaultProps} isLoading={true} />)

    expect(screen.getByText('创建中...')).toBeInTheDocument()
  })

  it('shows loading state for edit mode', () => {
    render(
      <FollowUpForm 
        {...defaultProps} 
        initialData={mockInitialData} 
        isLoading={true} 
      />
    )

    expect(screen.getByText('更新中...')).toBeInTheDocument()
  })

  it('calls onSubmit when form is submitted', async () => {
    const user = userEvent.setup()
    render(<FollowUpForm {...defaultProps} />)

    const form = screen.getByTestId('form')
    await user.click(screen.getByText('创建'))

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        content: 'Test content',
        nextFollowUpDate: '2024-01-20',
      })
    })
  })

  it('calls onOpenChange when cancel button is clicked', async () => {
    const user = userEvent.setup()
    render(<FollowUpForm {...defaultProps} />)

    const cancelButton = screen.getByText('取消')
    await user.click(cancelButton)

    expect(mockOnOpenChange).toHaveBeenCalledWith(false)
  })

  it('displays form fields correctly', () => {
    render(<FollowUpForm {...defaultProps} />)

    expect(screen.getByText('跟进内容 *')).toBeInTheDocument()
    expect(screen.getByText('下次跟进日期')).toBeInTheDocument()
    expect(screen.getByTestId('textarea')).toBeInTheDocument()
    expect(screen.getByTestId('input')).toBeInTheDocument()
  })

  it('shows character count for content field', () => {
    render(<FollowUpForm {...defaultProps} />)

    expect(screen.getByText('0/2000')).toBeInTheDocument()
  })

  it('disables buttons when loading', () => {
    render(<FollowUpForm {...defaultProps} isLoading={true} />)

    const cancelButton = screen.getByText('取消')
    const submitButton = screen.getByText('创建中...')

    expect(cancelButton).toBeDisabled()
    expect(submitButton).toBeDisabled()
  })

  it('handles form submission error', async () => {
    const mockOnSubmitWithError = vi.fn().mockRejectedValue(new Error('Submission failed'))
    
    render(<FollowUpForm {...defaultProps} onSubmit={mockOnSubmitWithError} />)

    const form = screen.getByTestId('form')
    fireEvent.submit(form)

    await waitFor(() => {
      expect(mockOnSubmitWithError).toHaveBeenCalled()
    })
  })

  it('resets form when dialog opens', () => {
    const { rerender } = render(<FollowUpForm {...defaultProps} open={false} />)
    
    rerender(<FollowUpForm {...defaultProps} open={true} />)

    // Form should be reset when dialog opens
    // This is tested through the useEffect in the component
  })

  it('populates form with initial data in edit mode', () => {
    render(<FollowUpForm {...defaultProps} initialData={mockInitialData} />)

    // The form should be populated with initial data
    // This is handled by the defaultValues in useForm
  })

  it('shows correct button text for create vs edit mode', () => {
    const { rerender } = render(<FollowUpForm {...defaultProps} />)
    expect(screen.getByText('创建')).toBeInTheDocument()

    rerender(<FollowUpForm {...defaultProps} initialData={mockInitialData} />)
    expect(screen.getByText('更新')).toBeInTheDocument()
  })

  it('validates required fields', () => {
    render(<FollowUpForm {...defaultProps} />)

    // The form validation is handled by Zod schema
    // Required field validation would be tested through form submission
    expect(screen.getByText('跟进内容 *')).toBeInTheDocument()
  })

  it('sets minimum date for next follow-up date', () => {
    render(<FollowUpForm {...defaultProps} />)

    const dateInput = screen.getByTestId('input')
    const today = new Date().toISOString().split('T')[0]
    
    expect(dateInput).toHaveAttribute('min', today)
  })
})
