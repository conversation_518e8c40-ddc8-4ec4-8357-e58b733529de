import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { AppState, AppActions, Notification } from './types'

// 应用状态的初始值
const initialAppState: AppState = {
  theme: 'system',
  sidebarCollapsed: false,
  notifications: [],
  loading: {},
  errors: {},
}

// 创建应用 Store
export const useAppStore = create<AppState & AppActions>()(
  persist(
    immer((set, get) => ({
      ...initialAppState,

      // 设置主题
      setTheme: (theme: 'light' | 'dark' | 'system') => {
        set(state => {
          state.theme = theme
        })
      },

      // 切换侧边栏
      toggleSidebar: () => {
        set(state => {
          state.sidebarCollapsed = !state.sidebarCollapsed
        })
      },

      // 设置侧边栏折叠状态
      setSidebarCollapsed: (collapsed: boolean) => {
        set(state => {
          state.sidebarCollapsed = collapsed
        })
      },

      // 添加通知
      addNotification: (notification: Omit<Notification, 'id'>) => {
        const id =
          Date.now().toString() + Math.random().toString(36).substr(2, 9)
        const newNotification: Notification = {
          ...notification,
          id,
          duration: notification.duration ?? 5000,
        }

        set(state => {
          state.notifications.push(newNotification)
        })

        // 自动移除通知
        if (newNotification.duration && newNotification.duration > 0) {
          setTimeout(() => {
            get().removeNotification(id)
          }, newNotification.duration)
        }
      },

      // 移除通知
      removeNotification: (id: string) => {
        set(state => {
          state.notifications = state.notifications.filter(n => n.id !== id)
        })
      },

      // 清空所有通知
      clearNotifications: () => {
        set(state => {
          state.notifications = []
        })
      },

      // 设置加载状态
      setLoading: (key: string, loading: boolean) => {
        set(state => {
          if (loading) {
            state.loading[key] = true
          } else {
            delete state.loading[key]
          }
        })
      },

      // 设置错误信息
      setError: (key: string, error: string | null) => {
        set(state => {
          if (error) {
            state.errors[key] = error
          } else {
            delete state.errors[key]
          }
        })
      },

      // 清除错误信息
      clearError: (key: string) => {
        set(state => {
          delete state.errors[key]
        })
      },

      // 清除所有错误信息
      clearAllErrors: () => {
        set(state => {
          state.errors = {}
        })
      },
    })),
    {
      name: 'app-store',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        theme: state.theme,
        sidebarCollapsed: state.sidebarCollapsed,
      }),
    }
  )
)

// 选择器函数
export const selectTheme = (state: AppState & AppActions) => state.theme
export const selectSidebarCollapsed = (state: AppState & AppActions) =>
  state.sidebarCollapsed
export const selectNotifications = (state: AppState & AppActions) =>
  state.notifications
export const selectLoading = (state: AppState & AppActions) => state.loading
export const selectErrors = (state: AppState & AppActions) => state.errors
export const selectIsLoading =
  (key: string) => (state: AppState & AppActions) =>
    state.loading[key] || false
export const selectError = (key: string) => (state: AppState & AppActions) =>
  state.errors[key] || null
