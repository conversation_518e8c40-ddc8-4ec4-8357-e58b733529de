/**
 * 表单状态管理测试
 */

import { act, renderHook } from '@testing-library/react'
import { useFormManagement } from '../index'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('FormStore', () => {
  beforeEach(() => {
    // 清除所有 mock 调用
    jest.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  describe('表单注册和注销', () => {
    it('应该注册新表单', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', {
          title: '测试表单',
          description: '这是一个测试表单',
        })
      })

      const formState = result.current.getFormState('test-form')
      expect(formState).toBeDefined()
      expect(formState?.options.title).toBe('测试表单')
      expect(formState?.options.description).toBe('这是一个测试表单')
    })

    it('应该注销表单', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', {
          title: '测试表单',
        })
      })

      expect(result.current.getFormState('test-form')).toBeDefined()

      act(() => {
        result.current.unregisterForm('test-form')
      })

      expect(result.current.getFormState('test-form')).toBeUndefined()
    })

    it('应该获取所有已注册的表单', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('form1', { title: '表单1' })
        result.current.registerForm('form2', { title: '表单2' })
      })

      const allForms = result.current.getAllForms()
      expect(Object.keys(allForms)).toHaveLength(2)
      expect(allForms['form1']).toBeDefined()
      expect(allForms['form2']).toBeDefined()
    })
  })

  describe('表单数据操作', () => {
    it('应该设置表单数据', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { title: '测试表单' })
        result.current.setFormData('test-form', { name: '张三', age: 30 })
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.data).toEqual({ name: '张三', age: 30 })
    })

    it('应该更新表单数据', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { title: '测试表单' })
        result.current.setFormData('test-form', { name: '张三', age: 30 })
        result.current.updateFormData('test-form', { age: 31, email: '<EMAIL>' })
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.data).toEqual({
        name: '张三',
        age: 31,
        email: '<EMAIL>',
      })
    })

    it('应该清除表单数据', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { title: '测试表单' })
        result.current.setFormData('test-form', { name: '张三', age: 30 })
        result.current.clearFormData('test-form')
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.data).toEqual({})
    })
  })

  describe('表单验证', () => {
    it('应该设置表单错误', () => {
      const { result } = renderHook(() => useFormManagement())

      const errors = {
        name: '姓名不能为空',
        email: '邮箱格式不正确',
      }

      act(() => {
        result.current.registerForm('test-form', { title: '测试表单' })
        result.current.setFormErrors('test-form', errors)
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.errors).toEqual(errors)
    })

    it('应该清除表单错误', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { title: '测试表单' })
        result.current.setFormErrors('test-form', { name: '姓名不能为空' })
        result.current.clearFormErrors('test-form')
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.errors).toEqual({})
    })

    it('应该设置表单验证状态', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { title: '测试表单' })
        result.current.setFormValidating('test-form', true)
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.isValidating).toBe(true)

      act(() => {
        result.current.setFormValidating('test-form', false)
      })

      expect(result.current.getFormState('test-form')?.isValidating).toBe(false)
    })
  })

  describe('表单提交', () => {
    it('应该设置表单提交状态', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { title: '测试表单' })
        result.current.setFormSubmitting('test-form', true)
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.isSubmitting).toBe(true)

      act(() => {
        result.current.setFormSubmitting('test-form', false)
      })

      expect(result.current.getFormState('test-form')?.isSubmitting).toBe(false)
    })

    it('应该记录表单提交历史', () => {
      const { result } = renderHook(() => useFormManagement())

      const submitData = { name: '张三', age: 30 }

      act(() => {
        result.current.registerForm('test-form', { title: '测试表单' })
        result.current.addSubmitHistory('test-form', {
          data: submitData,
          success: true,
          timestamp: new Date(),
        })
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.submitHistory).toHaveLength(1)
      expect(formState?.submitHistory[0].data).toEqual(submitData)
      expect(formState?.submitHistory[0].success).toBe(true)
    })

    it('应该限制提交历史记录数量', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { title: '测试表单' })
        
        // 添加超过限制的提交记录
        for (let i = 0; i < 15; i++) {
          result.current.addSubmitHistory('test-form', {
            data: { index: i },
            success: true,
            timestamp: new Date(),
          })
        }
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.submitHistory).toHaveLength(10) // 默认限制为10条
    })
  })

  describe('表单缓存', () => {
    it('应该缓存表单数据', () => {
      const { result } = renderHook(() => useFormManagement())

      const cacheData = { name: '张三', age: 30 }

      act(() => {
        result.current.registerForm('test-form', { title: '测试表单' })
        result.current.cacheFormData('test-form', cacheData)
      })

      const cachedData = result.current.getCachedFormData('test-form')
      expect(cachedData?.data).toEqual(cacheData)
      expect(cachedData?.timestamp).toBeInstanceOf(Date)
    })

    it('应该清除表单缓存', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { title: '测试表单' })
        result.current.cacheFormData('test-form', { name: '张三' })
        result.current.clearFormCache('test-form')
      })

      const cachedData = result.current.getCachedFormData('test-form')
      expect(cachedData).toBeUndefined()
    })

    it('应该清除所有表单缓存', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('form1', { title: '表单1' })
        result.current.registerForm('form2', { title: '表单2' })
        result.current.cacheFormData('form1', { data: 'form1' })
        result.current.cacheFormData('form2', { data: 'form2' })
        result.current.clearAllCache()
      })

      expect(result.current.getCachedFormData('form1')).toBeUndefined()
      expect(result.current.getCachedFormData('form2')).toBeUndefined()
    })
  })

  describe('自动保存', () => {
    beforeEach(() => {
      jest.useFakeTimers()
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    it('应该启用自动保存', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { 
          title: '测试表单',
          autoSave: true,
          autoSaveInterval: 1000,
        })
        result.current.enableAutoSave('test-form')
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.autoSaveEnabled).toBe(true)
    })

    it('应该禁用自动保存', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { 
          title: '测试表单',
          autoSave: true,
        })
        result.current.enableAutoSave('test-form')
        result.current.disableAutoSave('test-form')
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.autoSaveEnabled).toBe(false)
    })
  })

  describe('数据持久化', () => {
    it('应该保存数据到 localStorage', () => {
      const { result } = renderHook(() => useFormManagement())

      const formData = { name: '张三', age: 30 }

      act(() => {
        result.current.registerForm('test-form', { 
          title: '测试表单',
          persist: true,
        })
        result.current.setFormData('test-form', formData)
        result.current.saveToStorage('test-form')
      })

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'form_test-form',
        JSON.stringify(formData)
      )
    })

    it('应该从 localStorage 加载数据', () => {
      const formData = { name: '张三', age: 30 }
      localStorageMock.getItem.mockReturnValue(JSON.stringify(formData))

      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { 
          title: '测试表单',
          persist: true,
        })
        result.current.loadFromStorage('test-form')
      })

      const formState = result.current.getFormState('test-form')
      expect(formState?.data).toEqual(formData)
    })

    it('应该从 localStorage 删除数据', () => {
      const { result } = renderHook(() => useFormManagement())

      act(() => {
        result.current.registerForm('test-form', { 
          title: '测试表单',
          persist: true,
        })
        result.current.removeFromStorage('test-form')
      })

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('form_test-form')
    })
  })
})
