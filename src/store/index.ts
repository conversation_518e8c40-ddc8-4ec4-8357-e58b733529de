/**
 * 全局状态管理入口
 *
 * 这个文件导出所有的 store 和相关的 hooks，
 * 提供统一的状态管理接口
 */

import React from 'react'

// 导出所有 store
export { useAuthStore } from './auth-store'
export { useAppStore } from './app-store'
export { useApiStore } from './api-store'
export { useFormStore, formSelectors } from './form-store'

// 导出类型定义
export type {
  User,
  UserRole,
  AuthState,
  AuthActions,
  AppState,
  AppActions,
  ApiState,
  ApiActions,
  FormState,
  FormCacheItem,
  FormSubmitHistory,
  FormManagementState,
  FormManagementActions,
  FormOptions,
  Notification,
  RootStore,
} from './types'

// 导出异步工具函数
export {
  useAsyncOperation,
  createAsyncState,
  withRetry,
  debounceAsync,
  throttleAsync,
} from './async-utils'

// 导出选择器函数
export {
  selectUser,
  selectIsAuthenticated,
  selectPermissions,
  selectRole,
  selectIsLoading as selectAuthLoading,
  selectError as selectAuthError,
} from './auth-store'

export {
  selectTheme,
  selectSidebarCollapsed,
  selectNotifications,
  selectLoading,
  selectErrors,
  selectIsLoading as selectAppIsLoading,
  selectError as selectAppError,
} from './app-store'

export {
  selectCache,
  selectPendingRequests,
  selectCacheItem,
  selectIsPending,
  cleanExpiredCache,
} from './api-store'

// 组合 hooks
import { useAuthStore } from './auth-store'
import { useAppStore } from './app-store'
import { useApiStore } from './api-store'
import { useFormStore } from './form-store'

/**
 * 认证相关的组合 hook
 */
export const useAuth = () => {
  const authStore = useAuthStore()
  return {
    // 状态
    user: authStore.user,
    isAuthenticated: authStore.isAuthenticated,
    isLoading: authStore.isLoading,
    error: authStore.error,
    permissions: authStore.permissions,
    role: authStore.role,

    // 操作
    login: authStore.login,
    logout: authStore.logout,
    refreshAuth: authStore.refreshAuth,

    // 权限检查
    hasPermission: authStore.hasPermission,
    hasAnyPermission: authStore.hasAnyPermission,
    hasAllPermissions: authStore.hasAllPermissions,
    hasRole: authStore.hasRole,
    hasAnyRole: authStore.hasAnyRole,
  }
}

/**
 * 应用状态相关的组合 hook
 */
export const useApp = () => {
  const appStore = useAppStore()
  return {
    // 状态
    theme: appStore.theme,
    sidebarCollapsed: appStore.sidebarCollapsed,
    notifications: appStore.notifications,
    loading: appStore.loading,
    errors: appStore.errors,

    // 操作
    setTheme: appStore.setTheme,
    toggleSidebar: appStore.toggleSidebar,
    setSidebarCollapsed: appStore.setSidebarCollapsed,
    addNotification: appStore.addNotification,
    removeNotification: appStore.removeNotification,
    clearNotifications: appStore.clearNotifications,
    setLoading: appStore.setLoading,
    setError: appStore.setError,
    clearError: appStore.clearError,
    clearAllErrors: appStore.clearAllErrors,
  }
}

/**
 * API 缓存相关的组合 hook
 */
export const useApiCache = () => {
  const apiStore = useApiStore()
  return {
    // 状态
    cache: apiStore.cache,
    pendingRequests: apiStore.pendingRequests,

    // 操作
    setCache: apiStore.setCache,
    getCache: apiStore.getCache,
    clearCache: apiStore.clearCache,
    addPendingRequest: apiStore.addPendingRequest,
    removePendingRequest: apiStore.removePendingRequest,
    isPending: apiStore.isPending,
  }
}

/**
 * 通知相关的便捷 hook
 */
export const useNotifications = () => {
  const {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
  } = useApp()

  const showSuccess = (title: string, message?: string) => {
    addNotification({ type: 'success', title, message })
  }

  const showError = (title: string, message?: string) => {
    addNotification({ type: 'error', title, message })
  }

  const showWarning = (title: string, message?: string) => {
    addNotification({ type: 'warning', title, message })
  }

  const showInfo = (title: string, message?: string) => {
    addNotification({ type: 'info', title, message })
  }

  return {
    notifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    removeNotification,
    clearNotifications,
  }
}

/**
 * 加载状态相关的便捷 hook
 */
export const useLoading = (key?: string) => {
  const { loading, setLoading } = useApp()

  if (key) {
    return {
      isLoading: loading[key] || false,
      setLoading: (isLoading: boolean) => setLoading(key, isLoading),
    }
  }

  return {
    loading,
    setLoading,
  }
}

/**
 * 错误状态相关的便捷 hook
 */
export const useError = (key?: string) => {
  const { errors, setError, clearError } = useApp()

  if (key) {
    return {
      error: errors[key] || null,
      setError: (error: string | null) => setError(key, error),
      clearError: () => clearError(key),
    }
  }

  return {
    errors,
    setError,
    clearError,
  }
}

/**
 * 表单管理相关的组合 hook
 */
export const useFormManagement = () => {
  const formStore = useFormStore()
  return {
    // 状态
    forms: formStore.forms,
    cache: formStore.cache,
    submitHistory: formStore.submitHistory,
    globalSettings: formStore.globalSettings,

    // 表单注册和注销
    registerForm: formStore.registerForm,
    unregisterForm: formStore.unregisterForm,

    // 表单数据操作
    updateFormData: formStore.updateFormData,
    setFormField: formStore.setFormField,
    resetForm: formStore.resetForm,

    // 表单状态操作
    setFormSubmitting: formStore.setFormSubmitting,
    setFormValidating: formStore.setFormValidating,
    setFormErrors: formStore.setFormErrors,
    setFormTouched: formStore.setFormTouched,

    // 表单验证
    validateForm: formStore.validateForm,
    validateField: formStore.validateField,

    // 表单提交
    submitForm: formStore.submitForm,

    // 缓存操作
    cacheFormData: formStore.cacheFormData,
    getCachedFormData: formStore.getCachedFormData,
    clearFormCache: formStore.clearFormCache,

    // 历史记录
    addSubmitHistory: formStore.addSubmitHistory,
    getSubmitHistory: formStore.getSubmitHistory,
    clearSubmitHistory: formStore.clearSubmitHistory,

    // 全局设置
    updateGlobalSettings: formStore.updateGlobalSettings,

    // 工具方法
    getForm: formStore.getForm,
    isFormDirty: formStore.isFormDirty,
    isFormValid: formStore.isFormValid,
    isFormSubmitting: formStore.isFormSubmitting,
  }
}

/**
 * 单个表单相关的便捷 hook
 */
export const useForm = <T extends Record<string, any> = Record<string, any>>(
  formId: string,
  initialData?: Partial<T>,
  options?: FormOptions
) => {
  const formManagement = useFormManagement()

  // 自动注册表单
  React.useEffect(() => {
    formManagement.registerForm(formId, initialData, options)

    return () => {
      formManagement.unregisterForm(formId)
    }
  }, [formId])

  const form = formManagement.getForm(formId)

  return {
    // 表单状态
    form,
    data: form?.data as Partial<T>,
    originalData: form?.originalData as Partial<T>,
    isDirty: form?.isDirty ?? false,
    isValid: form?.isValid ?? false,
    isSubmitting: form?.isSubmitting ?? false,
    isValidating: form?.isValidating ?? false,
    errors: form?.errors ?? {},
    touchedFields: form?.touchedFields ?? {},

    // 表单操作
    updateData: (data: Partial<T>) =>
      formManagement.updateFormData(formId, data),
    setField: (field: string, value: any) =>
      formManagement.setFormField(formId, field, value),
    reset: () => formManagement.resetForm(formId),
    submit: (submitFn: (data: T) => Promise<any>) =>
      formManagement.submitForm(formId, submitFn),

    // 状态操作
    setSubmitting: (submitting: boolean) =>
      formManagement.setFormSubmitting(formId, submitting),
    setValidating: (validating: boolean) =>
      formManagement.setFormValidating(formId, validating),
    setErrors: (errors: Record<string, any>) =>
      formManagement.setFormErrors(formId, errors),
    setTouched: (field: string, touched: boolean) =>
      formManagement.setFormTouched(formId, field, touched),

    // 验证
    validate: () => formManagement.validateForm(formId),
    validateField: (field: string) =>
      formManagement.validateField(formId, field),
  }
}
