/**
 * 表单状态管理 Store
 * 
 * 基于 Zustand 实现的表单状态管理系统，提供：
 * - 表单数据缓存和持久化
 * - 表单提交状态跟踪
 * - 表单验证状态管理
 * - 表单历史记录
 * - 自动保存功能
 */

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { 
  FormManagementState, 
  FormManagementActions, 
  FormState, 
  FormCacheItem, 
  FormSubmitHistory,
  FormOptions 
} from './types'

// 默认全局设置
const DEFAULT_GLOBAL_SETTINGS = {
  autoSaveEnabled: true,
  autoSaveInterval: 2000, // 2秒
  cacheExpiration: 24 * 60 * 60 * 1000, // 24小时
  maxHistoryItems: 50,
  validationDebounce: 300, // 300ms
}

// 创建表单管理 Store
export const useFormStore = create<FormManagementState & FormManagementActions>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // 初始状态
      forms: {},
      cache: {},
      submitHistory: [],
      globalSettings: DEFAULT_GLOBAL_SETTINGS,

      // 表单注册和注销
      registerForm: <T extends Record<string, any>>(
        formId: string,
        initialData: Partial<T> = {},
        options: FormOptions = {}
      ) => {
        set((state) => {
          // 检查是否已存在
          if (state.forms[formId]) {
            console.warn(`表单 ${formId} 已存在，将被覆盖`)
          }

          // 创建表单状态
          const formState: FormState<T> = {
            formId,
            data: { ...initialData },
            originalData: { ...initialData },
            isDirty: false,
            isValid: true,
            isSubmitting: false,
            isValidating: false,
            submitCount: 0,
            errors: {},
            touchedFields: {},
            autoSaveEnabled: options.autoSave ?? state.globalSettings.autoSaveEnabled,
            autoSaveInterval: options.autoSaveInterval ?? state.globalSettings.autoSaveInterval,
            persistenceKey: options.persistenceKey,
          }

          state.forms[formId] = formState

          // 如果启用持久化，尝试从缓存加载数据
          if (options.persistence && options.persistenceKey) {
            const cached = state.cache[options.persistenceKey]
            if (cached && cached.expiresAt && cached.expiresAt > new Date()) {
              formState.data = { ...formState.data, ...cached.data }
              formState.isDirty = true
            }
          }
        })
      },

      unregisterForm: (formId: string) => {
        set((state) => {
          delete state.forms[formId]
        })
      },

      // 表单数据操作
      updateFormData: <T extends Record<string, any>>(
        formId: string,
        data: Partial<T>
      ) => {
        set((state) => {
          const form = state.forms[formId]
          if (!form) {
            console.warn(`表单 ${formId} 不存在`)
            return
          }

          // 更新数据
          form.data = { ...form.data, ...data }
          
          // 检查是否有变化
          form.isDirty = JSON.stringify(form.data) !== JSON.stringify(form.originalData)
          
          // 如果启用持久化，保存到缓存
          if (form.persistenceKey) {
            const cacheItem: FormCacheItem = {
              formId,
              data: form.data,
              timestamp: new Date(),
              expiresAt: new Date(Date.now() + state.globalSettings.cacheExpiration),
            }
            state.cache[form.persistenceKey] = cacheItem
          }
        })
      },

      setFormField: (formId: string, field: string, value: any) => {
        set((state) => {
          const form = state.forms[formId]
          if (!form) {
            console.warn(`表单 ${formId} 不存在`)
            return
          }

          // 更新字段值
          form.data[field] = value
          form.touchedFields[field] = true
          
          // 检查是否有变化
          form.isDirty = JSON.stringify(form.data) !== JSON.stringify(form.originalData)
          
          // 清除该字段的错误
          if (form.errors[field]) {
            delete form.errors[field]
          }
        })
      },

      resetForm: (formId: string) => {
        set((state) => {
          const form = state.forms[formId]
          if (!form) {
            console.warn(`表单 ${formId} 不存在`)
            return
          }

          // 重置表单数据
          form.data = { ...form.originalData }
          form.isDirty = false
          form.errors = {}
          form.touchedFields = {}
          form.isSubmitting = false
          form.isValidating = false
        })
      },

      // 表单状态操作
      setFormSubmitting: (formId: string, submitting: boolean) => {
        set((state) => {
          const form = state.forms[formId]
          if (form) {
            form.isSubmitting = submitting
          }
        })
      },

      setFormValidating: (formId: string, validating: boolean) => {
        set((state) => {
          const form = state.forms[formId]
          if (form) {
            form.isValidating = validating
            if (validating) {
              form.lastValidationTime = new Date()
            }
          }
        })
      },

      setFormErrors: (formId: string, errors: Record<string, any>) => {
        set((state) => {
          const form = state.forms[formId]
          if (form) {
            form.errors = errors
            form.isValid = Object.keys(errors).length === 0
          }
        })
      },

      setFormTouched: (formId: string, field: string, touched: boolean) => {
        set((state) => {
          const form = state.forms[formId]
          if (form) {
            form.touchedFields[field] = touched
          }
        })
      },

      // 表单验证（占位符实现，实际验证逻辑在组件中处理）
      validateForm: async (formId: string): Promise<boolean> => {
        const form = get().forms[formId]
        if (!form) return false
        
        // 这里可以添加全局验证逻辑
        return form.isValid
      },

      validateField: async (formId: string, field: string): Promise<boolean> => {
        const form = get().forms[formId]
        if (!form) return false
        
        // 这里可以添加字段级验证逻辑
        return !form.errors[field]
      },

      // 表单提交
      submitForm: async <T extends Record<string, any>>(
        formId: string,
        submitFn: (data: T) => Promise<any>
      ): Promise<any> => {
        const startTime = Date.now()
        
        try {
          // 设置提交状态
          get().setFormSubmitting(formId, true)
          
          const form = get().forms[formId]
          if (!form) {
            throw new Error(`表单 ${formId} 不存在`)
          }

          // 执行提交函数
          const result = await submitFn(form.data as T)
          
          // 更新表单状态
          set((state) => {
            const currentForm = state.forms[formId]
            if (currentForm) {
              currentForm.submitCount += 1
              currentForm.lastSubmitTime = new Date()
              currentForm.originalData = { ...currentForm.data }
              currentForm.isDirty = false
            }
          })

          // 添加成功历史记录
          get().addSubmitHistory({
            formId,
            data: form.data as T,
            success: true,
            duration: Date.now() - startTime,
          })

          return result
        } catch (error) {
          // 添加失败历史记录
          get().addSubmitHistory({
            formId,
            data: get().forms[formId]?.data as T,
            success: false,
            error: error instanceof Error ? error.message : '未知错误',
            duration: Date.now() - startTime,
          })
          
          throw error
        } finally {
          // 清除提交状态
          get().setFormSubmitting(formId, false)
        }
      },

      // 缓存操作
      cacheFormData: <T extends Record<string, any>>(
        formId: string,
        data: Partial<T>,
        expiration?: number
      ) => {
        set((state) => {
          const expiresAt = expiration 
            ? new Date(Date.now() + expiration)
            : new Date(Date.now() + state.globalSettings.cacheExpiration)

          const cacheItem: FormCacheItem = {
            formId,
            data,
            timestamp: new Date(),
            expiresAt,
          }

          state.cache[formId] = cacheItem
        })
      },

      getCachedFormData: (formId: string): FormCacheItem | null => {
        const cached = get().cache[formId]
        if (!cached) return null
        
        // 检查是否过期
        if (cached.expiresAt && cached.expiresAt < new Date()) {
          // 清除过期缓存
          set((state) => {
            delete state.cache[formId]
          })
          return null
        }
        
        return cached
      },

      clearFormCache: (formId?: string) => {
        set((state) => {
          if (formId) {
            delete state.cache[formId]
          } else {
            state.cache = {}
          }
        })
      },

      // 历史记录
      addSubmitHistory: <T extends Record<string, any>>(
        history: Omit<FormSubmitHistory<T>, 'timestamp'>
      ) => {
        set((state) => {
          const newHistory: FormSubmitHistory<T> = {
            ...history,
            timestamp: new Date(),
          }
          
          state.submitHistory.unshift(newHistory)
          
          // 限制历史记录数量
          if (state.submitHistory.length > state.globalSettings.maxHistoryItems) {
            state.submitHistory = state.submitHistory.slice(0, state.globalSettings.maxHistoryItems)
          }
        })
      },

      getSubmitHistory: (formId?: string): FormSubmitHistory[] => {
        const history = get().submitHistory
        return formId 
          ? history.filter(item => item.formId === formId)
          : history
      },

      clearSubmitHistory: (formId?: string) => {
        set((state) => {
          if (formId) {
            state.submitHistory = state.submitHistory.filter(item => item.formId !== formId)
          } else {
            state.submitHistory = []
          }
        })
      },

      // 全局设置
      updateGlobalSettings: (settings: Partial<FormManagementState['globalSettings']>) => {
        set((state) => {
          state.globalSettings = { ...state.globalSettings, ...settings }
        })
      },

      // 工具方法
      getForm: (formId: string): FormState | null => {
        return get().forms[formId] || null
      },

      isFormDirty: (formId: string): boolean => {
        const form = get().forms[formId]
        return form?.isDirty ?? false
      },

      isFormValid: (formId: string): boolean => {
        const form = get().forms[formId]
        return form?.isValid ?? false
      },

      isFormSubmitting: (formId: string): boolean => {
        const form = get().forms[formId]
        return form?.isSubmitting ?? false
      },
    }))
  )
)

// 表单状态选择器
export const formSelectors = {
  getForm: (formId: string) => (state: FormManagementState) => state.forms[formId],
  isFormDirty: (formId: string) => (state: FormManagementState) => state.forms[formId]?.isDirty ?? false,
  isFormValid: (formId: string) => (state: FormManagementState) => state.forms[formId]?.isValid ?? false,
  isFormSubmitting: (formId: string) => (state: FormManagementState) => state.forms[formId]?.isSubmitting ?? false,
  getFormErrors: (formId: string) => (state: FormManagementState) => state.forms[formId]?.errors ?? {},
  getFormData: (formId: string) => (state: FormManagementState) => state.forms[formId]?.data ?? {},
}
