'use client'

import { useEffect } from 'react'
import { useAuthStore } from './auth-store'
import { useSession } from '@/lib/auth-client'

/**
 * Store Provider 组件
 *
 * 负责初始化和同步状态管理
 */
export function StoreProvider({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession()
  const { setUser, refreshAuth, user } = useAuthStore()

  // 同步认证状态
  useEffect(() => {
    if (session?.user) {
      const currentUser = {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name || '',
        avatar: session.user.image || undefined,
      }

      // 只有当用户信息发生变化时才更新
      if (!user || user.id !== currentUser.id) {
        setUser(currentUser)
        // 刷新权限和角色信息
        refreshAuth()
      }
    } else if (user) {
      // 用户已登出，清除状态
      setUser(null)
    }
  }, [session, setUser, refreshAuth, user])

  return <>{children}</>
}

/**
 * Store 初始化 Hook
 *
 * 在应用启动时执行一些初始化操作
 */
export function useStoreInitialization() {
  const { refreshAuth, isAuthenticated } = useAuthStore()

  useEffect(() => {
    // 如果用户已认证，刷新权限信息
    if (isAuthenticated) {
      refreshAuth()
    }
  }, [isAuthenticated, refreshAuth])

  // 可以在这里添加其他初始化逻辑
  useEffect(() => {
    // 清理过期缓存
    const { cleanExpiredCache } = require('./api-store')
    cleanExpiredCache()
  }, [])
}
