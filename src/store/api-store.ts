import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { ApiState, ApiActions } from './types'

// API 状态的初始值
const initialApiState: ApiState = {
  cache: {},
  pendingRequests: [],
}

// 默认缓存过期时间（5分钟）
const DEFAULT_CACHE_EXPIRY = 5 * 60 * 1000

// 创建 API Store
export const useApiStore = create<ApiState & ApiActions>()(
  immer((set, get) => ({
    ...initialApiState,

    // 设置缓存
    setCache: (
      key: string,
      data: any,
      expiry: number = DEFAULT_CACHE_EXPIRY
    ) => {
      set(state => {
        state.cache[key] = {
          data,
          timestamp: Date.now(),
          expiry: Date.now() + expiry,
        }
      })
    },

    // 获取缓存
    getCache: (key: string) => {
      const { cache } = get()
      const cached = cache[key]

      if (!cached) return null

      // 检查是否过期
      if (Date.now() > cached.expiry) {
        // 清除过期缓存
        set(state => {
          delete state.cache[key]
        })
        return null
      }

      return cached.data
    },

    // 清除缓存
    clearCache: (key?: string) => {
      set(state => {
        if (key) {
          delete state.cache[key]
        } else {
          state.cache = {}
        }
      })
    },

    // 添加待处理请求
    addPendingRequest: (key: string) => {
      set(state => {
        if (!state.pendingRequests.includes(key)) {
          state.pendingRequests.push(key)
        }
      })
    },

    // 移除待处理请求
    removePendingRequest: (key: string) => {
      set(state => {
        const index = state.pendingRequests.indexOf(key)
        if (index > -1) {
          state.pendingRequests.splice(index, 1)
        }
      })
    },

    // 检查是否有待处理请求
    isPending: (key: string) => {
      const { pendingRequests } = get()
      return pendingRequests.includes(key)
    },
  }))
)

// 选择器函数
export const selectCache = (state: ApiState & ApiActions) => state.cache
export const selectPendingRequests = (state: ApiState & ApiActions) =>
  state.pendingRequests
export const selectCacheItem =
  (key: string) => (state: ApiState & ApiActions) => {
    const cached = state.cache[key]
    if (!cached || Date.now() > cached.expiry) return null
    return cached.data
  }
export const selectIsPending =
  (key: string) => (state: ApiState & ApiActions) =>
    state.pendingRequests.includes(key)

// 缓存清理工具函数
export const cleanExpiredCache = () => {
  const { cache, clearCache } = useApiStore.getState()
  const now = Date.now()

  Object.keys(cache).forEach(key => {
    if (cache[key].expiry < now) {
      clearCache(key)
    }
  })
}

// 定期清理过期缓存
if (typeof window !== 'undefined') {
  setInterval(cleanExpiredCache, 60000) // 每分钟清理一次
}
