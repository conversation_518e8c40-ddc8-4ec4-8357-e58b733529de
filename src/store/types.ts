/**
 * 全局状态类型定义
 */

// 用户信息类型
export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  role?: UserRole
  permissions?: string[]
}

// 用户角色类型
export interface UserRole {
  id: string
  name: string
  displayName: string
  description?: string
}

// 认证状态类型
export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  permissions: string[]
  role: UserRole | null
}

// 认证操作类型
export interface AuthActions {
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setPermissions: (permissions: string[]) => void
  setRole: (role: UserRole | null) => void
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  refreshAuth: () => Promise<void>
  hasPermission: (permission: string) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  hasAllPermissions: (permissions: string[]) => boolean
  hasRole: (roleName: string) => boolean
  hasAnyRole: (roleNames: string[]) => boolean
}

// 应用状态类型
export interface AppState {
  theme: 'light' | 'dark' | 'system'
  sidebarCollapsed: boolean
  notifications: Notification[]
  loading: Record<string, boolean>
  errors: Record<string, string | null>
}

// 应用操作类型
export interface AppActions {
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  toggleSidebar: () => void
  setSidebarCollapsed: (collapsed: boolean) => void
  addNotification: (notification: Omit<Notification, 'id'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
  setLoading: (key: string, loading: boolean) => void
  setError: (key: string, error: string | null) => void
  clearError: (key: string) => void
  clearAllErrors: () => void
}

// 通知类型
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

// API 状态类型
export interface ApiState {
  cache: Record<
    string,
    {
      data: any
      timestamp: number
      expiry: number
    }
  >
  pendingRequests: string[]
}

// API 操作类型
export interface ApiActions {
  setCache: (key: string, data: any, expiry?: number) => void
  getCache: (key: string) => any
  clearCache: (key?: string) => void
  addPendingRequest: (key: string) => void
  removePendingRequest: (key: string) => void
  isPending: (key: string) => boolean
}

// 表单状态类型
export interface FormState<
  T extends Record<string, any> = Record<string, any>,
> {
  formId: string
  data: Partial<T>
  originalData: Partial<T>
  isDirty: boolean
  isValid: boolean
  isSubmitting: boolean
  isValidating: boolean
  submitCount: number
  errors: Record<string, any>
  touchedFields: Record<string, boolean>
  lastSubmitTime?: Date
  lastValidationTime?: Date
  autoSaveEnabled: boolean
  autoSaveInterval?: number
  persistenceKey?: string
}

// 表单缓存项
export interface FormCacheItem<
  T extends Record<string, any> = Record<string, any>,
> {
  formId: string
  data: Partial<T>
  timestamp: Date
  expiresAt?: Date
  metadata?: Record<string, any>
}

// 表单提交历史
export interface FormSubmitHistory<
  T extends Record<string, any> = Record<string, any>,
> {
  formId: string
  data: T
  timestamp: Date
  success: boolean
  error?: string
  duration: number
}

// 表单管理状态类型
export interface FormManagementState {
  forms: Record<string, FormState>
  cache: Record<string, FormCacheItem>
  submitHistory: FormSubmitHistory[]
  globalSettings: {
    autoSaveEnabled: boolean
    autoSaveInterval: number
    cacheExpiration: number
    maxHistoryItems: number
    validationDebounce: number
  }
}

// 表单管理操作类型
export interface FormManagementActions {
  // 表单注册和注销
  registerForm: <T extends Record<string, any>>(
    formId: string,
    initialData?: Partial<T>,
    options?: FormOptions
  ) => void
  unregisterForm: (formId: string) => void

  // 表单数据操作
  updateFormData: <T extends Record<string, any>>(
    formId: string,
    data: Partial<T>
  ) => void
  setFormField: (formId: string, field: string, value: any) => void
  resetForm: (formId: string) => void

  // 表单状态操作
  setFormSubmitting: (formId: string, submitting: boolean) => void
  setFormValidating: (formId: string, validating: boolean) => void
  setFormErrors: (formId: string, errors: Record<string, any>) => void
  setFormTouched: (formId: string, field: string, touched: boolean) => void

  // 表单验证
  validateForm: (formId: string) => Promise<boolean>
  validateField: (formId: string, field: string) => Promise<boolean>

  // 表单提交
  submitForm: <T extends Record<string, any>>(
    formId: string,
    submitFn: (data: T) => Promise<any>
  ) => Promise<any>

  // 缓存操作
  cacheFormData: <T extends Record<string, any>>(
    formId: string,
    data: Partial<T>,
    expiration?: number
  ) => void
  getCachedFormData: (formId: string) => FormCacheItem | null
  clearFormCache: (formId?: string) => void

  // 历史记录
  addSubmitHistory: <T extends Record<string, any>>(
    history: Omit<FormSubmitHistory<T>, 'timestamp'>
  ) => void
  getSubmitHistory: (formId?: string) => FormSubmitHistory[]
  clearSubmitHistory: (formId?: string) => void

  // 全局设置
  updateGlobalSettings: (
    settings: Partial<FormManagementState['globalSettings']>
  ) => void

  // 工具方法
  getForm: (formId: string) => FormState | null
  isFormDirty: (formId: string) => boolean
  isFormValid: (formId: string) => boolean
  isFormSubmitting: (formId: string) => boolean
}

// 表单选项类型
export interface FormOptions {
  autoSave?: boolean
  autoSaveInterval?: number
  persistence?: boolean
  persistenceKey?: string
  validation?: {
    debounce?: number
    validateOnChange?: boolean
    validateOnBlur?: boolean
  }
}

// 组合的 Store 类型
export interface RootStore
  extends AuthState,
    AuthActions,
    AppState,
    AppActions,
    ApiState,
    ApiActions,
    FormManagementState,
    FormManagementActions {}
