import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { AuthState, AuthActions, User, UserRole } from './types'
// import { authClient } from '@/lib/auth-client'
// import { getUserPermissions, getUserRole } from '@/lib/auth-utils'

// 临时客户端版本
async function getUserPermissions(userId: string): Promise<string[]> {
  return [
    'consultation:read',
    'consultation:write',
    'follow-up:read',
    'follow-up:write',
    'statistics:read',
    'reservation:read',
    'reservation:write',
    'admission:read',
    'admission:write',
    'checkout:read',
    'checkout:write',
  ]
}

async function getUserRole(userId: string): Promise<any> {
  return {
    id: '1',
    name: 'admin',
    displayName: '管理员',
  }
}

// 认证状态的初始值
const initialAuthState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  permissions: [],
  role: null,
}

// 创建认证 Store
export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    immer((set, get) => ({
      ...initialAuthState,

      // 设置用户信息
      setUser: (user: User | null) => {
        set(state => {
          state.user = user
          state.isAuthenticated = !!user
          if (!user) {
            state.permissions = []
            state.role = null
          }
        })
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set(state => {
          state.isLoading = loading
        })
      },

      // 设置错误信息
      setError: (error: string | null) => {
        set(state => {
          state.error = error
        })
      },

      // 设置权限
      setPermissions: (permissions: string[]) => {
        set(state => {
          state.permissions = permissions
        })
      },

      // 设置角色
      setRole: (role: UserRole | null) => {
        set(state => {
          state.role = role
        })
      },

      // 登录
      login: async (email: string, password: string) => {
        const { setUser, setLoading, setError, refreshAuth } = get()

        try {
          setLoading(true)
          setError(null)

          // const result = await authClient.signIn.email({
          //   email,
          //   password,
          // })
          const result = {
            error: null,
            data: { user: { id: '1', email, name: 'Test User' } },
          } // 临时模拟

          if (result.error) {
            throw new Error(result.error.message)
          }

          if (result.data?.user) {
            setUser({
              id: result.data.user.id,
              email: result.data.user.email,
              name: result.data.user.name || '',
              avatar: result.data.user.image || undefined,
            })

            // 刷新权限和角色信息
            await refreshAuth()
          }
        } catch (error) {
          const message = error instanceof Error ? error.message : '登录失败'
          setError(message)
          throw error
        } finally {
          setLoading(false)
        }
      },

      // 登出
      logout: async () => {
        const { setUser, setLoading, setError } = get()

        try {
          setLoading(true)
          setError(null)

          // await authClient.signOut() // 临时注释
          setUser(null)
        } catch (error) {
          const message = error instanceof Error ? error.message : '登出失败'
          setError(message)
          throw error
        } finally {
          setLoading(false)
        }
      },

      // 刷新认证信息
      refreshAuth: async () => {
        const { user, setPermissions, setRole, setError } = get()

        if (!user?.id) return

        try {
          // 获取用户权限
          const permissions = await getUserPermissions(user.id)
          setPermissions(permissions)

          // 获取用户角色
          const role = await getUserRole(user.id)
          setRole(role)
        } catch (error) {
          console.error('刷新认证信息失败:', error)
          const message =
            error instanceof Error ? error.message : '刷新认证信息失败'
          setError(message)
        }
      },

      // 检查权限
      hasPermission: (permission: string) => {
        const { permissions } = get()
        return permissions.includes(permission)
      },

      // 检查是否有任一权限
      hasAnyPermission: (requiredPermissions: string[]) => {
        const { permissions } = get()
        return requiredPermissions.some(permission =>
          permissions.includes(permission)
        )
      },

      // 检查是否有所有权限
      hasAllPermissions: (requiredPermissions: string[]) => {
        const { permissions } = get()
        return requiredPermissions.every(permission =>
          permissions.includes(permission)
        )
      },

      // 检查角色
      hasRole: (roleName: string) => {
        const { role } = get()
        return role?.name === roleName
      },

      // 检查是否有任一角色
      hasAnyRole: (roleNames: string[]) => {
        const { role } = get()
        return role ? roleNames.includes(role.name) : false
      },
    })),
    {
      name: 'auth-store',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        permissions: state.permissions,
        role: state.role,
      }),
    }
  )
)

// 选择器函数
export const selectUser = (state: AuthState & AuthActions) => state.user
export const selectIsAuthenticated = (state: AuthState & AuthActions) =>
  state.isAuthenticated
export const selectPermissions = (state: AuthState & AuthActions) =>
  state.permissions
export const selectRole = (state: AuthState & AuthActions) => state.role
export const selectIsLoading = (state: AuthState & AuthActions) =>
  state.isLoading
export const selectError = (state: AuthState & AuthActions) => state.error
