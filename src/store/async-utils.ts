/**
 * 异步状态管理工具函数
 */

import { useApp, useNotifications } from './index'

/**
 * 异步操作包装器
 *
 * 自动处理加载状态、错误处理和成功通知
 */
export function useAsyncOperation() {
  const { setLoading, setError, clearError } = useApp()
  const { showSuccess, showError } = useNotifications()

  const execute = async <T>(
    operation: () => Promise<T>,
    options: {
      loadingKey: string
      successMessage?: string
      errorMessage?: string
      showSuccessNotification?: boolean
      showErrorNotification?: boolean
      onSuccess?: (result: T) => void
      onError?: (error: Error) => void
    }
  ): Promise<T | null> => {
    const {
      loadingKey,
      successMessage = '操作成功',
      errorMessage = '操作失败',
      showSuccessNotification = true,
      showErrorNotification = true,
      onSuccess,
      onError,
    } = options

    try {
      setLoading(loadingKey, true)
      clearError(loadingKey)

      const result = await operation()

      if (showSuccessNotification) {
        showSuccess(successMessage)
      }

      onSuccess?.(result)
      return result
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : errorMessage
      setError(loadingKey, errorMsg)

      if (showErrorNotification) {
        showError(errorMessage, errorMsg)
      }

      onError?.(error instanceof Error ? error : new Error(errorMsg))
      return null
    } finally {
      setLoading(loadingKey, false)
    }
  }

  return { execute }
}

/**
 * 创建异步状态管理 Hook
 */
export function createAsyncState<T>(initialData: T | null = null) {
  return function useAsyncState(key: string) {
    const { setLoading, setError, clearError } = useApp()
    const { showError } = useNotifications()

    const execute = async (
      operation: () => Promise<T>,
      options: {
        successMessage?: string
        errorMessage?: string
        showErrorNotification?: boolean
        onSuccess?: (result: T) => void
        onError?: (error: Error) => void
      } = {}
    ): Promise<T | null> => {
      const {
        errorMessage = '操作失败',
        showErrorNotification = true,
        onSuccess,
        onError,
      } = options

      try {
        setLoading(key, true)
        clearError(key)

        const result = await operation()
        onSuccess?.(result)
        return result
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : errorMessage
        setError(key, errorMsg)

        if (showErrorNotification) {
          showError(errorMessage, errorMsg)
        }

        onError?.(error instanceof Error ? error : new Error(errorMsg))
        return null
      } finally {
        setLoading(key, false)
      }
    }

    return { execute }
  }
}

/**
 * 重试机制
 */
export function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  return new Promise((resolve, reject) => {
    let attempts = 0

    const attempt = async () => {
      try {
        const result = await operation()
        resolve(result)
      } catch (error) {
        attempts++
        if (attempts >= maxRetries) {
          reject(error)
        } else {
          setTimeout(attempt, delay * attempts)
        }
      }
    }

    attempt()
  })
}

/**
 * 防抖异步操作
 */
export function debounceAsync<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  delay: number
): (...args: T) => Promise<R> {
  let timeoutId: NodeJS.Timeout | null = null
  let latestResolve: ((value: R) => void) | null = null
  let latestReject: ((reason: any) => void) | null = null

  return (...args: T): Promise<R> => {
    return new Promise((resolve, reject) => {
      // 取消之前的调用
      if (timeoutId) {
        clearTimeout(timeoutId)
        if (latestReject) {
          latestReject(new Error('Debounced'))
        }
      }

      latestResolve = resolve
      latestReject = reject

      timeoutId = setTimeout(async () => {
        try {
          const result = await fn(...args)
          if (latestResolve) {
            latestResolve(result)
          }
        } catch (error) {
          if (latestReject) {
            latestReject(error)
          }
        } finally {
          timeoutId = null
          latestResolve = null
          latestReject = null
        }
      }, delay)
    })
  }
}

/**
 * 节流异步操作
 */
export function throttleAsync<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  delay: number
): (...args: T) => Promise<R | null> {
  let lastExecution = 0
  let isExecuting = false

  return async (...args: T): Promise<R | null> => {
    const now = Date.now()

    if (isExecuting || now - lastExecution < delay) {
      return null
    }

    isExecuting = true
    lastExecution = now

    try {
      const result = await fn(...args)
      return result
    } finally {
      isExecuting = false
    }
  }
}
