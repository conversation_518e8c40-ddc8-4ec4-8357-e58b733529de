/**
 * 表单验证工具
 *
 * 提供常用的验证规则和 Zod schema 构建器
 */

import { z } from 'zod'

// 常用验证规则
export const ValidationRules = {
  // 必填字段
  required: (message = '此字段为必填项') => z.string().min(1, message),

  // 邮箱验证
  email: (message = '请输入有效的邮箱地址') => z.string().email(message),

  // 手机号验证
  phone: (message = '请输入有效的手机号码') =>
    z.string().regex(/^1[3-9]\d{9}$/, message),

  // 身份证号验证
  idCard: (message = '请输入有效的身份证号码') =>
    z.string().regex(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message),

  // 密码验证
  password: (minLength = 6, message = `密码长度至少${minLength}位`) =>
    z.string().min(minLength, message),

  // 强密码验证
  strongPassword: (
    message = '密码必须包含大小写字母、数字和特殊字符，长度至少8位'
  ) =>
    z
      .string()
      .min(8, '密码长度至少8位')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        message
      ),

  // 数字验证
  number: (message = '请输入有效的数字') =>
    z.number({ invalid_type_error: message }),

  // 正整数验证
  positiveInteger: (message = '请输入正整数') =>
    z.number().int().positive(message),

  // 非负数验证
  nonNegative: (message = '请输入非负数') => z.number().nonnegative(message),

  // 范围验证
  range: (
    min: number,
    max: number,
    message = `请输入${min}到${max}之间的数值`
  ) => z.number().min(min, message).max(max, message),

  // 日期验证
  date: (message = '请选择有效的日期') =>
    z.date({ invalid_type_error: message }),

  // 未来日期验证
  futureDate: (message = '请选择未来的日期') =>
    z.date().refine(date => date > new Date(), { message }),

  // 过去日期验证
  pastDate: (message = '请选择过去的日期') =>
    z.date().refine(date => date < new Date(), { message }),

  // URL验证
  url: (message = '请输入有效的URL地址') => z.string().url(message),

  // 文件大小验证
  fileSize: (maxSize: number, message = `文件大小不能超过${maxSize}MB`) =>
    z
      .instanceof(File)
      .refine(file => file.size <= maxSize * 1024 * 1024, { message }),

  // 文件类型验证
  fileType: (allowedTypes: string[], message = '文件类型不支持') =>
    z
      .instanceof(File)
      .refine(file => allowedTypes.includes(file.type), { message }),

  // 数组长度验证
  arrayLength: (min?: number, max?: number) => {
    let schema = z.array(z.any())
    if (min !== undefined) {
      schema = schema.min(min, `至少选择${min}项`)
    }
    if (max !== undefined) {
      schema = schema.max(max, `最多选择${max}项`)
    }
    return schema
  },

  // 字符串长度验证
  stringLength: (min?: number, max?: number) => {
    let schema = z.string()
    if (min !== undefined) {
      schema = schema.min(min, `长度至少${min}个字符`)
    }
    if (max !== undefined) {
      schema = schema.max(max, `长度不能超过${max}个字符`)
    }
    return schema
  },

  // 自定义正则验证
  regex: (pattern: RegExp, message = '格式不正确') =>
    z.string().regex(pattern, message),

  // 确认密码验证
  confirmPassword: (passwordField: string) =>
    z.string().refine(
      (value, ctx) => {
        const password = ctx.parent[passwordField]
        return value === password
      },
      { message: '两次输入的密码不一致' }
    ),
}

// 业务特定验证规则
export const BusinessValidationRules = {
  // 老人年龄验证（60-120岁）
  elderAge: (message = '老人年龄应在60-120岁之间') =>
    ValidationRules.range(60, 120, message),

  // 护理等级验证
  careLevel: (message = '请选择有效的护理等级') =>
    z.enum(['1', '2', '3', '4', '5'], { message }),

  // 房间号验证
  roomNumber: (message = '房间号格式不正确') =>
    ValidationRules.regex(/^[A-Z]\d{3}$/, message),

  // 床位号验证
  bedNumber: (message = '床位号格式不正确') =>
    ValidationRules.regex(/^[A-Z]\d{3}-[12]$/, message),

  // 医保卡号验证
  medicalCardNumber: (message = '请输入有效的医保卡号') =>
    ValidationRules.regex(/^\d{18}$/, message),

  // 紧急联系人关系验证
  emergencyRelationship: (message = '请选择紧急联系人关系') =>
    z.enum(['子女', '配偶', '兄弟姐妹', '其他亲属', '朋友', '其他'], {
      message,
    }),

  // 入住状态验证
  checkInStatus: (message = '请选择有效的入住状态') =>
    z.enum(['待入住', '已入住', '请假', '退住', '转院'], { message }),

  // 费用金额验证（非负数，最多2位小数）
  feeAmount: (message = '请输入有效的费用金额') =>
    z
      .number()
      .nonnegative(message)
      .refine(
        val =>
          Number.isFinite(val) && val.toString().split('.')[1]?.length <= 2,
        {
          message: '金额最多保留2位小数',
        }
      ),

  // 月份验证（1-12）
  month: (message = '请选择有效的月份') =>
    ValidationRules.range(1, 12, message),

  // 护理记录类型验证
  nursingRecordType: (message = '请选择护理记录类型') =>
    z.enum(['生活护理', '医疗护理', '康复护理', '心理护理', '其他'], {
      message,
    }),

  // 药品剂量验证
  medicationDosage: (message = '请输入有效的药品剂量') =>
    ValidationRules.regex(/^\d+(\.\d{1,2})?(mg|g|ml|片|粒|支)$/, message),

  // 血压验证
  bloodPressure: (message = '请输入有效的血压值') =>
    ValidationRules.regex(/^\d{2,3}\/\d{2,3}$/, message),

  // 体温验证（35-42度）
  bodyTemperature: (message = '体温应在35-42度之间') =>
    z.number().min(35, message).max(42, message),

  // 心率验证（40-200次/分）
  heartRate: (message = '心率应在40-200次/分之间') =>
    ValidationRules.range(40, 200, message),

  // 库存数量验证
  stockQuantity: (message = '请输入有效的库存数量') =>
    ValidationRules.positiveInteger(message),

  // 供应商编码验证
  supplierCode: (message = '供应商编码格式不正确') =>
    ValidationRules.regex(/^SUP\d{6}$/, message),

  // 设备编号验证
  deviceNumber: (message = '设备编号格式不正确') =>
    ValidationRules.regex(/^DEV\d{8}$/, message),
}

// 常用表单 Schema
export const CommonSchemas = {
  // 用户登录
  login: z.object({
    email: ValidationRules.email(),
    password: ValidationRules.required('请输入密码'),
    remember: z.boolean().optional(),
  }),

  // 用户注册
  register: z
    .object({
      name: ValidationRules.required('请输入姓名'),
      email: ValidationRules.email(),
      phone: ValidationRules.phone(),
      password: ValidationRules.strongPassword(),
      confirmPassword: z.string(),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: '两次输入的密码不一致',
      path: ['confirmPassword'],
    }),

  // 个人信息
  profile: z.object({
    name: ValidationRules.required('请输入姓名'),
    email: ValidationRules.email(),
    phone: ValidationRules.phone(),
    avatar: z.string().optional(),
    bio: ValidationRules.stringLength(0, 500).optional(),
  }),

  // 地址信息
  address: z.object({
    province: ValidationRules.required('请选择省份'),
    city: ValidationRules.required('请选择城市'),
    district: ValidationRules.required('请选择区县'),
    street: ValidationRules.required('请输入详细地址'),
    zipCode: ValidationRules.regex(
      /^\d{6}$/,
      '请输入有效的邮政编码'
    ).optional(),
  }),

  // 联系人信息
  contact: z.object({
    name: ValidationRules.required('请输入联系人姓名'),
    phone: ValidationRules.phone(),
    email: ValidationRules.email().optional(),
    relationship: ValidationRules.required('请选择关系'),
  }),
}

// 业务表单 Schema
export const BusinessSchemas = {
  // 老人基本信息
  elderInfo: z.object({
    name: ValidationRules.required('请输入老人姓名'),
    gender: z.enum(['男', '女'], { message: '请选择性别' }),
    age: BusinessValidationRules.elderAge(),
    idCard: ValidationRules.idCard(),
    phone: ValidationRules.phone().optional(),
    medicalCardNumber: BusinessValidationRules.medicalCardNumber().optional(),
    careLevel: BusinessValidationRules.careLevel(),
    checkInDate: ValidationRules.date('请选择入住日期'),
    checkInStatus: BusinessValidationRules.checkInStatus(),
    roomNumber: BusinessValidationRules.roomNumber().optional(),
    bedNumber: BusinessValidationRules.bedNumber().optional(),
    notes: ValidationRules.stringLength(0, 1000).optional(),
  }),

  // 紧急联系人信息
  emergencyContact: z.object({
    name: ValidationRules.required('请输入联系人姓名'),
    relationship: BusinessValidationRules.emergencyRelationship(),
    phone: ValidationRules.phone(),
    secondaryPhone: ValidationRules.phone().optional(),
    address: ValidationRules.required('请输入联系人地址'),
    isPrimary: z.boolean().default(false),
  }),

  // 健康信息
  healthInfo: z.object({
    bloodType: z
      .enum(['A', 'B', 'AB', 'O'], { message: '请选择血型' })
      .optional(),
    allergies: ValidationRules.stringLength(0, 500).optional(),
    medications: ValidationRules.stringLength(0, 500).optional(),
    medicalHistory: ValidationRules.stringLength(0, 1000).optional(),
    disabilities: ValidationRules.stringLength(0, 500).optional(),
    dietaryRestrictions: ValidationRules.stringLength(0, 500).optional(),
  }),

  // 护理记录
  nursingRecord: z.object({
    elderId: ValidationRules.required('请选择老人'),
    recordType: BusinessValidationRules.nursingRecordType(),
    content: ValidationRules.required('请输入护理内容'),
    nurseId: ValidationRules.required('请选择护理员'),
    recordTime: ValidationRules.date('请选择记录时间'),
    vitalSigns: z
      .object({
        bloodPressure: BusinessValidationRules.bloodPressure().optional(),
        bodyTemperature: BusinessValidationRules.bodyTemperature().optional(),
        heartRate: BusinessValidationRules.heartRate().optional(),
        weight: ValidationRules.nonNegative('体重不能为负数').optional(),
      })
      .optional(),
    notes: ValidationRules.stringLength(0, 500).optional(),
  }),

  // 房间信息
  roomInfo: z.object({
    roomNumber: BusinessValidationRules.roomNumber(),
    roomType: z.enum(['单人间', '双人间', '三人间', '四人间'], {
      message: '请选择房间类型',
    }),
    floor: ValidationRules.positiveInteger('请输入有效的楼层'),
    area: ValidationRules.nonNegative('面积不能为负数'),
    facilities: ValidationRules.arrayLength(0, 20).optional(),
    dailyRate: BusinessValidationRules.feeAmount(),
    monthlyRate: BusinessValidationRules.feeAmount(),
    status: z.enum(['可用', '已占用', '维修中', '停用'], {
      message: '请选择房间状态',
    }),
    notes: ValidationRules.stringLength(0, 500).optional(),
  }),

  // 费用账单
  feeBill: z.object({
    elderId: ValidationRules.required('请选择老人'),
    billMonth: BusinessValidationRules.month(),
    billYear: ValidationRules.range(2020, 2030, '请选择有效年份'),
    roomFee: BusinessValidationRules.feeAmount(),
    careFee: BusinessValidationRules.feeAmount(),
    mealFee: BusinessValidationRules.feeAmount(),
    medicalFee: BusinessValidationRules.feeAmount().optional(),
    otherFee: BusinessValidationRules.feeAmount().optional(),
    discount: BusinessValidationRules.feeAmount().optional(),
    totalAmount: BusinessValidationRules.feeAmount(),
    dueDate: ValidationRules.futureDate('缴费截止日期应为未来日期'),
    status: z.enum(['未缴费', '已缴费', '部分缴费', '逾期'], {
      message: '请选择缴费状态',
    }),
    notes: ValidationRules.stringLength(0, 500).optional(),
  }),

  // 库存物品
  inventoryItem: z.object({
    itemName: ValidationRules.required('请输入物品名称'),
    itemCode: ValidationRules.regex(/^ITEM\d{6}$/, '物品编码格式不正确'),
    category: ValidationRules.required('请选择物品分类'),
    specification: ValidationRules.stringLength(0, 200).optional(),
    unit: ValidationRules.required('请输入计量单位'),
    currentStock: BusinessValidationRules.stockQuantity(),
    minStock: BusinessValidationRules.stockQuantity(),
    maxStock: BusinessValidationRules.stockQuantity(),
    unitPrice: BusinessValidationRules.feeAmount(),
    supplierId: ValidationRules.required('请选择供应商'),
    storageLocation: ValidationRules.required('请输入存储位置'),
    expiryDate: ValidationRules.date('请选择过期日期').optional(),
    notes: ValidationRules.stringLength(0, 500).optional(),
  }),

  // 供应商信息
  supplierInfo: z.object({
    supplierCode: BusinessValidationRules.supplierCode(),
    supplierName: ValidationRules.required('请输入供应商名称'),
    contactPerson: ValidationRules.required('请输入联系人'),
    contactPhone: ValidationRules.phone(),
    contactEmail: ValidationRules.email().optional(),
    address: ValidationRules.required('请输入供应商地址'),
    businessLicense: ValidationRules.regex(
      /^\d{18}$/,
      '营业执照号格式不正确'
    ).optional(),
    taxNumber: ValidationRules.regex(
      /^[A-Z0-9]{15,20}$/,
      '税号格式不正确'
    ).optional(),
    bankAccount: ValidationRules.regex(
      /^\d{16,19}$/,
      '银行账号格式不正确'
    ).optional(),
    status: z.enum(['合作中', '暂停合作', '终止合作'], {
      message: '请选择合作状态',
    }),
    notes: ValidationRules.stringLength(0, 500).optional(),
  }),

  // 设备信息
  deviceInfo: z.object({
    deviceNumber: BusinessValidationRules.deviceNumber(),
    deviceName: ValidationRules.required('请输入设备名称'),
    deviceType: ValidationRules.required('请选择设备类型'),
    brand: ValidationRules.required('请输入设备品牌'),
    model: ValidationRules.required('请输入设备型号'),
    purchaseDate: ValidationRules.pastDate('采购日期应为过去日期'),
    warrantyPeriod: ValidationRules.positiveInteger('保修期应为正整数（月）'),
    location: ValidationRules.required('请输入设备位置'),
    status: z.enum(['正常', '故障', '维修中', '报废'], {
      message: '请选择设备状态',
    }),
    lastMaintenanceDate: ValidationRules.date('请选择最后维护日期').optional(),
    nextMaintenanceDate:
      ValidationRules.futureDate('下次维护日期应为未来日期').optional(),
    notes: ValidationRules.stringLength(0, 500).optional(),
  }),
}

// Schema 构建器
export class SchemaBuilder<T extends Record<string, any> = {}> {
  private schema: z.ZodObject<any>

  constructor(initialSchema?: z.ZodObject<any>) {
    this.schema = initialSchema || z.object({})
  }

  // 添加字段
  field<K extends string, V extends z.ZodTypeAny>(
    key: K,
    validation: V
  ): SchemaBuilder<T & Record<K, z.infer<V>>> {
    this.schema = this.schema.extend({ [key]: validation })
    return this as any
  }

  // 添加必填字段
  required<K extends string>(
    key: K,
    message?: string
  ): SchemaBuilder<T & Record<K, string>> {
    return this.field(key, ValidationRules.required(message))
  }

  // 添加可选字段
  optional<K extends string>(
    key: K,
    validation: z.ZodTypeAny
  ): SchemaBuilder<T & Record<K, z.infer<typeof validation> | undefined>> {
    return this.field(key, validation.optional())
  }

  // 添加邮箱字段
  email<K extends string>(
    key: K,
    message?: string
  ): SchemaBuilder<T & Record<K, string>> {
    return this.field(key, ValidationRules.email(message))
  }

  // 添加手机号字段
  phone<K extends string>(
    key: K,
    message?: string
  ): SchemaBuilder<T & Record<K, string>> {
    return this.field(key, ValidationRules.phone(message))
  }

  // 添加数字字段
  number<K extends string>(
    key: K,
    min?: number,
    max?: number
  ): SchemaBuilder<T & Record<K, number>> {
    let validation = ValidationRules.number()
    if (min !== undefined) validation = validation.min(min)
    if (max !== undefined) validation = validation.max(max)
    return this.field(key, validation)
  }

  // 添加日期字段
  date<K extends string>(
    key: K,
    message?: string
  ): SchemaBuilder<T & Record<K, Date>> {
    return this.field(key, ValidationRules.date(message))
  }

  // 添加数组字段
  array<K extends string, V extends z.ZodTypeAny>(
    key: K,
    itemValidation: V,
    min?: number,
    max?: number
  ): SchemaBuilder<T & Record<K, z.infer<V>[]>> {
    let validation = z.array(itemValidation)
    if (min !== undefined) validation = validation.min(min)
    if (max !== undefined) validation = validation.max(max)
    return this.field(key, validation)
  }

  // 添加枚举字段
  enum<K extends string, V extends readonly [string, ...string[]]>(
    key: K,
    values: V
  ): SchemaBuilder<T & Record<K, V[number]>> {
    return this.field(key, z.enum(values))
  }

  // 添加布尔字段
  boolean<K extends string>(key: K): SchemaBuilder<T & Record<K, boolean>> {
    return this.field(key, z.boolean())
  }

  // 添加自定义验证
  refine(
    check: (data: T) => boolean,
    message: string | { message: string; path?: string[] }
  ): SchemaBuilder<T> {
    this.schema = this.schema.refine(check, message)
    return this
  }

  // 构建最终 schema
  build(): z.ZodObject<any> {
    return this.schema
  }

  // 获取类型
  infer(): T {
    return {} as T
  }
}

// 创建 schema 构建器
export function createSchema<
  T extends Record<string, any> = {},
>(): SchemaBuilder<T> {
  return new SchemaBuilder<T>()
}

// 验证工具函数
export const ValidationUtils = {
  // 验证身份证号
  validateIdCard: (idCard: string): boolean => {
    return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)
  },

  // 验证手机号
  validatePhone: (phone: string): boolean => {
    return /^1[3-9]\d{9}$/.test(phone)
  },

  // 验证邮箱
  validateEmail: (email: string): boolean => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  },

  // 验证密码强度
  validatePasswordStrength: (
    password: string
  ): {
    score: number
    feedback: string[]
  } => {
    const feedback: string[] = []
    let score = 0

    if (password.length >= 8) {
      score += 1
    } else {
      feedback.push('密码长度至少8位')
    }

    if (/[a-z]/.test(password)) {
      score += 1
    } else {
      feedback.push('包含小写字母')
    }

    if (/[A-Z]/.test(password)) {
      score += 1
    } else {
      feedback.push('包含大写字母')
    }

    if (/\d/.test(password)) {
      score += 1
    } else {
      feedback.push('包含数字')
    }

    if (/[@$!%*?&]/.test(password)) {
      score += 1
    } else {
      feedback.push('包含特殊字符')
    }

    return { score, feedback }
  },

  // 格式化文件大小
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 检查文件类型
  checkFileType: (file: File, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(file.type)
  },

  // 检查文件大小
  checkFileSize: (file: File, maxSizeMB: number): boolean => {
    return file.size <= maxSizeMB * 1024 * 1024
  },
}
