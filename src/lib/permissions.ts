/**
 * 权限管理系统
 * 定义系统中所有的权限和角色
 */

// 权限模块定义
export const PERMISSION_MODULES = {
  USER: 'user',
  ROLE: 'role',
  ELDER: 'elder',
  CONSULTATION: 'consultation',
  ROOM: 'room',
  CARE: 'care',
  HEALTH: 'health',
  FINANCE: 'finance',
  INVENTORY: 'inventory',
  SYSTEM: 'system',
} as const

// 权限操作定义
export const PERMISSION_ACTIONS = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  EXPORT: 'export',
  IMPORT: 'import',
  APPROVE: 'approve',
  REJECT: 'reject',
} as const

// 生成权限名称的辅助函数
export function createPermission(module: string, action: string): string {
  return `${module}:${action}`
}

// 系统权限定义
export const PERMISSIONS = {
  // 用户管理权限
  USER_CREATE: createPermission(
    PERMISSION_MODULES.USER,
    PERMISSION_ACTIONS.CREATE
  ),
  USER_READ: createPermission(PERMISSION_MODULES.USER, PERMISSION_ACTIONS.READ),
  USER_UPDATE: createPermission(
    PERMISSION_MODULES.USER,
    PERMISSION_ACTIONS.UPDATE
  ),
  USER_DELETE: createPermission(
    PERMISSION_MODULES.USER,
    PERMISSION_ACTIONS.DELETE
  ),
  USER_EXPORT: createPermission(
    PERMISSION_MODULES.USER,
    PERMISSION_ACTIONS.EXPORT
  ),

  // 角色管理权限
  ROLE_CREATE: createPermission(
    PERMISSION_MODULES.ROLE,
    PERMISSION_ACTIONS.CREATE
  ),
  ROLE_READ: createPermission(PERMISSION_MODULES.ROLE, PERMISSION_ACTIONS.READ),
  ROLE_UPDATE: createPermission(
    PERMISSION_MODULES.ROLE,
    PERMISSION_ACTIONS.UPDATE
  ),
  ROLE_DELETE: createPermission(
    PERMISSION_MODULES.ROLE,
    PERMISSION_ACTIONS.DELETE
  ),

  // 老人信息管理权限
  ELDER_CREATE: createPermission(
    PERMISSION_MODULES.ELDER,
    PERMISSION_ACTIONS.CREATE
  ),
  ELDER_READ: createPermission(
    PERMISSION_MODULES.ELDER,
    PERMISSION_ACTIONS.READ
  ),
  ELDER_UPDATE: createPermission(
    PERMISSION_MODULES.ELDER,
    PERMISSION_ACTIONS.UPDATE
  ),
  ELDER_DELETE: createPermission(
    PERMISSION_MODULES.ELDER,
    PERMISSION_ACTIONS.DELETE
  ),
  ELDER_EXPORT: createPermission(
    PERMISSION_MODULES.ELDER,
    PERMISSION_ACTIONS.EXPORT
  ),

  // 咨询管理权限
  CONSULTATION_CREATE: createPermission(
    PERMISSION_MODULES.CONSULTATION,
    PERMISSION_ACTIONS.CREATE
  ),
  CONSULTATION_READ: createPermission(
    PERMISSION_MODULES.CONSULTATION,
    PERMISSION_ACTIONS.READ
  ),
  CONSULTATION_UPDATE: createPermission(
    PERMISSION_MODULES.CONSULTATION,
    PERMISSION_ACTIONS.UPDATE
  ),
  CONSULTATION_DELETE: createPermission(
    PERMISSION_MODULES.CONSULTATION,
    PERMISSION_ACTIONS.DELETE
  ),
  CONSULTATION_EXPORT: createPermission(
    PERMISSION_MODULES.CONSULTATION,
    PERMISSION_ACTIONS.EXPORT
  ),

  // 房间管理权限
  ROOM_CREATE: createPermission(
    PERMISSION_MODULES.ROOM,
    PERMISSION_ACTIONS.CREATE
  ),
  ROOM_READ: createPermission(PERMISSION_MODULES.ROOM, PERMISSION_ACTIONS.READ),
  ROOM_UPDATE: createPermission(
    PERMISSION_MODULES.ROOM,
    PERMISSION_ACTIONS.UPDATE
  ),
  ROOM_DELETE: createPermission(
    PERMISSION_MODULES.ROOM,
    PERMISSION_ACTIONS.DELETE
  ),

  // 护理管理权限
  CARE_CREATE: createPermission(
    PERMISSION_MODULES.CARE,
    PERMISSION_ACTIONS.CREATE
  ),
  CARE_READ: createPermission(PERMISSION_MODULES.CARE, PERMISSION_ACTIONS.READ),
  CARE_UPDATE: createPermission(
    PERMISSION_MODULES.CARE,
    PERMISSION_ACTIONS.UPDATE
  ),
  CARE_DELETE: createPermission(
    PERMISSION_MODULES.CARE,
    PERMISSION_ACTIONS.DELETE
  ),
  CARE_APPROVE: createPermission(
    PERMISSION_MODULES.CARE,
    PERMISSION_ACTIONS.APPROVE
  ),

  // 健康管理权限
  HEALTH_CREATE: createPermission(
    PERMISSION_MODULES.HEALTH,
    PERMISSION_ACTIONS.CREATE
  ),
  HEALTH_READ: createPermission(
    PERMISSION_MODULES.HEALTH,
    PERMISSION_ACTIONS.READ
  ),
  HEALTH_UPDATE: createPermission(
    PERMISSION_MODULES.HEALTH,
    PERMISSION_ACTIONS.UPDATE
  ),
  HEALTH_DELETE: createPermission(
    PERMISSION_MODULES.HEALTH,
    PERMISSION_ACTIONS.DELETE
  ),
  HEALTH_EXPORT: createPermission(
    PERMISSION_MODULES.HEALTH,
    PERMISSION_ACTIONS.EXPORT
  ),

  // 财务管理权限
  FINANCE_CREATE: createPermission(
    PERMISSION_MODULES.FINANCE,
    PERMISSION_ACTIONS.CREATE
  ),
  FINANCE_READ: createPermission(
    PERMISSION_MODULES.FINANCE,
    PERMISSION_ACTIONS.READ
  ),
  FINANCE_UPDATE: createPermission(
    PERMISSION_MODULES.FINANCE,
    PERMISSION_ACTIONS.UPDATE
  ),
  FINANCE_DELETE: createPermission(
    PERMISSION_MODULES.FINANCE,
    PERMISSION_ACTIONS.DELETE
  ),
  FINANCE_APPROVE: createPermission(
    PERMISSION_MODULES.FINANCE,
    PERMISSION_ACTIONS.APPROVE
  ),
  FINANCE_EXPORT: createPermission(
    PERMISSION_MODULES.FINANCE,
    PERMISSION_ACTIONS.EXPORT
  ),

  // 库存管理权限
  INVENTORY_CREATE: createPermission(
    PERMISSION_MODULES.INVENTORY,
    PERMISSION_ACTIONS.CREATE
  ),
  INVENTORY_READ: createPermission(
    PERMISSION_MODULES.INVENTORY,
    PERMISSION_ACTIONS.READ
  ),
  INVENTORY_UPDATE: createPermission(
    PERMISSION_MODULES.INVENTORY,
    PERMISSION_ACTIONS.UPDATE
  ),
  INVENTORY_DELETE: createPermission(
    PERMISSION_MODULES.INVENTORY,
    PERMISSION_ACTIONS.DELETE
  ),
  INVENTORY_APPROVE: createPermission(
    PERMISSION_MODULES.INVENTORY,
    PERMISSION_ACTIONS.APPROVE
  ),

  // 系统管理权限
  SYSTEM_CREATE: createPermission(
    PERMISSION_MODULES.SYSTEM,
    PERMISSION_ACTIONS.CREATE
  ),
  SYSTEM_READ: createPermission(
    PERMISSION_MODULES.SYSTEM,
    PERMISSION_ACTIONS.READ
  ),
  SYSTEM_UPDATE: createPermission(
    PERMISSION_MODULES.SYSTEM,
    PERMISSION_ACTIONS.UPDATE
  ),
  SYSTEM_DELETE: createPermission(
    PERMISSION_MODULES.SYSTEM,
    PERMISSION_ACTIONS.DELETE
  ),
} as const

// 角色定义
export const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  NURSE: 'nurse',
  DOCTOR: 'doctor',
  FINANCE: 'finance',
  RECEPTIONIST: 'receptionist',
} as const

// 角色权限映射
export const ROLE_PERMISSIONS: Record<string, string[]> = {
  [ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS), // 超级管理员拥有所有权限

  [ROLES.ADMIN]: [
    // 用户管理
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_EXPORT,
    // 角色管理
    PERMISSIONS.ROLE_READ,
    PERMISSIONS.ROLE_UPDATE,
    // 老人信息管理
    PERMISSIONS.ELDER_CREATE,
    PERMISSIONS.ELDER_READ,
    PERMISSIONS.ELDER_UPDATE,
    PERMISSIONS.ELDER_DELETE,
    PERMISSIONS.ELDER_EXPORT,
    // 咨询管理
    PERMISSIONS.CONSULTATION_CREATE,
    PERMISSIONS.CONSULTATION_READ,
    PERMISSIONS.CONSULTATION_UPDATE,
    PERMISSIONS.CONSULTATION_DELETE,
    PERMISSIONS.CONSULTATION_EXPORT,
    // 房间管理
    PERMISSIONS.ROOM_CREATE,
    PERMISSIONS.ROOM_READ,
    PERMISSIONS.ROOM_UPDATE,
    PERMISSIONS.ROOM_DELETE,
    // 护理管理
    PERMISSIONS.CARE_READ,
    PERMISSIONS.CARE_UPDATE,
    PERMISSIONS.CARE_APPROVE,
    // 健康管理
    PERMISSIONS.HEALTH_READ,
    PERMISSIONS.HEALTH_EXPORT,
    // 财务管理
    PERMISSIONS.FINANCE_READ,
    PERMISSIONS.FINANCE_APPROVE,
    PERMISSIONS.FINANCE_EXPORT,
    // 库存管理
    PERMISSIONS.INVENTORY_READ,
    PERMISSIONS.INVENTORY_APPROVE,
    // 系统管理
    PERMISSIONS.SYSTEM_READ,
    PERMISSIONS.SYSTEM_UPDATE,
  ],

  [ROLES.NURSE]: [
    // 老人信息管理
    PERMISSIONS.ELDER_READ,
    PERMISSIONS.ELDER_UPDATE,
    // 护理管理
    PERMISSIONS.CARE_CREATE,
    PERMISSIONS.CARE_READ,
    PERMISSIONS.CARE_UPDATE,
    // 健康管理
    PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.HEALTH_READ,
    PERMISSIONS.HEALTH_UPDATE,
    // 库存管理
    PERMISSIONS.INVENTORY_READ,
    PERMISSIONS.INVENTORY_UPDATE,
  ],

  [ROLES.DOCTOR]: [
    // 老人信息管理
    PERMISSIONS.ELDER_READ,
    PERMISSIONS.ELDER_UPDATE,
    // 护理管理
    PERMISSIONS.CARE_READ,
    PERMISSIONS.CARE_UPDATE,
    PERMISSIONS.CARE_APPROVE,
    // 健康管理
    PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.HEALTH_READ,
    PERMISSIONS.HEALTH_UPDATE,
    PERMISSIONS.HEALTH_DELETE,
    PERMISSIONS.HEALTH_EXPORT,
  ],

  [ROLES.FINANCE]: [
    // 老人信息管理
    PERMISSIONS.ELDER_READ,
    // 财务管理
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_READ,
    PERMISSIONS.FINANCE_UPDATE,
    PERMISSIONS.FINANCE_DELETE,
    PERMISSIONS.FINANCE_EXPORT,
  ],

  [ROLES.RECEPTIONIST]: [
    // 老人信息管理
    PERMISSIONS.ELDER_READ,
    // 咨询管理
    PERMISSIONS.CONSULTATION_CREATE,
    PERMISSIONS.CONSULTATION_READ,
    PERMISSIONS.CONSULTATION_UPDATE,
    PERMISSIONS.CONSULTATION_EXPORT,
    // 房间管理
    PERMISSIONS.ROOM_READ,
  ],
}

// 菜单权限映射
export interface MenuItem {
  key: string
  label: string
  icon?: string
  path?: string
  permissions?: string[]
  children?: MenuItem[]
}

export const MENU_ITEMS: MenuItem[] = [
  {
    key: 'dashboard',
    label: '首页概览',
    icon: 'Home',
    path: '/',
  },
  {
    key: 'marketing',
    label: '营销管理',
    icon: 'Users',
    permissions: [PERMISSIONS.CONSULTATION_READ],
    children: [
      {
        key: 'consultation',
        label: '咨询接待',
        path: '/marketing/consultation',
        permissions: [PERMISSIONS.CONSULTATION_READ],
      },
      {
        key: 'follow-up',
        label: '跟进记录',
        path: '/marketing/follow-up',
        permissions: [PERMISSIONS.CONSULTATION_READ],
      },
      {
        key: 'performance',
        label: '业绩统计',
        path: '/marketing/performance',
        permissions: [PERMISSIONS.CONSULTATION_EXPORT],
      },
    ],
  },
  {
    key: 'residence',
    label: '居住管理',
    icon: 'Building',
    permissions: [PERMISSIONS.ELDER_READ, PERMISSIONS.ROOM_READ],
    children: [
      {
        key: 'overview',
        label: '入住总览',
        path: '/residence/overview',
        permissions: [PERMISSIONS.ELDER_READ],
      },
      {
        key: 'reservation',
        label: '预订管理',
        path: '/residence/reservation',
        permissions: [PERMISSIONS.ROOM_READ],
      },
      {
        key: 'checkin',
        label: '入住流程',
        path: '/residence/checkin',
        permissions: [PERMISSIONS.ELDER_CREATE, PERMISSIONS.ELDER_UPDATE],
      },
      {
        key: 'checkout',
        label: '退住管理',
        path: '/residence/checkout',
        permissions: [PERMISSIONS.ELDER_UPDATE],
      },
    ],
  },
  {
    key: 'care',
    label: '护理服务',
    icon: 'Heart',
    permissions: [PERMISSIONS.CARE_READ],
    children: [
      {
        key: 'service-overview',
        label: '在住服务',
        path: '/care/service',
        permissions: [PERMISSIONS.CARE_READ],
      },
      {
        key: 'care-plan',
        label: '护理计划',
        path: '/care/plan',
        permissions: [PERMISSIONS.CARE_READ, PERMISSIONS.CARE_CREATE],
      },
      {
        key: 'care-record',
        label: '护理记录',
        path: '/care/record',
        permissions: [PERMISSIONS.CARE_READ, PERMISSIONS.CARE_CREATE],
      },
      {
        key: 'schedule',
        label: '护理排班',
        path: '/care/schedule',
        permissions: [PERMISSIONS.CARE_READ],
      },
      {
        key: 'dashboard',
        label: '照护看板',
        path: '/care/dashboard',
        permissions: [PERMISSIONS.CARE_READ],
      },
    ],
  },
  {
    key: 'finance',
    label: '财务管理',
    icon: 'DollarSign',
    permissions: [PERMISSIONS.FINANCE_READ],
    children: [
      {
        key: 'billing',
        label: '费用账单',
        path: '/finance/billing',
        permissions: [PERMISSIONS.FINANCE_READ],
      },
      {
        key: 'payment',
        label: '缴费管理',
        path: '/finance/payment',
        permissions: [PERMISSIONS.FINANCE_READ, PERMISSIONS.FINANCE_CREATE],
      },
      {
        key: 'report',
        label: '财务报表',
        path: '/finance/report',
        permissions: [PERMISSIONS.FINANCE_EXPORT],
      },
      {
        key: 'settings',
        label: '费用设置',
        path: '/finance/settings',
        permissions: [PERMISSIONS.FINANCE_UPDATE],
      },
    ],
  },
  {
    key: 'inventory',
    label: '库存管理',
    icon: 'Package',
    permissions: [PERMISSIONS.INVENTORY_READ],
    children: [
      {
        key: 'stock',
        label: '库存查询',
        path: '/inventory/stock',
        permissions: [PERMISSIONS.INVENTORY_READ],
      },
      {
        key: 'inbound',
        label: '入库管理',
        path: '/inventory/inbound',
        permissions: [PERMISSIONS.INVENTORY_CREATE],
      },
      {
        key: 'outbound',
        label: '出库管理',
        path: '/inventory/outbound',
        permissions: [PERMISSIONS.INVENTORY_CREATE],
      },
      {
        key: 'transfer',
        label: '物品调拨',
        path: '/inventory/transfer',
        permissions: [PERMISSIONS.INVENTORY_UPDATE],
      },
    ],
  },
  {
    key: 'system',
    label: '系统管理',
    icon: 'Settings',
    permissions: [PERMISSIONS.SYSTEM_READ],
    children: [
      {
        key: 'users',
        label: '用户管理',
        path: '/system/users',
        permissions: [PERMISSIONS.USER_READ],
      },
      {
        key: 'roles',
        label: '角色权限',
        path: '/system/roles',
        permissions: [PERMISSIONS.ROLE_READ],
      },
      {
        key: 'logs',
        label: '系统日志',
        path: '/system/logs',
        permissions: [PERMISSIONS.SYSTEM_READ],
      },
      {
        key: 'config',
        label: '系统配置',
        path: '/system/config',
        permissions: [PERMISSIONS.SYSTEM_UPDATE],
      },
    ],
  },
]

/**
 * 检查用户是否有访问菜单项的权限
 */
export function hasMenuPermission(
  menuItem: MenuItem,
  userPermissions: string[]
): boolean {
  // 如果菜单项没有权限要求，则允许访问
  if (!menuItem.permissions || menuItem.permissions.length === 0) {
    return true
  }

  // 检查用户是否有任一所需权限
  return menuItem.permissions.some(permission =>
    userPermissions.includes(permission)
  )
}

/**
 * 过滤用户可访问的菜单项
 */
export function filterMenuItems(
  menuItems: MenuItem[],
  userPermissions: string[]
): MenuItem[] {
  return menuItems
    .filter(item => hasMenuPermission(item, userPermissions))
    .map(item => ({
      ...item,
      children: item.children
        ? filterMenuItems(item.children, userPermissions)
        : undefined,
    }))
    .filter(item => !item.children || item.children.length > 0)
}
