import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 导出格式类型
export type ExportFormat = 'csv' | 'excel' | 'pdf' | 'json'

// 导出配置
export interface ExportConfig {
  format: ExportFormat
  filename?: string
  title?: string
  dateRange?: {
    start?: string
    end?: string
  }
  includeCharts?: boolean
}

// 导出结果
export interface ExportResult {
  success: boolean
  filename: string
  url?: string
  blob?: Blob
  error?: string
}

// 销售业绩导出数据
export interface SalesPerformanceExportData {
  overview: {
    totalConsultations: number
    signedConsultations: number
    totalFollowUps: number
    activeSalesCount: number
    conversionRate: number
  }
  ranking: Array<{
    userName: string
    userEmail: string
    consultationCount: number
    signedCount: number
    followUpCount: number
    conversionRate: string
  }>
  trend: Array<{
    date: string
    consultationCount: number
    signedCount: number
    conversionRate: string
  }>
}

// 渠道分析导出数据
export interface ChannelAnalysisExportData {
  analysis: Array<{
    channelName: string
    totalCount: number
    signedCount: number
    followingCount: number
    pendingCount: number
    abandonedCount: number
    conversionRate: string
    followUpRate: string
  }>
  comparison: Array<{
    channelName: string
    totalCount: number
    signedCount: number
    conversionRate: string
    percentage: string
  }>
}

/**
 * 统计数据导出器
 */
export class StatisticsExporter {
  /**
   * 导出销售业绩数据
   */
  static async exportSalesPerformance(
    data: SalesPerformanceExportData,
    config: ExportConfig
  ): Promise<ExportResult> {
    try {
      switch (config.format) {
        case 'csv':
          return await this.exportSalesPerformanceCSV(data, config)
        case 'json':
          return await this.exportSalesPerformanceJSON(data, config)
        case 'pdf':
          return await this.exportSalesPerformancePDF(data, config)
        default:
          throw new Error(`不支持的导出格式: ${config.format}`)
      }
    } catch (error) {
      return {
        success: false,
        filename: config.filename || 'sales_performance_export',
        error: error instanceof Error ? error.message : '导出失败',
      }
    }
  }

  /**
   * 导出渠道分析数据
   */
  static async exportChannelAnalysis(
    data: ChannelAnalysisExportData,
    config: ExportConfig
  ): Promise<ExportResult> {
    try {
      switch (config.format) {
        case 'csv':
          return await this.exportChannelAnalysisCSV(data, config)
        case 'json':
          return await this.exportChannelAnalysisJSON(data, config)
        case 'pdf':
          return await this.exportChannelAnalysisPDF(data, config)
        default:
          throw new Error(`不支持的导出格式: ${config.format}`)
      }
    } catch (error) {
      return {
        success: false,
        filename: config.filename || 'channel_analysis_export',
        error: error instanceof Error ? error.message : '导出失败',
      }
    }
  }

  /**
   * 导出销售业绩 CSV
   */
  private static async exportSalesPerformanceCSV(
    data: SalesPerformanceExportData,
    config: ExportConfig
  ): Promise<ExportResult> {
    let csvContent = '\uFEFF' // BOM for UTF-8

    // 添加标题
    if (config.title) {
      csvContent += `${config.title}\n\n`
    }

    // 添加日期范围
    if (config.dateRange?.start && config.dateRange?.end) {
      csvContent += `统计时间范围,${config.dateRange.start} 至 ${config.dateRange.end}\n\n`
    }

    // 业绩概览
    csvContent += '业绩概览\n'
    csvContent += '指标,数值\n'
    csvContent += `总咨询数,${data.overview.totalConsultations}\n`
    csvContent += `签约数量,${data.overview.signedConsultations}\n`
    csvContent += `跟进次数,${data.overview.totalFollowUps}\n`
    csvContent += `活跃销售员,${data.overview.activeSalesCount}\n`
    csvContent += `转化率,${data.overview.conversionRate}%\n\n`

    // 销售员排行榜
    csvContent += '销售员排行榜\n'
    csvContent += '排名,姓名,邮箱,咨询数量,签约数量,跟进次数,转化率\n'
    data.ranking.forEach((item, index) => {
      csvContent += `${index + 1},${item.userName},${item.userEmail},${item.consultationCount},${item.signedCount},${item.followUpCount},${item.conversionRate}%\n`
    })

    csvContent += '\n销售趋势\n'
    csvContent += '日期,咨询数量,签约数量,转化率\n'
    data.trend.forEach(item => {
      csvContent += `${item.date},${item.consultationCount},${item.signedCount},${item.conversionRate}%\n`
    })

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' })
    const filename = config.filename || `销售业绩报表_${format(new Date(), 'yyyyMMdd_HHmmss')}.csv`

    return {
      success: true,
      blob,
      url: URL.createObjectURL(blob),
      filename,
    }
  }

  /**
   * 导出渠道分析 CSV
   */
  private static async exportChannelAnalysisCSV(
    data: ChannelAnalysisExportData,
    config: ExportConfig
  ): Promise<ExportResult> {
    let csvContent = '\uFEFF' // BOM for UTF-8

    // 添加标题
    if (config.title) {
      csvContent += `${config.title}\n\n`
    }

    // 添加日期范围
    if (config.dateRange?.start && config.dateRange?.end) {
      csvContent += `统计时间范围,${config.dateRange.start} 至 ${config.dateRange.end}\n\n`
    }

    // 渠道分析详情
    csvContent += '渠道分析详情\n'
    csvContent += '渠道名称,总咨询数,签约数量,跟进中,待跟进,已放弃,转化率,跟进率\n'
    data.analysis.forEach(item => {
      csvContent += `${item.channelName},${item.totalCount},${item.signedCount},${item.followingCount},${item.pendingCount},${item.abandonedCount},${item.conversionRate}%,${item.followUpRate}%\n`
    })

    csvContent += '\n渠道对比\n'
    csvContent += '渠道名称,咨询数量,签约数量,转化率,占比\n'
    data.comparison.forEach(item => {
      csvContent += `${item.channelName},${item.totalCount},${item.signedCount},${item.conversionRate}%,${item.percentage}%\n`
    })

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' })
    const filename = config.filename || `渠道分析报表_${format(new Date(), 'yyyyMMdd_HHmmss')}.csv`

    return {
      success: true,
      blob,
      url: URL.createObjectURL(blob),
      filename,
    }
  }

  /**
   * 导出销售业绩 JSON
   */
  private static async exportSalesPerformanceJSON(
    data: SalesPerformanceExportData,
    config: ExportConfig
  ): Promise<ExportResult> {
    const exportData = {
      title: config.title || '销售业绩报表',
      exportTime: format(new Date(), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN }),
      dateRange: config.dateRange,
      data,
    }

    const jsonContent = JSON.stringify(exportData, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8' })
    const filename = config.filename || `销售业绩报表_${format(new Date(), 'yyyyMMdd_HHmmss')}.json`

    return {
      success: true,
      blob,
      url: URL.createObjectURL(blob),
      filename,
    }
  }

  /**
   * 导出渠道分析 JSON
   */
  private static async exportChannelAnalysisJSON(
    data: ChannelAnalysisExportData,
    config: ExportConfig
  ): Promise<ExportResult> {
    const exportData = {
      title: config.title || '渠道分析报表',
      exportTime: format(new Date(), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN }),
      dateRange: config.dateRange,
      data,
    }

    const jsonContent = JSON.stringify(exportData, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8' })
    const filename = config.filename || `渠道分析报表_${format(new Date(), 'yyyyMMdd_HHmmss')}.json`

    return {
      success: true,
      blob,
      url: URL.createObjectURL(blob),
      filename,
    }
  }

  /**
   * 导出销售业绩 PDF (简化版本)
   */
  private static async exportSalesPerformancePDF(
    data: SalesPerformanceExportData,
    config: ExportConfig
  ): Promise<ExportResult> {
    // 注意：这里是简化实现，实际项目中需要安装并使用 jsPDF 或其他 PDF 库
    throw new Error('PDF 导出功能需要安装 jsPDF 库')
  }

  /**
   * 导出渠道分析 PDF (简化版本)
   */
  private static async exportChannelAnalysisPDF(
    data: ChannelAnalysisExportData,
    config: ExportConfig
  ): Promise<ExportResult> {
    // 注意：这里是简化实现，实际项目中需要安装并使用 jsPDF 库
    throw new Error('PDF 导出功能需要安装 jsPDF 库')
  }

  /**
   * 下载文件
   */
  static downloadFile(result: ExportResult) {
    if (!result.success || !result.url) {
      throw new Error(result.error || '导出失败')
    }

    const link = document.createElement('a')
    link.href = result.url
    link.download = result.filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理 URL
    setTimeout(() => {
      URL.revokeObjectURL(result.url!)
    }, 1000)
  }
}
