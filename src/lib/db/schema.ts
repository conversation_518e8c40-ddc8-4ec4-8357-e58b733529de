import {
  pgTable,
  text,
  timestamp,
  integer,
  boolean,
  uuid,
  decimal,
  date,
  index,
} from 'drizzle-orm/pg-core'
import { relations } from 'drizzle-orm'

// ============ Better Auth 认证表 ============

// Better Auth 用户表
export const betterAuthUsers = pgTable(
  'user',
  {
    id: text('id').primaryKey(),
    email: text('email').notNull().unique(),
    emailVerified: boolean('emailVerified').notNull().default(false),
    name: text('name').notNull(),
    phone: text('phone'),
    roleId: uuid('roleId').references(() => roles.id),
    isActive: boolean('isActive').notNull().default(true),
    lastLoginAt: timestamp('lastLoginAt'),
    createdAt: timestamp('createdAt').notNull(),
    updatedAt: timestamp('updatedAt').notNull(),
    image: text('image'),
  },
  table => ({
    emailIdx: index('user_email_idx').on(table.email),
    roleIdx: index('user_role_idx').on(table.roleId),
  })
)

// Better Auth 会话表
export const betterAuthSessions = pgTable(
  'session',
  {
    id: text('id').primaryKey(),
    expiresAt: timestamp('expiresAt').notNull(),
    token: text('token').notNull().unique(),
    createdAt: timestamp('createdAt').notNull(),
    updatedAt: timestamp('updatedAt').notNull(),
    ipAddress: text('ipAddress'),
    userAgent: text('userAgent'),
    userId: text('userId')
      .notNull()
      .references(() => betterAuthUsers.id, { onDelete: 'cascade' }),
  },
  table => ({
    tokenIdx: index('session_token_idx').on(table.token),
    userIdx: index('session_user_idx').on(table.userId),
  })
)

// Better Auth 账户表
export const betterAuthAccounts = pgTable(
  'account',
  {
    id: text('id').primaryKey(),
    accountId: text('accountId').notNull(),
    providerId: text('providerId').notNull(),
    userId: text('userId')
      .notNull()
      .references(() => betterAuthUsers.id, { onDelete: 'cascade' }),
    accessToken: text('accessToken'),
    refreshToken: text('refreshToken'),
    idToken: text('idToken'),
    accessTokenExpiresAt: timestamp('accessTokenExpiresAt'),
    refreshTokenExpiresAt: timestamp('refreshTokenExpiresAt'),
    scope: text('scope'),
    password: text('password'),
    createdAt: timestamp('createdAt').notNull(),
    updatedAt: timestamp('updatedAt').notNull(),
  },
  table => ({
    userIdx: index('account_user_idx').on(table.userId),
    providerIdx: index('account_provider_idx').on(table.providerId),
  })
)

// Better Auth 验证表
export const betterAuthVerifications = pgTable(
  'verification',
  {
    id: text('id').primaryKey(),
    identifier: text('identifier').notNull(),
    value: text('value').notNull(),
    expiresAt: timestamp('expiresAt').notNull(),
    createdAt: timestamp('createdAt'),
    updatedAt: timestamp('updatedAt'),
  },
  table => ({
    identifierIdx: index('verification_identifier_idx').on(table.identifier),
  })
)

// ============ 系统管理表 ============

// 用户表
export const users = pgTable(
  'users',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    email: text('email').notNull().unique(),
    name: text('name').notNull(),
    phone: text('phone'),
    roleId: uuid('role_id').references(() => roles.id),
    isActive: boolean('is_active').notNull().default(true),
    lastLoginAt: timestamp('last_login_at'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    emailIdx: index('users_email_idx').on(table.email),
    roleIdx: index('users_role_idx').on(table.roleId),
  })
)

// 角色表
export const roles = pgTable('roles', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull().unique(),
  description: text('description'),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// 权限表
export const permissions = pgTable('permissions', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull().unique(),
  description: text('description'),
  module: text('module').notNull(),
  action: text('action').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
})

// 角色权限关联表
export const rolePermissions = pgTable(
  'role_permissions',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    roleId: uuid('role_id')
      .notNull()
      .references(() => roles.id, { onDelete: 'cascade' }),
    permissionId: uuid('permission_id')
      .notNull()
      .references(() => permissions.id, { onDelete: 'cascade' }),
    createdAt: timestamp('created_at').notNull().defaultNow(),
  },
  table => ({
    rolePermissionIdx: index('role_permissions_role_permission_idx').on(
      table.roleId,
      table.permissionId
    ),
  })
)

// ============ 老人信息管理表 ============

// 老人基本信息表
export const elderInfo = pgTable(
  'elder_info',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    age: integer('age').notNull(),
    gender: integer('gender').notNull(), // 1: 男, 2: 女
    idCard: text('id_card').unique(),
    phone: text('phone'),
    address: text('address'),
    emergencyContactName: text('emergency_contact_name'),
    emergencyContactPhone: text('emergency_contact_phone'),
    emergencyContactRelation: text('emergency_contact_relation'),
    medicalHistory: text('medical_history'),
    allergies: text('allergies'),
    medications: text('medications'),
    careLevel: integer('care_level').notNull().default(1), // 1: 自理, 2: 半自理, 3: 不能自理
    roomId: uuid('room_id').references(() => rooms.id),
    status: integer('status').notNull().default(1), // 1: 在院, 2: 请假, 3: 出院
    admissionDate: date('admission_date'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    nameIdx: index('elder_info_name_idx').on(table.name),
    idCardIdx: index('elder_info_id_card_idx').on(table.idCard),
    roomIdx: index('elder_info_room_idx').on(table.roomId),
    statusIdx: index('elder_info_status_idx').on(table.status),
  })
)
// ============ 咨询管理表 ============

// 咨询记录表
export const consultations = pgTable(
  'consultations',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    elderInfoId: uuid('elder_info_id').references(() => elderInfo.id),
    consultantName: text('consultant_name').notNull(),
    consultantPhone: text('consultant_phone').notNull(),
    consultantRelation: text('consultant_relation'),
    purpose: text('purpose').notNull(),
    expectedCheckInDate: date('expected_check_in_date'),
    mediaChannel: integer('media_channel').notNull(), // 1: 电话, 2: 微信, 3: 现场, 4: 其他
    status: integer('status').notNull().default(1), // 1: 待跟进, 2: 已跟进, 3: 已入住, 4: 已放弃
    notes: text('notes'),
    followUpDate: date('follow_up_date'),
    assignedUserId: text('assigned_user_id').references(
      () => betterAuthUsers.id
    ),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    statusIdx: index('consultations_status_idx').on(table.status),
    assignedUserIdx: index('consultations_assigned_user_idx').on(
      table.assignedUserId
    ),
    createdAtIdx: index('consultations_created_at_idx').on(table.createdAt),
  })
)

// 咨询跟进记录表
export const consultationFollowUps = pgTable(
  'consultation_follow_ups',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    consultationId: uuid('consultation_id')
      .notNull()
      .references(() => consultations.id, { onDelete: 'cascade' }),
    userId: text('user_id')
      .notNull()
      .references(() => betterAuthUsers.id),
    content: text('content').notNull(),
    nextFollowUpDate: date('next_follow_up_date'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
  },
  table => ({
    consultationIdx: index('consultation_follow_ups_consultation_idx').on(
      table.consultationId
    ),
    createdAtIdx: index('consultation_follow_ups_created_at_idx').on(
      table.createdAt
    ),
  })
)

// ============ 房间管理表 ============

// 房间表
export const rooms = pgTable(
  'rooms',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    roomNumber: text('room_number').notNull().unique(),
    roomType: integer('room_type').notNull(), // 1: 单人间, 2: 双人间, 3: 三人间, 4: 四人间
    floor: integer('floor').notNull(),
    capacity: integer('capacity').notNull(),
    currentOccupancy: integer('current_occupancy').notNull().default(0),
    monthlyRate: decimal('monthly_rate', { precision: 10, scale: 2 }),
    facilities: text('facilities'), // JSON string of facilities
    status: integer('status').notNull().default(1), // 1: 可用, 2: 维修中, 3: 停用
    description: text('description'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    roomNumberIdx: index('rooms_room_number_idx').on(table.roomNumber),
    statusIdx: index('rooms_status_idx').on(table.status),
    floorIdx: index('rooms_floor_idx').on(table.floor),
  })
)

// 预订记录表
export const reservations = pgTable(
  'reservations',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    elderInfoId: uuid('elder_info_id').references(() => elderInfo.id),
    roomId: uuid('room_id')
      .notNull()
      .references(() => rooms.id),
    reservationNumber: text('reservation_number').notNull().unique(),
    reservationDate: date('reservation_date').notNull(),
    expectedCheckInDate: date('expected_check_in_date').notNull(),
    expectedCheckOutDate: date('expected_check_out_date'),
    actualCheckInDate: date('actual_check_in_date'),
    actualCheckOutDate: date('actual_check_out_date'),
    status: integer('status').notNull().default(1), // 1: 待确认, 2: 已确认, 3: 已入住, 4: 已取消, 5: 已过期
    reservationType: integer('reservation_type').notNull().default(1), // 1: 长期入住, 2: 短期体验, 3: 临时住宿
    contactName: text('contact_name').notNull(),
    contactPhone: text('contact_phone').notNull(),
    contactRelation: text('contact_relation'),
    specialRequirements: text('special_requirements'),
    depositAmount: decimal('deposit_amount', { precision: 10, scale: 2 }),
    depositPaid: boolean('deposit_paid').notNull().default(false),
    notes: text('notes'),
    createdBy: text('created_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    elderInfoIdx: index('reservations_elder_info_idx').on(table.elderInfoId),
    roomIdx: index('reservations_room_idx').on(table.roomId),
    statusIdx: index('reservations_status_idx').on(table.status),
    reservationDateIdx: index('reservations_reservation_date_idx').on(
      table.reservationDate
    ),
    expectedCheckInIdx: index('reservations_expected_check_in_idx').on(
      table.expectedCheckInDate
    ),
    reservationNumberIdx: index('reservations_reservation_number_idx').on(
      table.reservationNumber
    ),
  })
)

// ============ 护理管理表 ============

// 护理计划表
export const carePlans = pgTable(
  'care_plans',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    elderInfoId: uuid('elder_info_id')
      .notNull()
      .references(() => elderInfo.id, { onDelete: 'cascade' }),
    planName: text('plan_name').notNull(),
    description: text('description'),
    careLevel: integer('care_level').notNull(),
    startDate: date('start_date').notNull(),
    endDate: date('end_date'),
    status: integer('status').notNull().default(1), // 1: 活跃, 2: 暂停, 3: 完成
    createdBy: text('created_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    elderIdx: index('care_plans_elder_idx').on(table.elderInfoId),
    statusIdx: index('care_plans_status_idx').on(table.status),
  })
)

// 护理记录表
export const careRecords = pgTable(
  'care_records',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    elderInfoId: uuid('elder_info_id')
      .notNull()
      .references(() => elderInfo.id, { onDelete: 'cascade' }),
    carePlanId: uuid('care_plan_id').references(() => carePlans.id),
    caregiverId: text('caregiver_id')
      .notNull()
      .references(() => betterAuthUsers.id),
    careType: integer('care_type').notNull(), // 1: 日常护理, 2: 医疗护理, 3: 康复护理, 4: 心理护理
    content: text('content').notNull(),
    duration: integer('duration'), // 护理时长(分钟)
    notes: text('notes'),
    recordDate: date('record_date').notNull(),
    recordTime: timestamp('record_time').notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
  },
  table => ({
    elderIdx: index('care_records_elder_idx').on(table.elderInfoId),
    caregiverIdx: index('care_records_caregiver_idx').on(table.caregiverId),
    recordDateIdx: index('care_records_record_date_idx').on(table.recordDate),
  })
)
// ============ 健康管理表 ============

// 健康档案表
export const healthRecords = pgTable(
  'health_records',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    elderInfoId: uuid('elder_info_id')
      .notNull()
      .references(() => elderInfo.id, { onDelete: 'cascade' }),
    recordType: integer('record_type').notNull(), // 1: 体检, 2: 就医, 3: 用药, 4: 生命体征
    title: text('title').notNull(),
    content: text('content').notNull(),
    doctorName: text('doctor_name'),
    hospital: text('hospital'),
    diagnosis: text('diagnosis'),
    treatment: text('treatment'),
    medications: text('medications'),
    recordDate: date('record_date').notNull(),
    attachments: text('attachments'), // JSON string of file paths
    createdBy: text('created_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    elderIdx: index('health_records_elder_idx').on(table.elderInfoId),
    recordTypeIdx: index('health_records_record_type_idx').on(table.recordType),
    recordDateIdx: index('health_records_record_date_idx').on(table.recordDate),
  })
)

// 生命体征记录表
export const vitalSigns = pgTable(
  'vital_signs',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    elderInfoId: uuid('elder_info_id')
      .notNull()
      .references(() => elderInfo.id, { onDelete: 'cascade' }),
    temperature: decimal('temperature', { precision: 4, scale: 1 }), // 体温
    bloodPressureSystolic: integer('blood_pressure_systolic'), // 收缩压
    bloodPressureDiastolic: integer('blood_pressure_diastolic'), // 舒张压
    heartRate: integer('heart_rate'), // 心率
    respiratoryRate: integer('respiratory_rate'), // 呼吸频率
    bloodSugar: decimal('blood_sugar', { precision: 5, scale: 2 }), // 血糖
    weight: decimal('weight', { precision: 5, scale: 2 }), // 体重
    height: decimal('height', { precision: 5, scale: 2 }), // 身高
    notes: text('notes'),
    measuredBy: text('measured_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    measuredAt: timestamp('measured_at').notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
  },
  table => ({
    elderIdx: index('vital_signs_elder_idx').on(table.elderInfoId),
    measuredAtIdx: index('vital_signs_measured_at_idx').on(table.measuredAt),
  })
)

// ============ 入住流程管理表 ============

// 入住申请表
export const admissionApplications = pgTable(
  'admission_applications',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    applicationNumber: text('application_number').notNull().unique(),
    elderInfoId: uuid('elder_info_id').references(() => elderInfo.id),
    reservationId: uuid('reservation_id').references(() => reservations.id),
    applicantName: text('applicant_name').notNull(),
    applicantPhone: text('applicant_phone').notNull(),
    applicantRelation: text('applicant_relation').notNull(),
    applicantIdCard: text('applicant_id_card'),
    elderName: text('elder_name').notNull(),
    elderAge: integer('elder_age').notNull(),
    elderGender: integer('elder_gender').notNull(), // 1: 男, 2: 女
    elderIdCard: text('elder_id_card'),
    elderPhone: text('elder_phone'),
    emergencyContactName: text('emergency_contact_name').notNull(),
    emergencyContactPhone: text('emergency_contact_phone').notNull(),
    emergencyContactRelation: text('emergency_contact_relation').notNull(),
    medicalHistory: text('medical_history'),
    currentMedications: text('current_medications'),
    allergies: text('allergies'),
    specialNeeds: text('special_needs'),
    preferredRoomType: integer('preferred_room_type'), // 1: 单人间, 2: 双人间, 3: 三人间, 4: 四人间
    expectedAdmissionDate: date('expected_admission_date').notNull(),
    status: integer('status').notNull().default(1), // 1: 待评估, 2: 评估中, 3: 待审核, 4: 审核中, 5: 已通过, 6: 已拒绝, 7: 已入住, 8: 已取消
    applicationDate: date('application_date').notNull(),
    notes: text('notes'),
    attachments: text('attachments'), // JSON string of file paths
    createdBy: text('created_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    applicationNumberIdx: index(
      'admission_applications_application_number_idx'
    ).on(table.applicationNumber),
    elderInfoIdx: index('admission_applications_elder_info_idx').on(
      table.elderInfoId
    ),
    statusIdx: index('admission_applications_status_idx').on(table.status),
    applicationDateIdx: index('admission_applications_application_date_idx').on(
      table.applicationDate
    ),
    createdByIdx: index('admission_applications_created_by_idx').on(
      table.createdBy
    ),
  })
)

// 入住评估记录表
export const admissionAssessments = pgTable(
  'admission_assessments',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    applicationId: uuid('application_id')
      .notNull()
      .references(() => admissionApplications.id, { onDelete: 'cascade' }),
    assessorId: text('assessor_id')
      .notNull()
      .references(() => betterAuthUsers.id),
    assessmentType: integer('assessment_type').notNull(), // 1: 健康评估, 2: 护理评估, 3: 心理评估, 4: 风险评估
    assessmentDate: date('assessment_date').notNull(),

    // 健康评估
    physicalCondition: integer('physical_condition'), // 1: 良好, 2: 一般, 3: 较差, 4: 很差
    mentalCondition: integer('mental_condition'), // 1: 清醒, 2: 轻度认知障碍, 3: 中度认知障碍, 4: 重度认知障碍
    mobilityLevel: integer('mobility_level'), // 1: 完全自理, 2: 部分自理, 3: 需要协助, 4: 完全依赖

    // 护理评估
    careLevel: integer('care_level'), // 1: 自理, 2: 半自理, 3: 不能自理, 4: 特护
    nursingNeeds: text('nursing_needs'), // JSON array of nursing requirements
    medicalSupport: text('medical_support'), // JSON array of medical support needs

    // 风险评估
    fallRisk: integer('fall_risk'), // 1: 低风险, 2: 中风险, 3: 高风险
    cognitiveRisk: integer('cognitive_risk'), // 1: 低风险, 2: 中风险, 3: 高风险
    behaviorRisk: integer('behavior_risk'), // 1: 低风险, 2: 中风险, 3: 高风险

    // 评估结果
    overallScore: integer('overall_score'),
    recommendedCareLevel: integer('recommended_care_level'),
    suitabilityAssessment: integer('suitability_assessment'), // 1: 适合, 2: 有条件适合, 3: 不适合
    recommendations: text('recommendations'),
    notes: text('notes'),
    attachments: text('attachments'), // JSON string of file paths

    status: integer('status').notNull().default(1), // 1: 进行中, 2: 已完成, 3: 需要重评
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    applicationIdx: index('admission_assessments_application_idx').on(
      table.applicationId
    ),
    assessorIdx: index('admission_assessments_assessor_idx').on(
      table.assessorId
    ),
    assessmentTypeIdx: index('admission_assessments_assessment_type_idx').on(
      table.assessmentType
    ),
    assessmentDateIdx: index('admission_assessments_assessment_date_idx').on(
      table.assessmentDate
    ),
    statusIdx: index('admission_assessments_status_idx').on(table.status),
  })
)

// 审核流程记录表
export const admissionReviews = pgTable(
  'admission_reviews',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    applicationId: uuid('application_id')
      .notNull()
      .references(() => admissionApplications.id, { onDelete: 'cascade' }),
    reviewLevel: integer('review_level').notNull(), // 1: 初审, 2: 复审, 3: 终审
    reviewerId: text('reviewer_id')
      .notNull()
      .references(() => betterAuthUsers.id),
    reviewDate: date('review_date').notNull(),
    reviewResult: integer('review_result').notNull(), // 1: 通过, 2: 拒绝, 3: 需要补充材料, 4: 转下级审核
    reviewComments: text('review_comments'),
    conditions: text('conditions'), // 审核条件或要求
    nextReviewerId: text('next_reviewer_id').references(
      () => betterAuthUsers.id
    ),
    attachments: text('attachments'), // JSON string of file paths
    createdAt: timestamp('created_at').notNull().defaultNow(),
  },
  table => ({
    applicationIdx: index('admission_reviews_application_idx').on(
      table.applicationId
    ),
    reviewLevelIdx: index('admission_reviews_review_level_idx').on(
      table.reviewLevel
    ),
    reviewerIdx: index('admission_reviews_reviewer_idx').on(table.reviewerId),
    reviewDateIdx: index('admission_reviews_review_date_idx').on(
      table.reviewDate
    ),
    reviewResultIdx: index('admission_reviews_review_result_idx').on(
      table.reviewResult
    ),
  })
)

// 合同管理表
export const admissionContracts = pgTable(
  'admission_contracts',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    applicationId: uuid('application_id')
      .notNull()
      .references(() => admissionApplications.id, { onDelete: 'cascade' }),
    contractNumber: text('contract_number').notNull().unique(),
    contractType: integer('contract_type').notNull(), // 1: 长期入住合同, 2: 短期体验合同, 3: 临时住宿合同
    templateId: uuid('template_id'), // 合同模板ID

    // 合同基本信息
    contractTitle: text('contract_title').notNull(),
    partyA: text('party_a').notNull(), // 甲方（养老院）
    partyB: text('party_b').notNull(), // 乙方（入住者或代理人）
    elderName: text('elder_name').notNull(),
    roomNumber: text('room_number'),

    // 合同条款
    serviceContent: text('service_content'), // 服务内容
    serviceStandard: text('service_standard'), // 服务标准
    feeStructure: text('fee_structure'), // 费用结构 JSON
    paymentTerms: text('payment_terms'), // 付款条款
    contractPeriod: text('contract_period'), // 合同期限
    terminationConditions: text('termination_conditions'), // 终止条件
    liabilityClause: text('liability_clause'), // 责任条款
    disputeResolution: text('dispute_resolution'), // 争议解决

    // 合同状态
    status: integer('status').notNull().default(1), // 1: 草稿, 2: 待签署, 3: 已签署, 4: 已生效, 5: 已终止, 6: 已作废
    signDate: date('sign_date'),
    effectiveDate: date('effective_date'),
    expiryDate: date('expiry_date'),

    // 签署信息
    partyASignature: text('party_a_signature'), // 甲方签名文件路径
    partyBSignature: text('party_b_signature'), // 乙方签名文件路径
    partyASignDate: timestamp('party_a_sign_date'),
    partyBSignDate: timestamp('party_b_sign_date'),
    witnessSignature: text('witness_signature'), // 见证人签名
    witnessName: text('witness_name'),

    // 文件管理
    contractFile: text('contract_file'), // 合同文件路径
    attachments: text('attachments'), // JSON string of attachment file paths

    notes: text('notes'),
    createdBy: text('created_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    applicationIdx: index('admission_contracts_application_idx').on(
      table.applicationId
    ),
    contractNumberIdx: index('admission_contracts_contract_number_idx').on(
      table.contractNumber
    ),
    statusIdx: index('admission_contracts_status_idx').on(table.status),
    signDateIdx: index('admission_contracts_sign_date_idx').on(table.signDate),
    effectiveDateIdx: index('admission_contracts_effective_date_idx').on(
      table.effectiveDate
    ),
  })
)

// 入住缴费记录表
export const admissionPayments = pgTable(
  'admission_payments',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    applicationId: uuid('application_id')
      .notNull()
      .references(() => admissionApplications.id, { onDelete: 'cascade' }),
    contractId: uuid('contract_id').references(() => admissionContracts.id),
    paymentNumber: text('payment_number').notNull().unique(),
    paymentType: integer('payment_type').notNull(), // 1: 押金, 2: 入住费, 3: 预缴费用, 4: 其他费用

    // 费用明细
    feeItems: text('fee_items'), // JSON array of fee items
    totalAmount: decimal('total_amount', { precision: 12, scale: 2 }).notNull(),
    discountAmount: decimal('discount_amount', {
      precision: 12,
      scale: 2,
    }).default('0'),
    actualAmount: decimal('actual_amount', {
      precision: 12,
      scale: 2,
    }).notNull(),

    // 支付信息
    paymentMethod: integer('payment_method').notNull(), // 1: 现金, 2: 银行转账, 3: 支付宝, 4: 微信, 5: 刷卡
    paymentStatus: integer('payment_status').notNull().default(1), // 1: 待支付, 2: 已支付, 3: 部分支付, 4: 已退款, 5: 支付失败
    paymentDate: date('payment_date'),
    transactionId: text('transaction_id'), // 交易流水号

    // 收据信息
    receiptNumber: text('receipt_number'),
    receiptFile: text('receipt_file'), // 收据文件路径

    notes: text('notes'),
    receivedBy: text('received_by').references(() => betterAuthUsers.id),
    createdBy: text('created_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    applicationIdx: index('admission_payments_application_idx').on(
      table.applicationId
    ),
    contractIdx: index('admission_payments_contract_idx').on(table.contractId),
    paymentNumberIdx: index('admission_payments_payment_number_idx').on(
      table.paymentNumber
    ),
    paymentTypeIdx: index('admission_payments_payment_type_idx').on(
      table.paymentType
    ),
    paymentStatusIdx: index('admission_payments_payment_status_idx').on(
      table.paymentStatus
    ),
    paymentDateIdx: index('admission_payments_payment_date_idx').on(
      table.paymentDate
    ),
  })
)

// 房间分配记录表
export const roomAssignments = pgTable(
  'room_assignments',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    applicationId: uuid('application_id')
      .notNull()
      .references(() => admissionApplications.id, { onDelete: 'cascade' }),
    roomId: uuid('room_id')
      .notNull()
      .references(() => rooms.id),
    bedNumber: text('bed_number'), // 床位号
    assignmentType: integer('assignment_type').notNull(), // 1: 临时分配, 2: 正式分配, 3: 调房
    assignmentDate: date('assignment_date').notNull(),
    effectiveDate: date('effective_date').notNull(),
    expiryDate: date('expiry_date'),

    // 分配原因和条件
    assignmentReason: text('assignment_reason'),
    specialRequirements: text('special_requirements'),
    roommates: text('roommates'), // JSON array of roommate info

    status: integer('status').notNull().default(1), // 1: 待确认, 2: 已确认, 3: 已入住, 4: 已调房, 5: 已退房
    confirmDate: date('confirm_date'),
    confirmBy: text('confirm_by').references(() => betterAuthUsers.id),

    notes: text('notes'),
    assignedBy: text('assigned_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    applicationIdx: index('room_assignments_application_idx').on(
      table.applicationId
    ),
    roomIdx: index('room_assignments_room_idx').on(table.roomId),
    assignmentDateIdx: index('room_assignments_assignment_date_idx').on(
      table.assignmentDate
    ),
    statusIdx: index('room_assignments_status_idx').on(table.status),
    effectiveDateIdx: index('room_assignments_effective_date_idx').on(
      table.effectiveDate
    ),
  })
)

// ============ 财务管理表 ============

// 费用项目表
export const feeItems = pgTable(
  'fee_items',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    category: integer('category').notNull(), // 1: 住宿费, 2: 护理费, 3: 餐费, 4: 医疗费, 5: 其他
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    unit: text('unit').notNull(), // 单位: 月, 天, 次
    description: text('description'),
    isActive: boolean('is_active').notNull().default(true),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    categoryIdx: index('fee_items_category_idx').on(table.category),
    isActiveIdx: index('fee_items_is_active_idx').on(table.isActive),
  })
)

// 账单表
export const bills = pgTable(
  'bills',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    elderInfoId: uuid('elder_info_id')
      .notNull()
      .references(() => elderInfo.id, { onDelete: 'cascade' }),
    billNumber: text('bill_number').notNull().unique(),
    billMonth: text('bill_month').notNull(), // YYYY-MM
    totalAmount: decimal('total_amount', { precision: 12, scale: 2 }).notNull(),
    paidAmount: decimal('paid_amount', { precision: 12, scale: 2 })
      .notNull()
      .default('0'),
    status: integer('status').notNull().default(1), // 1: 未支付, 2: 部分支付, 3: 已支付, 4: 已退款
    dueDate: date('due_date').notNull(),
    notes: text('notes'),
    createdBy: text('created_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    elderIdx: index('bills_elder_idx').on(table.elderInfoId),
    billNumberIdx: index('bills_bill_number_idx').on(table.billNumber),
    statusIdx: index('bills_status_idx').on(table.status),
    billMonthIdx: index('bills_bill_month_idx').on(table.billMonth),
  })
)

// 账单明细表
export const billItems = pgTable(
  'bill_items',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    billId: uuid('bill_id')
      .notNull()
      .references(() => bills.id, { onDelete: 'cascade' }),
    feeItemId: uuid('fee_item_id')
      .notNull()
      .references(() => feeItems.id),
    quantity: decimal('quantity', { precision: 10, scale: 2 }).notNull(),
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    amount: decimal('amount', { precision: 12, scale: 2 }).notNull(),
    description: text('description'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
  },
  table => ({
    billIdx: index('bill_items_bill_idx').on(table.billId),
  })
)

// 支付记录表
export const payments = pgTable(
  'payments',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    billId: uuid('bill_id')
      .notNull()
      .references(() => bills.id, { onDelete: 'cascade' }),
    paymentNumber: text('payment_number').notNull().unique(),
    amount: decimal('amount', { precision: 12, scale: 2 }).notNull(),
    paymentMethod: integer('payment_method').notNull(), // 1: 现金, 2: 银行转账, 3: 支付宝, 4: 微信, 5: 刷卡
    paymentDate: date('payment_date').notNull(),
    notes: text('notes'),
    receivedBy: text('received_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
  },
  table => ({
    billIdx: index('payments_bill_idx').on(table.billId),
    paymentNumberIdx: index('payments_payment_number_idx').on(
      table.paymentNumber
    ),
    paymentDateIdx: index('payments_payment_date_idx').on(table.paymentDate),
  })
)
// ============ 退住管理表 ============

// 退住申请表
export const checkoutApplications = pgTable(
  'checkout_applications',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    applicationNumber: text('application_number').notNull().unique(),
    elderInfoId: uuid('elder_info_id')
      .notNull()
      .references(() => elderInfo.id),
    roomAssignmentId: uuid('room_assignment_id').references(
      () => roomAssignments.id
    ),

    // 申请信息
    applicantName: text('applicant_name').notNull(),
    applicantPhone: text('applicant_phone').notNull(),
    applicantRelation: text('applicant_relation').notNull(),
    applicantIdCard: text('applicant_id_card'),

    // 退住信息
    checkoutReason: text('checkout_reason').notNull(), // 退住原因
    checkoutType: integer('checkout_type').notNull(), // 1: 正常退住, 2: 转院, 3: 死亡, 4: 其他
    expectedCheckoutDate: date('expected_checkout_date').notNull(),
    actualCheckoutDate: date('actual_checkout_date'),

    // 物品和文件
    personalBelongings: text('personal_belongings'), // JSON: 个人物品清单
    medicalRecords: text('medical_records'), // JSON: 医疗记录
    attachments: text('attachments'), // JSON: 附件文件

    // 状态和流程
    status: integer('status').notNull().default(1), // 1: 待审核, 2: 审核中, 3: 已通过, 4: 已拒绝, 5: 费用结算中, 6: 房间清理中, 7: 已完成
    applicationDate: date('application_date').notNull(),
    notes: text('notes'),

    // 审核信息
    reviewedBy: text('reviewed_by').references(() => betterAuthUsers.id),
    reviewedAt: timestamp('reviewed_at'),
    reviewNotes: text('review_notes'),

    createdBy: text('created_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    applicationNumberIdx: index(
      'checkout_applications_application_number_idx'
    ).on(table.applicationNumber),
    elderInfoIdx: index('checkout_applications_elder_info_idx').on(
      table.elderInfoId
    ),
    statusIdx: index('checkout_applications_status_idx').on(table.status),
    applicationDateIdx: index('checkout_applications_application_date_idx').on(
      table.applicationDate
    ),
    checkoutTypeIdx: index('checkout_applications_checkout_type_idx').on(
      table.checkoutType
    ),
  })
)

// 退住审核记录表
export const checkoutReviews = pgTable(
  'checkout_reviews',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    applicationId: uuid('application_id')
      .notNull()
      .references(() => checkoutApplications.id, { onDelete: 'cascade' }),

    // 审核信息
    reviewLevel: integer('review_level').notNull(), // 1: 初审, 2: 复审, 3: 终审
    reviewType: integer('review_type').notNull(), // 1: 护理部审核, 2: 医务部审核, 3: 财务部审核, 4: 院长审核
    reviewResult: integer('review_result').notNull(), // 1: 通过, 2: 拒绝, 3: 需要补充材料
    reviewNotes: text('review_notes'),

    // 审核要求和建议
    requirements: text('requirements'), // JSON: 审核要求
    suggestions: text('suggestions'), // 审核建议

    // 审核人员和时间
    reviewedBy: text('reviewed_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    reviewedAt: timestamp('reviewed_at').notNull().defaultNow(),

    // 下一步处理
    nextReviewLevel: integer('next_review_level'),
    nextReviewer: text('next_reviewer').references(() => betterAuthUsers.id),

    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    applicationIdx: index('checkout_reviews_application_idx').on(
      table.applicationId
    ),
    reviewLevelIdx: index('checkout_reviews_review_level_idx').on(
      table.reviewLevel
    ),
    reviewTypeIdx: index('checkout_reviews_review_type_idx').on(
      table.reviewType
    ),
    reviewResultIdx: index('checkout_reviews_review_result_idx').on(
      table.reviewResult
    ),
    reviewedByIdx: index('checkout_reviews_reviewed_by_idx').on(
      table.reviewedBy
    ),
  })
)

// 退住费用结算表
export const checkoutSettlements = pgTable(
  'checkout_settlements',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    applicationId: uuid('application_id')
      .notNull()
      .references(() => checkoutApplications.id, { onDelete: 'cascade' }),
    settlementNumber: text('settlement_number').notNull().unique(),

    // 费用计算
    totalCharges: decimal('total_charges', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 总费用
    totalPayments: decimal('total_payments', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 已付费用
    depositAmount: decimal('deposit_amount', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 押金金额
    refundableDeposit: decimal('refundable_deposit', {
      precision: 10,
      scale: 2,
    })
      .notNull()
      .default('0'), // 可退押金
    outstandingBalance: decimal('outstanding_balance', {
      precision: 10,
      scale: 2,
    })
      .notNull()
      .default('0'), // 欠费金额
    finalRefund: decimal('final_refund', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 最终退款

    // 费用明细
    accommodationFees: decimal('accommodation_fees', {
      precision: 10,
      scale: 2,
    })
      .notNull()
      .default('0'), // 住宿费
    careFees: decimal('care_fees', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 护理费
    mealFees: decimal('meal_fees', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 餐费
    medicalFees: decimal('medical_fees', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 医疗费
    otherFees: decimal('other_fees', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 其他费用

    // 扣除项目
    damageFees: decimal('damage_fees', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 损坏赔偿
    cleaningFees: decimal('cleaning_fees', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 清洁费
    penaltyFees: decimal('penalty_fees', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 违约金

    // 结算信息
    settlementDate: date('settlement_date').notNull(),
    settlementPeriodStart: date('settlement_period_start').notNull(),
    settlementPeriodEnd: date('settlement_period_end').notNull(),

    // 退款信息
    refundMethod: integer('refund_method'), // 1: 现金, 2: 银行转账, 3: 支付宝, 4: 微信
    refundAccount: text('refund_account'), // 退款账户
    refundDate: date('refund_date'),
    refundStatus: integer('refund_status').notNull().default(1), // 1: 待退款, 2: 已退款, 3: 退款失败

    // 结算状态
    status: integer('status').notNull().default(1), // 1: 计算中, 2: 待确认, 3: 已确认, 4: 已完成
    notes: text('notes'),

    // 操作人员
    calculatedBy: text('calculated_by')
      .notNull()
      .references(() => betterAuthUsers.id),
    confirmedBy: text('confirmed_by').references(() => betterAuthUsers.id),
    confirmedAt: timestamp('confirmed_at'),

    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    applicationIdx: index('checkout_settlements_application_idx').on(
      table.applicationId
    ),
    settlementNumberIdx: index('checkout_settlements_settlement_number_idx').on(
      table.settlementNumber
    ),
    settlementDateIdx: index('checkout_settlements_settlement_date_idx').on(
      table.settlementDate
    ),
    statusIdx: index('checkout_settlements_status_idx').on(table.status),
    refundStatusIdx: index('checkout_settlements_refund_status_idx').on(
      table.refundStatus
    ),
  })
)

// 房间清理记录表
export const roomCleaningRecords = pgTable(
  'room_cleaning_records',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    applicationId: uuid('application_id')
      .notNull()
      .references(() => checkoutApplications.id, { onDelete: 'cascade' }),
    roomId: uuid('room_id')
      .notNull()
      .references(() => rooms.id),
    cleaningNumber: text('cleaning_number').notNull().unique(),

    // 清理任务
    cleaningTasks: text('cleaning_tasks'), // JSON: 清理任务列表
    personalItemsInventory: text('personal_items_inventory'), // JSON: 个人物品清点
    facilityInspection: text('facility_inspection'), // JSON: 设施检查
    damageAssessment: text('damage_assessment'), // JSON: 损坏评估

    // 清理状态
    cleaningStatus: integer('cleaning_status').notNull().default(1), // 1: 待清理, 2: 清理中, 3: 清理完成, 4: 检查中, 5: 检查通过, 6: 需要维修

    // 时间记录
    startTime: timestamp('start_time'),
    endTime: timestamp('end_time'),
    inspectionTime: timestamp('inspection_time'),

    // 清理结果
    cleaningResult: text('cleaning_result'), // 清理结果描述
    foundItems: text('found_items'), // JSON: 发现的遗留物品
    maintenanceNeeded: boolean('maintenance_needed').notNull().default(false),
    maintenanceItems: text('maintenance_items'), // JSON: 需要维修的项目

    // 人员分配
    assignedTo: text('assigned_to')
      .notNull()
      .references(() => betterAuthUsers.id),
    inspectedBy: text('inspected_by').references(() => betterAuthUsers.id),

    // 备注
    notes: text('notes'),

    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    applicationIdx: index('room_cleaning_records_application_idx').on(
      table.applicationId
    ),
    roomIdx: index('room_cleaning_records_room_idx').on(table.roomId),
    cleaningNumberIdx: index('room_cleaning_records_cleaning_number_idx').on(
      table.cleaningNumber
    ),
    cleaningStatusIdx: index('room_cleaning_records_cleaning_status_idx').on(
      table.cleaningStatus
    ),
    assignedToIdx: index('room_cleaning_records_assigned_to_idx').on(
      table.assignedTo
    ),
  })
)

// ============ 系统配置表 ============

// 系统配置表
export const systemConfigs = pgTable(
  'system_configs',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    key: text('key').notNull().unique(),
    value: text('value').notNull(),
    description: text('description'),
    category: text('category').notNull(),
    isEditable: boolean('is_editable').notNull().default(true),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    keyIdx: index('system_configs_key_idx').on(table.key),
    categoryIdx: index('system_configs_category_idx').on(table.category),
  })
)

// 数据字典表
export const dictionaries = pgTable(
  'dictionaries',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    type: text('type').notNull(),
    code: text('code').notNull(),
    name: text('name').notNull(),
    value: text('value'),
    parentId: uuid('parent_id'),
    sortOrder: integer('sort_order').notNull().default(0),
    isActive: boolean('is_active').notNull().default(true),
    description: text('description'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  table => ({
    typeCodeIdx: index('dictionaries_type_code_idx').on(table.type, table.code),
    parentIdx: index('dictionaries_parent_idx').on(table.parentId),
    sortOrderIdx: index('dictionaries_sort_order_idx').on(table.sortOrder),
  })
)

// ============ 关系定义 ============

// Better Auth 用户关系
export const betterAuthUsersRelations = relations(
  betterAuthUsers,
  ({ one, many }) => ({
    role: one(roles, {
      fields: [betterAuthUsers.roleId],
      references: [roles.id],
    }),
    sessions: many(betterAuthSessions),
    accounts: many(betterAuthAccounts),
    consultations: many(consultations),
    consultationFollowUps: many(consultationFollowUps),
    carePlans: many(carePlans),
    careRecords: many(careRecords),
    healthRecords: many(healthRecords),
    vitalSigns: many(vitalSigns),
    bills: many(bills),
    payments: many(payments),
  })
)

// Better Auth 会话关系
export const betterAuthSessionsRelations = relations(
  betterAuthSessions,
  ({ one }) => ({
    user: one(betterAuthUsers, {
      fields: [betterAuthSessions.userId],
      references: [betterAuthUsers.id],
    }),
  })
)

// Better Auth 账户关系
export const betterAuthAccountsRelations = relations(
  betterAuthAccounts,
  ({ one }) => ({
    user: one(betterAuthUsers, {
      fields: [betterAuthAccounts.userId],
      references: [betterAuthUsers.id],
    }),
  })
)

// 用户关系 (保留原有的用户表关系)
export const usersRelations = relations(users, ({ one, many }) => ({
  role: one(roles, {
    fields: [users.roleId],
    references: [roles.id],
  }),
  consultations: many(consultations),
  consultationFollowUps: many(consultationFollowUps),
  carePlans: many(carePlans),
  careRecords: many(careRecords),
  healthRecords: many(healthRecords),
  vitalSigns: many(vitalSigns),
  bills: many(bills),
  payments: many(payments),
}))

// 角色关系
export const rolesRelations = relations(roles, ({ many }) => ({
  users: many(users),
  rolePermissions: many(rolePermissions),
}))

// 权限关系
export const permissionsRelations = relations(permissions, ({ many }) => ({
  rolePermissions: many(rolePermissions),
}))

// 角色权限关系
export const rolePermissionsRelations = relations(
  rolePermissions,
  ({ one }) => ({
    role: one(roles, {
      fields: [rolePermissions.roleId],
      references: [roles.id],
    }),
    permission: one(permissions, {
      fields: [rolePermissions.permissionId],
      references: [permissions.id],
    }),
  })
)

// 老人信息关系
export const elderInfoRelations = relations(elderInfo, ({ one, many }) => ({
  room: one(rooms, {
    fields: [elderInfo.roomId],
    references: [rooms.id],
  }),
  consultations: many(consultations),
  reservations: many(reservations),
  carePlans: many(carePlans),
  careRecords: many(careRecords),
  healthRecords: many(healthRecords),
  vitalSigns: many(vitalSigns),
  bills: many(bills),
}))

// 咨询关系
export const consultationsRelations = relations(
  consultations,
  ({ one, many }) => ({
    elderInfo: one(elderInfo, {
      fields: [consultations.elderInfoId],
      references: [elderInfo.id],
    }),
    assignedUser: one(betterAuthUsers, {
      fields: [consultations.assignedUserId],
      references: [betterAuthUsers.id],
    }),
    followUps: many(consultationFollowUps),
  })
)

// 咨询跟进关系
export const consultationFollowUpsRelations = relations(
  consultationFollowUps,
  ({ one }) => ({
    consultation: one(consultations, {
      fields: [consultationFollowUps.consultationId],
      references: [consultations.id],
    }),
    user: one(betterAuthUsers, {
      fields: [consultationFollowUps.userId],
      references: [betterAuthUsers.id],
    }),
  })
)

// 房间关系
export const roomsRelations = relations(rooms, ({ many }) => ({
  elderInfo: many(elderInfo),
  reservations: many(reservations),
}))

// 预订关系
export const reservationsRelations = relations(reservations, ({ one }) => ({
  elderInfo: one(elderInfo, {
    fields: [reservations.elderInfoId],
    references: [elderInfo.id],
  }),
  room: one(rooms, {
    fields: [reservations.roomId],
    references: [rooms.id],
  }),
  createdByUser: one(betterAuthUsers, {
    fields: [reservations.createdBy],
    references: [betterAuthUsers.id],
  }),
}))

// 护理计划关系
export const carePlansRelations = relations(carePlans, ({ one, many }) => ({
  elderInfo: one(elderInfo, {
    fields: [carePlans.elderInfoId],
    references: [elderInfo.id],
  }),
  createdByUser: one(betterAuthUsers, {
    fields: [carePlans.createdBy],
    references: [betterAuthUsers.id],
  }),
  careRecords: many(careRecords),
}))

// 护理记录关系
export const careRecordsRelations = relations(careRecords, ({ one }) => ({
  elderInfo: one(elderInfo, {
    fields: [careRecords.elderInfoId],
    references: [elderInfo.id],
  }),
  carePlan: one(carePlans, {
    fields: [careRecords.carePlanId],
    references: [carePlans.id],
  }),
  caregiver: one(betterAuthUsers, {
    fields: [careRecords.caregiverId],
    references: [betterAuthUsers.id],
  }),
}))
// 健康档案关系
export const healthRecordsRelations = relations(healthRecords, ({ one }) => ({
  elderInfo: one(elderInfo, {
    fields: [healthRecords.elderInfoId],
    references: [elderInfo.id],
  }),
  createdByUser: one(betterAuthUsers, {
    fields: [healthRecords.createdBy],
    references: [betterAuthUsers.id],
  }),
}))

// 生命体征关系
export const vitalSignsRelations = relations(vitalSigns, ({ one }) => ({
  elderInfo: one(elderInfo, {
    fields: [vitalSigns.elderInfoId],
    references: [elderInfo.id],
  }),
  measuredByUser: one(betterAuthUsers, {
    fields: [vitalSigns.measuredBy],
    references: [betterAuthUsers.id],
  }),
}))

// 费用项目关系
export const feeItemsRelations = relations(feeItems, ({ many }) => ({
  billItems: many(billItems),
}))

// 账单关系
export const billsRelations = relations(bills, ({ one, many }) => ({
  elderInfo: one(elderInfo, {
    fields: [bills.elderInfoId],
    references: [elderInfo.id],
  }),
  createdByUser: one(betterAuthUsers, {
    fields: [bills.createdBy],
    references: [betterAuthUsers.id],
  }),
  billItems: many(billItems),
  payments: many(payments),
}))

// 账单明细关系
export const billItemsRelations = relations(billItems, ({ one }) => ({
  bill: one(bills, {
    fields: [billItems.billId],
    references: [bills.id],
  }),
  feeItem: one(feeItems, {
    fields: [billItems.feeItemId],
    references: [feeItems.id],
  }),
}))

// 支付记录关系
export const paymentsRelations = relations(payments, ({ one }) => ({
  bill: one(bills, {
    fields: [payments.billId],
    references: [bills.id],
  }),
  receivedByUser: one(betterAuthUsers, {
    fields: [payments.receivedBy],
    references: [betterAuthUsers.id],
  }),
}))

// 数据字典关系
export const dictionariesRelations = relations(
  dictionaries,
  ({ one, many }) => ({
    parent: one(dictionaries, {
      fields: [dictionaries.parentId],
      references: [dictionaries.id],
    }),
    children: many(dictionaries),
  })
)

// ============ TypeScript 类型导出 ============

// Better Auth 类型
export type BetterAuthUser = typeof betterAuthUsers.$inferSelect
export type NewBetterAuthUser = typeof betterAuthUsers.$inferInsert
export type BetterAuthSession = typeof betterAuthSessions.$inferSelect
export type NewBetterAuthSession = typeof betterAuthSessions.$inferInsert
export type BetterAuthAccount = typeof betterAuthAccounts.$inferSelect
export type NewBetterAuthAccount = typeof betterAuthAccounts.$inferInsert
export type BetterAuthVerification = typeof betterAuthVerifications.$inferSelect
export type NewBetterAuthVerification =
  typeof betterAuthVerifications.$inferInsert

// 系统管理类型
export type User = typeof users.$inferSelect
export type NewUser = typeof users.$inferInsert
export type Role = typeof roles.$inferSelect
export type NewRole = typeof roles.$inferInsert
export type Permission = typeof permissions.$inferSelect
export type NewPermission = typeof permissions.$inferInsert
export type RolePermission = typeof rolePermissions.$inferSelect
export type NewRolePermission = typeof rolePermissions.$inferInsert

// 老人信息类型
export type ElderInfo = typeof elderInfo.$inferSelect
export type NewElderInfo = typeof elderInfo.$inferInsert

// 咨询管理类型
export type Consultation = typeof consultations.$inferSelect
export type NewConsultation = typeof consultations.$inferInsert
export type ConsultationFollowUp = typeof consultationFollowUps.$inferSelect
export type NewConsultationFollowUp = typeof consultationFollowUps.$inferInsert

// 房间管理类型
export type Room = typeof rooms.$inferSelect
export type NewRoom = typeof rooms.$inferInsert
export type Reservation = typeof reservations.$inferSelect
export type NewReservation = typeof reservations.$inferInsert

// 护理管理类型
export type CarePlan = typeof carePlans.$inferSelect
export type NewCarePlan = typeof carePlans.$inferInsert
export type CareRecord = typeof careRecords.$inferSelect
export type NewCareRecord = typeof careRecords.$inferInsert

// 健康管理类型
export type HealthRecord = typeof healthRecords.$inferSelect
export type NewHealthRecord = typeof healthRecords.$inferInsert
export type VitalSign = typeof vitalSigns.$inferSelect
export type NewVitalSign = typeof vitalSigns.$inferInsert

// 财务管理类型
export type FeeItem = typeof feeItems.$inferSelect
export type NewFeeItem = typeof feeItems.$inferInsert
export type Bill = typeof bills.$inferSelect
export type NewBill = typeof bills.$inferInsert
export type BillItem = typeof billItems.$inferSelect
export type NewBillItem = typeof billItems.$inferInsert
export type Payment = typeof payments.$inferSelect
export type NewPayment = typeof payments.$inferInsert

// 退住管理类型
export type CheckoutApplication = typeof checkoutApplications.$inferSelect
export type NewCheckoutApplication = typeof checkoutApplications.$inferInsert
export type CheckoutReview = typeof checkoutReviews.$inferSelect
export type NewCheckoutReview = typeof checkoutReviews.$inferInsert
export type CheckoutSettlement = typeof checkoutSettlements.$inferSelect
export type NewCheckoutSettlement = typeof checkoutSettlements.$inferInsert
export type RoomCleaningRecord = typeof roomCleaningRecords.$inferSelect
export type NewRoomCleaningRecord = typeof roomCleaningRecords.$inferInsert

// 系统配置类型
export type SystemConfig = typeof systemConfigs.$inferSelect
export type NewSystemConfig = typeof systemConfigs.$inferInsert
export type Dictionary = typeof dictionaries.$inferSelect
export type NewDictionary = typeof dictionaries.$inferInsert
