import { drizzle } from 'drizzle-orm/postgres-js'
const postgres = require('postgres')
import * as schema from './schema'

const connectionString =
  process.env.DATABASE_URL || 'postgresql://localhost:5432/temp'

// Disable prefetch as it is not supported for "Transaction" pool mode
const client = postgres(connectionString, { prepare: false })
export const db = drizzle(client, { schema })

export * from './schema'
