import { db } from './index'
import {
  roles,
  permissions,
  rolePermissions,
  users,
  dictionaries,
  systemConfigs,
  feeItems,
  rooms,
  elderInfo,
} from './schema'

// 系统角色数据
const systemRoles = [
  {
    name: 'super_admin',
    description: '超级管理员',
    isActive: true,
  },
  {
    name: 'admin',
    description: '管理员',
    isActive: true,
  },
  {
    name: 'nurse',
    description: '护理员',
    isActive: true,
  },
  {
    name: 'doctor',
    description: '医生',
    isActive: true,
  },
  {
    name: 'finance',
    description: '财务人员',
    isActive: true,
  },
  {
    name: 'receptionist',
    description: '前台接待',
    isActive: true,
  },
]

// 系统权限数据
const systemPermissions = [
  // 用户管理权限
  {
    name: 'user.view',
    description: '查看用户',
    module: 'user',
    action: 'view',
  },
  {
    name: 'user.create',
    description: '创建用户',
    module: 'user',
    action: 'create',
  },
  {
    name: 'user.update',
    description: '更新用户',
    module: 'user',
    action: 'update',
  },
  {
    name: 'user.delete',
    description: '删除用户',
    module: 'user',
    action: 'delete',
  },

  // 角色管理权限
  {
    name: 'role.view',
    description: '查看角色',
    module: 'role',
    action: 'view',
  },
  {
    name: 'role.create',
    description: '创建角色',
    module: 'role',
    action: 'create',
  },
  {
    name: 'role.update',
    description: '更新角色',
    module: 'role',
    action: 'update',
  },
  {
    name: 'role.delete',
    description: '删除角色',
    module: 'role',
    action: 'delete',
  },

  // 老人信息管理权限
  {
    name: 'elder.view',
    description: '查看老人信息',
    module: 'elder',
    action: 'view',
  },
  {
    name: 'elder.create',
    description: '创建老人信息',
    module: 'elder',
    action: 'create',
  },
  {
    name: 'elder.update',
    description: '更新老人信息',
    module: 'elder',
    action: 'update',
  },
  {
    name: 'elder.delete',
    description: '删除老人信息',
    module: 'elder',
    action: 'delete',
  },

  // 咨询管理权限
  {
    name: 'consultation.view',
    description: '查看咨询记录',
    module: 'consultation',
    action: 'view',
  },
  {
    name: 'consultation.create',
    description: '创建咨询记录',
    module: 'consultation',
    action: 'create',
  },
  {
    name: 'consultation.update',
    description: '更新咨询记录',
    module: 'consultation',
    action: 'update',
  },
  {
    name: 'consultation.delete',
    description: '删除咨询记录',
    module: 'consultation',
    action: 'delete',
  },

  // 房间管理权限
  {
    name: 'room.view',
    description: '查看房间信息',
    module: 'room',
    action: 'view',
  },
  {
    name: 'room.create',
    description: '创建房间信息',
    module: 'room',
    action: 'create',
  },
  {
    name: 'room.update',
    description: '更新房间信息',
    module: 'room',
    action: 'update',
  },
  {
    name: 'room.delete',
    description: '删除房间信息',
    module: 'room',
    action: 'delete',
  },

  // 护理管理权限
  {
    name: 'care.view',
    description: '查看护理记录',
    module: 'care',
    action: 'view',
  },
  {
    name: 'care.create',
    description: '创建护理记录',
    module: 'care',
    action: 'create',
  },
  {
    name: 'care.update',
    description: '更新护理记录',
    module: 'care',
    action: 'update',
  },
  {
    name: 'care.delete',
    description: '删除护理记录',
    module: 'care',
    action: 'delete',
  },

  // 健康管理权限
  {
    name: 'health.view',
    description: '查看健康记录',
    module: 'health',
    action: 'view',
  },
  {
    name: 'health.create',
    description: '创建健康记录',
    module: 'health',
    action: 'create',
  },
  {
    name: 'health.update',
    description: '更新健康记录',
    module: 'health',
    action: 'update',
  },
  {
    name: 'health.delete',
    description: '删除健康记录',
    module: 'health',
    action: 'delete',
  },

  // 财务管理权限
  {
    name: 'finance.view',
    description: '查看财务记录',
    module: 'finance',
    action: 'view',
  },
  {
    name: 'finance.create',
    description: '创建财务记录',
    module: 'finance',
    action: 'create',
  },
  {
    name: 'finance.update',
    description: '更新财务记录',
    module: 'finance',
    action: 'update',
  },
  {
    name: 'finance.delete',
    description: '删除财务记录',
    module: 'finance',
    action: 'delete',
  },

  // 系统配置权限
  {
    name: 'system.view',
    description: '查看系统配置',
    module: 'system',
    action: 'view',
  },
  {
    name: 'system.update',
    description: '更新系统配置',
    module: 'system',
    action: 'update',
  },
]

// 数据字典数据
const dictionaryData = [
  // 性别字典
  { type: 'gender', code: '1', name: '男', value: '1', sortOrder: 1 },
  { type: 'gender', code: '2', name: '女', value: '2', sortOrder: 2 },

  // 护理等级字典
  { type: 'care_level', code: '1', name: '自理', value: '1', sortOrder: 1 },
  { type: 'care_level', code: '2', name: '半自理', value: '2', sortOrder: 2 },
  { type: 'care_level', code: '3', name: '不能自理', value: '3', sortOrder: 3 },

  // 老人状态字典
  { type: 'elder_status', code: '1', name: '在院', value: '1', sortOrder: 1 },
  { type: 'elder_status', code: '2', name: '请假', value: '2', sortOrder: 2 },
  { type: 'elder_status', code: '3', name: '出院', value: '3', sortOrder: 3 },

  // 咨询渠道字典
  { type: 'media_channel', code: '1', name: '电话', value: '1', sortOrder: 1 },
  { type: 'media_channel', code: '2', name: '微信', value: '2', sortOrder: 2 },
  { type: 'media_channel', code: '3', name: '现场', value: '3', sortOrder: 3 },
  { type: 'media_channel', code: '4', name: '其他', value: '4', sortOrder: 4 },

  // 咨询状态字典
  {
    type: 'consultation_status',
    code: '1',
    name: '待跟进',
    value: '1',
    sortOrder: 1,
  },
  {
    type: 'consultation_status',
    code: '2',
    name: '已跟进',
    value: '2',
    sortOrder: 2,
  },
  {
    type: 'consultation_status',
    code: '3',
    name: '已入住',
    value: '3',
    sortOrder: 3,
  },
  {
    type: 'consultation_status',
    code: '4',
    name: '已放弃',
    value: '4',
    sortOrder: 4,
  },

  // 房间类型字典
  { type: 'room_type', code: '1', name: '单人间', value: '1', sortOrder: 1 },
  { type: 'room_type', code: '2', name: '双人间', value: '2', sortOrder: 2 },
  { type: 'room_type', code: '3', name: '三人间', value: '3', sortOrder: 3 },
  { type: 'room_type', code: '4', name: '四人间', value: '4', sortOrder: 4 },

  // 房间状态字典
  { type: 'room_status', code: '1', name: '可用', value: '1', sortOrder: 1 },
  { type: 'room_status', code: '2', name: '维修中', value: '2', sortOrder: 2 },
  { type: 'room_status', code: '3', name: '停用', value: '3', sortOrder: 3 },

  // 护理类型字典
  { type: 'care_type', code: '1', name: '日常护理', value: '1', sortOrder: 1 },
  { type: 'care_type', code: '2', name: '医疗护理', value: '2', sortOrder: 2 },
  { type: 'care_type', code: '3', name: '康复护理', value: '3', sortOrder: 3 },
  { type: 'care_type', code: '4', name: '心理护理', value: '4', sortOrder: 4 },

  // 健康记录类型字典
  {
    type: 'health_record_type',
    code: '1',
    name: '体检',
    value: '1',
    sortOrder: 1,
  },
  {
    type: 'health_record_type',
    code: '2',
    name: '就医',
    value: '2',
    sortOrder: 2,
  },
  {
    type: 'health_record_type',
    code: '3',
    name: '用药',
    value: '3',
    sortOrder: 3,
  },
  {
    type: 'health_record_type',
    code: '4',
    name: '生命体征',
    value: '4',
    sortOrder: 4,
  },

  // 费用类别字典
  { type: 'fee_category', code: '1', name: '住宿费', value: '1', sortOrder: 1 },
  { type: 'fee_category', code: '2', name: '护理费', value: '2', sortOrder: 2 },
  { type: 'fee_category', code: '3', name: '餐费', value: '3', sortOrder: 3 },
  { type: 'fee_category', code: '4', name: '医疗费', value: '4', sortOrder: 4 },
  { type: 'fee_category', code: '5', name: '其他', value: '5', sortOrder: 5 },

  // 账单状态字典
  { type: 'bill_status', code: '1', name: '未支付', value: '1', sortOrder: 1 },
  {
    type: 'bill_status',
    code: '2',
    name: '部分支付',
    value: '2',
    sortOrder: 2,
  },
  { type: 'bill_status', code: '3', name: '已支付', value: '3', sortOrder: 3 },
  { type: 'bill_status', code: '4', name: '已退款', value: '4', sortOrder: 4 },

  // 支付方式字典
  { type: 'payment_method', code: '1', name: '现金', value: '1', sortOrder: 1 },
  {
    type: 'payment_method',
    code: '2',
    name: '银行转账',
    value: '2',
    sortOrder: 2,
  },
  {
    type: 'payment_method',
    code: '3',
    name: '支付宝',
    value: '3',
    sortOrder: 3,
  },
  { type: 'payment_method', code: '4', name: '微信', value: '4', sortOrder: 4 },
  { type: 'payment_method', code: '5', name: '刷卡', value: '5', sortOrder: 5 },
]
// 系统配置数据
const systemConfigData = [
  {
    key: 'system.name',
    value: '养老院管理系统',
    description: '系统名称',
    category: 'system',
  },
  {
    key: 'system.version',
    value: '1.0.0',
    description: '系统版本',
    category: 'system',
  },
  {
    key: 'system.company',
    value: '养老院',
    description: '机构名称',
    category: 'system',
  },
  {
    key: 'system.address',
    value: '北京市朝阳区',
    description: '机构地址',
    category: 'system',
  },
  {
    key: 'system.phone',
    value: '010-12345678',
    description: '联系电话',
    category: 'system',
  },
  {
    key: 'system.email',
    value: '<EMAIL>',
    description: '联系邮箱',
    category: 'system',
  },

  // 业务配置
  {
    key: 'business.check_in_time',
    value: '14:00',
    description: '入住时间',
    category: 'business',
  },
  {
    key: 'business.check_out_time',
    value: '12:00',
    description: '退房时间',
    category: 'business',
  },
  {
    key: 'business.bill_due_days',
    value: '30',
    description: '账单到期天数',
    category: 'business',
  },
  {
    key: 'business.follow_up_days',
    value: '7',
    description: '咨询跟进天数',
    category: 'business',
  },

  // 通知配置
  {
    key: 'notification.email_enabled',
    value: 'true',
    description: '邮件通知开关',
    category: 'notification',
  },
  {
    key: 'notification.sms_enabled',
    value: 'true',
    description: '短信通知开关',
    category: 'notification',
  },
  {
    key: 'notification.bill_reminder_days',
    value: '3',
    description: '账单提醒天数',
    category: 'notification',
  },
]

// 基础费用项目数据
const baseFeeItems = [
  {
    name: '单人间住宿费',
    category: 1,
    unitPrice: '3000.00',
    unit: '月',
    description: '单人间月租费用',
  },
  {
    name: '双人间住宿费',
    category: 1,
    unitPrice: '2000.00',
    unit: '月',
    description: '双人间月租费用',
  },
  {
    name: '三人间住宿费',
    category: 1,
    unitPrice: '1500.00',
    unit: '月',
    description: '三人间月租费用',
  },
  {
    name: '四人间住宿费',
    category: 1,
    unitPrice: '1200.00',
    unit: '月',
    description: '四人间月租费用',
  },

  {
    name: '一级护理费',
    category: 2,
    unitPrice: '800.00',
    unit: '月',
    description: '自理老人护理费',
  },
  {
    name: '二级护理费',
    category: 2,
    unitPrice: '1200.00',
    unit: '月',
    description: '半自理老人护理费',
  },
  {
    name: '三级护理费',
    category: 2,
    unitPrice: '1800.00',
    unit: '月',
    description: '不能自理老人护理费',
  },

  {
    name: '标准餐费',
    category: 3,
    unitPrice: '600.00',
    unit: '月',
    description: '标准三餐费用',
  },
  {
    name: '营养餐费',
    category: 3,
    unitPrice: '800.00',
    unit: '月',
    description: '营养餐费用',
  },

  {
    name: '体检费',
    category: 4,
    unitPrice: '200.00',
    unit: '次',
    description: '常规体检费用',
  },
  {
    name: '药品费',
    category: 4,
    unitPrice: '0.00',
    unit: '次',
    description: '药品费用（按实际计算）',
  },

  {
    name: '洗衣费',
    category: 5,
    unitPrice: '100.00',
    unit: '月',
    description: '洗衣服务费',
  },
  {
    name: '理发费',
    category: 5,
    unitPrice: '30.00',
    unit: '次',
    description: '理发服务费',
  },
]

// 示例房间数据
const sampleRooms = [
  // 一楼房间
  {
    roomNumber: '101',
    roomType: 1,
    floor: 1,
    capacity: 1,
    monthlyRate: '3000.00',
    facilities: '["独立卫生间", "空调", "电视", "衣柜"]',
    description: '一楼单人间',
  },
  {
    roomNumber: '102',
    roomType: 2,
    floor: 1,
    capacity: 2,
    monthlyRate: '2000.00',
    facilities: '["独立卫生间", "空调", "电视", "衣柜"]',
    description: '一楼双人间',
  },
  {
    roomNumber: '103',
    roomType: 2,
    floor: 1,
    capacity: 2,
    monthlyRate: '2000.00',
    facilities: '["独立卫生间", "空调", "电视", "衣柜"]',
    description: '一楼双人间',
  },
  {
    roomNumber: '104',
    roomType: 3,
    floor: 1,
    capacity: 3,
    monthlyRate: '1500.00',
    facilities: '["共用卫生间", "空调", "电视", "衣柜"]',
    description: '一楼三人间',
  },
  {
    roomNumber: '105',
    roomType: 4,
    floor: 1,
    capacity: 4,
    monthlyRate: '1200.00',
    facilities: '["共用卫生间", "空调", "电视", "衣柜"]',
    description: '一楼四人间',
  },

  // 二楼房间
  {
    roomNumber: '201',
    roomType: 1,
    floor: 2,
    capacity: 1,
    monthlyRate: '3200.00',
    facilities: '["独立卫生间", "空调", "电视", "衣柜", "阳台"]',
    description: '二楼单人间带阳台',
  },
  {
    roomNumber: '202',
    roomType: 2,
    floor: 2,
    capacity: 2,
    monthlyRate: '2200.00',
    facilities: '["独立卫生间", "空调", "电视", "衣柜", "阳台"]',
    description: '二楼双人间带阳台',
  },
  {
    roomNumber: '203',
    roomType: 2,
    floor: 2,
    capacity: 2,
    monthlyRate: '2200.00',
    facilities: '["独立卫生间", "空调", "电视", "衣柜", "阳台"]',
    description: '二楼双人间带阳台',
  },
  {
    roomNumber: '204',
    roomType: 3,
    floor: 2,
    capacity: 3,
    monthlyRate: '1600.00',
    facilities: '["共用卫生间", "空调", "电视", "衣柜"]',
    description: '二楼三人间',
  },
  {
    roomNumber: '205',
    roomType: 4,
    floor: 2,
    capacity: 4,
    monthlyRate: '1300.00',
    facilities: '["共用卫生间", "空调", "电视", "衣柜"]',
    description: '二楼四人间',
  },
]

// 测试用户数据
const testUsers = [
  {
    email: '<EMAIL>',
    name: '系统管理员',
    phone: '13800138000',
    isActive: true,
  },
  {
    email: '<EMAIL>',
    name: '护理员张三',
    phone: '13800138001',
    isActive: true,
  },
  {
    email: '<EMAIL>',
    name: '医生李四',
    phone: '13800138002',
    isActive: true,
  },
  {
    email: '<EMAIL>',
    name: '财务王五',
    phone: '13800138003',
    isActive: true,
  },
]

// 示例老人数据
const sampleElders = [
  {
    name: '张老太',
    age: 78,
    gender: 2,
    idCard: '110101194501011234',
    phone: '13900139001',
    address: '北京市东城区某某街道',
    emergencyContactName: '张小明',
    emergencyContactPhone: '13900139011',
    emergencyContactRelation: '儿子',
    medicalHistory: '高血压，糖尿病',
    allergies: '青霉素过敏',
    medications: '降压药，降糖药',
    careLevel: 2,
    status: 1,
    admissionDate: '2024-01-15',
  },
  {
    name: '李老爷',
    age: 82,
    gender: 1,
    idCard: '110101194201011235',
    phone: '13900139002',
    address: '北京市西城区某某胡同',
    emergencyContactName: '李小红',
    emergencyContactPhone: '13900139012',
    emergencyContactRelation: '女儿',
    medicalHistory: '冠心病，关节炎',
    allergies: '无',
    medications: '心脏病药物',
    careLevel: 3,
    status: 1,
    admissionDate: '2024-02-01',
  },
]

export async function seedDatabase() {
  try {
    console.log('开始插入基础数据...')

    // 1. 插入角色数据
    console.log('插入角色数据...')
    const insertedRoles = await db.insert(roles).values(systemRoles).returning()
    console.log(`插入了 ${insertedRoles.length} 个角色`)

    // 2. 插入权限数据
    console.log('插入权限数据...')
    const insertedPermissions = await db
      .insert(permissions)
      .values(systemPermissions)
      .returning()
    console.log(`插入了 ${insertedPermissions.length} 个权限`)

    // 3. 为超级管理员角色分配所有权限
    console.log('分配角色权限...')
    const superAdminRole = insertedRoles.find(
      role => role.name === 'super_admin'
    )
    if (superAdminRole) {
      const rolePermissionData = insertedPermissions.map(permission => ({
        roleId: superAdminRole.id,
        permissionId: permission.id,
      }))
      await db.insert(rolePermissions).values(rolePermissionData)
      console.log(`为超级管理员分配了 ${rolePermissionData.length} 个权限`)
    }

    // 4. 插入数据字典
    console.log('插入数据字典...')
    await db.insert(dictionaries).values(dictionaryData)
    console.log(`插入了 ${dictionaryData.length} 个字典项`)

    // 5. 插入系统配置
    console.log('插入系统配置...')
    await db.insert(systemConfigs).values(systemConfigData)
    console.log(`插入了 ${systemConfigData.length} 个系统配置`)

    // 6. 插入费用项目
    console.log('插入费用项目...')
    await db.insert(feeItems).values(baseFeeItems)
    console.log(`插入了 ${baseFeeItems.length} 个费用项目`)

    // 7. 插入房间数据
    console.log('插入房间数据...')
    const insertedRooms = await db.insert(rooms).values(sampleRooms).returning()
    console.log(`插入了 ${insertedRooms.length} 个房间`)

    // 8. 插入测试用户（需要先有角色ID）
    console.log('插入测试用户...')
    const adminRole = insertedRoles.find(role => role.name === 'admin')
    const nurseRole = insertedRoles.find(role => role.name === 'nurse')
    const doctorRole = insertedRoles.find(role => role.name === 'doctor')
    const financeRole = insertedRoles.find(role => role.name === 'finance')

    const testUsersWithRoles = [
      { ...testUsers[0], roleId: adminRole?.id },
      { ...testUsers[1], roleId: nurseRole?.id },
      { ...testUsers[2], roleId: doctorRole?.id },
      { ...testUsers[3], roleId: financeRole?.id },
    ]

    const insertedUsers = await db
      .insert(users)
      .values(testUsersWithRoles)
      .returning()
    console.log(`插入了 ${insertedUsers.length} 个测试用户`)

    // 9. 插入示例老人数据（分配房间）
    console.log('插入示例老人数据...')
    const sampleEldersWithRooms = sampleElders.map((elder, index) => ({
      ...elder,
      roomId: insertedRooms[index]?.id, // 分配前两个房间
    }))

    const insertedElders = await db
      .insert(elderInfo)
      .values(sampleEldersWithRooms)
      .returning()
    console.log(`插入了 ${insertedElders.length} 个示例老人`)

    console.log('基础数据插入完成！')

    return {
      roles: insertedRoles.length,
      permissions: insertedPermissions.length,
      dictionaries: dictionaryData.length,
      systemConfigs: systemConfigData.length,
      feeItems: baseFeeItems.length,
      rooms: insertedRooms.length,
      users: insertedUsers.length,
      elders: insertedElders.length,
    }
  } catch (error) {
    console.error('插入基础数据时出错:', error)
    throw error
  }
}

// 如果直接运行此文件，则执行种子数据插入
if (require.main === module) {
  seedDatabase()
    .then(result => {
      console.log('种子数据插入成功:', result)
      process.exit(0)
    })
    .catch(error => {
      console.error('种子数据插入失败:', error)
      process.exit(1)
    })
}
