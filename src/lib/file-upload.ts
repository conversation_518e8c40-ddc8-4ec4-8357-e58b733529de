/**
 * 文件上传工具函数
 *
 * 提供 Supabase Storage 文件上传、下载、删除等功能
 */

import { supabase } from './supabase'
import { randomUUID } from 'crypto'

// 文件上传配置
export const UPLOAD_CONFIG = {
  // 存储桶名称
  BUCKET_NAME: 'consultation-files',

  // 文件大小限制 (MB)
  MAX_FILE_SIZE: 10,

  // 允许的文件类型
  ALLOWED_TYPES: {
    images: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    documents: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
    ],
  },

  // 文件路径前缀
  PATHS: {
    consultation: 'consultation',
    elder: 'elder',
    health: 'health',
    documents: 'documents',
  },
}

// 文件信息接口
export interface FileUploadResult {
  id: string
  fileName: string
  originalName: string
  fileSize: number
  mimeType: string
  url: string
  path: string
  uploadedAt: Date
}

// 上传进度回调
export type UploadProgressCallback = (progress: number) => void

/**
 * 验证文件类型和大小
 */
export function validateFile(file: File): string | null {
  // 检查文件大小
  const maxSizeBytes = UPLOAD_CONFIG.MAX_FILE_SIZE * 1024 * 1024
  if (file.size > maxSizeBytes) {
    return `文件大小不能超过 ${UPLOAD_CONFIG.MAX_FILE_SIZE}MB`
  }

  // 检查文件类型
  const allAllowedTypes = [
    ...UPLOAD_CONFIG.ALLOWED_TYPES.images,
    ...UPLOAD_CONFIG.ALLOWED_TYPES.documents,
  ]

  if (!allAllowedTypes.includes(file.type)) {
    return '不支持的文件类型'
  }

  return null
}

/**
 * 生成文件路径
 */
export function generateFilePath(
  category: keyof typeof UPLOAD_CONFIG.PATHS,
  fileName: string,
  userId?: string
): string {
  const timestamp = Date.now()
  const uuid = randomUUID().slice(0, 8)
  const extension = fileName.split('.').pop()
  const baseName = fileName.replace(/\.[^/.]+$/, '')

  // 清理文件名，移除特殊字符
  const cleanBaseName = baseName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')
  const cleanFileName = `${cleanBaseName}_${timestamp}_${uuid}.${extension}`

  const pathPrefix = UPLOAD_CONFIG.PATHS[category]
  return userId
    ? `${pathPrefix}/${userId}/${cleanFileName}`
    : `${pathPrefix}/${cleanFileName}`
}

/**
 * 上传单个文件到 Supabase Storage
 */
export async function uploadFile(
  file: File,
  category: keyof typeof UPLOAD_CONFIG.PATHS,
  userId?: string,
  onProgress?: UploadProgressCallback
): Promise<FileUploadResult> {
  // 验证文件
  const validationError = validateFile(file)
  if (validationError) {
    throw new Error(validationError)
  }

  // 生成文件路径
  const filePath = generateFilePath(category, file.name, userId)

  try {
    // 上传文件
    const { data, error } = await supabase.storage
      .from(UPLOAD_CONFIG.BUCKET_NAME)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
      })

    if (error) {
      throw new Error(`文件上传失败: ${error.message}`)
    }

    // 获取公共 URL
    const { data: urlData } = supabase.storage
      .from(UPLOAD_CONFIG.BUCKET_NAME)
      .getPublicUrl(filePath)

    return {
      id: randomUUID(),
      fileName: filePath.split('/').pop() || file.name,
      originalName: file.name,
      fileSize: file.size,
      mimeType: file.type,
      url: urlData.publicUrl,
      path: filePath,
      uploadedAt: new Date(),
    }
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : '文件上传失败')
  }
}

/**
 * 批量上传文件
 */
export async function uploadFiles(
  files: File[],
  category: keyof typeof UPLOAD_CONFIG.PATHS,
  userId?: string,
  onProgress?: (fileIndex: number, progress: number) => void
): Promise<FileUploadResult[]> {
  const results: FileUploadResult[] = []
  const errors: string[] = []

  for (let i = 0; i < files.length; i++) {
    try {
      const result = await uploadFile(files[i], category, userId, progress =>
        onProgress?.(i, progress)
      )
      results.push(result)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败'
      errors.push(`${files[i].name}: ${errorMessage}`)
    }
  }

  if (errors.length > 0) {
    throw new Error(`部分文件上传失败:\n${errors.join('\n')}`)
  }

  return results
}

/**
 * 删除文件
 */
export async function deleteFile(filePath: string): Promise<void> {
  try {
    const { error } = await supabase.storage
      .from(UPLOAD_CONFIG.BUCKET_NAME)
      .remove([filePath])

    if (error) {
      throw new Error(`文件删除失败: ${error.message}`)
    }
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : '文件删除失败')
  }
}

/**
 * 批量删除文件
 */
export async function deleteFiles(filePaths: string[]): Promise<void> {
  try {
    const { error } = await supabase.storage
      .from(UPLOAD_CONFIG.BUCKET_NAME)
      .remove(filePaths)

    if (error) {
      throw new Error(`文件删除失败: ${error.message}`)
    }
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : '批量删除文件失败')
  }
}

/**
 * 获取文件下载 URL
 */
export async function getFileDownloadUrl(
  filePath: string,
  expiresIn: number = 3600
): Promise<string> {
  try {
    const { data, error } = await supabase.storage
      .from(UPLOAD_CONFIG.BUCKET_NAME)
      .createSignedUrl(filePath, expiresIn)

    if (error) {
      throw new Error(`获取下载链接失败: ${error.message}`)
    }

    return data.signedUrl
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : '获取下载链接失败')
  }
}

/**
 * 检查文件是否存在
 */
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    const { data, error } = await supabase.storage
      .from(UPLOAD_CONFIG.BUCKET_NAME)
      .list(filePath.split('/').slice(0, -1).join('/'))

    if (error) {
      return false
    }

    const fileName = filePath.split('/').pop()
    return data.some(file => file.name === fileName)
  } catch (error) {
    return false
  }
}

/**
 * 获取文件信息
 */
export async function getFileInfo(filePath: string) {
  try {
    const { data, error } = await supabase.storage
      .from(UPLOAD_CONFIG.BUCKET_NAME)
      .list(filePath.split('/').slice(0, -1).join('/'))

    if (error) {
      throw new Error(`获取文件信息失败: ${error.message}`)
    }

    const fileName = filePath.split('/').pop()
    const fileInfo = data.find(file => file.name === fileName)

    if (!fileInfo) {
      throw new Error('文件不存在')
    }

    return fileInfo
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : '获取文件信息失败')
  }
}

/**
 * 获取文件类型分类
 */
export function getFileCategory(
  mimeType: string
): 'image' | 'document' | 'other' {
  if (UPLOAD_CONFIG.ALLOWED_TYPES.images.includes(mimeType)) {
    return 'image'
  }
  if (UPLOAD_CONFIG.ALLOWED_TYPES.documents.includes(mimeType)) {
    return 'document'
  }
  return 'other'
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
