/**
 * 表单工具函数测试
 */

import { 
  FormDataTransformer, 
  FormDataFormatter, 
  FormDataSerializer,
  FormDataValidator 
} from '../form-utils'

describe('FormDataTransformer', () => {
  describe('transformValue', () => {
    it('应该转换字符串类型', () => {
      const result = FormDataTransformer.transformValue(123, { type: 'string' })
      expect(result).toBe('123')
    })

    it('应该转换数字类型', () => {
      const result = FormDataTransformer.transformValue('123', { type: 'number' })
      expect(result).toBe(123)
    })

    it('应该处理无效数字', () => {
      const result = FormDataTransformer.transformValue('abc', { 
        type: 'number', 
        defaultValue: 0 
      })
      expect(result).toBe(0)
    })

    it('应该转换布尔类型', () => {
      expect(FormDataTransformer.transformValue('true', { type: 'boolean' })).toBe(true)
      expect(FormDataTransformer.transformValue('false', { type: 'boolean' })).toBe(false)
      expect(FormDataTransformer.transformValue('1', { type: 'boolean' })).toBe(true)
      expect(FormDataTransformer.transformValue('0', { type: 'boolean' })).toBe(false)
    })

    it('应该转换日期类型', () => {
      const dateString = '2023-12-25'
      const result = FormDataTransformer.transformValue(dateString, { type: 'date' })
      expect(result).toBeInstanceOf(Date)
      expect(result.getFullYear()).toBe(2023)
    })

    it('应该转换数组类型', () => {
      const result = FormDataTransformer.transformValue('a,b,c', { type: 'array' })
      expect(result).toEqual(['a', 'b', 'c'])
    })

    it('应该处理邮箱格式', () => {
      const result = FormDataTransformer.transformValue('  <EMAIL>  ', { 
        type: 'email' 
      })
      expect(result).toBe('<EMAIL>')
    })

    it('应该处理手机号格式', () => {
      const result = FormDataTransformer.transformValue('138-0013-8000', { 
        type: 'phone' 
      })
      expect(result).toBe('13800138000')
    })

    it('应该应用字符串处理选项', () => {
      const result = FormDataTransformer.transformValue('  hello world  ', {
        type: 'string',
        trim: true,
        uppercase: true,
        removeSpaces: true,
      })
      expect(result).toBe('HELLOWORLD')
    })
  })

  describe('transformObject', () => {
    it('应该根据映射转换对象', () => {
      const data = {
        firstName: 'John',
        lastName: 'Doe',
        age: '30',
        email: '  <EMAIL>  ',
      }

      const mappings = [
        { source: 'firstName', target: 'name.first' },
        { source: 'lastName', target: 'name.last' },
        { 
          source: 'age', 
          target: 'age',
          transform: { type: 'number' as const }
        },
        { 
          source: 'email', 
          target: 'email',
          transform: { type: 'email' as const }
        },
      ]

      const result = FormDataTransformer.transformObject(data, mappings)
      
      expect(result).toEqual({
        name: {
          first: 'John',
          last: 'Doe',
        },
        age: 30,
        email: '<EMAIL>',
      })
    })
  })

  describe('getNestedValue', () => {
    it('应该获取嵌套属性值', () => {
      const obj = {
        user: {
          profile: {
            name: 'John Doe',
          },
        },
      }

      const result = FormDataTransformer.getNestedValue(obj, 'user.profile.name')
      expect(result).toBe('John Doe')
    })

    it('应该处理不存在的路径', () => {
      const obj = { user: {} }
      const result = FormDataTransformer.getNestedValue(obj, 'user.profile.name')
      expect(result).toBeUndefined()
    })
  })

  describe('setNestedValue', () => {
    it('应该设置嵌套属性值', () => {
      const obj = {}
      FormDataTransformer.setNestedValue(obj, 'user.profile.name', 'John Doe')
      
      expect(obj).toEqual({
        user: {
          profile: {
            name: 'John Doe',
          },
        },
      })
    })
  })
})

describe('FormDataFormatter', () => {
  describe('formatDate', () => {
    it('应该格式化日期', () => {
      const date = new Date('2023-12-25T10:30:00')
      const result = FormDataFormatter.formatDate(date, 'yyyy-MM-dd')
      expect(result).toBe('2023-12-25')
    })

    it('应该处理空日期', () => {
      const result = FormDataFormatter.formatDate(null)
      expect(result).toBe('')
    })

    it('应该处理无效日期', () => {
      const result = FormDataFormatter.formatDate('invalid-date')
      expect(result).toBe('')
    })
  })

  describe('formatCurrency', () => {
    it('应该格式化货币', () => {
      const result = FormDataFormatter.formatCurrency(1234.56)
      expect(result).toBe('¥1,234.56')
    })

    it('应该处理无效数字', () => {
      const result = FormDataFormatter.formatCurrency('abc')
      expect(result).toBe('¥0.00')
    })

    it('应该支持自定义货币符号', () => {
      const result = FormDataFormatter.formatCurrency(1234.56, '$')
      expect(result).toBe('$1,234.56')
    })
  })

  describe('formatPercentage', () => {
    it('应该格式化百分比', () => {
      const result = FormDataFormatter.formatPercentage(85.5)
      expect(result).toBe('85.5%')
    })

    it('应该支持自定义小数位数', () => {
      const result = FormDataFormatter.formatPercentage(85.555, 2)
      expect(result).toBe('85.56%')
    })
  })

  describe('formatPhone', () => {
    it('应该格式化手机号', () => {
      const result = FormDataFormatter.formatPhone('13800138000')
      expect(result).toBe('138 0013 8000')
    })

    it('应该处理非11位号码', () => {
      const result = FormDataFormatter.formatPhone('12345')
      expect(result).toBe('12345')
    })
  })

  describe('formatIdCard', () => {
    it('应该格式化身份证号', () => {
      const result = FormDataFormatter.formatIdCard('110101199001011234')
      expect(result).toBe('110101 19900101 1234')
    })
  })

  describe('formatArray', () => {
    it('应该格式化数组', () => {
      const result = FormDataFormatter.formatArray(['a', 'b', 'c'])
      expect(result).toBe('a, b, c')
    })

    it('应该支持自定义分隔符', () => {
      const result = FormDataFormatter.formatArray(['a', 'b', 'c'], ' | ')
      expect(result).toBe('a | b | c')
    })
  })

  describe('formatFileSize', () => {
    it('应该格式化文件大小', () => {
      expect(FormDataFormatter.formatFileSize(0)).toBe('0 B')
      expect(FormDataFormatter.formatFileSize(1024)).toBe('1 KB')
      expect(FormDataFormatter.formatFileSize(1024 * 1024)).toBe('1 MB')
      expect(FormDataFormatter.formatFileSize(1024 * 1024 * 1024)).toBe('1 GB')
    })
  })
})

describe('FormDataSerializer', () => {
  describe('toJSON', () => {
    it('应该序列化为JSON', () => {
      const data = { name: 'John', age: 30 }
      const result = FormDataSerializer.toJSON(data)
      expect(result).toBe('{"name":"John","age":30}')
    })

    it('应该处理日期对象', () => {
      const date = new Date('2023-12-25T10:30:00Z')
      const data = { date }
      const result = FormDataSerializer.toJSON(data)
      const parsed = JSON.parse(result)
      expect(parsed.date.__type).toBe('Date')
      expect(parsed.date.value).toBe(date.toISOString())
    })
  })

  describe('fromJSON', () => {
    it('应该从JSON反序列化', () => {
      const json = '{"name":"John","age":30}'
      const result = FormDataSerializer.fromJSON(json)
      expect(result).toEqual({ name: 'John', age: 30 })
    })

    it('应该恢复日期对象', () => {
      const date = new Date('2023-12-25T10:30:00Z')
      const json = `{"date":{"__type":"Date","value":"${date.toISOString()}"}}`
      const result = FormDataSerializer.fromJSON(json)
      expect(result.date).toBeInstanceOf(Date)
      expect(result.date.getTime()).toBe(date.getTime())
    })
  })

  describe('toQueryString', () => {
    it('应该转换为查询字符串', () => {
      const data = { name: 'John', age: 30, tags: ['a', 'b'] }
      const result = FormDataSerializer.toQueryString(data)
      expect(result).toContain('name=John')
      expect(result).toContain('age=30')
      expect(result).toContain('tags=a')
      expect(result).toContain('tags=b')
    })
  })

  describe('fromQueryString', () => {
    it('应该从查询字符串解析', () => {
      const queryString = 'name=John&age=30&tags=a&tags=b'
      const result = FormDataSerializer.fromQueryString(queryString)
      expect(result.name).toBe('John')
      expect(result.age).toBe('30')
      expect(result.tags).toEqual(['a', 'b'])
    })
  })

  describe('toFormData', () => {
    it('应该转换为FormData', () => {
      const data = { name: 'John', age: 30 }
      const result = FormDataSerializer.toFormData(data)
      expect(result).toBeInstanceOf(FormData)
      expect(result.get('name')).toBe('John')
      expect(result.get('age')).toBe('30')
    })
  })
})

describe('FormDataValidator', () => {
  describe('validateRequired', () => {
    it('应该验证必填字段', () => {
      const data = { name: 'John', email: '' }
      const requiredFields = ['name', 'email', 'age']
      const errors = FormDataValidator.validateRequired(data, requiredFields)
      
      expect(errors).toHaveLength(2)
      expect(errors).toContain('字段 email 为必填项')
      expect(errors).toContain('字段 age 为必填项')
    })
  })

  describe('validateTypes', () => {
    it('应该验证数据类型', () => {
      const data = { 
        name: 'John', 
        age: 'thirty', 
        email: 'invalid-email',
        active: 'yes'
      }
      const typeMap = {
        name: 'string' as const,
        age: 'number' as const,
        email: 'email' as const,
        active: 'boolean' as const,
      }
      
      const errors = FormDataValidator.validateTypes(data, typeMap)
      expect(errors.length).toBeGreaterThan(0)
    })
  })
})
