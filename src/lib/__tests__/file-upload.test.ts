/**
 * @vitest-environment jsdom
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  validateFile,
  generateFilePath,
  getFileCategory,
  formatFileSize,
  uploadFile,
  deleteFile,
  UPLOAD_CONFIG,
} from '../file-upload'

// Mock Supabase
const mockSupabaseStorage = {
  from: vi.fn(() => ({
    upload: vi.fn(),
    getPublicUrl: vi.fn(),
    remove: vi.fn(),
    createSignedUrl: vi.fn(),
    list: vi.fn(),
  })),
}

vi.mock('../supabase', () => ({
  supabase: {
    storage: mockSupabaseStorage,
  },
}))

// Mock crypto.randomUUID
vi.mock('crypto', () => ({
  randomUUID: vi.fn(() => 'mock-uuid-1234'),
}))

describe('file-upload utils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('validateFile', () => {
    it('validates file size correctly', () => {
      const smallFile = new File(['test'], 'test.txt', { type: 'text/plain' })
      Object.defineProperty(smallFile, 'size', { value: 1024 }) // 1KB

      const largeFile = new File(['test'], 'large.txt', { type: 'text/plain' })
      Object.defineProperty(largeFile, 'size', { value: 11 * 1024 * 1024 }) // 11MB

      expect(validateFile(smallFile)).toBeNull()
      expect(validateFile(largeFile)).toBe('文件大小不能超过 10MB')
    })

    it('validates file type correctly', () => {
      const validImageFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      const validDocFile = new File(['test'], 'test.pdf', { type: 'application/pdf' })
      const invalidFile = new File(['test'], 'test.exe', { type: 'application/x-executable' })

      expect(validateFile(validImageFile)).toBeNull()
      expect(validateFile(validDocFile)).toBeNull()
      expect(validateFile(invalidFile)).toBe('不支持的文件类型')
    })
  })

  describe('generateFilePath', () => {
    it('generates correct file path without userId', () => {
      const path = generateFilePath('consultation', 'test.pdf')
      
      expect(path).toMatch(/^consultation\/test_\d+_mock-uuid\.pdf$/)
    })

    it('generates correct file path with userId', () => {
      const path = generateFilePath('consultation', 'test.pdf', 'user123')
      
      expect(path).toMatch(/^consultation\/user123\/test_\d+_mock-uuid\.pdf$/)
    })

    it('cleans special characters from filename', () => {
      const path = generateFilePath('consultation', 'test file@#$.pdf')
      
      expect(path).toMatch(/^consultation\/test_file____\d+_mock-uuid\.pdf$/)
    })

    it('handles Chinese characters in filename', () => {
      const path = generateFilePath('consultation', '测试文件.pdf')
      
      expect(path).toMatch(/^consultation\/测试文件_\d+_mock-uuid\.pdf$/)
    })
  })

  describe('getFileCategory', () => {
    it('categorizes image files correctly', () => {
      expect(getFileCategory('image/jpeg')).toBe('image')
      expect(getFileCategory('image/png')).toBe('image')
      expect(getFileCategory('image/gif')).toBe('image')
      expect(getFileCategory('image/webp')).toBe('image')
    })

    it('categorizes document files correctly', () => {
      expect(getFileCategory('application/pdf')).toBe('document')
      expect(getFileCategory('application/msword')).toBe('document')
      expect(getFileCategory('text/plain')).toBe('document')
    })

    it('categorizes unknown files as other', () => {
      expect(getFileCategory('video/mp4')).toBe('other')
      expect(getFileCategory('audio/mp3')).toBe('other')
    })
  })

  describe('formatFileSize', () => {
    it('formats bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 Bytes')
      expect(formatFileSize(512)).toBe('512 Bytes')
      expect(formatFileSize(1024)).toBe('1 KB')
      expect(formatFileSize(1536)).toBe('1.5 KB')
      expect(formatFileSize(1024 * 1024)).toBe('1 MB')
      expect(formatFileSize(1.5 * 1024 * 1024)).toBe('1.5 MB')
      expect(formatFileSize(1024 * 1024 * 1024)).toBe('1 GB')
    })
  })

  describe('uploadFile', () => {
    it('uploads file successfully', async () => {
      const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
      Object.defineProperty(mockFile, 'size', { value: 1024 })

      const mockStorageChain = {
        upload: vi.fn().mockResolvedValue({
          data: { path: 'consultation/test_123_mock-uuid.pdf' },
          error: null,
        }),
        getPublicUrl: vi.fn().mockReturnValue({
          data: { publicUrl: 'http://example.com/test.pdf' },
        }),
      }

      mockSupabaseStorage.from.mockReturnValue(mockStorageChain)

      const result = await uploadFile(mockFile, 'consultation', 'user123')

      expect(result).toEqual({
        id: 'mock-uuid-1234',
        fileName: 'test_123_mock-uuid.pdf',
        originalName: 'test.pdf',
        fileSize: 1024,
        mimeType: 'application/pdf',
        url: 'http://example.com/test.pdf',
        path: expect.stringMatching(/^consultation\/user123\/test_\d+_mock-uuid\.pdf$/),
        uploadedAt: expect.any(Date),
      })

      expect(mockStorageChain.upload).toHaveBeenCalledWith(
        expect.stringMatching(/^consultation\/user123\/test_\d+_mock-uuid\.pdf$/),
        mockFile,
        {
          cacheControl: '3600',
          upsert: false,
        }
      )
    })

    it('throws error for invalid file', async () => {
      const invalidFile = new File(['test'], 'test.exe', { type: 'application/x-executable' })

      await expect(uploadFile(invalidFile, 'consultation')).rejects.toThrow('不支持的文件类型')
    })

    it('throws error when upload fails', async () => {
      const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
      Object.defineProperty(mockFile, 'size', { value: 1024 })

      const mockStorageChain = {
        upload: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Upload failed' },
        }),
      }

      mockSupabaseStorage.from.mockReturnValue(mockStorageChain)

      await expect(uploadFile(mockFile, 'consultation')).rejects.toThrow('文件上传失败: Upload failed')
    })
  })

  describe('deleteFile', () => {
    it('deletes file successfully', async () => {
      const mockStorageChain = {
        remove: vi.fn().mockResolvedValue({
          error: null,
        }),
      }

      mockSupabaseStorage.from.mockReturnValue(mockStorageChain)

      await deleteFile('consultation/test.pdf')

      expect(mockStorageChain.remove).toHaveBeenCalledWith(['consultation/test.pdf'])
    })

    it('throws error when deletion fails', async () => {
      const mockStorageChain = {
        remove: vi.fn().mockResolvedValue({
          error: { message: 'Delete failed' },
        }),
      }

      mockSupabaseStorage.from.mockReturnValue(mockStorageChain)

      await expect(deleteFile('consultation/test.pdf')).rejects.toThrow('文件删除失败: Delete failed')
    })
  })

  describe('UPLOAD_CONFIG', () => {
    it('has correct configuration values', () => {
      expect(UPLOAD_CONFIG.BUCKET_NAME).toBe('consultation-files')
      expect(UPLOAD_CONFIG.MAX_FILE_SIZE).toBe(10)
      expect(UPLOAD_CONFIG.ALLOWED_TYPES.images).toContain('image/jpeg')
      expect(UPLOAD_CONFIG.ALLOWED_TYPES.documents).toContain('application/pdf')
      expect(UPLOAD_CONFIG.PATHS.consultation).toBe('consultation')
    })
  })
})
