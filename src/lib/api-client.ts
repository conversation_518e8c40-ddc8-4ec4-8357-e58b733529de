/**
 * API 客户端
 *
 * 基于 fetch 的 API 客户端，提供请求拦截器、错误处理、缓存和数据验证
 */

import { z } from 'zod'
import { useApiStore, useAppStore } from '@/store'

// API 响应基础类型
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
  code: z.number().optional(),
})

export type ApiResponse<T = any> = {
  success: boolean
  data?: T
  error?: string
  message?: string
  code?: number
}

// 请求配置类型
export interface RequestConfig extends RequestInit {
  url: string
  timeout?: number
  cache?: boolean
  cacheExpiry?: number
  retries?: number
  retryDelay?: number
  validateResponse?: z.ZodSchema
  skipAuth?: boolean
  skipErrorHandling?: boolean
}

// 错误类型
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public data?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// 网络错误
export class NetworkError extends ApiError {
  constructor(message: string = '网络连接失败') {
    super(message)
    this.name = 'NetworkError'
  }
}

// 超时错误
export class TimeoutError extends ApiError {
  constructor(message: string = '请求超时') {
    super(message)
    this.name = 'TimeoutError'
  }
}

// 验证错误
export class ValidationError extends ApiError {
  constructor(
    message: string = '数据验证失败',
    public validationErrors?: any
  ) {
    super(message)
    this.name = 'ValidationError'
  }
}

/**
 * API 客户端类
 */
class ApiClient {
  private baseURL: string
  private defaultTimeout: number = 10000
  private defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  }

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL
  }

  /**
   * 设置默认请求头
   */
  setDefaultHeader(key: string, value: string) {
    this.defaultHeaders[key] = value
  }

  /**
   * 移除默认请求头
   */
  removeDefaultHeader(key: string) {
    delete this.defaultHeaders[key]
  }

  /**
   * 设置认证令牌
   */
  setAuthToken(token: string) {
    this.setDefaultHeader('Authorization', `Bearer ${token}`)
  }

  /**
   * 清除认证令牌
   */
  clearAuthToken() {
    this.removeDefaultHeader('Authorization')
  }

  /**
   * 请求拦截器
   */
  private async requestInterceptor(
    config: RequestConfig
  ): Promise<RequestConfig> {
    // 添加默认请求头
    const headers = {
      ...this.defaultHeaders,
      ...config.headers,
    }

    // 如果不跳过认证，尝试添加认证信息
    if (!config.skipAuth) {
      // 这里可以从 auth store 获取 token
      // const authStore = useAuthStore.getState()
      // if (authStore.user && authStore.session) {
      //   headers.Authorization = `Bearer ${authStore.session.token}`
      // }
    }

    return {
      ...config,
      headers,
    }
  }

  /**
   * 响应拦截器
   */
  private async responseInterceptor(
    response: Response,
    config: RequestConfig
  ): Promise<any> {
    // 检查响应状态
    if (!response.ok) {
      const errorData = await this.parseErrorResponse(response)
      throw new ApiError(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        errorData.code,
        errorData.data
      )
    }

    // 解析响应数据
    const data = await this.parseResponse(response)

    // 验证响应数据
    if (config.validateResponse) {
      try {
        return config.validateResponse.parse(data)
      } catch (error) {
        throw new ValidationError('响应数据验证失败', error)
      }
    }

    return data
  }

  /**
   * 解析响应数据
   */
  private async parseResponse(response: Response): Promise<any> {
    const contentType = response.headers.get('content-type')

    if (contentType?.includes('application/json')) {
      return await response.json()
    } else if (contentType?.includes('text/')) {
      return await response.text()
    } else {
      return await response.blob()
    }
  }

  /**
   * 解析错误响应
   */
  private async parseErrorResponse(response: Response): Promise<any> {
    try {
      const contentType = response.headers.get('content-type')
      if (contentType?.includes('application/json')) {
        return await response.json()
      } else {
        return { message: await response.text() }
      }
    } catch {
      return { message: response.statusText }
    }
  }

  /**
   * 创建带超时的 fetch
   */
  private createTimeoutFetch(timeout: number) {
    return (url: string, options: RequestInit) => {
      return Promise.race([
        fetch(url, options),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new TimeoutError()), timeout)
        ),
      ])
    }
  }

  /**
   * 重试机制
   */
  private async withRetry<T>(
    operation: () => Promise<T>,
    retries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error as Error

        // 如果是最后一次尝试，直接抛出错误
        if (attempt === retries) {
          throw lastError
        }

        // 如果是客户端错误（4xx），不重试
        if (
          error instanceof ApiError &&
          error.status &&
          error.status >= 400 &&
          error.status < 500
        ) {
          throw error
        }

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * (attempt + 1)))
      }
    }

    throw lastError!
  }

  /**
   * 核心请求方法
   */
  async request<T = any>(config: RequestConfig): Promise<T> {
    const { setLoading, setError } = useAppStore.getState()
    const {
      getCache,
      setCache,
      addPendingRequest,
      removePendingRequest,
      isPending,
    } = useApiStore.getState()

    const url = config.url.startsWith('http')
      ? config.url
      : `${this.baseURL}${config.url}`
    const cacheKey = `${config.method || 'GET'}:${url}:${JSON.stringify(config.body || {})}`

    try {
      // 检查是否正在请求中
      if (isPending(cacheKey)) {
        throw new ApiError('请求正在处理中，请稍候')
      }

      // 检查缓存
      if (
        config.cache !== false &&
        (config.method === 'GET' || !config.method)
      ) {
        const cachedData = getCache(cacheKey)
        if (cachedData) {
          return cachedData
        }
      }

      // 标记请求开始
      addPendingRequest(cacheKey)
      setLoading(cacheKey, true)
      setError(cacheKey, null)

      // 应用请求拦截器
      const interceptedConfig = await this.requestInterceptor(config)

      // 创建请求操作
      const requestOperation = async () => {
        const timeoutFetch = this.createTimeoutFetch(
          config.timeout || this.defaultTimeout
        )

        const response = await timeoutFetch(url, {
          method: config.method || 'GET',
          headers: interceptedConfig.headers,
          body: config.body,
          ...interceptedConfig,
        })

        return await this.responseInterceptor(response, config)
      }

      // 执行请求（带重试）
      const data = await this.withRetry(
        requestOperation,
        config.retries || 0,
        config.retryDelay || 1000
      )

      // 缓存响应数据
      if (
        config.cache !== false &&
        (config.method === 'GET' || !config.method)
      ) {
        setCache(cacheKey, data, config.cacheExpiry)
      }

      return data
    } catch (error) {
      // 错误处理
      if (!config.skipErrorHandling) {
        const errorMessage = error instanceof Error ? error.message : '请求失败'
        setError(cacheKey, errorMessage)
      }

      throw error
    } finally {
      // 清理状态
      removePendingRequest(cacheKey)
      setLoading(cacheKey, false)
    }
  }

  /**
   * GET 请求
   */
  async get<T = any>(
    url: string,
    config?: Omit<RequestConfig, 'url' | 'method'>
  ): Promise<T> {
    return this.request<T>({ ...config, url, method: 'GET' })
  }

  /**
   * POST 请求
   */
  async post<T = any>(
    url: string,
    data?: any,
    config?: Omit<RequestConfig, 'url' | 'method' | 'body'>
  ): Promise<T> {
    return this.request<T>({
      ...config,
      url,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * PUT 请求
   */
  async put<T = any>(
    url: string,
    data?: any,
    config?: Omit<RequestConfig, 'url' | 'method' | 'body'>
  ): Promise<T> {
    return this.request<T>({
      ...config,
      url,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * PATCH 请求
   */
  async patch<T = any>(
    url: string,
    data?: any,
    config?: Omit<RequestConfig, 'url' | 'method' | 'body'>
  ): Promise<T> {
    return this.request<T>({
      ...config,
      url,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * DELETE 请求
   */
  async delete<T = any>(
    url: string,
    config?: Omit<RequestConfig, 'url' | 'method'>
  ): Promise<T> {
    return this.request<T>({ ...config, url, method: 'DELETE' })
  }
}

// 创建默认的 API 客户端实例
export const apiClient = new ApiClient()

// 导出类型和错误类
export { ApiClient }
export type { RequestConfig }
