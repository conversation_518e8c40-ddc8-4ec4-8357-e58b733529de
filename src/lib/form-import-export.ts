/**
 * 表单数据导入导出工具
 * 
 * 支持多种格式的数据导入导出：
 * - Excel (.xlsx, .xls)
 * - CSV
 * - JSON
 * - XML
 */

import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { FormDataTransformer, FormDataFormatter, FieldMapping } from './form-utils'

// 导出格式
export type ExportFormat = 'excel' | 'csv' | 'json' | 'xml'

// 导入配置
export interface ImportConfig {
  format: ExportFormat
  mappings?: FieldMapping[]
  skipRows?: number
  headers?: string[]
  encoding?: string
}

// 导出配置
export interface ExportConfig {
  format: ExportFormat
  filename?: string
  headers?: Record<string, string>
  mappings?: FieldMapping[]
  includeHeaders?: boolean
  dateFormat?: string
}

// 导入结果
export interface ImportResult {
  success: boolean
  data: any[]
  errors: string[]
  warnings: string[]
  totalRows: number
  validRows: number
}

// 导出结果
export interface ExportResult {
  success: boolean
  blob?: Blob
  url?: string
  filename: string
  error?: string
}

// 表单数据导入器
export class FormDataImporter {
  /**
   * 导入数据
   */
  static async import(file: File, config: ImportConfig): Promise<ImportResult> {
    try {
      switch (config.format) {
        case 'csv':
          return await this.importCSV(file, config)
        case 'json':
          return await this.importJSON(file, config)
        case 'excel':
          return await this.importExcel(file, config)
        case 'xml':
          return await this.importXML(file, config)
        default:
          throw new Error(`不支持的导入格式: ${config.format}`)
      }
    } catch (error) {
      return {
        success: false,
        data: [],
        errors: [error instanceof Error ? error.message : '导入失败'],
        warnings: [],
        totalRows: 0,
        validRows: 0,
      }
    }
  }

  /**
   * 导入 CSV 文件
   */
  private static async importCSV(file: File, config: ImportConfig): Promise<ImportResult> {
    const text = await file.text()
    const lines = text.split('\n').filter(line => line.trim())
    
    if (lines.length === 0) {
      return {
        success: false,
        data: [],
        errors: ['文件为空'],
        warnings: [],
        totalRows: 0,
        validRows: 0,
      }
    }

    const skipRows = config.skipRows || 0
    const dataLines = lines.slice(skipRows)
    const headers = config.headers || this.parseCSVLine(dataLines[0])
    const dataRows = dataLines.slice(config.headers ? 0 : 1)

    const result: ImportResult = {
      success: true,
      data: [],
      errors: [],
      warnings: [],
      totalRows: dataRows.length,
      validRows: 0,
    }

    dataRows.forEach((line, index) => {
      try {
        const values = this.parseCSVLine(line)
        const rowData: Record<string, any> = {}

        headers.forEach((header, headerIndex) => {
          if (headerIndex < values.length) {
            rowData[header] = values[headerIndex]
          }
        })

        // 应用字段映射
        const transformedData = config.mappings
          ? FormDataTransformer.transformObject(rowData, config.mappings)
          : rowData

        result.data.push(transformedData)
        result.validRows++
      } catch (error) {
        result.errors.push(`第 ${index + 1} 行解析失败: ${error}`)
      }
    })

    return result
  }

  /**
   * 导入 JSON 文件
   */
  private static async importJSON(file: File, config: ImportConfig): Promise<ImportResult> {
    const text = await file.text()
    
    try {
      const jsonData = JSON.parse(text)
      const dataArray = Array.isArray(jsonData) ? jsonData : [jsonData]

      const result: ImportResult = {
        success: true,
        data: [],
        errors: [],
        warnings: [],
        totalRows: dataArray.length,
        validRows: 0,
      }

      dataArray.forEach((item, index) => {
        try {
          // 应用字段映射
          const transformedData = config.mappings
            ? FormDataTransformer.transformObject(item, config.mappings)
            : item

          result.data.push(transformedData)
          result.validRows++
        } catch (error) {
          result.errors.push(`第 ${index + 1} 项处理失败: ${error}`)
        }
      })

      return result
    } catch (error) {
      return {
        success: false,
        data: [],
        errors: ['JSON 格式错误'],
        warnings: [],
        totalRows: 0,
        validRows: 0,
      }
    }
  }

  /**
   * 导入 Excel 文件 (简化版本，实际项目中需要使用 xlsx 库)
   */
  private static async importExcel(file: File, config: ImportConfig): Promise<ImportResult> {
    // 注意：这里是简化实现，实际项目中需要安装并使用 xlsx 库
    throw new Error('Excel 导入需要安装 xlsx 库')
  }

  /**
   * 导入 XML 文件
   */
  private static async importXML(file: File, config: ImportConfig): Promise<ImportResult> {
    const text = await file.text()
    
    try {
      const parser = new DOMParser()
      const xmlDoc = parser.parseFromString(text, 'text/xml')
      
      // 简化的 XML 解析，实际项目中可能需要更复杂的解析逻辑
      const items = xmlDoc.querySelectorAll('item')
      const dataArray: any[] = []

      items.forEach(item => {
        const obj: Record<string, any> = {}
        item.childNodes.forEach(child => {
          if (child.nodeType === Node.ELEMENT_NODE) {
            const element = child as Element
            obj[element.tagName] = element.textContent
          }
        })
        dataArray.push(obj)
      })

      const result: ImportResult = {
        success: true,
        data: [],
        errors: [],
        warnings: [],
        totalRows: dataArray.length,
        validRows: 0,
      }

      dataArray.forEach((item, index) => {
        try {
          // 应用字段映射
          const transformedData = config.mappings
            ? FormDataTransformer.transformObject(item, config.mappings)
            : item

          result.data.push(transformedData)
          result.validRows++
        } catch (error) {
          result.errors.push(`第 ${index + 1} 项处理失败: ${error}`)
        }
      })

      return result
    } catch (error) {
      return {
        success: false,
        data: [],
        errors: ['XML 格式错误'],
        warnings: [],
        totalRows: 0,
        validRows: 0,
      }
    }
  }

  /**
   * 解析 CSV 行
   */
  private static parseCSVLine(line: string): string[] {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    result.push(current.trim())
    return result
  }
}

// 表单数据导出器
export class FormDataExporter {
  /**
   * 导出数据
   */
  static async export(data: any[], config: ExportConfig): Promise<ExportResult> {
    try {
      switch (config.format) {
        case 'csv':
          return await this.exportCSV(data, config)
        case 'json':
          return await this.exportJSON(data, config)
        case 'excel':
          return await this.exportExcel(data, config)
        case 'xml':
          return await this.exportXML(data, config)
        default:
          throw new Error(`不支持的导出格式: ${config.format}`)
      }
    } catch (error) {
      return {
        success: false,
        filename: config.filename || 'export',
        error: error instanceof Error ? error.message : '导出失败',
      }
    }
  }

  /**
   * 导出为 CSV
   */
  private static async exportCSV(data: any[], config: ExportConfig): Promise<ExportResult> {
    if (data.length === 0) {
      throw new Error('没有数据可导出')
    }

    // 应用字段映射
    const transformedData = config.mappings
      ? data.map(item => FormDataTransformer.transformObject(item, config.mappings!))
      : data

    // 获取所有字段
    const allFields = new Set<string>()
    transformedData.forEach(item => {
      Object.keys(item).forEach(key => allFields.add(key))
    })

    const fields = Array.from(allFields)
    const headers = config.headers || {}
    
    // 生成 CSV 内容
    let csvContent = ''
    
    // 添加标题行
    if (config.includeHeaders !== false) {
      const headerRow = fields.map(field => headers[field] || field)
      csvContent += this.formatCSVRow(headerRow) + '\n'
    }
    
    // 添加数据行
    transformedData.forEach(item => {
      const row = fields.map(field => {
        const value = item[field]
        if (value instanceof Date) {
          return FormDataFormatter.formatDate(value, config.dateFormat)
        }
        return String(value || '')
      })
      csvContent += this.formatCSVRow(row) + '\n'
    })

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' })
    const filename = config.filename || `export_${format(new Date(), 'yyyyMMdd_HHmmss')}.csv`

    return {
      success: true,
      blob,
      url: URL.createObjectURL(blob),
      filename,
    }
  }

  /**
   * 导出为 JSON
   */
  private static async exportJSON(data: any[], config: ExportConfig): Promise<ExportResult> {
    // 应用字段映射
    const transformedData = config.mappings
      ? data.map(item => FormDataTransformer.transformObject(item, config.mappings!))
      : data

    const jsonContent = JSON.stringify(transformedData, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8' })
    const filename = config.filename || `export_${format(new Date(), 'yyyyMMdd_HHmmss')}.json`

    return {
      success: true,
      blob,
      url: URL.createObjectURL(blob),
      filename,
    }
  }

  /**
   * 导出为 Excel (简化版本)
   */
  private static async exportExcel(data: any[], config: ExportConfig): Promise<ExportResult> {
    // 注意：这里是简化实现，实际项目中需要安装并使用 xlsx 库
    throw new Error('Excel 导出需要安装 xlsx 库')
  }

  /**
   * 导出为 XML
   */
  private static async exportXML(data: any[], config: ExportConfig): Promise<ExportResult> {
    // 应用字段映射
    const transformedData = config.mappings
      ? data.map(item => FormDataTransformer.transformObject(item, config.mappings!))
      : data

    let xmlContent = '<?xml version="1.0" encoding="UTF-8"?>\n<data>\n'
    
    transformedData.forEach(item => {
      xmlContent += '  <item>\n'
      Object.entries(item).forEach(([key, value]) => {
        const xmlValue = value instanceof Date 
          ? FormDataFormatter.formatDate(value, config.dateFormat)
          : String(value || '')
        xmlContent += `    <${key}>${this.escapeXML(xmlValue)}</${key}>\n`
      })
      xmlContent += '  </item>\n'
    })
    
    xmlContent += '</data>'

    const blob = new Blob([xmlContent], { type: 'application/xml;charset=utf-8' })
    const filename = config.filename || `export_${format(new Date(), 'yyyyMMdd_HHmmss')}.xml`

    return {
      success: true,
      blob,
      url: URL.createObjectURL(blob),
      filename,
    }
  }

  /**
   * 格式化 CSV 行
   */
  private static formatCSVRow(row: string[]): string {
    return row.map(value => {
      // 如果包含逗号、引号或换行符，需要用引号包围
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        return `"${value.replace(/"/g, '""')}"`
      }
      return value
    }).join(',')
  }

  /**
   * 转义 XML 特殊字符
   */
  private static escapeXML(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
  }
}

// 导出工具函数
export const formImportExport = {
  import: FormDataImporter.import.bind(FormDataImporter),
  export: FormDataExporter.export.bind(FormDataExporter),
}
