/**
 * 路由配置和管理工具
 */

export interface RouteConfig {
  path: string
  title: string
  description?: string
  icon?: string
  parent?: string
  children?: RouteConfig[]
  permissions?: string[]
  hidden?: boolean
}

// 系统路由配置
export const ROUTES: RouteConfig[] = [
  {
    path: '/',
    title: '首页',
    description: '系统概览和数据统计',
  },
  {
    path: '/marketing',
    title: '营销管理',
    description: '客户咨询和销售管理',
    children: [
      {
        path: '/marketing/consultation',
        title: '咨询接待',
        description: '客户咨询记录和管理',
        permissions: ['marketing:consultation:read'],
      },
      {
        path: '/marketing/follow-up',
        title: '跟进记录',
        description: '客户跟进和沟通记录',
        permissions: ['marketing:follow-up:read'],
      },
      {
        path: '/marketing/performance',
        title: '业绩统计',
        description: '销售业绩分析和报表',
        permissions: ['marketing:performance:read'],
      },
    ],
  },
  {
    path: '/residence',
    title: '居住管理',
    description: '入住和退住流程管理',
    children: [
      {
        path: '/residence/overview',
        title: '入住总览',
        description: '入住情况概览和统计',
        permissions: ['residence:overview:read'],
      },
      {
        path: '/residence/reservation',
        title: '预订管理',
        description: '房间预订和管理',
        permissions: ['residence:reservation:read'],
      },
      {
        path: '/residence/checkin',
        title: '入住流程',
        description: '入住申请和办理流程',
        permissions: ['residence:checkin:read'],
      },
      {
        path: '/residence/checkout',
        title: '退住管理',
        description: '退住申请和办理',
        permissions: ['residence:checkout:read'],
      },
    ],
  },
  {
    path: '/nursing',
    title: '护理服务',
    description: '护理计划和服务管理',
    children: [
      {
        path: '/nursing/service',
        title: '在住服务',
        description: '在住老人服务管理',
        permissions: ['nursing:service:read'],
      },
      {
        path: '/nursing/plan',
        title: '护理计划',
        description: '护理计划制定和管理',
        permissions: ['nursing:plan:read'],
      },
      {
        path: '/nursing/record',
        title: '护理记录',
        description: '护理服务记录和跟踪',
        permissions: ['nursing:record:read'],
      },
      {
        path: '/nursing/dashboard',
        title: '照护看板',
        description: '护理数据统计和分析',
        permissions: ['nursing:dashboard:read'],
      },
    ],
  },
  {
    path: '/finance',
    title: '财务管理',
    description: '费用结算和财务管理',
    children: [
      {
        path: '/finance/bill',
        title: '费用账单',
        description: '费用账单生成和管理',
        permissions: ['finance:bill:read'],
      },
      {
        path: '/finance/payment',
        title: '缴费管理',
        description: '缴费记录和管理',
        permissions: ['finance:payment:read'],
      },
      {
        path: '/finance/query',
        title: '费用查询',
        description: '费用查询和统计',
        permissions: ['finance:query:read'],
      },
      {
        path: '/finance/report',
        title: '财务报表',
        description: '财务报表生成和分析',
        permissions: ['finance:report:read'],
      },
    ],
  },
  {
    path: '/inventory',
    title: '库存管理',
    description: '物品库存和调拨管理',
    children: [
      {
        path: '/inventory/query',
        title: '库存查询',
        description: '库存查询和统计',
        permissions: ['inventory:query:read'],
      },
      {
        path: '/inventory/inbound',
        title: '入库管理',
        description: '物品入库记录和管理',
        permissions: ['inventory:inbound:read'],
      },
      {
        path: '/inventory/outbound',
        title: '出库管理',
        description: '物品出库记录和管理',
        permissions: ['inventory:outbound:read'],
      },
      {
        path: '/inventory/transfer',
        title: '调拨管理',
        description: '物品调拨和审核',
        permissions: ['inventory:transfer:read'],
      },
    ],
  },
  {
    path: '/system',
    title: '系统管理',
    description: '系统配置和用户管理',
    children: [
      {
        path: '/system/users',
        title: '用户管理',
        description: '系统用户账号管理',
        permissions: ['system:users:read'],
      },
      {
        path: '/system/roles',
        title: '角色管理',
        description: '用户角色和权限管理',
        permissions: ['system:roles:read'],
      },
      {
        path: '/system/logs',
        title: '系统日志',
        description: '系统操作日志查询',
        permissions: ['system:logs:read'],
      },
      {
        path: '/system/config',
        title: '基础配置',
        description: '系统基础参数配置',
        permissions: ['system:config:read'],
      },
    ],
  },
]

/**
 * 根据路径查找路由配置
 */
export function findRouteByPath(path: string): RouteConfig | null {
  const findInRoutes = (routes: RouteConfig[]): RouteConfig | null => {
    for (const route of routes) {
      if (route.path === path) {
        return route
      }
      if (route.children) {
        const found = findInRoutes(route.children)
        if (found) return found
      }
    }
    return null
  }

  return findInRoutes(ROUTES)
}

/**
 * 获取路由的面包屑路径
 */
export function getBreadcrumbPath(path: string): RouteConfig[] {
  const breadcrumbs: RouteConfig[] = []

  const findPath = (
    routes: RouteConfig[],
    targetPath: string,
    currentPath: RouteConfig[] = []
  ): boolean => {
    for (const route of routes) {
      const newPath = [...currentPath, route]

      if (route.path === targetPath) {
        breadcrumbs.push(...newPath)
        return true
      }

      if (route.children && findPath(route.children, targetPath, newPath)) {
        return true
      }
    }
    return false
  }

  findPath(ROUTES, path)
  return breadcrumbs
}

/**
 * 检查路由是否需要权限
 */
export function checkRoutePermissions(
  path: string,
  userPermissions: string[]
): boolean {
  const route = findRouteByPath(path)

  if (!route || !route.permissions || route.permissions.length === 0) {
    return true // 无权限要求的路由默认允许访问
  }

  return route.permissions.some(permission =>
    userPermissions.includes(permission)
  )
}

/**
 * 过滤用户可访问的路由
 */
export function filterAccessibleRoutes(
  routes: RouteConfig[],
  userPermissions: string[]
): RouteConfig[] {
  return routes
    .filter(
      route =>
        !route.hidden && checkRoutePermissions(route.path, userPermissions)
    )
    .map(route => ({
      ...route,
      children: route.children
        ? filterAccessibleRoutes(route.children, userPermissions)
        : undefined,
    }))
    .filter(route => !route.children || route.children.length > 0)
}

/**
 * 生成页面标题
 */
export function generatePageTitle(path: string): string {
  const route = findRouteByPath(path)
  const breadcrumbs = getBreadcrumbPath(path)

  if (breadcrumbs.length > 1) {
    return breadcrumbs.map(r => r.title).join(' - ')
  }

  return route?.title || '养老院管理系统'
}
