/**
 * 表单数据处理工具
 * 
 * 提供表单数据的转换、格式化、序列化等功能
 */

import { format, parse, isValid } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 数据类型枚举
export type DataType = 
  | 'string' 
  | 'number' 
  | 'boolean' 
  | 'date' 
  | 'array' 
  | 'object'
  | 'email'
  | 'phone'
  | 'currency'
  | 'percentage'

// 转换配置
export interface TransformConfig {
  type: DataType
  format?: string
  defaultValue?: any
  nullable?: boolean
  trim?: boolean
  uppercase?: boolean
  lowercase?: boolean
  removeSpaces?: boolean
}

// 字段映射配置
export interface FieldMapping {
  source: string
  target: string
  transform?: TransformConfig
}

// 表单数据转换器
export class FormDataTransformer {
  /**
   * 转换单个值
   */
  static transformValue(value: any, config: TransformConfig): any {
    if (value === null || value === undefined) {
      return config.nullable ? null : config.defaultValue
    }

    let result = value

    // 字符串预处理
    if (typeof result === 'string') {
      if (config.trim) result = result.trim()
      if (config.removeSpaces) result = result.replace(/\s+/g, '')
      if (config.uppercase) result = result.toUpperCase()
      if (config.lowercase) result = result.toLowerCase()
    }

    // 类型转换
    switch (config.type) {
      case 'string':
        return String(result)

      case 'number':
        const num = Number(result)
        return isNaN(num) ? config.defaultValue || 0 : num

      case 'boolean':
        if (typeof result === 'boolean') return result
        if (typeof result === 'string') {
          const lower = result.toLowerCase()
          return lower === 'true' || lower === '1' || lower === 'yes'
        }
        return Boolean(result)

      case 'date':
        if (result instanceof Date) return result
        if (typeof result === 'string') {
          const parsed = config.format 
            ? parse(result, config.format, new Date())
            : new Date(result)
          return isValid(parsed) ? parsed : config.defaultValue || null
        }
        return config.defaultValue || null

      case 'array':
        if (Array.isArray(result)) return result
        if (typeof result === 'string') {
          try {
            return JSON.parse(result)
          } catch {
            return result.split(',').map(item => item.trim())
          }
        }
        return config.defaultValue || []

      case 'object':
        if (typeof result === 'object' && result !== null) return result
        if (typeof result === 'string') {
          try {
            return JSON.parse(result)
          } catch {
            return config.defaultValue || {}
          }
        }
        return config.defaultValue || {}

      case 'email':
        return String(result).toLowerCase().trim()

      case 'phone':
        return String(result).replace(/\D/g, '')

      case 'currency':
        const currency = Number(result)
        return isNaN(currency) ? config.defaultValue || 0 : Math.round(currency * 100) / 100

      case 'percentage':
        const percentage = Number(result)
        return isNaN(percentage) ? config.defaultValue || 0 : Math.min(100, Math.max(0, percentage))

      default:
        return result
    }
  }

  /**
   * 转换对象数据
   */
  static transformObject(data: Record<string, any>, mappings: FieldMapping[]): Record<string, any> {
    const result: Record<string, any> = {}

    mappings.forEach(mapping => {
      const sourceValue = this.getNestedValue(data, mapping.source)
      const transformedValue = mapping.transform 
        ? this.transformValue(sourceValue, mapping.transform)
        : sourceValue

      this.setNestedValue(result, mapping.target, transformedValue)
    })

    return result
  }

  /**
   * 获取嵌套属性值
   */
  static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj)
  }

  /**
   * 设置嵌套属性值
   */
  static setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.')
    const lastKey = keys.pop()!
    
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {}
      }
      return current[key]
    }, obj)

    target[lastKey] = value
  }
}

// 表单数据格式化器
export class FormDataFormatter {
  /**
   * 格式化日期
   */
  static formatDate(date: Date | string | null, formatString = 'yyyy-MM-dd'): string {
    if (!date) return ''
    
    const dateObj = date instanceof Date ? date : new Date(date)
    if (!isValid(dateObj)) return ''
    
    return format(dateObj, formatString, { locale: zhCN })
  }

  /**
   * 格式化货币
   */
  static formatCurrency(amount: number | string, currency = '¥'): string {
    const num = Number(amount)
    if (isNaN(num)) return `${currency}0.00`
    
    return `${currency}${num.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`
  }

  /**
   * 格式化百分比
   */
  static formatPercentage(value: number | string, decimals = 1): string {
    const num = Number(value)
    if (isNaN(num)) return '0%'
    
    return `${num.toFixed(decimals)}%`
  }

  /**
   * 格式化手机号
   */
  static formatPhone(phone: string): string {
    const cleaned = phone.replace(/\D/g, '')
    if (cleaned.length === 11) {
      return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
    }
    return phone
  }

  /**
   * 格式化身份证号
   */
  static formatIdCard(idCard: string): string {
    const cleaned = idCard.replace(/\s/g, '')
    if (cleaned.length === 18) {
      return cleaned.replace(/(\d{6})(\d{8})(\d{4})/, '$1 $2 $3')
    }
    return idCard
  }

  /**
   * 格式化数组为字符串
   */
  static formatArray(arr: any[], separator = ', '): string {
    if (!Array.isArray(arr)) return ''
    return arr.join(separator)
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  }
}

// 表单数据序列化器
export class FormDataSerializer {
  /**
   * 序列化表单数据为 JSON
   */
  static toJSON(data: any, pretty = false): string {
    try {
      return JSON.stringify(data, this.jsonReplacer, pretty ? 2 : 0)
    } catch (error) {
      console.error('Failed to serialize data to JSON:', error)
      return '{}'
    }
  }

  /**
   * 从 JSON 反序列化表单数据
   */
  static fromJSON(json: string): any {
    try {
      return JSON.parse(json, this.jsonReviver)
    } catch (error) {
      console.error('Failed to deserialize JSON data:', error)
      return {}
    }
  }

  /**
   * 序列化为 URL 查询参数
   */
  static toQueryString(data: Record<string, any>): string {
    const params = new URLSearchParams()
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        if (Array.isArray(value)) {
          value.forEach(item => params.append(key, String(item)))
        } else if (value instanceof Date) {
          params.append(key, value.toISOString())
        } else {
          params.append(key, String(value))
        }
      }
    })
    
    return params.toString()
  }

  /**
   * 从 URL 查询参数反序列化
   */
  static fromQueryString(queryString: string): Record<string, any> {
    const params = new URLSearchParams(queryString)
    const result: Record<string, any> = {}
    
    for (const [key, value] of params.entries()) {
      if (result[key]) {
        // 如果键已存在，转换为数组
        if (Array.isArray(result[key])) {
          result[key].push(value)
        } else {
          result[key] = [result[key], value]
        }
      } else {
        result[key] = value
      }
    }
    
    return result
  }

  /**
   * 序列化为 FormData
   */
  static toFormData(data: Record<string, any>): FormData {
    const formData = new FormData()
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        if (value instanceof File) {
          formData.append(key, value)
        } else if (Array.isArray(value)) {
          value.forEach((item, index) => {
            if (item instanceof File) {
              formData.append(`${key}[${index}]`, item)
            } else {
              formData.append(`${key}[${index}]`, String(item))
            }
          })
        } else if (value instanceof Date) {
          formData.append(key, value.toISOString())
        } else if (typeof value === 'object') {
          formData.append(key, JSON.stringify(value))
        } else {
          formData.append(key, String(value))
        }
      }
    })
    
    return formData
  }

  /**
   * JSON 序列化替换器
   */
  private static jsonReplacer(key: string, value: any): any {
    if (value instanceof Date) {
      return { __type: 'Date', value: value.toISOString() }
    }
    if (value instanceof File) {
      return { __type: 'File', name: value.name, size: value.size, type: value.type }
    }
    return value
  }

  /**
   * JSON 反序列化恢复器
   */
  private static jsonReviver(key: string, value: any): any {
    if (value && typeof value === 'object' && value.__type) {
      switch (value.__type) {
        case 'Date':
          return new Date(value.value)
        case 'File':
          // 注意：File 对象无法完全恢复，只能恢复元数据
          return { name: value.name, size: value.size, type: value.type }
        default:
          return value
      }
    }
    return value
  }
}

// 表单数据验证器
export class FormDataValidator {
  /**
   * 验证必填字段
   */
  static validateRequired(data: Record<string, any>, requiredFields: string[]): string[] {
    const errors: string[] = []
    
    requiredFields.forEach(field => {
      const value = FormDataTransformer.getNestedValue(data, field)
      if (value === null || value === undefined || value === '') {
        errors.push(`字段 ${field} 为必填项`)
      }
    })
    
    return errors
  }

  /**
   * 验证数据类型
   */
  static validateTypes(data: Record<string, any>, typeMap: Record<string, DataType>): string[] {
    const errors: string[] = []
    
    Object.entries(typeMap).forEach(([field, expectedType]) => {
      const value = FormDataTransformer.getNestedValue(data, field)
      if (value !== null && value !== undefined) {
        if (!this.isValidType(value, expectedType)) {
          errors.push(`字段 ${field} 类型不正确，期望 ${expectedType}`)
        }
      }
    })
    
    return errors
  }

  /**
   * 检查值是否为指定类型
   */
  private static isValidType(value: any, type: DataType): boolean {
    switch (type) {
      case 'string':
        return typeof value === 'string'
      case 'number':
        return typeof value === 'number' && !isNaN(value)
      case 'boolean':
        return typeof value === 'boolean'
      case 'date':
        return value instanceof Date && isValid(value)
      case 'array':
        return Array.isArray(value)
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value)
      case 'email':
        return typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
      case 'phone':
        return typeof value === 'string' && /^1[3-9]\d{9}$/.test(value.replace(/\D/g, ''))
      default:
        return true
    }
  }
}

// 导出工具函数
export const formUtils = {
  transform: FormDataTransformer,
  format: FormDataFormatter,
  serialize: FormDataSerializer,
  validate: FormDataValidator,
}
