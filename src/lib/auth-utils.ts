import { auth } from './auth'
import { db } from './db'
import {
  betterAuthUsers,
  roles,
  permissions,
  rolePermissions,
} from './db/schema'
import { eq } from 'drizzle-orm'

/**
 * 获取用户权限列表
 */
export async function getUserPermissions(userId: string): Promise<string[]> {
  try {
    const userWithRole = await db
      .select({
        roleId: betterAuthUsers.roleId,
      })
      .from(betterAuthUsers)
      .where(eq(betterAuthUsers.id, userId))
      .limit(1)

    if (!userWithRole[0]?.roleId) {
      return []
    }

    const userPermissions = await db
      .select({
        permission: permissions.name,
      })
      .from(rolePermissions)
      .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
      .where(eq(rolePermissions.roleId, userWithRole[0].roleId))

    return userPermissions.map(p => p.permission)
  } catch (error) {
    console.error('获取用户权限失败:', error)
    return []
  }
}

/**
 * 检查用户是否有指定权限
 */
export async function hasPermission(
  userId: string,
  permission: string
): Promise<boolean> {
  const permissions = await getUserPermissions(userId)
  return permissions.includes(permission)
}

/**
 * 检查用户是否有任一权限
 */
export async function hasAnyPermission(
  userId: string,
  permissions: string[]
): Promise<boolean> {
  const userPermissions = await getUserPermissions(userId)
  return permissions.some(permission => userPermissions.includes(permission))
}

/**
 * 检查用户是否有所有权限
 */
export async function hasAllPermissions(
  userId: string,
  permissions: string[]
): Promise<boolean> {
  const userPermissions = await getUserPermissions(userId)
  return permissions.every(permission => userPermissions.includes(permission))
}

/**
 * 获取用户角色信息
 */
export async function getUserRole(userId: string) {
  try {
    const userWithRole = await db
      .select({
        role: {
          id: roles.id,
          name: roles.name,
          description: roles.description,
        },
      })
      .from(betterAuthUsers)
      .innerJoin(roles, eq(betterAuthUsers.roleId, roles.id))
      .where(eq(betterAuthUsers.id, userId))
      .limit(1)

    return userWithRole[0]?.role || null
  } catch (error) {
    console.error('获取用户角色失败:', error)
    return null
  }
}

/**
 * 权限常量定义
 */
export const PERMISSIONS = {
  // 用户管理
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',

  // 角色管理
  ROLE_CREATE: 'role:create',
  ROLE_READ: 'role:read',
  ROLE_UPDATE: 'role:update',
  ROLE_DELETE: 'role:delete',

  // 老人信息管理
  ELDER_CREATE: 'elder:create',
  ELDER_READ: 'elder:read',
  ELDER_UPDATE: 'elder:update',
  ELDER_DELETE: 'elder:delete',

  // 咨询管理
  CONSULTATION_CREATE: 'consultation:create',
  CONSULTATION_READ: 'consultation:read',
  CONSULTATION_UPDATE: 'consultation:update',
  CONSULTATION_DELETE: 'consultation:delete',

  // 房间管理
  ROOM_CREATE: 'room:create',
  ROOM_READ: 'room:read',
  ROOM_UPDATE: 'room:update',
  ROOM_DELETE: 'room:delete',

  // 护理管理
  CARE_CREATE: 'care:create',
  CARE_READ: 'care:read',
  CARE_UPDATE: 'care:update',
  CARE_DELETE: 'care:delete',

  // 健康管理
  HEALTH_CREATE: 'health:create',
  HEALTH_READ: 'health:read',
  HEALTH_UPDATE: 'health:update',
  HEALTH_DELETE: 'health:delete',

  // 财务管理
  FINANCE_CREATE: 'finance:create',
  FINANCE_READ: 'finance:read',
  FINANCE_UPDATE: 'finance:update',
  FINANCE_DELETE: 'finance:delete',

  // 系统配置
  SYSTEM_CREATE: 'system:create',
  SYSTEM_READ: 'system:read',
  SYSTEM_UPDATE: 'system:update',
  SYSTEM_DELETE: 'system:delete',
} as const

/**
 * 角色常量定义
 */
export const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  NURSE: 'nurse',
  DOCTOR: 'doctor',
  FINANCE: 'finance',
  RECEPTIONIST: 'receptionist',
} as const

/**
 * 权限检查装饰器（用于API路由）
 */
export function requirePermission(permission: string) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const method = descriptor.value
    descriptor.value = async function (...args: any[]) {
      const request = args[0] as Request

      try {
        const session = await auth.api.getSession({
          headers: request.headers,
        })

        if (!session) {
          return Response.json(
            { error: '未授权访问', code: 'UNAUTHORIZED' },
            { status: 401 }
          )
        }

        const hasRequiredPermission = await hasPermission(
          session.user.id,
          permission
        )

        if (!hasRequiredPermission) {
          return Response.json(
            { error: '权限不足', code: 'FORBIDDEN' },
            { status: 403 }
          )
        }

        return method.apply(this, args)
      } catch (error) {
        console.error('权限检查失败:', error)
        return Response.json(
          { error: '权限验证异常', code: 'AUTH_ERROR' },
          { status: 500 }
        )
      }
    }
  }
}

/**
 * 获取当前会话用户信息
 */
export async function getCurrentUser(request: Request) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return null
    }

    // 获取完整的用户信息（包括角色）
    const userWithRole = await db
      .select({
        id: betterAuthUsers.id,
        email: betterAuthUsers.email,
        name: betterAuthUsers.name,
        phone: betterAuthUsers.phone,
        isActive: betterAuthUsers.isActive,
        lastLoginAt: betterAuthUsers.lastLoginAt,
        role: {
          id: roles.id,
          name: roles.name,
          description: roles.description,
        },
      })
      .from(betterAuthUsers)
      .leftJoin(roles, eq(betterAuthUsers.roleId, roles.id))
      .where(eq(betterAuthUsers.id, session.user.id))
      .limit(1)

    return userWithRole[0] || null
  } catch (error) {
    console.error('获取当前用户失败:', error)
    return null
  }
}
