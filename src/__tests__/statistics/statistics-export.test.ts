import { describe, it, expect, beforeEach, vi } from 'vitest'
import { StatisticsExporter } from '@/lib/statistics-export'
import type {
  SalesPerformanceExportData,
  ChannelAnalysisExportData,
} from '@/lib/statistics-export'

describe('StatisticsExporter', () => {
  let mockSalesData: SalesPerformanceExportData
  let mockChannelData: ChannelAnalysisExportData

  beforeEach(() => {
    mockSalesData = {
      overview: {
        totalConsultations: 150,
        signedConsultations: 45,
        totalFollowUps: 320,
        activeSalesCount: 8,
        conversionRate: 30.0,
      },
      ranking: [
        {
          userName: '张三',
          userEmail: '<EMAIL>',
          consultationCount: 25,
          signedCount: 8,
          followUpCount: 45,
          conversionRate: '32.00',
        },
        {
          userName: '李四',
          userEmail: '<EMAIL>',
          consultationCount: 20,
          signedCount: 6,
          followUpCount: 38,
          conversionRate: '30.00',
        },
      ],
      trend: [
        {
          date: '2024-01-01',
          consultationCount: 10,
          signedCount: 3,
          conversionRate: '30.00',
        },
        {
          date: '2024-01-02',
          consultationCount: 12,
          signedCount: 4,
          conversionRate: '33.33',
        },
      ],
    }

    mockChannelData = {
      analysis: [
        {
          channelName: '电话咨询',
          totalCount: 80,
          signedCount: 25,
          followingCount: 30,
          pendingCount: 15,
          abandonedCount: 10,
          conversionRate: '31.25',
          followUpRate: '68.75',
        },
        {
          channelName: '微信咨询',
          totalCount: 70,
          signedCount: 20,
          followingCount: 25,
          pendingCount: 15,
          abandonedCount: 10,
          conversionRate: '28.57',
          followUpRate: '64.29',
        },
      ],
      comparison: [
        {
          channelName: '电话咨询',
          totalCount: 80,
          signedCount: 25,
          conversionRate: '31.25',
          percentage: '53.33',
        },
        {
          channelName: '微信咨询',
          totalCount: 70,
          signedCount: 20,
          conversionRate: '28.57',
          percentage: '46.67',
        },
      ],
    }
  })

  describe('销售业绩数据导出', () => {
    it('应该成功导出 CSV 格式的销售业绩数据', async () => {
      const result = await StatisticsExporter.exportSalesPerformance(
        mockSalesData,
        {
          format: 'csv',
          title: '销售业绩测试报表',
          filename: 'test_sales_performance.csv',
        }
      )

      expect(result.success).toBe(true)
      expect(result.filename).toBe('test_sales_performance.csv')
      expect(result.blob).toBeInstanceOf(Blob)
      expect(result.url).toBeDefined()

      // 验证 CSV 内容
      if (result.blob) {
        const csvContent = await result.blob.text()
        expect(csvContent).toContain('销售业绩测试报表')
        expect(csvContent).toContain('业绩概览')
        expect(csvContent).toContain('总咨询数,150')
        expect(csvContent).toContain('签约数量,45')
        expect(csvContent).toContain('转化率,30%')
        expect(csvContent).toContain('销售员排行榜')
        expect(csvContent).toContain('张三')
        expect(csvContent).toContain('李四')
        expect(csvContent).toContain('销售趋势')
        expect(csvContent).toContain('2024-01-01')
      }
    })

    it('应该成功导出 JSON 格式的销售业绩数据', async () => {
      const result = await StatisticsExporter.exportSalesPerformance(
        mockSalesData,
        {
          format: 'json',
          title: '销售业绩测试报表',
          filename: 'test_sales_performance.json',
        }
      )

      expect(result.success).toBe(true)
      expect(result.filename).toBe('test_sales_performance.json')
      expect(result.blob).toBeInstanceOf(Blob)

      // 验证 JSON 内容
      if (result.blob) {
        const jsonContent = await result.blob.text()
        const parsedData = JSON.parse(jsonContent)

        expect(parsedData.title).toBe('销售业绩测试报表')
        expect(parsedData.exportTime).toBeDefined()
        expect(parsedData.data.overview.totalConsultations).toBe(150)
        expect(parsedData.data.ranking).toHaveLength(2)
        expect(parsedData.data.trend).toHaveLength(2)
      }
    })

    it('应该处理不支持的导出格式', async () => {
      const result = await StatisticsExporter.exportSalesPerformance(
        mockSalesData,
        {
          format: 'xml' as any,
          filename: 'test.xml',
        }
      )

      expect(result.success).toBe(false)
      expect(result.error).toContain('不支持的导出格式')
    })

    it('应该处理 PDF 导出错误', async () => {
      const result = await StatisticsExporter.exportSalesPerformance(
        mockSalesData,
        {
          format: 'pdf',
          filename: 'test.pdf',
        }
      )

      expect(result.success).toBe(false)
      expect(result.error).toContain('PDF 导出功能需要安装 jsPDF 库')
    })
  })

  describe('渠道分析数据导出', () => {
    it('应该成功导出 CSV 格式的渠道分析数据', async () => {
      const result = await StatisticsExporter.exportChannelAnalysis(
        mockChannelData,
        {
          format: 'csv',
          title: '渠道分析测试报表',
          filename: 'test_channel_analysis.csv',
          dateRange: {
            start: '2024-01-01',
            end: '2024-01-31',
          },
        }
      )

      expect(result.success).toBe(true)
      expect(result.filename).toBe('test_channel_analysis.csv')
      expect(result.blob).toBeInstanceOf(Blob)

      // 验证 CSV 内容
      if (result.blob) {
        const csvContent = await result.blob.text()
        expect(csvContent).toContain('渠道分析测试报表')
        expect(csvContent).toContain('统计时间范围,2024-01-01 至 2024-01-31')
        expect(csvContent).toContain('渠道分析详情')
        expect(csvContent).toContain('电话咨询')
        expect(csvContent).toContain('微信咨询')
        expect(csvContent).toContain('渠道对比')
      }
    })

    it('应该成功导出 JSON 格式的渠道分析数据', async () => {
      const result = await StatisticsExporter.exportChannelAnalysis(
        mockChannelData,
        {
          format: 'json',
          title: '渠道分析测试报表',
          filename: 'test_channel_analysis.json',
        }
      )

      expect(result.success).toBe(true)
      expect(result.filename).toBe('test_channel_analysis.json')
      expect(result.blob).toBeInstanceOf(Blob)

      // 验证 JSON 内容
      if (result.blob) {
        const jsonContent = await result.blob.text()
        const parsedData = JSON.parse(jsonContent)

        expect(parsedData.title).toBe('渠道分析测试报表')
        expect(parsedData.data.analysis).toHaveLength(2)
        expect(parsedData.data.comparison).toHaveLength(2)
        expect(parsedData.data.analysis[0].channelName).toBe('电话咨询')
      }
    })
  })

  describe('文件下载功能', () => {
    it('应该能够触发文件下载', () => {
      // Mock DOM 方法
      const mockCreateElement = vi.fn()
      const mockAppendChild = vi.fn()
      const mockRemoveChild = vi.fn()
      const mockClick = vi.fn()

      const mockLink = {
        href: '',
        download: '',
        click: mockClick,
      }

      global.document = {
        createElement: mockCreateElement.mockReturnValue(mockLink),
        body: {
          appendChild: mockAppendChild,
          removeChild: mockRemoveChild,
        },
      } as any

      global.URL = {
        createObjectURL: vi.fn().mockReturnValue('blob:test-url'),
        revokeObjectURL: vi.fn(),
      } as any

      const mockResult = {
        success: true,
        filename: 'test.csv',
        url: 'blob:test-url',
        blob: new Blob(['test'], { type: 'text/csv' }),
      }

      StatisticsExporter.downloadFile(mockResult)

      expect(mockCreateElement).toHaveBeenCalledWith('a')
      expect(mockLink.href).toBe('blob:test-url')
      expect(mockLink.download).toBe('test.csv')
      expect(mockAppendChild).toHaveBeenCalledWith(mockLink)
      expect(mockClick).toHaveBeenCalled()
      expect(mockRemoveChild).toHaveBeenCalledWith(mockLink)
    })

    it('应该处理下载失败的情况', () => {
      const mockResult = {
        success: false,
        filename: 'test.csv',
        error: '导出失败',
      }

      expect(() => {
        StatisticsExporter.downloadFile(mockResult)
      }).toThrow('导出失败')
    })
  })

  describe('数据验证', () => {
    it('应该正确处理空数据', async () => {
      const emptyData: SalesPerformanceExportData = {
        overview: {
          totalConsultations: 0,
          signedConsultations: 0,
          totalFollowUps: 0,
          activeSalesCount: 0,
          conversionRate: 0,
        },
        ranking: [],
        trend: [],
      }

      const result = await StatisticsExporter.exportSalesPerformance(
        emptyData,
        {
          format: 'csv',
          filename: 'empty_data.csv',
        }
      )

      expect(result.success).toBe(true)

      if (result.blob) {
        const csvContent = await result.blob.text()
        expect(csvContent).toContain('总咨询数,0')
        expect(csvContent).toContain('签约数量,0')
      }
    })

    it('应该正确处理特殊字符', async () => {
      const specialData: SalesPerformanceExportData = {
        overview: mockSalesData.overview,
        ranking: [
          {
            userName: '张三,测试',
            userEmail: 'test"<EMAIL>',
            consultationCount: 10,
            signedCount: 3,
            followUpCount: 15,
            conversionRate: '30.00',
          },
        ],
        trend: [],
      }

      const result = await StatisticsExporter.exportSalesPerformance(
        specialData,
        {
          format: 'csv',
          filename: 'special_chars.csv',
        }
      )

      expect(result.success).toBe(true)

      if (result.blob) {
        const csvContent = await result.blob.text()
        expect(csvContent).toContain('张三,测试')
        expect(csvContent).toContain('test"<EMAIL>')
      }
    })
  })
})
