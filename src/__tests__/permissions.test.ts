/**
 * 权限系统集成测试
 */

import { describe, it, expect } from '@jest/globals'
import {
  PERMISSIONS,
  ROLES,
  ROLE_PERMISSIONS,
  hasMenuPermission,
  filterMenuItems,
  type MenuItem,
} from '@/lib/permissions'

describe('权限系统', () => {
  describe('权限常量', () => {
    it('应该定义所有必要的权限', () => {
      expect(PERMISSIONS.USER_CREATE).toBe('user:create')
      expect(PERMISSIONS.USER_READ).toBe('user:read')
      expect(PERMISSIONS.USER_UPDATE).toBe('user:update')
      expect(PERMISSIONS.USER_DELETE).toBe('user:delete')

      expect(PERMISSIONS.ELDER_CREATE).toBe('elder:create')
      expect(PERMISSIONS.ELDER_READ).toBe('elder:read')

      expect(PERMISSIONS.CARE_CREATE).toBe('care:create')
      expect(PERMISSIONS.CARE_READ).toBe('care:read')

      expect(PERMISSIONS.FINANCE_CREATE).toBe('finance:create')
      expect(PERMISSIONS.FINANCE_READ).toBe('finance:read')
    })

    it('应该定义所有角色', () => {
      expect(ROLES.SUPER_ADMIN).toBe('super_admin')
      expect(ROLES.ADMIN).toBe('admin')
      expect(ROLES.NURSE).toBe('nurse')
      expect(ROLES.DOCTOR).toBe('doctor')
      expect(ROLES.FINANCE).toBe('finance')
      expect(ROLES.RECEPTIONIST).toBe('receptionist')
    })
  })

  describe('角色权限映射', () => {
    it('超级管理员应该拥有所有权限', () => {
      const superAdminPermissions = ROLE_PERMISSIONS[ROLES.SUPER_ADMIN]
      const allPermissions = Object.values(PERMISSIONS)

      expect(superAdminPermissions).toEqual(allPermissions)
    })

    it('护理员应该有护理相关权限', () => {
      const nursePermissions = ROLE_PERMISSIONS[ROLES.NURSE]

      expect(nursePermissions).toContain(PERMISSIONS.CARE_CREATE)
      expect(nursePermissions).toContain(PERMISSIONS.CARE_READ)
      expect(nursePermissions).toContain(PERMISSIONS.CARE_UPDATE)
      expect(nursePermissions).toContain(PERMISSIONS.HEALTH_CREATE)
      expect(nursePermissions).toContain(PERMISSIONS.ELDER_READ)
    })

    it('财务人员应该有财务相关权限', () => {
      const financePermissions = ROLE_PERMISSIONS[ROLES.FINANCE]

      expect(financePermissions).toContain(PERMISSIONS.FINANCE_CREATE)
      expect(financePermissions).toContain(PERMISSIONS.FINANCE_READ)
      expect(financePermissions).toContain(PERMISSIONS.FINANCE_UPDATE)
      expect(financePermissions).toContain(PERMISSIONS.FINANCE_DELETE)
      expect(financePermissions).toContain(PERMISSIONS.ELDER_READ)
    })

    it('前台接待应该有咨询相关权限', () => {
      const receptionistPermissions = ROLE_PERMISSIONS[ROLES.RECEPTIONIST]

      expect(receptionistPermissions).toContain(PERMISSIONS.CONSULTATION_CREATE)
      expect(receptionistPermissions).toContain(PERMISSIONS.CONSULTATION_READ)
      expect(receptionistPermissions).toContain(PERMISSIONS.CONSULTATION_UPDATE)
      expect(receptionistPermissions).toContain(PERMISSIONS.ELDER_READ)
      expect(receptionistPermissions).toContain(PERMISSIONS.ROOM_READ)
    })
  })

  describe('菜单权限检查', () => {
    const mockMenuItem: MenuItem = {
      key: 'test',
      label: '测试菜单',
      permissions: [PERMISSIONS.USER_READ, PERMISSIONS.USER_CREATE],
    }

    it('应该允许有权限的用户访问菜单', () => {
      const userPermissions = [PERMISSIONS.USER_READ, PERMISSIONS.ELDER_READ]
      const hasAccess = hasMenuPermission(mockMenuItem, userPermissions)

      expect(hasAccess).toBe(true)
    })

    it('应该拒绝没有权限的用户访问菜单', () => {
      const userPermissions = [PERMISSIONS.ELDER_READ, PERMISSIONS.CARE_READ]
      const hasAccess = hasMenuPermission(mockMenuItem, userPermissions)

      expect(hasAccess).toBe(false)
    })

    it('应该允许访问没有权限要求的菜单', () => {
      const menuWithoutPermissions: MenuItem = {
        key: 'public',
        label: '公开菜单',
      }
      const userPermissions: string[] = []
      const hasAccess = hasMenuPermission(
        menuWithoutPermissions,
        userPermissions
      )

      expect(hasAccess).toBe(true)
    })
  })

  describe('菜单过滤', () => {
    const mockMenuItems: MenuItem[] = [
      {
        key: 'dashboard',
        label: '首页',
        path: '/',
      },
      {
        key: 'users',
        label: '用户管理',
        permissions: [PERMISSIONS.USER_READ],
        children: [
          {
            key: 'user-list',
            label: '用户列表',
            path: '/users',
            permissions: [PERMISSIONS.USER_READ],
          },
          {
            key: 'user-create',
            label: '创建用户',
            path: '/users/create',
            permissions: [PERMISSIONS.USER_CREATE],
          },
        ],
      },
      {
        key: 'finance',
        label: '财务管理',
        permissions: [PERMISSIONS.FINANCE_READ],
        children: [
          {
            key: 'billing',
            label: '账单管理',
            path: '/finance/billing',
            permissions: [PERMISSIONS.FINANCE_READ],
          },
        ],
      },
    ]

    it('应该过滤掉用户没有权限的菜单项', () => {
      const userPermissions = [PERMISSIONS.USER_READ]
      const filteredMenus = filterMenuItems(mockMenuItems, userPermissions)

      expect(filteredMenus).toHaveLength(2) // 首页 + 用户管理
      expect(filteredMenus[0].key).toBe('dashboard')
      expect(filteredMenus[1].key).toBe('users')
      expect(filteredMenus[1].children).toHaveLength(1) // 只有用户列表
    })

    it('应该保留用户有权限的所有菜单项', () => {
      const userPermissions = [
        PERMISSIONS.USER_READ,
        PERMISSIONS.USER_CREATE,
        PERMISSIONS.FINANCE_READ,
      ]
      const filteredMenus = filterMenuItems(mockMenuItems, userPermissions)

      expect(filteredMenus).toHaveLength(3) // 所有菜单
      expect(filteredMenus[1].children).toHaveLength(2) // 用户管理的两个子菜单
      expect(filteredMenus[2].key).toBe('finance')
    })

    it('应该移除没有可访问子菜单的父菜单', () => {
      const userPermissions: string[] = [] // 没有任何权限
      const filteredMenus = filterMenuItems(mockMenuItems, userPermissions)

      expect(filteredMenus).toHaveLength(1) // 只有首页
      expect(filteredMenus[0].key).toBe('dashboard')
    })
  })
})
