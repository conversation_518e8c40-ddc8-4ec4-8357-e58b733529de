/**
 * 用户界面 E2E 测试
 *
 * 注意：这些测试需要在有完整环境设置的情况下运行
 * 包括数据库连接、认证服务等
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useAuth } from '@/hooks/use-auth'
import { PERMISSIONS } from '@/lib/permissions'

// 模拟认证 Hook
jest.mock('@/hooks/use-auth')
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

// 模拟 Next.js 路由
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}))

describe('用户管理界面', () => {
  beforeEach(() => {
    // 重置模拟
    jest.clearAllMocks()

    // 设置默认的认证状态
    mockUseAuth.mockReturnValue({
      user: {
        id: 'test-user',
        email: '<EMAIL>',
        name: '测试用户',
        role: { name: 'admin', description: '管理员' },
        permissions: Object.values(PERMISSIONS),
      },
      session: null,
      isAuthenticated: true,
      isLoading: false,
      error: null,
      accessibleMenuItems: [],
      canAccessRoute: jest.fn().mockReturnValue(true),
      hasPermission: jest.fn().mockReturnValue(true),
      hasAnyPermission: jest.fn().mockReturnValue(true),
      hasAllPermissions: jest.fn().mockReturnValue(true),
      role: { name: 'admin', description: '管理员' },
      loading: false,
      hasRole: jest.fn().mockReturnValue(true),
      hasAnyRole: jest.fn().mockReturnValue(true),
      permissions: Object.values(PERMISSIONS),
    })
  })

  describe('登录页面', () => {
    it('应该渲染登录表单', async () => {
      // 动态导入登录页面组件
      const { default: LoginPage } = await import('@/app/login/page')

      render(<LoginPage />)

      expect(screen.getByText('养老院管理系统')).toBeInTheDocument()
      expect(screen.getByLabelText('邮箱地址')).toBeInTheDocument()
      expect(screen.getByLabelText('密码')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument()
    })

    it('应该显示表单验证错误', async () => {
      const { default: LoginPage } = await import('@/app/login/page')

      render(<LoginPage />)

      const submitButton = screen.getByRole('button', { name: '登录' })
      fireEvent.click(submitButton)

      // 检查是否显示了必填字段的验证
      const emailInput = screen.getByLabelText('邮箱地址')
      const passwordInput = screen.getByLabelText('密码')

      expect(emailInput).toBeRequired()
      expect(passwordInput).toBeRequired()
    })

    it('应该处理登录表单提交', async () => {
      const { default: LoginPage } = await import('@/app/login/page')

      render(<LoginPage />)

      const emailInput = screen.getByLabelText('邮箱地址')
      const passwordInput = screen.getByLabelText('密码')
      const submitButton = screen.getByRole('button', { name: '登录' })

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)

      // 检查是否显示加载状态
      await waitFor(() => {
        expect(screen.getByText('登录中...')).toBeInTheDocument()
      })
    })
  })

  describe('权限守卫组件', () => {
    it('应该根据权限显示或隐藏内容', async () => {
      const { PermissionGuard } = await import(
        '@/components/auth/PermissionGuard'
      )

      // 有权限的情况
      mockUseAuth.mockReturnValue({
        ...mockUseAuth(),
        hasPermission: jest.fn().mockReturnValue(true),
      })

      const { rerender } = render(
        <PermissionGuard permission={PERMISSIONS.USER_READ}>
          <div>有权限的内容</div>
        </PermissionGuard>
      )

      expect(screen.getByText('有权限的内容')).toBeInTheDocument()

      // 没有权限的情况
      mockUseAuth.mockReturnValue({
        ...mockUseAuth(),
        hasPermission: jest.fn().mockReturnValue(false),
      })

      rerender(
        <PermissionGuard permission={PERMISSIONS.USER_READ}>
          <div>有权限的内容</div>
        </PermissionGuard>
      )

      expect(screen.queryByText('有权限的内容')).not.toBeInTheDocument()
    })

    it('应该显示自定义的无权限提示', async () => {
      const { PermissionGuard } = await import(
        '@/components/auth/PermissionGuard'
      )

      mockUseAuth.mockReturnValue({
        ...mockUseAuth(),
        hasPermission: jest.fn().mockReturnValue(false),
      })

      render(
        <PermissionGuard
          permission={PERMISSIONS.USER_READ}
          fallback={<div>无权限访问</div>}
        >
          <div>有权限的内容</div>
        </PermissionGuard>
      )

      expect(screen.getByText('无权限访问')).toBeInTheDocument()
      expect(screen.queryByText('有权限的内容')).not.toBeInTheDocument()
    })
  })

  describe('角色守卫组件', () => {
    it('应该根据角色显示或隐藏内容', async () => {
      const { RoleGuard } = await import('@/components/auth/RoleGuard')

      // 有角色的情况
      mockUseAuth.mockReturnValue({
        ...mockUseAuth(),
        hasRole: jest.fn().mockReturnValue(true),
      })

      const { rerender } = render(
        <RoleGuard role="admin">
          <div>管理员内容</div>
        </RoleGuard>
      )

      expect(screen.getByText('管理员内容')).toBeInTheDocument()

      // 没有角色的情况
      mockUseAuth.mockReturnValue({
        ...mockUseAuth(),
        hasRole: jest.fn().mockReturnValue(false),
      })

      rerender(
        <RoleGuard role="admin">
          <div>管理员内容</div>
        </RoleGuard>
      )

      expect(screen.queryByText('管理员内容')).not.toBeInTheDocument()
    })
  })
})

// 模拟测试工具函数
function mockComponent(name: string) {
  return function MockedComponent(props: any) {
    return <div data-testid={`mocked-${name}`} {...props} />
  }
}

// 导出测试工具
export { mockComponent }
