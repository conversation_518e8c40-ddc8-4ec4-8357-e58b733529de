import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useReservations } from '@/hooks/use-reservations'

// Mock useApi hook
const mockRequest = vi.fn()
vi.mock('@/hooks/use-api', () => ({
  useApi: () => ({
    request: mockRequest,
  }),
}))

describe('useReservations', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getReservations', () => {
    it('should fetch reservations with default parameters', async () => {
      const mockResponse = {
        data: {
          list: [
            {
              id: 'reservation-1',
              reservationNumber: 'RES20241201123456',
              contactName: '张三',
              contactPhone: '13800138000',
              status: 1,
            },
          ],
          pagination: {
            page: 1,
            limit: 10,
            total: 1,
            totalPages: 1,
          },
        },
      }

      mockRequest.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useReservations())

      let reservationsData
      await act(async () => {
        reservationsData = await result.current.getReservations()
      })

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/api/reservations?',
        method: 'GET',
      })
      expect(reservationsData).toEqual(mockResponse.data)
    })

    it('should fetch reservations with custom parameters', async () => {
      const mockResponse = { data: { list: [], pagination: {} } }
      mockRequest.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useReservations())

      const params = {
        page: 2,
        limit: 20,
        search: '张三',
        status: '1',
        roomId: 'room-1',
        startDate: '2024-12-01',
        endDate: '2024-12-31',
        sortBy: 'reservationDate' as const,
        sortOrder: 'asc' as const,
      }

      await act(async () => {
        await result.current.getReservations(params)
      })

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/api/reservations?page=2&limit=20&search=张三&status=1&roomId=room-1&startDate=2024-12-01&endDate=2024-12-31&sortBy=reservationDate&sortOrder=asc',
        method: 'GET',
      })
    })

    it('should handle loading state correctly', async () => {
      mockRequest.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))

      const { result } = renderHook(() => useReservations())

      expect(result.current.isLoading).toBe(false)

      act(() => {
        result.current.getReservations()
      })

      expect(result.current.isLoading).toBe(true)

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 150))
      })

      expect(result.current.isLoading).toBe(false)
    })
  })

  describe('getReservation', () => {
    it('should fetch single reservation by id', async () => {
      const mockResponse = {
        data: {
          id: 'reservation-1',
          reservationNumber: 'RES20241201123456',
          contactName: '张三',
          contactPhone: '13800138000',
          status: 1,
        },
      }

      mockRequest.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useReservations())

      let reservationData
      await act(async () => {
        reservationData = await result.current.getReservation('reservation-1')
      })

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/api/reservations/reservation-1',
        method: 'GET',
      })
      expect(reservationData).toEqual(mockResponse.data)
    })
  })

  describe('createReservation', () => {
    it('should create new reservation', async () => {
      const mockResponse = {
        data: {
          id: 'reservation-1',
          reservationNumber: 'RES20241201123456',
          contactName: '张三',
          contactPhone: '13800138000',
        },
      }

      const createData = {
        roomId: 'room-1',
        expectedCheckInDate: '2024-12-15',
        contactName: '张三',
        contactPhone: '13800138000',
        reservationType: 1,
      }

      mockRequest.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useReservations())

      let createdReservation
      await act(async () => {
        createdReservation = await result.current.createReservation(createData)
      })

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/api/reservations',
        method: 'POST',
        data: createData,
      })
      expect(createdReservation).toEqual(mockResponse.data)
    })
  })

  describe('updateReservation', () => {
    it('should update existing reservation', async () => {
      const mockResponse = {
        data: {
          id: 'reservation-1',
          contactName: '张三更新',
          status: 2,
        },
      }

      const updateData = {
        contactName: '张三更新',
        status: 2,
      }

      mockRequest.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useReservations())

      let updatedReservation
      await act(async () => {
        updatedReservation = await result.current.updateReservation('reservation-1', updateData)
      })

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/api/reservations/reservation-1',
        method: 'PUT',
        data: updateData,
      })
      expect(updatedReservation).toEqual(mockResponse.data)
    })
  })

  describe('deleteReservation', () => {
    it('should delete reservation', async () => {
      const mockResponse = { data: { success: true } }
      mockRequest.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useReservations())

      let deleteResult
      await act(async () => {
        deleteResult = await result.current.deleteReservation('reservation-1')
      })

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/api/reservations/reservation-1',
        method: 'DELETE',
      })
      expect(deleteResult).toEqual(mockResponse.data)
    })
  })

  describe('checkRoomAvailability', () => {
    it('should check room availability with required parameters', async () => {
      const mockResponse = {
        data: {
          rooms: [
            {
              id: 'room-1',
              roomNumber: '101',
              isAvailable: true,
              hasReservationConflict: false,
              isRoomFull: false,
            },
          ],
          summary: {
            totalRooms: 1,
            availableRooms: 1,
            conflictRooms: 0,
            fullRooms: 0,
          },
        },
      }

      const params = {
        checkInDate: '2024-12-15',
        checkOutDate: '2024-12-20',
      }

      mockRequest.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useReservations())

      let availabilityData
      await act(async () => {
        availabilityData = await result.current.checkRoomAvailability(params)
      })

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/api/reservations/availability?checkInDate=2024-12-15&checkOutDate=2024-12-20',
        method: 'GET',
      })
      expect(availabilityData).toEqual(mockResponse.data)
    })

    it('should check room availability with all parameters', async () => {
      const mockResponse = { data: { rooms: [], summary: {} } }
      mockRequest.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useReservations())

      const params = {
        checkInDate: '2024-12-15',
        checkOutDate: '2024-12-20',
        roomType: '1',
        capacity: '2',
        excludeReservationId: 'reservation-1',
      }

      await act(async () => {
        await result.current.checkRoomAvailability(params)
      })

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/api/reservations/availability?checkInDate=2024-12-15&checkOutDate=2024-12-20&roomType=1&capacity=2&excludeReservationId=reservation-1',
        method: 'GET',
      })
    })
  })

  describe('checkReservationConflict', () => {
    it('should check reservation conflict', async () => {
      const mockResponse = {
        data: {
          hasConflict: false,
          room: {
            id: 'room-1',
            roomNumber: '101',
          },
          conflictingReservations: [],
        },
      }

      const conflictData = {
        roomId: 'room-1',
        checkInDate: '2024-12-15',
        checkOutDate: '2024-12-20',
      }

      mockRequest.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useReservations())

      let conflictResult
      await act(async () => {
        conflictResult = await result.current.checkReservationConflict(conflictData)
      })

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/api/reservations/availability',
        method: 'POST',
        data: conflictData,
      })
      expect(conflictResult).toEqual(mockResponse.data)
    })

    it('should check reservation conflict with exclusion', async () => {
      const mockResponse = { data: { hasConflict: false } }
      mockRequest.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useReservations())

      const conflictData = {
        roomId: 'room-1',
        checkInDate: '2024-12-15',
        excludeReservationId: 'reservation-1',
      }

      await act(async () => {
        await result.current.checkReservationConflict(conflictData)
      })

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/api/reservations/availability',
        method: 'POST',
        data: conflictData,
      })
    })
  })

  describe('error handling', () => {
    it('should handle API errors gracefully', async () => {
      const mockError = new Error('API Error')
      mockRequest.mockRejectedValue(mockError)

      const { result } = renderHook(() => useReservations())

      await expect(
        act(async () => {
          await result.current.getReservations()
        })
      ).rejects.toThrow('API Error')

      expect(result.current.isLoading).toBe(false)
    })

    it('should reset loading state on error', async () => {
      mockRequest.mockRejectedValue(new Error('Network Error'))

      const { result } = renderHook(() => useReservations())

      try {
        await act(async () => {
          await result.current.createReservation({
            roomId: 'room-1',
            expectedCheckInDate: '2024-12-15',
            contactName: '张三',
            contactPhone: '13800138000',
          })
        })
      } catch (error) {
        // Expected error
      }

      expect(result.current.isLoading).toBe(false)
    })
  })
})
