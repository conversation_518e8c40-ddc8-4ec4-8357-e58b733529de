import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useResidenceOverview } from '@/hooks/use-residence-overview'

// Mock useApi hook
const mockRequest = vi.fn()
vi.mock('@/hooks/use-api', () => ({
  useApi: () => ({
    request: mockRequest,
  }),
}))

describe('useResidenceOverview', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useResidenceOverview())

    expect(result.current.isLoading).toBe(false)
    expect(typeof result.current.getResidenceOverview).toBe('function')
  })

  it('should handle successful API call', async () => {
    const mockData = {
      overview: {
        totalRooms: 100,
        availableRooms: 80,
        occupiedRooms: 60,
        maintenanceRooms: 5,
        totalBeds: 200,
        occupiedBeds: 150,
        occupancyRate: 75,
        totalResidents: 145,
        newAdmissions: 8,
        pendingReservations: 12,
      },
      roomTypes: [],
      reservations: { pendingReservations: 12, statusDistribution: [] },
      recentAdmissions: [],
      trend: [],
      dateRange: { start: '2024-01-01', end: '2024-01-31' },
    }

    mockRequest.mockResolvedValue({ data: mockData })

    const { result } = renderHook(() => useResidenceOverview())

    let responseData
    await act(async () => {
      responseData = await result.current.getResidenceOverview()
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/residence/overview?',
      method: 'GET',
    })
    expect(responseData).toEqual(mockData)
    expect(result.current.isLoading).toBe(false)
  })

  it('should set loading state correctly during API call', async () => {
    mockRequest.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({ data: {} }), 100))
    )

    const { result } = renderHook(() => useResidenceOverview())

    act(() => {
      result.current.getResidenceOverview()
    })

    expect(result.current.isLoading).toBe(true)

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 150))
    })

    expect(result.current.isLoading).toBe(false)
  })

  it('should handle API call with period parameter', async () => {
    mockRequest.mockResolvedValue({ data: {} })

    const { result } = renderHook(() => useResidenceOverview())

    await act(async () => {
      await result.current.getResidenceOverview({ period: 'month' })
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/residence/overview?period=month',
      method: 'GET',
    })
  })

  it('should handle API call with date range parameters', async () => {
    mockRequest.mockResolvedValue({ data: {} })

    const { result } = renderHook(() => useResidenceOverview())

    await act(async () => {
      await result.current.getResidenceOverview({
        startDate: '2024-01-01',
        endDate: '2024-01-31',
      })
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/residence/overview?startDate=2024-01-01&endDate=2024-01-31',
      method: 'GET',
    })
  })

  it('should handle API call with all parameters', async () => {
    mockRequest.mockResolvedValue({ data: {} })

    const { result } = renderHook(() => useResidenceOverview())

    await act(async () => {
      await result.current.getResidenceOverview({
        period: 'quarter',
        startDate: '2024-01-01',
        endDate: '2024-03-31',
      })
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/residence/overview?period=quarter&startDate=2024-01-01&endDate=2024-03-31',
      method: 'GET',
    })
  })

  it('should handle API errors gracefully', async () => {
    const error = new Error('API Error')
    mockRequest.mockRejectedValue(error)

    const { result } = renderHook(() => useResidenceOverview())

    await expect(
      act(async () => {
        await result.current.getResidenceOverview()
      })
    ).rejects.toThrow('API Error')

    expect(result.current.isLoading).toBe(false)
  })

  it('should reset loading state even when API call fails', async () => {
    mockRequest.mockRejectedValue(new Error('Network error'))

    const { result } = renderHook(() => useResidenceOverview())

    try {
      await act(async () => {
        await result.current.getResidenceOverview()
      })
    } catch (error) {
      // Expected to throw
    }

    expect(result.current.isLoading).toBe(false)
  })

  it('should handle empty parameters object', async () => {
    mockRequest.mockResolvedValue({ data: {} })

    const { result } = renderHook(() => useResidenceOverview())

    await act(async () => {
      await result.current.getResidenceOverview({})
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/residence/overview?',
      method: 'GET',
    })
  })

  it('should handle undefined parameters', async () => {
    mockRequest.mockResolvedValue({ data: {} })

    const { result } = renderHook(() => useResidenceOverview())

    await act(async () => {
      await result.current.getResidenceOverview(undefined)
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/residence/overview?',
      method: 'GET',
    })
  })

  it('should handle multiple concurrent API calls', async () => {
    mockRequest.mockResolvedValue({ data: {} })

    const { result } = renderHook(() => useResidenceOverview())

    await act(async () => {
      const promises = [
        result.current.getResidenceOverview({ period: 'week' }),
        result.current.getResidenceOverview({ period: 'month' }),
        result.current.getResidenceOverview({ period: 'year' }),
      ]
      await Promise.all(promises)
    })

    expect(mockRequest).toHaveBeenCalledTimes(3)
    expect(result.current.isLoading).toBe(false)
  })

  it('should properly encode URL parameters', async () => {
    mockRequest.mockResolvedValue({ data: {} })

    const { result } = renderHook(() => useResidenceOverview())

    await act(async () => {
      await result.current.getResidenceOverview({
        period: 'month',
        startDate: '2024-01-01',
        endDate: '2024-01-31',
      })
    })

    const expectedUrl = '/api/residence/overview?period=month&startDate=2024-01-01&endDate=2024-01-31'
    expect(mockRequest).toHaveBeenCalledWith({
      url: expectedUrl,
      method: 'GET',
    })
  })
})
