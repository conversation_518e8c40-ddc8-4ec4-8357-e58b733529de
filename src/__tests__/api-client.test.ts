/**
 * API 客户端测试
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import {
  ApiClient,
  ApiError,
  TimeoutError,
  ValidationError,
} from '@/lib/api-client'
import { z } from 'zod'

// Mock fetch
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock stores
vi.mock('@/store', () => ({
  useApiStore: {
    getState: () => ({
      getCache: vi.fn().mockReturnValue(null),
      setCache: vi.fn(),
      addPendingRequest: vi.fn(),
      removePendingRequest: vi.fn(),
      isPending: vi.fn().mockReturnValue(false),
    }),
  },
  useAppStore: {
    getState: () => ({
      setLoading: vi.fn(),
      setError: vi.fn(),
    }),
  },
}))

// Helper function to create mock response
const createMockResponse = (
  data: any,
  options: {
    ok?: boolean
    status?: number
    statusText?: string
    contentType?: string
  } = {}
) => ({
  ok: options.ok ?? true,
  status: options.status ?? 200,
  statusText: options.statusText ?? 'OK',
  headers: {
    get: (key: string) =>
      key === 'content-type'
        ? (options.contentType ?? 'application/json')
        : null,
  },
  json: () => Promise.resolve(data),
  text: () =>
    Promise.resolve(typeof data === 'string' ? data : JSON.stringify(data)),
  blob: () => Promise.resolve(data),
})

describe('ApiClient', () => {
  let apiClient: ApiClient

  beforeEach(() => {
    apiClient = new ApiClient('/api')
    mockFetch.mockClear()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('基础请求功能', () => {
    it('should make GET request successfully', async () => {
      const mockData = { id: 1, name: 'Test' }
      mockFetch.mockResolvedValueOnce(createMockResponse(mockData))

      const result = await apiClient.get('/test')

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        })
      )
      expect(result).toEqual(mockData)
    })

    it('should make POST request with data', async () => {
      const requestData = { name: 'New Item' }
      const responseData = { id: 1, ...requestData }

      mockFetch.mockResolvedValueOnce(
        createMockResponse(responseData, { status: 201 })
      )

      const result = await apiClient.post('/test', requestData)

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify(requestData),
        })
      )
      expect(result).toEqual(responseData)
    })

    it('should make PUT request', async () => {
      const requestData = { id: 1, name: 'Updated Item' }

      mockFetch.mockResolvedValueOnce(createMockResponse(requestData))

      const result = await apiClient.put('/test/1', requestData)

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test/1',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(requestData),
        })
      )
      expect(result).toEqual(requestData)
    })

    it('should make DELETE request', async () => {
      mockFetch.mockResolvedValueOnce(createMockResponse({}, { status: 204 }))

      await apiClient.delete('/test/1')

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test/1',
        expect.objectContaining({
          method: 'DELETE',
        })
      )
    })
  })

  describe('错误处理', () => {
    it('should handle HTTP error responses', async () => {
      const errorResponse = {
        message: 'Not Found',
        code: 'NOT_FOUND',
      }

      // Set up mock for both calls
      mockFetch
        .mockResolvedValueOnce(
          createMockResponse(errorResponse, {
            ok: false,
            status: 404,
            statusText: 'Not Found',
          })
        )
        .mockResolvedValueOnce(
          createMockResponse(errorResponse, {
            ok: false,
            status: 404,
            statusText: 'Not Found',
          })
        )

      await expect(apiClient.get('/test')).rejects.toThrow(ApiError)
      await expect(apiClient.get('/test')).rejects.toThrow('Not Found')
    })

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      await expect(apiClient.get('/test')).rejects.toThrow()
    })

    it('should handle timeout errors', async () => {
      // Mock a slow response
      mockFetch.mockImplementationOnce(
        () => new Promise(resolve => setTimeout(resolve, 2000))
      )

      await expect(apiClient.get('/test', { timeout: 100 })).rejects.toThrow(
        TimeoutError
      )
    })
  })

  describe('请求拦截器', () => {
    it('should add default headers', async () => {
      apiClient.setDefaultHeader('X-Custom-Header', 'custom-value')

      mockFetch.mockResolvedValueOnce(createMockResponse({}))

      await apiClient.get('/test')

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-Custom-Header': 'custom-value',
          }),
        })
      )
    })

    it('should set and clear auth token', async () => {
      apiClient.setAuthToken('test-token')

      mockFetch.mockResolvedValueOnce(createMockResponse({}))

      await apiClient.get('/test')

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'Bearer test-token',
          }),
        })
      )

      apiClient.clearAuthToken()

      mockFetch.mockResolvedValueOnce(createMockResponse({}))

      await apiClient.get('/test')

      expect(mockFetch).toHaveBeenLastCalledWith(
        '/api/test',
        expect.objectContaining({
          headers: expect.not.objectContaining({
            Authorization: expect.any(String),
          }),
        })
      )
    })
  })

  describe('数据验证', () => {
    it('should validate response data with Zod schema', async () => {
      const schema = z.object({
        id: z.number(),
        name: z.string(),
      })

      const validData = { id: 1, name: 'Test' }
      mockFetch.mockResolvedValueOnce(createMockResponse(validData))

      const result = await apiClient.get('/test', {
        validateResponse: schema,
      })

      expect(result).toEqual(validData)
    })

    it('should throw validation error for invalid response data', async () => {
      const schema = z.object({
        id: z.number(),
        name: z.string(),
      })

      const invalidData = { id: 'invalid', name: 123 }
      mockFetch.mockResolvedValueOnce(createMockResponse(invalidData))

      await expect(
        apiClient.get('/test', { validateResponse: schema })
      ).rejects.toThrow(ValidationError)
    })
  })

  describe('重试机制', () => {
    it('should retry on server errors', async () => {
      // 前两次失败，第三次成功
      mockFetch
        .mockRejectedValueOnce(new Error('Server error'))
        .mockRejectedValueOnce(new Error('Server error'))
        .mockResolvedValueOnce(createMockResponse({ success: true }))

      const result = await apiClient.get('/test', {
        retries: 2,
        retryDelay: 10, // 快速重试用于测试
      })

      expect(mockFetch).toHaveBeenCalledTimes(3)
      expect(result).toEqual({ success: true })
    })

    it('should not retry on client errors', async () => {
      mockFetch.mockResolvedValueOnce(
        createMockResponse(
          { message: 'Bad Request' },
          { ok: false, status: 400, statusText: 'Bad Request' }
        )
      )

      await expect(apiClient.get('/test', { retries: 2 })).rejects.toThrow(
        ApiError
      )

      expect(mockFetch).toHaveBeenCalledTimes(1)
    })
  })

  describe('响应类型处理', () => {
    it('should handle JSON responses', async () => {
      const jsonData = { message: 'success' }
      mockFetch.mockResolvedValueOnce(createMockResponse(jsonData))

      const result = await apiClient.get('/test')
      expect(result).toEqual(jsonData)
    })

    it('should handle text responses', async () => {
      const textData = 'plain text response'
      mockFetch.mockResolvedValueOnce(
        createMockResponse(textData, { contentType: 'text/plain' })
      )

      const result = await apiClient.get('/test')
      expect(result).toBe(textData)
    })

    it('should handle blob responses', async () => {
      const blobData = new Blob(['binary data'])
      mockFetch.mockResolvedValueOnce(
        createMockResponse(blobData, {
          contentType: 'application/octet-stream',
        })
      )

      const result = await apiClient.get('/test')
      expect(result).toBe(blobData)
    })
  })
})
