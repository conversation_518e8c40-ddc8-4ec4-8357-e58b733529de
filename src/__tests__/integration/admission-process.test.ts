import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals'
import { NextRequest } from 'next/server'
import { db } from '@/lib/db'
import { 
  admissionApplications, 
  admissionAssessments,
  admissionReviews,
  admissionContracts,
  admissionPayments,
  roomAssignments,
  rooms,
  betterAuthUsers
} from '@/lib/db/schema'
import { eq } from 'drizzle-orm'

// 模拟用户认证
jest.mock('@/lib/auth-utils', () => ({
  getCurrentUser: jest.fn(() => Promise.resolve({
    id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
  })),
}))

// API 路由处理器
import { POST as createApplication } from '@/app/api/admission-applications/route'
import { POST as createAssessment } from '@/app/api/admission-assessments/route'
import { POST as createReview } from '@/app/api/admission-reviews/route'
import { POST as createContract } from '@/app/api/admission-contracts/route'
import { POST as createPayment } from '@/app/api/admission-payments/route'
import { POST as createRoomAssignment } from '@/app/api/room-assignments/route'

describe('入住流程集成测试', () => {
  let testApplicationId: string
  let testRoomId: string
  let testContractId: string

  beforeAll(async () => {
    // 创建测试房间
    const testRoom = await db
      .insert(rooms)
      .values({
        roomNumber: 'TEST-001',
        roomType: 1,
        floor: 1,
        bedCount: 1,
        area: 20,
        monthlyRent: 3000,
        status: 1, // 可用
        facilities: JSON.stringify(['空调', '独立卫生间']),
        description: '测试房间',
      })
      .returning()

    testRoomId = testRoom[0].id
  })

  afterAll(async () => {
    // 清理测试数据
    if (testApplicationId) {
      await db.delete(roomAssignments).where(eq(roomAssignments.applicationId, testApplicationId))
      await db.delete(admissionPayments).where(eq(admissionPayments.applicationId, testApplicationId))
      await db.delete(admissionContracts).where(eq(admissionContracts.applicationId, testApplicationId))
      await db.delete(admissionReviews).where(eq(admissionReviews.applicationId, testApplicationId))
      await db.delete(admissionAssessments).where(eq(admissionAssessments.applicationId, testApplicationId))
      await db.delete(admissionApplications).where(eq(admissionApplications.id, testApplicationId))
    }
    
    if (testRoomId) {
      await db.delete(rooms).where(eq(rooms.id, testRoomId))
    }
  })

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('完整入住流程测试', () => {
    it('应该能够完成完整的入住流程', async () => {
      // 1. 创建入住申请
      const applicationData = {
        elderName: '张三',
        elderAge: 75,
        elderGender: 1,
        elderIdCard: '110101194801011234',
        elderPhone: '13800138001',
        applicantName: '张小明',
        applicantRelation: '儿子',
        applicantPhone: '13800138002',
        applicantIdCard: '110101197001011234',
        applicantAddress: '北京市朝阳区测试街道1号',
        emergencyContact: '李四',
        emergencyPhone: '13800138003',
        emergencyRelation: '女儿',
        healthCondition: '身体健康，行动自如',
        medicalHistory: '高血压',
        currentMedications: '降压药',
        careNeeds: '日常生活照料',
        specialRequirements: '需要无障碍设施',
        expectedAdmissionDate: '2024-02-01',
        notes: '测试申请',
      }

      const applicationRequest = new NextRequest('http://localhost/api/admission-applications', {
        method: 'POST',
        body: JSON.stringify(applicationData),
        headers: { 'Content-Type': 'application/json' },
      })

      const applicationResponse = await createApplication(applicationRequest)
      const applicationResult = await applicationResponse.json()

      expect(applicationResponse.status).toBe(200)
      expect(applicationResult.success).toBe(true)
      expect(applicationResult.data.elderName).toBe('张三')

      testApplicationId = applicationResult.data.id

      // 2. 创建健康评估
      const healthAssessmentData = {
        applicationId: testApplicationId,
        assessmentType: 1, // 健康评估
        physicalCondition: 2,
        mentalCondition: 1,
        mobilityLevel: 2,
        careLevel: 2,
        nursingNeeds: ['日常护理', '用药提醒'],
        medicalSupport: ['定期体检'],
        fallRisk: 2,
        cognitiveRisk: 1,
        behaviorRisk: 1,
        recommendations: '适合入住，需要基础护理',
        notes: '健康状况良好',
      }

      const healthAssessmentRequest = new NextRequest('http://localhost/api/admission-assessments', {
        method: 'POST',
        body: JSON.stringify(healthAssessmentData),
        headers: { 'Content-Type': 'application/json' },
      })

      const healthAssessmentResponse = await createAssessment(healthAssessmentRequest)
      const healthAssessmentResult = await healthAssessmentResponse.json()

      expect(healthAssessmentResponse.status).toBe(200)
      expect(healthAssessmentResult.success).toBe(true)

      // 3. 创建护理评估
      const nursingAssessmentData = {
        applicationId: testApplicationId,
        assessmentType: 2, // 护理评估
        physicalCondition: 2,
        mentalCondition: 1,
        mobilityLevel: 2,
        careLevel: 2,
        nursingNeeds: ['日常护理', '用药管理'],
        medicalSupport: ['康复训练'],
        fallRisk: 2,
        cognitiveRisk: 1,
        behaviorRisk: 1,
        recommendations: '需要二级护理',
        notes: '护理需求适中',
      }

      const nursingAssessmentRequest = new NextRequest('http://localhost/api/admission-assessments', {
        method: 'POST',
        body: JSON.stringify(nursingAssessmentData),
        headers: { 'Content-Type': 'application/json' },
      })

      const nursingAssessmentResponse = await createAssessment(nursingAssessmentRequest)
      const nursingAssessmentResult = await nursingAssessmentResponse.json()

      expect(nursingAssessmentResponse.status).toBe(200)
      expect(nursingAssessmentResult.success).toBe(true)

      // 4. 初审
      const initialReviewData = {
        applicationId: testApplicationId,
        reviewLevel: 1,
        reviewResult: 1, // 通过
        reviewComments: '初审通过，申请材料齐全',
      }

      const initialReviewRequest = new NextRequest('http://localhost/api/admission-reviews', {
        method: 'POST',
        body: JSON.stringify(initialReviewData),
        headers: { 'Content-Type': 'application/json' },
      })

      const initialReviewResponse = await createReview(initialReviewRequest)
      const initialReviewResult = await initialReviewResponse.json()

      expect(initialReviewResponse.status).toBe(200)
      expect(initialReviewResult.success).toBe(true)

      // 5. 复审
      const secondaryReviewData = {
        applicationId: testApplicationId,
        reviewLevel: 2,
        reviewResult: 1, // 通过
        reviewComments: '复审通过，评估结果合理',
      }

      const secondaryReviewRequest = new NextRequest('http://localhost/api/admission-reviews', {
        method: 'POST',
        body: JSON.stringify(secondaryReviewData),
        headers: { 'Content-Type': 'application/json' },
      })

      const secondaryReviewResponse = await createReview(secondaryReviewRequest)
      const secondaryReviewResult = await secondaryReviewResponse.json()

      expect(secondaryReviewResponse.status).toBe(200)
      expect(secondaryReviewResult.success).toBe(true)

      // 6. 终审
      const finalReviewData = {
        applicationId: testApplicationId,
        reviewLevel: 3,
        reviewResult: 1, // 通过
        reviewComments: '终审通过，同意入住',
      }

      const finalReviewRequest = new NextRequest('http://localhost/api/admission-reviews', {
        method: 'POST',
        body: JSON.stringify(finalReviewData),
        headers: { 'Content-Type': 'application/json' },
      })

      const finalReviewResponse = await createReview(finalReviewRequest)
      const finalReviewResult = await finalReviewResponse.json()

      expect(finalReviewResponse.status).toBe(200)
      expect(finalReviewResult.success).toBe(true)

      // 7. 创建合同
      const contractData = {
        applicationId: testApplicationId,
        contractType: 1,
        contractTitle: '养老服务合同',
        partyA: '测试养老院',
        partyB: '张小明',
        elderName: '张三',
        roomNumber: 'TEST-001',
        serviceContent: '提供日常生活照料服务',
        serviceStandard: '二级护理标准',
        feeStructure: {
          monthlyFee: 3000,
          deposit: 6000,
          serviceFee: 500,
        },
        paymentTerms: '按月支付',
        contractPeriod: '一年',
        effectiveDate: '2024-02-01',
        expiryDate: '2025-01-31',
      }

      const contractRequest = new NextRequest('http://localhost/api/admission-contracts', {
        method: 'POST',
        body: JSON.stringify(contractData),
        headers: { 'Content-Type': 'application/json' },
      })

      const contractResponse = await createContract(contractRequest)
      const contractResult = await contractResponse.json()

      expect(contractResponse.status).toBe(200)
      expect(contractResult.success).toBe(true)

      testContractId = contractResult.data.id

      // 8. 缴费（押金）
      const depositPaymentData = {
        applicationId: testApplicationId,
        contractId: testContractId,
        paymentType: 1, // 押金
        amount: 6000,
        paymentMethod: 2, // 银行转账
        paymentDate: '2024-01-25',
        description: '入住押金',
      }

      const depositPaymentRequest = new NextRequest('http://localhost/api/admission-payments', {
        method: 'POST',
        body: JSON.stringify(depositPaymentData),
        headers: { 'Content-Type': 'application/json' },
      })

      const depositPaymentResponse = await createPayment(depositPaymentRequest)
      const depositPaymentResult = await depositPaymentResponse.json()

      expect(depositPaymentResponse.status).toBe(200)
      expect(depositPaymentResult.success).toBe(true)

      // 9. 缴费（首月费用）
      const monthlyPaymentData = {
        applicationId: testApplicationId,
        contractId: testContractId,
        paymentType: 2, // 月费
        amount: 3500,
        paymentMethod: 2, // 银行转账
        paymentDate: '2024-01-25',
        description: '首月费用（月费+服务费）',
      }

      const monthlyPaymentRequest = new NextRequest('http://localhost/api/admission-payments', {
        method: 'POST',
        body: JSON.stringify(monthlyPaymentData),
        headers: { 'Content-Type': 'application/json' },
      })

      const monthlyPaymentResponse = await createPayment(monthlyPaymentRequest)
      const monthlyPaymentResult = await monthlyPaymentResponse.json()

      expect(monthlyPaymentResponse.status).toBe(200)
      expect(monthlyPaymentResult.success).toBe(true)

      // 10. 房间分配
      const roomAssignmentData = {
        applicationId: testApplicationId,
        roomId: testRoomId,
        assignmentDate: '2024-01-30',
        moveInDate: '2024-02-01',
        notes: '按计划入住',
      }

      const roomAssignmentRequest = new NextRequest('http://localhost/api/room-assignments', {
        method: 'POST',
        body: JSON.stringify(roomAssignmentData),
        headers: { 'Content-Type': 'application/json' },
      })

      const roomAssignmentResponse = await createRoomAssignment(roomAssignmentRequest)
      const roomAssignmentResult = await roomAssignmentResponse.json()

      expect(roomAssignmentResponse.status).toBe(200)
      expect(roomAssignmentResult.success).toBe(true)

      // 验证最终状态
      const finalApplication = await db
        .select()
        .from(admissionApplications)
        .where(eq(admissionApplications.id, testApplicationId))
        .limit(1)

      expect(finalApplication[0].status).toBe(5) // 已通过

      const finalRoom = await db
        .select()
        .from(rooms)
        .where(eq(rooms.id, testRoomId))
        .limit(1)

      expect(finalRoom[0].status).toBe(2) // 已分配
    }, 30000) // 增加超时时间

    it('应该正确处理审核拒绝的情况', async () => {
      // 创建一个会被拒绝的申请
      const applicationData = {
        elderName: '李四',
        elderAge: 85,
        elderGender: 2,
        elderIdCard: '110101193801011234',
        elderPhone: '13800138004',
        applicantName: '李小红',
        applicantRelation: '女儿',
        applicantPhone: '13800138005',
        applicantIdCard: '110101197501011234',
        applicantAddress: '北京市海淀区测试街道2号',
        emergencyContact: '王五',
        emergencyPhone: '13800138006',
        emergencyRelation: '儿子',
        healthCondition: '身体状况较差',
        medicalHistory: '严重心脏病',
        currentMedications: '多种药物',
        careNeeds: '重度护理',
        specialRequirements: '需要专业医疗设备',
        expectedAdmissionDate: '2024-02-15',
        notes: '测试拒绝申请',
      }

      const applicationRequest = new NextRequest('http://localhost/api/admission-applications', {
        method: 'POST',
        body: JSON.stringify(applicationData),
        headers: { 'Content-Type': 'application/json' },
      })

      const applicationResponse = await createApplication(applicationRequest)
      const applicationResult = await applicationResponse.json()

      expect(applicationResponse.status).toBe(200)
      expect(applicationResult.success).toBe(true)

      const rejectedApplicationId = applicationResult.data.id

      try {
        // 创建评估（显示不适合）
        const assessmentData = {
          applicationId: rejectedApplicationId,
          assessmentType: 1,
          physicalCondition: 4, // 差
          mentalCondition: 3,
          mobilityLevel: 4,
          careLevel: 4,
          nursingNeeds: ['重度护理', '医疗监护'],
          medicalSupport: ['专业医疗设备', '24小时监护'],
          fallRisk: 3,
          cognitiveRisk: 3,
          behaviorRisk: 2,
          recommendations: '不适合入住，建议专业医疗机构',
          notes: '护理需求超出能力范围',
        }

        const assessmentRequest = new NextRequest('http://localhost/api/admission-assessments', {
          method: 'POST',
          body: JSON.stringify(assessmentData),
          headers: { 'Content-Type': 'application/json' },
        })

        const assessmentResponse = await createAssessment(assessmentRequest)
        expect(assessmentResponse.status).toBe(200)

        // 初审拒绝
        const reviewData = {
          applicationId: rejectedApplicationId,
          reviewLevel: 1,
          reviewResult: 2, // 拒绝
          reviewComments: '评估结果显示护理需求超出我院能力范围，建议转至专业医疗机构',
        }

        const reviewRequest = new NextRequest('http://localhost/api/admission-reviews', {
          method: 'POST',
          body: JSON.stringify(reviewData),
          headers: { 'Content-Type': 'application/json' },
        })

        const reviewResponse = await createReview(reviewRequest)
        const reviewResult = await reviewResponse.json()

        expect(reviewResponse.status).toBe(200)
        expect(reviewResult.success).toBe(true)

        // 验证申请状态变为已拒绝
        const finalApplication = await db
          .select()
          .from(admissionApplications)
          .where(eq(admissionApplications.id, rejectedApplicationId))
          .limit(1)

        expect(finalApplication[0].status).toBe(6) // 已拒绝
      } finally {
        // 清理测试数据
        await db.delete(admissionReviews).where(eq(admissionReviews.applicationId, rejectedApplicationId))
        await db.delete(admissionAssessments).where(eq(admissionAssessments.applicationId, rejectedApplicationId))
        await db.delete(admissionApplications).where(eq(admissionApplications.id, rejectedApplicationId))
      }
    })
  })
})
