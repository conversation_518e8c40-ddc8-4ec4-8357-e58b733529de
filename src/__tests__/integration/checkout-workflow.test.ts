import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { db } from '@/lib/db'
import { checkoutApplications, checkoutReviews, checkoutSettlements, roomCleaningRecords } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'

// Import API handlers
import { POST as createApplication } from '@/app/api/checkout-applications/route'
import { PUT as updateApplicationStatus } from '@/app/api/checkout-applications/[id]/status/route'
import { POST as createReview } from '@/app/api/checkout-reviews/route'
import { POST as createSettlement } from '@/app/api/checkout-settlements/route'
import { POST as confirmSettlement } from '@/app/api/checkout-settlements/[id]/confirm/route'
import { POST as createCleaningRecord } from '@/app/api/room-cleaning-records/route'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    transaction: vi.fn(),
  },
}))

vi.mock('@/lib/auth-utils', () => ({
  getCurrentUser: vi.fn(),
}))

vi.mock('date-fns', () => ({
  format: vi.fn(() => '20240127'),
  differenceInDays: vi.fn(() => 30),
}))

const mockUser = {
  id: 'user-1',
  name: 'Test User',
  email: '<EMAIL>',
}

const mockElder = {
  id: 'elder-1',
  name: '张三',
  age: 75,
  gender: '男',
  idCard: '110101194901010001',
  careLevel: '中度护理',
  status: 1,
}

const mockRoom = {
  id: 'room-1',
  roomNumber: 'A101',
  roomType: '单人间',
  status: 1,
}

const mockApplication = {
  id: 'app-1',
  applicationNumber: 'CO20240127001',
  elderInfoId: 'elder-1',
  applicantName: '李四',
  applicantPhone: '13800138000',
  applicantRelation: '子女',
  checkoutReason: '家庭原因',
  checkoutType: 1,
  expectedCheckoutDate: '2024-02-01',
  status: 1,
  applicationDate: '2024-01-27',
  createdBy: 'user-1',
  createdAt: new Date(),
  updatedAt: new Date(),
}

describe('Checkout Workflow Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(getCurrentUser).mockResolvedValue(mockUser)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Complete Checkout Process', () => {
    it('should complete the entire checkout workflow successfully', async () => {
      // Step 1: Create checkout application
      const applicationData = {
        elderInfoId: 'elder-1',
        applicantName: '李四',
        applicantPhone: '13800138000',
        applicantRelation: '子女',
        checkoutReason: '家庭原因',
        checkoutType: 1,
        expectedCheckoutDate: '2024-02-01',
        notes: '备注信息',
      }

      // Mock elder exists and is in residence
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([{ ...mockElder, status: 1 }]),
          }),
        }),
      } as any)

      // Mock no existing application
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([]),
          }),
        }),
      } as any)

      // Mock application number generation
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue([{ count: 0 }]),
        }),
      } as any)

      // Mock insert application
      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockReturnValue([mockApplication]),
        }),
      } as any)

      const createRequest = new NextRequest('http://localhost:3000/api/checkout-applications', {
        method: 'POST',
        body: JSON.stringify(applicationData),
      })

      const createResponse = await createApplication(createRequest)
      const createData = await createResponse.json()

      expect(createResponse.status).toBe(201)
      expect(createData.success).toBe(true)
      expect(createData.data.applicationNumber).toBe('CO20240127001')

      // Step 2: First level review (初审)
      const firstReviewData = {
        applicationId: 'app-1',
        reviewLevel: 1,
        reviewType: 1,
        reviewResult: 1, // 通过
        reviewNotes: '初审通过',
        nextReviewLevel: 2,
        nextReviewer: 'user-2',
      }

      // Mock application exists for review
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([mockApplication]),
          }),
        }),
      } as any)

      // Mock review transaction
      vi.mocked(db.transaction).mockImplementation(async (callback) => {
        const mockTx = {
          insert: vi.fn().mockReturnValue({
            values: vi.fn().mockReturnValue({
              returning: vi.fn().mockReturnValue([{
                id: 'review-1',
                ...firstReviewData,
                reviewerId: 'user-1',
                reviewDate: '2024-01-27',
              }]),
            }),
          }),
          update: vi.fn().mockReturnValue({
            set: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({}),
            }),
          }),
        }
        return await callback(mockTx as any)
      })

      const firstReviewRequest = new NextRequest('http://localhost:3000/api/checkout-reviews', {
        method: 'POST',
        body: JSON.stringify(firstReviewData),
      })

      const firstReviewResponse = await createReview(firstReviewRequest)
      const firstReviewResult = await firstReviewResponse.json()

      expect(firstReviewResponse.status).toBe(201)
      expect(firstReviewResult.success).toBe(true)

      // Step 3: Final review (终审)
      const finalReviewData = {
        applicationId: 'app-1',
        reviewLevel: 3,
        reviewType: 1,
        reviewResult: 1, // 通过
        reviewNotes: '终审通过，同意退住',
      }

      // Mock application exists for final review
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([{ ...mockApplication, status: 2 }]), // 审核中
          }),
        }),
      } as any)

      // Mock final review transaction
      vi.mocked(db.transaction).mockImplementation(async (callback) => {
        const mockTx = {
          insert: vi.fn().mockReturnValue({
            values: vi.fn().mockReturnValue({
              returning: vi.fn().mockReturnValue([{
                id: 'review-2',
                ...finalReviewData,
                reviewerId: 'user-2',
                reviewDate: '2024-01-28',
              }]),
            }),
          }),
          update: vi.fn().mockReturnValue({
            set: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({}),
            }),
          }),
        }
        return await callback(mockTx as any)
      })

      const finalReviewRequest = new NextRequest('http://localhost:3000/api/checkout-reviews', {
        method: 'POST',
        body: JSON.stringify(finalReviewData),
      })

      const finalReviewResponse = await createReview(finalReviewRequest)
      const finalReviewResult = await finalReviewResponse.json()

      expect(finalReviewResponse.status).toBe(201)
      expect(finalReviewResult.success).toBe(true)

      // Step 4: Update application status to trigger settlement creation
      // Mock application exists for status update
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              leftJoin: vi.fn().mockReturnValue({
                where: vi.fn().mockReturnValue({
                  limit: vi.fn().mockReturnValue([{
                    ...mockApplication,
                    status: 3, // 已通过
                    elder: mockElder,
                    room: mockRoom,
                  }]),
                }),
              }),
            }),
          }),
        }),
      } as any)

      // Mock status update transaction
      vi.mocked(db.transaction).mockImplementation(async (callback) => {
        const mockTx = {
          update: vi.fn().mockReturnValue({
            set: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({}),
            }),
          }),
          insert: vi.fn().mockReturnValue({
            values: vi.fn().mockReturnValue({
              returning: vi.fn().mockReturnValue([{
                id: 'settlement-1',
                applicationId: 'app-1',
                settlementNumber: 'ST20240128001',
                totalCharges: 5000,
                totalPayments: 4500,
                depositAmount: 2000,
                refundAmount: 1500,
                status: 1,
              }]),
            }),
          }),
        }
        return await callback(mockTx as any)
      })

      const statusUpdateRequest = new NextRequest('http://localhost:3000/api/checkout-applications/app-1/status', {
        method: 'PUT',
        body: JSON.stringify({ status: 5 }), // 费用结算中
      })

      const statusUpdateResponse = await updateApplicationStatus(statusUpdateRequest, { params: { id: 'app-1' } })
      const statusUpdateResult = await statusUpdateResponse.json()

      expect(statusUpdateResponse.status).toBe(200)
      expect(statusUpdateResult.success).toBe(true)

      // Step 5: Confirm settlement
      const mockSettlement = {
        id: 'settlement-1',
        applicationId: 'app-1',
        settlementNumber: 'ST20240128001',
        totalCharges: 5000,
        totalPayments: 4500,
        depositAmount: 2000,
        refundAmount: 1500,
        status: 1,
      }

      // Mock settlement exists
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([mockSettlement]),
          }),
        }),
      } as any)

      // Mock settlement confirmation transaction
      vi.mocked(db.transaction).mockImplementation(async (callback) => {
        const mockTx = {
          update: vi.fn().mockReturnValue({
            set: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({}),
            }),
          }),
        }
        return await callback(mockTx as any)
      })

      const confirmRequest = new NextRequest('http://localhost:3000/api/checkout-settlements/settlement-1/confirm', {
        method: 'POST',
        body: JSON.stringify({ 
          approved: true,
          notes: '费用结算确认完成',
        }),
      })

      const confirmResponse = await confirmSettlement(confirmRequest, { params: { id: 'settlement-1' } })
      const confirmResult = await confirmResponse.json()

      expect(confirmResponse.status).toBe(200)
      expect(confirmResult.success).toBe(true)

      // Step 6: Create cleaning record
      const cleaningData = {
        applicationId: 'app-1',
        roomId: 'room-1',
        assignedTo: 'cleaner-1',
        scheduledDate: '2024-01-30',
        notes: '需要彻底清洁',
      }

      // Mock cleaning record creation
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([{ ...mockApplication, status: 6 }]), // 房间清理中
          }),
        }),
      } as any)

      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue([{ count: 0 }]),
        }),
      } as any)

      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockReturnValue([{
            id: 'cleaning-1',
            ...cleaningData,
            cleaningNumber: 'CL20240130001',
            status: 1,
            tasks: [
              { task: '清理个人物品', completed: false, notes: '' },
              { task: '清洁房间', completed: false, notes: '' },
              { task: '设施检查', completed: false, notes: '' },
            ],
          }]),
        }),
      } as any)

      const cleaningRequest = new NextRequest('http://localhost:3000/api/room-cleaning-records', {
        method: 'POST',
        body: JSON.stringify(cleaningData),
      })

      const cleaningResponse = await createCleaningRecord(cleaningRequest)
      const cleaningResult = await cleaningResponse.json()

      expect(cleaningResponse.status).toBe(201)
      expect(cleaningResult.success).toBe(true)
      expect(cleaningResult.data.cleaningNumber).toBe('CL20240130001')

      // Verify the complete workflow
      expect(createData.data.status).toBe(1) // 待审核
      expect(firstReviewResult.data.reviewLevel).toBe(1) // 初审
      expect(finalReviewResult.data.reviewLevel).toBe(3) // 终审
      expect(statusUpdateResult.data.status).toBe(5) // 费用结算中
      expect(confirmResult.message).toContain('确认成功')
      expect(cleaningResult.data.status).toBe(1) // 清理中
    })

    it('should handle review rejection workflow', async () => {
      // Create application (same as above)
      const applicationData = {
        elderInfoId: 'elder-1',
        applicantName: '李四',
        applicantPhone: '13800138000',
        applicantRelation: '子女',
        checkoutReason: '家庭原因',
        checkoutType: 1,
        expectedCheckoutDate: '2024-02-01',
      }

      // Mock application creation
      vi.mocked(db.select).mockReturnValue({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([{ ...mockElder, status: 1 }]),
          }),
        }),
      } as any)

      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockReturnValue([mockApplication]),
        }),
      } as any)

      // Review with rejection
      const rejectReviewData = {
        applicationId: 'app-1',
        reviewLevel: 1,
        reviewType: 1,
        reviewResult: 2, // 拒绝
        reviewNotes: '材料不完整，需要补充',
        requirements: [
          { requirement: '补充医疗证明', completed: false, notes: '' },
          { requirement: '提供家属同意书', completed: false, notes: '' },
        ],
      }

      // Mock application exists for review
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([mockApplication]),
          }),
        }),
      } as any)

      // Mock review transaction with rejection
      vi.mocked(db.transaction).mockImplementation(async (callback) => {
        const mockTx = {
          insert: vi.fn().mockReturnValue({
            values: vi.fn().mockReturnValue({
              returning: vi.fn().mockReturnValue([{
                id: 'review-1',
                ...rejectReviewData,
                reviewerId: 'user-1',
                reviewDate: '2024-01-27',
              }]),
            }),
          }),
          update: vi.fn().mockReturnValue({
            set: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({}),
            }),
          }),
        }
        return await callback(mockTx as any)
      })

      const rejectRequest = new NextRequest('http://localhost:3000/api/checkout-reviews', {
        method: 'POST',
        body: JSON.stringify(rejectReviewData),
      })

      const rejectResponse = await createReview(rejectRequest)
      const rejectResult = await rejectResponse.json()

      expect(rejectResponse.status).toBe(201)
      expect(rejectResult.success).toBe(true)
      expect(rejectResult.data.reviewResult).toBe(2) // 拒绝

      // Verify that application status should be updated to rejected (4)
      // This would be handled by the review creation logic
    })
  })

  describe('Error Handling in Workflow', () => {
    it('should handle database errors during workflow', async () => {
      const applicationData = {
        elderInfoId: 'elder-1',
        applicantName: '李四',
        applicantPhone: '13800138000',
        applicantRelation: '子女',
        checkoutReason: '家庭原因',
        checkoutType: 1,
        expectedCheckoutDate: '2024-02-01',
      }

      // Mock database error
      vi.mocked(db.select).mockImplementation(() => {
        throw new Error('Database connection failed')
      })

      const request = new NextRequest('http://localhost:3000/api/checkout-applications', {
        method: 'POST',
        body: JSON.stringify(applicationData),
      })

      const response = await createApplication(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.code).toBe('INTERNAL_ERROR')
    })

    it('should handle invalid status transitions', async () => {
      // Mock application with completed status
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              leftJoin: vi.fn().mockReturnValue({
                where: vi.fn().mockReturnValue({
                  limit: vi.fn().mockReturnValue([{
                    ...mockApplication,
                    status: 7, // 已完成
                  }]),
                }),
              }),
            }),
          }),
        }),
      } as any)

      const request = new NextRequest('http://localhost:3000/api/checkout-applications/app-1/status', {
        method: 'PUT',
        body: JSON.stringify({ status: 1 }), // 尝试回到待审核状态
      })

      const response = await updateApplicationStatus(request, { params: { id: 'app-1' } })
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.code).toBe('INVALID_STATUS_TRANSITION')
    })
  })
})
