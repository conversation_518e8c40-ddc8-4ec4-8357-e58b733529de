/**
 * Zustand Store 测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useAuthStore } from '@/store/auth-store'
import { useAppStore } from '@/store/app-store'
import { useApiStore } from '@/store/api-store'

// Mock auth client
vi.mock('@/lib/auth-client', () => ({
  authClient: {
    signIn: {
      email: vi.fn(),
    },
    signOut: vi.fn(),
  },
}))

// Mock auth utils
vi.mock('@/lib/auth-utils', () => ({
  getUserPermissions: vi.fn().mockResolvedValue(['read:users', 'write:users']),
  getUserRole: vi
    .fn()
    .mockResolvedValue({ id: '1', name: 'admin', displayName: '管理员' }),
}))

describe('AuthStore', () => {
  beforeEach(() => {
    // 重置 store 状态
    useAuthStore.setState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      permissions: [],
      role: null,
    })
  })

  it('should set user correctly', () => {
    const { setUser } = useAuthStore.getState()
    const user = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
    }

    setUser(user)

    const state = useAuthStore.getState()
    expect(state.user).toEqual(user)
    expect(state.isAuthenticated).toBe(true)
  })

  it('should clear user on logout', () => {
    const { setUser } = useAuthStore.getState()
    const user = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
    }

    setUser(user)
    setUser(null)

    const state = useAuthStore.getState()
    expect(state.user).toBeNull()
    expect(state.isAuthenticated).toBe(false)
    expect(state.permissions).toEqual([])
    expect(state.role).toBeNull()
  })

  it('should check permissions correctly', () => {
    const {
      setPermissions,
      hasPermission,
      hasAnyPermission,
      hasAllPermissions,
    } = useAuthStore.getState()

    setPermissions(['read:users', 'write:users', 'delete:users'])

    expect(hasPermission('read:users')).toBe(true)
    expect(hasPermission('admin:system')).toBe(false)
    expect(hasAnyPermission(['read:users', 'admin:system'])).toBe(true)
    expect(hasAnyPermission(['admin:system', 'super:admin'])).toBe(false)
    expect(hasAllPermissions(['read:users', 'write:users'])).toBe(true)
    expect(hasAllPermissions(['read:users', 'admin:system'])).toBe(false)
  })

  it('should check roles correctly', () => {
    const { setRole, hasRole, hasAnyRole } = useAuthStore.getState()
    const role = { id: '1', name: 'admin', displayName: '管理员' }

    setRole(role)

    expect(hasRole('admin')).toBe(true)
    expect(hasRole('user')).toBe(false)
    expect(hasAnyRole(['admin', 'manager'])).toBe(true)
    expect(hasAnyRole(['user', 'guest'])).toBe(false)
  })
})

describe('AppStore', () => {
  beforeEach(() => {
    // 重置 store 状态
    useAppStore.setState({
      theme: 'system',
      sidebarCollapsed: false,
      notifications: [],
      loading: {},
      errors: {},
    })
  })

  it('should toggle sidebar correctly', () => {
    const { toggleSidebar, setSidebarCollapsed } = useAppStore.getState()

    toggleSidebar()
    expect(useAppStore.getState().sidebarCollapsed).toBe(true)

    toggleSidebar()
    expect(useAppStore.getState().sidebarCollapsed).toBe(false)

    setSidebarCollapsed(true)
    expect(useAppStore.getState().sidebarCollapsed).toBe(true)
  })

  it('should manage notifications correctly', () => {
    const { addNotification, removeNotification, clearNotifications } =
      useAppStore.getState()

    addNotification({
      type: 'success',
      title: 'Test Notification',
      message: 'This is a test',
    })

    let state = useAppStore.getState()
    expect(state.notifications).toHaveLength(1)
    expect(state.notifications[0].title).toBe('Test Notification')

    const notificationId = state.notifications[0].id
    removeNotification(notificationId)

    state = useAppStore.getState()
    expect(state.notifications).toHaveLength(0)

    // 添加多个通知
    addNotification({ type: 'info', title: 'Info 1' })
    addNotification({ type: 'warning', title: 'Warning 1' })

    state = useAppStore.getState()
    expect(state.notifications).toHaveLength(2)

    clearNotifications()
    state = useAppStore.getState()
    expect(state.notifications).toHaveLength(0)
  })

  it('should manage loading states correctly', () => {
    const { setLoading } = useAppStore.getState()

    setLoading('test-operation', true)
    let state = useAppStore.getState()
    expect(state.loading['test-operation']).toBe(true)

    setLoading('test-operation', false)
    state = useAppStore.getState()
    expect(state.loading['test-operation']).toBeUndefined()
  })

  it('should manage error states correctly', () => {
    const { setError, clearError, clearAllErrors } = useAppStore.getState()

    setError('test-operation', 'Test error message')
    let state = useAppStore.getState()
    expect(state.errors['test-operation']).toBe('Test error message')

    clearError('test-operation')
    state = useAppStore.getState()
    expect(state.errors['test-operation']).toBeUndefined()

    // 设置多个错误
    setError('operation1', 'Error 1')
    setError('operation2', 'Error 2')

    state = useAppStore.getState()
    expect(Object.keys(state.errors)).toHaveLength(2)

    clearAllErrors()
    state = useAppStore.getState()
    expect(Object.keys(state.errors)).toHaveLength(0)
  })
})

describe('ApiStore', () => {
  beforeEach(() => {
    // 重置 store 状态
    useApiStore.setState({
      cache: {},
      pendingRequests: [],
    })
  })

  it('should manage cache correctly', () => {
    const { setCache, getCache, clearCache } = useApiStore.getState()
    const testData = { id: 1, name: 'Test Data' }

    setCache('test-key', testData, 60000) // 1分钟过期

    let cachedData = getCache('test-key')
    expect(cachedData).toEqual(testData)

    clearCache('test-key')
    cachedData = getCache('test-key')
    expect(cachedData).toBeNull()
  })

  it('should handle cache expiry correctly', () => {
    const { setCache, getCache } = useApiStore.getState()
    const testData = { id: 1, name: 'Test Data' }

    // 设置已过期的缓存
    setCache('expired-key', testData, -1000) // 已过期

    const cachedData = getCache('expired-key')
    expect(cachedData).toBeNull()
  })

  it('should manage pending requests correctly', () => {
    const { addPendingRequest, removePendingRequest, isPending } =
      useApiStore.getState()

    addPendingRequest('test-request')
    expect(isPending('test-request')).toBe(true)

    removePendingRequest('test-request')
    expect(isPending('test-request')).toBe(false)
  })

  it('should clear all cache when no key provided', () => {
    const { setCache, clearCache, getCache } = useApiStore.getState()

    setCache('key1', 'data1')
    setCache('key2', 'data2')

    clearCache() // 清除所有缓存

    expect(getCache('key1')).toBeNull()
    expect(getCache('key2')).toBeNull()
  })
})
