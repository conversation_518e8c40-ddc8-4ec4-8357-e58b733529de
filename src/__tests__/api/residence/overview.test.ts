import { describe, it, expect, vi, beforeEach } from 'vitest'
import { NextRequest } from 'next/server'
import { GET } from '@/app/api/residence/overview/route'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    leftJoin: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    groupBy: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
  },
}))

vi.mock('@/lib/auth-utils', () => ({
  getCurrentUser: vi.fn(),
}))

vi.mock('drizzle-orm', () => ({
  eq: vi.fn(),
  and: vi.fn(),
  gte: vi.fn(),
  lte: vi.fn(),
  count: vi.fn(),
  sql: vi.fn(),
}))

describe('/api/residence/overview', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('GET', () => {
    it('should return 401 when user is not authenticated', async () => {
      const { getCurrentUser } = await import('@/lib/auth-utils')
      vi.mocked(getCurrentUser).mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/residence/overview')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.code).toBe('UNAUTHORIZED')
    })

    it('should return 400 for invalid query parameters', async () => {
      const { getCurrentUser } = await import('@/lib/auth-utils')
      vi.mocked(getCurrentUser).mockResolvedValue({ id: 'user1', email: '<EMAIL>' })

      const request = new NextRequest('http://localhost:3000/api/residence/overview?period=invalid')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.code).toBe('INVALID_PARAMS')
    })

    it('should return residence overview data successfully', async () => {
      const { getCurrentUser } = await import('@/lib/auth-utils')
      const { db } = await import('@/lib/db')

      vi.mocked(getCurrentUser).mockResolvedValue({ id: 'user1', email: '<EMAIL>' })

      // Mock database responses
      const mockRoomStats = [
        { status: 1, count: 50, totalCapacity: 100, currentOccupancy: 80 },
        { status: 2, count: 5, totalCapacity: 10, currentOccupancy: 0 },
      ]

      const mockOccupiedRooms = [{ count: 40 }]
      const mockTotalResidents = [{ count: 75 }]
      const mockRoomTypeStats = [
        { roomType: 1, count: 20, totalCapacity: 20, currentOccupancy: 18 },
        { roomType: 2, count: 30, totalCapacity: 60, currentOccupancy: 50 },
      ]

      const mockPendingReservations = [{ count: 5 }]
      const mockReservationStats = [
        { status: 1, count: 5 },
        { status: 2, count: 10 },
        { status: 3, count: 8 },
      ]

      const mockRecentAdmissions = [
        {
          id: '1',
          name: '张三',
          age: 75,
          gender: 1,
          roomNumber: '101',
          admissionDate: '2024-01-15',
          careLevel: 2,
        },
      ]

      // Setup mock chain
      vi.mocked(db.select).mockImplementation(() => {
        const mockQuery = {
          from: vi.fn().mockReturnThis(),
          leftJoin: vi.fn().mockReturnThis(),
          where: vi.fn().mockReturnThis(),
          groupBy: vi.fn().mockReturnThis(),
          orderBy: vi.fn().mockReturnThis(),
          limit: vi.fn().mockReturnThis(),
        }

        // Return different data based on the call context
        let callCount = 0
        const mockResults = [
          mockRoomStats,
          mockOccupiedRooms,
          mockTotalResidents,
          mockRoomTypeStats,
          mockPendingReservations,
          mockReservationStats,
          mockRecentAdmissions,
        ]

        mockQuery.from.mockImplementation(() => {
          const result = mockResults[callCount] || []
          callCount++
          return Promise.resolve(result)
        })

        mockQuery.where.mockImplementation(() => Promise.resolve(mockResults[callCount++] || []))
        mockQuery.groupBy.mockImplementation(() => Promise.resolve(mockResults[callCount++] || []))
        mockQuery.orderBy.mockImplementation(() => Promise.resolve(mockResults[callCount++] || []))
        mockQuery.limit.mockImplementation(() => Promise.resolve(mockResults[callCount++] || []))

        return mockQuery
      })

      const request = new NextRequest('http://localhost:3000/api/residence/overview?period=month')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveProperty('overview')
      expect(data.data).toHaveProperty('roomTypes')
      expect(data.data).toHaveProperty('reservations')
      expect(data.data).toHaveProperty('recentAdmissions')
      expect(data.data).toHaveProperty('trend')
      expect(data.data).toHaveProperty('dateRange')
    })

    it('should handle custom date range parameters', async () => {
      const { getCurrentUser } = await import('@/lib/auth-utils')
      vi.mocked(getCurrentUser).mockResolvedValue({ id: 'user1', email: '<EMAIL>' })

      const request = new NextRequest(
        'http://localhost:3000/api/residence/overview?startDate=2024-01-01&endDate=2024-01-31'
      )
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.dateRange).toEqual({
        start: '2024-01-01',
        end: '2024-01-31',
      })
    })

    it('should handle different period parameters', async () => {
      const { getCurrentUser } = await import('@/lib/auth-utils')
      vi.mocked(getCurrentUser).mockResolvedValue({ id: 'user1', email: '<EMAIL>' })

      const periods = ['week', 'month', 'quarter', 'year']

      for (const period of periods) {
        const request = new NextRequest(`http://localhost:3000/api/residence/overview?period=${period}`)
        const response = await GET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data).toHaveProperty('dateRange')
      }
    })

    it('should return 500 when database error occurs', async () => {
      const { getCurrentUser } = await import('@/lib/auth-utils')
      const { db } = await import('@/lib/db')

      vi.mocked(getCurrentUser).mockResolvedValue({ id: 'user1', email: '<EMAIL>' })
      vi.mocked(db.select).mockImplementation(() => {
        throw new Error('Database connection failed')
      })

      const request = new NextRequest('http://localhost:3000/api/residence/overview')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.code).toBe('INTERNAL_ERROR')
    })

    it('should calculate occupancy rate correctly', async () => {
      const { getCurrentUser } = await import('@/lib/auth-utils')
      const { db } = await import('@/lib/db')

      vi.mocked(getCurrentUser).mockResolvedValue({ id: 'user1', email: '<EMAIL>' })

      // Mock room stats with specific values for testing calculation
      const mockRoomStats = [
        { status: 1, count: 10, totalCapacity: 100, currentOccupancy: 75 },
      ]

      vi.mocked(db.select).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        groupBy: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        then: vi.fn().mockResolvedValue(mockRoomStats),
      }))

      const request = new NextRequest('http://localhost:3000/api/residence/overview')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.data.overview.occupancyRate).toBe(75) // 75/100 * 100 = 75%
    })

    it('should handle empty database results gracefully', async () => {
      const { getCurrentUser } = await import('@/lib/auth-utils')
      const { db } = await import('@/lib/db')

      vi.mocked(getCurrentUser).mockResolvedValue({ id: 'user1', email: '<EMAIL>' })

      // Mock empty results
      vi.mocked(db.select).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        leftJoin: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        groupBy: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        then: vi.fn().mockResolvedValue([]),
      }))

      const request = new NextRequest('http://localhost:3000/api/residence/overview')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.overview.totalRooms).toBe(0)
      expect(data.data.overview.occupancyRate).toBe(0)
    })
  })
})
