import { describe, it, expect, beforeEach } from '@jest/globals'
import { NextRequest } from 'next/server'

// 模拟用户认证
jest.mock('@/lib/auth-utils', () => ({
  getCurrentUser: jest.fn(() => Promise.resolve({
    id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
  })),
}))

// 模拟数据库
jest.mock('@/lib/db', () => ({
  db: {
    select: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    transaction: jest.fn(),
  },
}))

// API 路由处理器
import { GET as getApplications, POST as createApplication } from '@/app/api/admission-applications/route'
import { GET as getAssessments, POST as createAssessment } from '@/app/api/admission-assessments/route'
import { GET as getReviews, POST as createReview } from '@/app/api/admission-reviews/route'
import { GET as getContracts, POST as createContract } from '@/app/api/admission-contracts/route'
import { GET as getPayments, POST as createPayment } from '@/app/api/admission-payments/route'
import { GET as getRoomAssignments, POST as createRoomAssignment } from '@/app/api/room-assignments/route'

describe('入住管理 API 测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('入住申请 API', () => {
    it('GET /api/admission-applications - 应该返回申请列表', async () => {
      const { db } = require('@/lib/db')
      
      // 模拟数据库查询
      db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          leftJoin: jest.fn().mockReturnValue({
            where: jest.fn().mockReturnValue({
              orderBy: jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                  offset: jest.fn().mockResolvedValue([
                    {
                      id: 'app-1',
                      applicationNumber: 'APP20240101001',
                      elderName: '张三',
                      status: 1,
                      createdAt: new Date(),
                    },
                  ]),
                }),
              }),
            }),
          }),
        }),
      })

      // 模拟总数查询
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([{ count: 1 }]),
        }),
      })

      const request = new NextRequest('http://localhost/api/admission-applications?page=1&limit=10')
      const response = await getApplications(request)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.data.list).toHaveLength(1)
      expect(result.data.list[0].elderName).toBe('张三')
    })

    it('POST /api/admission-applications - 应该创建新申请', async () => {
      const { db } = require('@/lib/db')
      
      db.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn().mockResolvedValue([
            {
              id: 'new-app-id',
              applicationNumber: 'APP20240101002',
              elderName: '李四',
              status: 1,
            },
          ]),
        }),
      })

      const applicationData = {
        elderName: '李四',
        elderAge: 75,
        elderGender: 1,
        elderIdCard: '110101194801011234',
        elderPhone: '13800138001',
        applicantName: '李小明',
        applicantRelation: '儿子',
        applicantPhone: '13800138002',
        applicantIdCard: '110101197001011234',
        applicantAddress: '北京市朝阳区测试街道1号',
        emergencyContact: '王五',
        emergencyPhone: '13800138003',
        emergencyRelation: '女儿',
        healthCondition: '身体健康',
        medicalHistory: '无',
        currentMedications: '无',
        careNeeds: '日常生活照料',
        expectedAdmissionDate: '2024-02-01',
      }

      const request = new NextRequest('http://localhost/api/admission-applications', {
        method: 'POST',
        body: JSON.stringify(applicationData),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await createApplication(request)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.data.elderName).toBe('李四')
    })

    it('POST /api/admission-applications - 应该验证必填字段', async () => {
      const invalidData = {
        elderName: '', // 空字段
        elderAge: 75,
      }

      const request = new NextRequest('http://localhost/api/admission-applications', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await createApplication(request)
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.code).toBe('INVALID_DATA')
    })
  })

  describe('评估管理 API', () => {
    it('POST /api/admission-assessments - 应该创建评估记录', async () => {
      const { db } = require('@/lib/db')
      
      // 模拟申请存在检查
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([
              { id: 'app-1', status: 2 }, // 评估中
            ]),
          }),
        }),
      })

      // 模拟创建评估
      db.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn().mockResolvedValue([
            {
              id: 'assessment-1',
              applicationId: 'app-1',
              assessmentType: 1,
              overallScore: 75,
            },
          ]),
        }),
      })

      const assessmentData = {
        applicationId: 'app-1',
        assessmentType: 1,
        physicalCondition: 2,
        mentalCondition: 1,
        mobilityLevel: 2,
        careLevel: 2,
        nursingNeeds: ['日常护理'],
        fallRisk: 2,
        cognitiveRisk: 1,
        behaviorRisk: 1,
        recommendations: '适合入住',
      }

      const request = new NextRequest('http://localhost/api/admission-assessments', {
        method: 'POST',
        body: JSON.stringify(assessmentData),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await createAssessment(request)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.data.overallScore).toBe(75)
    })
  })

  describe('审核管理 API', () => {
    it('POST /api/admission-reviews - 应该创建审核记录', async () => {
      const { db } = require('@/lib/db')
      
      // 模拟申请存在检查
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([
              { id: 'app-1', status: 3 }, // 待审核
            ]),
          }),
        }),
      })

      // 模拟现有审核检查
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            orderBy: jest.fn().mockResolvedValue([]),
          }),
        }),
      })

      // 模拟事务
      db.transaction.mockImplementation(async (callback) => {
        const mockTx = {
          insert: jest.fn().mockReturnValue({
            values: jest.fn().mockReturnValue({
              returning: jest.fn().mockResolvedValue([
                {
                  id: 'review-1',
                  applicationId: 'app-1',
                  reviewLevel: 1,
                  reviewResult: 1,
                },
              ]),
            }),
          }),
          update: jest.fn().mockReturnValue({
            set: jest.fn().mockReturnValue({
              where: jest.fn().mockResolvedValue([]),
            }),
          }),
        }
        return callback(mockTx)
      })

      const reviewData = {
        applicationId: 'app-1',
        reviewLevel: 1,
        reviewResult: 1,
        reviewComments: '初审通过',
      }

      const request = new NextRequest('http://localhost/api/admission-reviews', {
        method: 'POST',
        body: JSON.stringify(reviewData),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await createReview(request)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
    })
  })

  describe('合同管理 API', () => {
    it('POST /api/admission-contracts - 应该创建合同', async () => {
      const { db } = require('@/lib/db')
      
      // 模拟申请存在检查
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([
              { id: 'app-1', status: 5 }, // 已通过
            ]),
          }),
        }),
      })

      // 模拟现有合同检查
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([]),
          }),
        }),
      })

      // 模拟创建合同
      db.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn().mockResolvedValue([
            {
              id: 'contract-1',
              contractNumber: 'CON20240101001',
              applicationId: 'app-1',
              contractTitle: '养老服务合同',
            },
          ]),
        }),
      })

      const contractData = {
        applicationId: 'app-1',
        contractType: 1,
        contractTitle: '养老服务合同',
        partyA: '测试养老院',
        partyB: '张小明',
        elderName: '张三',
        feeStructure: {
          monthlyFee: 3000,
          deposit: 6000,
        },
      }

      const request = new NextRequest('http://localhost/api/admission-contracts', {
        method: 'POST',
        body: JSON.stringify(contractData),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await createContract(request)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.data.contractTitle).toBe('养老服务合同')
    })
  })

  describe('缴费管理 API', () => {
    it('POST /api/admission-payments - 应该创建缴费记录', async () => {
      const { db } = require('@/lib/db')
      
      // 模拟申请存在检查
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([
              { id: 'app-1', status: 5 },
            ]),
          }),
        }),
      })

      // 模拟创建缴费记录
      db.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn().mockResolvedValue([
            {
              id: 'payment-1',
              paymentNumber: 'PAY20240101001',
              applicationId: 'app-1',
              amount: 3000,
            },
          ]),
        }),
      })

      const paymentData = {
        applicationId: 'app-1',
        paymentType: 2, // 月费
        amount: 3000,
        paymentMethod: 2, // 银行转账
        paymentDate: '2024-01-25',
        description: '首月费用',
      }

      const request = new NextRequest('http://localhost/api/admission-payments', {
        method: 'POST',
        body: JSON.stringify(paymentData),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await createPayment(request)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.data.amount).toBe(3000)
    })
  })

  describe('房间分配 API', () => {
    it('POST /api/room-assignments - 应该创建房间分配', async () => {
      const { db } = require('@/lib/db')
      
      // 模拟申请存在检查
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([
              { id: 'app-1', status: 5 },
            ]),
          }),
        }),
      })

      // 模拟房间存在检查
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([
              { id: 'room-1', status: 1 }, // 可用
            ]),
          }),
        }),
      })

      // 模拟现有分配检查
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([]),
          }),
        }),
      })

      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([]),
          }),
        }),
      })

      // 模拟事务
      db.transaction.mockImplementation(async (callback) => {
        const mockTx = {
          insert: jest.fn().mockReturnValue({
            values: jest.fn().mockResolvedValue([]),
          }),
          update: jest.fn().mockReturnValue({
            set: jest.fn().mockReturnValue({
              where: jest.fn().mockResolvedValue([]),
            }),
          }),
        }
        return callback(mockTx)
      })

      const assignmentData = {
        applicationId: 'app-1',
        roomId: 'room-1',
        assignmentDate: '2024-01-30',
        moveInDate: '2024-02-01',
      }

      const request = new NextRequest('http://localhost/api/room-assignments', {
        method: 'POST',
        body: JSON.stringify(assignmentData),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await createRoomAssignment(request)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
    })
  })
})
