import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { NextRequest } from 'next/server'
import { GET } from '@/app/api/statistics/sales-performance/route'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn(),
    selectDistinct: vi.fn(),
  },
}))

vi.mock('@/lib/auth-utils', () => ({
  getCurrentUser: vi.fn(),
}))

vi.mock('drizzle-orm', () => ({
  eq: vi.fn(),
  and: vi.fn(),
  gte: vi.fn(),
  lte: vi.fn(),
  count: vi.fn(),
  sql: vi.fn(),
}))

describe('/api/statistics/sales-performance', () => {
  const mockUser = {
    id: 'user-1',
    name: '测试用户',
    email: '<EMAIL>',
  }

  const mockDbResponse = {
    from: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    leftJoin: vi.fn().mockReturnThis(),
    groupBy: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock getCurrentUser
    const { getCurrentUser } = require('@/lib/auth-utils')
    getCurrentUser.mockResolvedValue(mockUser)

    // Mock database queries
    const { db } = require('@/lib/db')
    db.select.mockReturnValue(mockDbResponse)
    db.selectDistinct.mockReturnValue(mockDbResponse)
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('GET /api/statistics/sales-performance', () => {
    it('应该成功返回销售业绩统计数据', async () => {
      // Mock database responses
      mockDbResponse.from.mockImplementation(() => {
        const query = mockDbResponse.from.mock.calls[mockDbResponse.from.mock.calls.length - 1]
        
        // Mock different responses based on the query context
        if (mockDbResponse.from.mock.calls.length === 1) {
          // Total consultations query
          return Promise.resolve([{ count: 150 }])
        } else if (mockDbResponse.from.mock.calls.length === 2) {
          // Signed consultations query
          return Promise.resolve([{ count: 45 }])
        } else if (mockDbResponse.from.mock.calls.length === 3) {
          // Follow-ups query
          return Promise.resolve([{ count: 320 }])
        } else if (mockDbResponse.from.mock.calls.length === 4) {
          // Active sales count query
          return Promise.resolve([
            { userId: 'user-1' },
            { userId: 'user-2' },
            { userId: 'user-3' },
          ])
        } else if (mockDbResponse.from.mock.calls.length === 5) {
          // Sales ranking query
          return Promise.resolve([
            {
              userId: 'user-1',
              userName: '张三',
              userEmail: '<EMAIL>',
              consultationCount: 25,
              signedCount: 8,
            },
            {
              userId: 'user-2',
              userName: '李四',
              userEmail: '<EMAIL>',
              consultationCount: 20,
              signedCount: 6,
            },
          ])
        } else if (mockDbResponse.from.mock.calls.length === 6) {
          // Follow-up counts query
          return Promise.resolve([
            { userId: 'user-1', followUpCount: 45 },
            { userId: 'user-2', followUpCount: 38 },
          ])
        } else {
          // Trend data query
          return Promise.resolve([
            {
              date: '2024-01-01',
              consultationCount: 10,
              signedCount: 3,
            },
            {
              date: '2024-01-02',
              consultationCount: 12,
              signedCount: 4,
            },
          ])
        }
      })

      const request = new NextRequest('http://localhost:3000/api/statistics/sales-performance?period=month')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toBeDefined()
      expect(data.data.overview).toBeDefined()
      expect(data.data.ranking).toBeDefined()
      expect(data.data.trend).toBeDefined()
      expect(data.message).toBe('销售业绩统计获取成功')
    })

    it('应该处理带有日期范围的查询', async () => {
      mockDbResponse.from.mockResolvedValue([{ count: 100 }])

      const request = new NextRequest(
        'http://localhost:3000/api/statistics/sales-performance?startDate=2024-01-01&endDate=2024-01-31'
      )
      const response = await GET(request)

      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.success).toBe(true)
    })

    it('应该处理特定用户的查询', async () => {
      mockDbResponse.from.mockResolvedValue([{ count: 50 }])

      const request = new NextRequest(
        'http://localhost:3000/api/statistics/sales-performance?userId=user-1'
      )
      const response = await GET(request)

      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.success).toBe(true)
    })

    it('应该处理无效的查询参数', async () => {
      const request = new NextRequest(
        'http://localhost:3000/api/statistics/sales-performance?period=invalid'
      )
      const response = await GET(request)

      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.success).toBe(false)
      expect(data.error).toBe('请求参数格式错误')
      expect(data.code).toBe('INVALID_PARAMS')
    })

    it('应该处理未授权的访问', async () => {
      const { getCurrentUser } = require('@/lib/auth-utils')
      getCurrentUser.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/statistics/sales-performance')
      const response = await GET(request)

      expect(response.status).toBe(401)
      const data = await response.json()
      expect(data.success).toBe(false)
      expect(data.error).toBe('未授权访问')
      expect(data.code).toBe('UNAUTHORIZED')
    })

    it('应该处理数据库错误', async () => {
      const { getCurrentUser } = require('@/lib/auth-utils')
      getCurrentUser.mockResolvedValue(mockUser)

      mockDbResponse.from.mockRejectedValue(new Error('Database connection failed'))

      const request = new NextRequest('http://localhost:3000/api/statistics/sales-performance')
      const response = await GET(request)

      expect(response.status).toBe(500)
      const data = await response.json()
      expect(data.success).toBe(false)
      expect(data.error).toBe('服务器内部错误')
      expect(data.code).toBe('INTERNAL_ERROR')
    })

    it('应该正确计算时间范围', async () => {
      mockDbResponse.from.mockResolvedValue([{ count: 0 }])

      // Test week period
      const weekRequest = new NextRequest(
        'http://localhost:3000/api/statistics/sales-performance?period=week'
      )
      const weekResponse = await GET(weekRequest)
      expect(weekResponse.status).toBe(200)

      // Test quarter period
      const quarterRequest = new NextRequest(
        'http://localhost:3000/api/statistics/sales-performance?period=quarter'
      )
      const quarterResponse = await GET(quarterRequest)
      expect(quarterResponse.status).toBe(200)

      // Test year period
      const yearRequest = new NextRequest(
        'http://localhost:3000/api/statistics/sales-performance?period=year'
      )
      const yearResponse = await GET(yearRequest)
      expect(yearResponse.status).toBe(200)
    })

    it('应该正确处理空数据', async () => {
      // Mock empty responses
      mockDbResponse.from.mockImplementation(() => {
        const callCount = mockDbResponse.from.mock.calls.length
        
        if (callCount <= 4) {
          return Promise.resolve([{ count: 0 }])
        } else if (callCount <= 6) {
          return Promise.resolve([])
        } else {
          return Promise.resolve([])
        }
      })

      const request = new NextRequest('http://localhost:3000/api/statistics/sales-performance')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.overview.totalConsultations).toBe(0)
      expect(data.data.overview.conversionRate).toBe(0)
      expect(data.data.ranking).toEqual([])
      expect(data.data.trend).toEqual([])
    })

    it('应该正确计算转化率', async () => {
      // Mock responses with specific data for conversion rate calculation
      mockDbResponse.from.mockImplementation(() => {
        const callCount = mockDbResponse.from.mock.calls.length
        
        if (callCount === 1) {
          // Total consultations: 100
          return Promise.resolve([{ count: 100 }])
        } else if (callCount === 2) {
          // Signed consultations: 30
          return Promise.resolve([{ count: 30 }])
        } else if (callCount === 3) {
          // Follow-ups
          return Promise.resolve([{ count: 150 }])
        } else if (callCount === 4) {
          // Active sales count
          return Promise.resolve([{ userId: 'user-1' }, { userId: 'user-2' }])
        } else if (callCount === 5) {
          // Sales ranking with conversion rates
          return Promise.resolve([
            {
              userId: 'user-1',
              userName: '张三',
              userEmail: '<EMAIL>',
              consultationCount: 60,
              signedCount: 20,
            },
            {
              userId: 'user-2',
              userName: '李四',
              userEmail: '<EMAIL>',
              consultationCount: 40,
              signedCount: 10,
            },
          ])
        } else {
          return Promise.resolve([])
        }
      })

      const request = new NextRequest('http://localhost:3000/api/statistics/sales-performance')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.data.overview.conversionRate).toBe(30.0) // 30/100 * 100
      
      // Check individual conversion rates
      const ranking = data.data.ranking
      expect(ranking[0].conversionRate).toBe('33.33') // 20/60 * 100
      expect(ranking[1].conversionRate).toBe('25.00') // 10/40 * 100
    })
  })
})
