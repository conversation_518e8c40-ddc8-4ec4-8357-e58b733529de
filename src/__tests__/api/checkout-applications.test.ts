import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/checkout-applications/route'
import { db } from '@/lib/db'
import { checkoutApplications, elderInfo, betterAuthUsers } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    transaction: vi.fn(),
  },
}))

vi.mock('@/lib/auth-utils', () => ({
  getCurrentUser: vi.fn(),
}))

vi.mock('date-fns', () => ({
  format: vi.fn(() => '20240127'),
  differenceInDays: vi.fn(() => 30),
}))

const mockUser = {
  id: 'user-1',
  name: 'Test User',
  email: '<EMAIL>',
}

const mockElder = {
  id: 'elder-1',
  name: '张三',
  age: 75,
  gender: '男',
  idCard: '110101194901010001',
  careLevel: '中度护理',
  status: 1,
}

const mockApplication = {
  id: 'app-1',
  applicationNumber: 'CO20240127001',
  elderInfoId: 'elder-1',
  applicantName: '李四',
  applicantPhone: '13800138000',
  applicantRelation: '子女',
  checkoutReason: '家庭原因',
  checkoutType: 1,
  expectedCheckoutDate: '2024-02-01',
  status: 1,
  applicationDate: '2024-01-27',
  createdBy: 'user-1',
  createdAt: new Date(),
  updatedAt: new Date(),
}

describe('/api/checkout-applications', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(getCurrentUser).mockResolvedValue(mockUser)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('GET /api/checkout-applications', () => {
    it('should return checkout applications list successfully', async () => {
      // Mock database queries
      const mockCountResult = [{ count: 1 }]
      const mockApplications = [
        {
          ...mockApplication,
          elder: mockElder,
          createdBy: mockUser,
        },
      ]

      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              leftJoin: vi.fn().mockReturnValue({
                where: vi.fn().mockReturnValue(mockCountResult),
              }),
            }),
          }),
        }),
      } as any)

      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              leftJoin: vi.fn().mockReturnValue({
                leftJoin: vi.fn().mockReturnValue({
                  where: vi.fn().mockReturnValue({
                    orderBy: vi.fn().mockReturnValue({
                      limit: vi.fn().mockReturnValue({
                        offset: vi.fn().mockReturnValue(mockApplications),
                      }),
                    }),
                  }),
                }),
              }),
            }),
          }),
        }),
      } as any)

      const request = new NextRequest('http://localhost:3000/api/checkout-applications?page=1&limit=10')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.list).toHaveLength(1)
      expect(data.data.list[0].applicationNumber).toBe('CO20240127001')
      expect(data.data.pagination.total).toBe(1)
    })

    it('should return 401 when user is not authenticated', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/checkout-applications')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.code).toBe('UNAUTHORIZED')
    })

    it('should handle search and filter parameters', async () => {
      const mockCountResult = [{ count: 0 }]
      const mockApplications: any[] = []

      vi.mocked(db.select).mockReturnValue({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              leftJoin: vi.fn().mockReturnValue({
                where: vi.fn().mockReturnValue(mockCountResult),
              }),
            }),
          }),
        }),
      } as any)

      vi.mocked(db.select).mockReturnValue({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              leftJoin: vi.fn().mockReturnValue({
                leftJoin: vi.fn().mockReturnValue({
                  where: vi.fn().mockReturnValue({
                    orderBy: vi.fn().mockReturnValue({
                      limit: vi.fn().mockReturnValue({
                        offset: vi.fn().mockReturnValue(mockApplications),
                      }),
                    }),
                  }),
                }),
              }),
            }),
          }),
        }),
      } as any)

      const request = new NextRequest('http://localhost:3000/api/checkout-applications?search=张三&status=1&checkoutType=1')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
    })
  })

  describe('POST /api/checkout-applications', () => {
    const validApplicationData = {
      elderInfoId: 'elder-1',
      applicantName: '李四',
      applicantPhone: '13800138000',
      applicantRelation: '子女',
      checkoutReason: '家庭原因',
      checkoutType: 1,
      expectedCheckoutDate: '2024-02-01',
      notes: '备注信息',
    }

    it('should create checkout application successfully', async () => {
      // Mock elder exists and is in residence
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([{ ...mockElder, status: 1 }]),
          }),
        }),
      } as any)

      // Mock no existing application
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([]),
          }),
        }),
      } as any)

      // Mock application number generation
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue([{ count: 0 }]),
        }),
      } as any)

      // Mock insert
      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockReturnValue([mockApplication]),
        }),
      } as any)

      const request = new NextRequest('http://localhost:3000/api/checkout-applications', {
        method: 'POST',
        body: JSON.stringify(validApplicationData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.applicationNumber).toBe('CO20240127001')
      expect(data.message).toBe('退住申请创建成功')
    })

    it('should return 400 when elder does not exist', async () => {
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([]),
          }),
        }),
      } as any)

      const request = new NextRequest('http://localhost:3000/api/checkout-applications', {
        method: 'POST',
        body: JSON.stringify(validApplicationData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.code).toBe('ELDER_NOT_FOUND')
    })

    it('should return 400 when elder is not in residence', async () => {
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([{ ...mockElder, status: 2 }]), // 已退住
          }),
        }),
      } as any)

      const request = new NextRequest('http://localhost:3000/api/checkout-applications', {
        method: 'POST',
        body: JSON.stringify(validApplicationData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.code).toBe('ELDER_NOT_IN_RESIDENCE')
    })

    it('should return 400 when elder already has pending application', async () => {
      // Mock elder exists and is in residence
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([{ ...mockElder, status: 1 }]),
          }),
        }),
      } as any)

      // Mock existing application
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([mockApplication]),
          }),
        }),
      } as any)

      const request = new NextRequest('http://localhost:3000/api/checkout-applications', {
        method: 'POST',
        body: JSON.stringify(validApplicationData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.code).toBe('APPLICATION_EXISTS')
    })

    it('should return 400 when validation fails', async () => {
      const invalidData = {
        elderInfoId: '', // 无效的 elderInfoId
        applicantName: '',
        applicantPhone: '',
        applicantRelation: '',
        checkoutReason: '',
        checkoutType: 0, // 无效的 checkoutType
        expectedCheckoutDate: '',
      }

      const request = new NextRequest('http://localhost:3000/api/checkout-applications', {
        method: 'POST',
        body: JSON.stringify(invalidData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.code).toBe('VALIDATION_ERROR')
      expect(data.errors).toBeDefined()
    })

    it('should return 401 when user is not authenticated', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/checkout-applications', {
        method: 'POST',
        body: JSON.stringify(validApplicationData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.code).toBe('UNAUTHORIZED')
    })

    it('should handle database errors gracefully', async () => {
      vi.mocked(db.select).mockImplementation(() => {
        throw new Error('Database connection failed')
      })

      const request = new NextRequest('http://localhost:3000/api/checkout-applications', {
        method: 'POST',
        body: JSON.stringify(validApplicationData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.code).toBe('INTERNAL_ERROR')
    })
  })
})
