import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/reservations/route'
import { GET as getById, PUT, DELETE } from '@/app/api/reservations/[id]/route'
import {
  GET as getAvailability,
  POST as checkConflict,
} from '@/app/api/reservations/availability/route'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
}))

vi.mock('@/lib/auth-utils', () => ({
  getCurrentUser: vi.fn(),
}))

vi.mock('@/lib/db/schema', () => ({
  reservations: {
    id: 'id',
    reservationNumber: 'reservationNumber',
    roomId: 'roomId',
    status: 'status',
    contactName: 'contactName',
    contactPhone: 'contactPhone',
    expectedCheckInDate: 'expectedCheckInDate',
    createdBy: 'createdBy',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  rooms: {
    id: 'id',
    roomNumber: 'roomNumber',
    roomType: 'roomType',
    status: 'status',
    capacity: 'capacity',
    currentOccupancy: 'currentOccupancy',
  },
  elderInfo: {
    id: 'id',
    name: 'name',
    age: 'age',
    gender: 'gender',
  },
  betterAuthUsers: {
    id: 'id',
    name: 'name',
    email: 'email',
  },
}))

const { db } = await import('@/lib/db')
const { getCurrentUser } = await import('@/lib/auth-utils')

describe('Reservations API', () => {
  const mockUser = {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
  }

  const mockReservation = {
    id: 'reservation-1',
    reservationNumber: 'RES20241201123456',
    roomId: 'room-1',
    elderInfoId: 'elder-1',
    expectedCheckInDate: '2024-12-15',
    expectedCheckOutDate: '2024-12-20',
    status: 1,
    reservationType: 1,
    contactName: '张三',
    contactPhone: '13800138000',
    contactRelation: '子女',
    specialRequirements: '需要轮椅',
    depositAmount: 1000,
    depositPaid: false,
    notes: '测试备注',
    createdBy: 'user-1',
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  const mockRoom = {
    id: 'room-1',
    roomNumber: '101',
    roomType: 1,
    floor: 1,
    capacity: 2,
    currentOccupancy: 0,
    monthlyRate: 3000,
    status: 1,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(getCurrentUser).mockResolvedValue(mockUser)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('GET /api/reservations', () => {
    it('should return reservations list successfully', async () => {
      // Mock database responses
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            where: vi.fn().mockResolvedValue([{ count: 1 }]),
          }),
        }),
      } as any)

      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              leftJoin: vi.fn().mockReturnValue({
                where: vi.fn().mockReturnValue({
                  orderBy: vi.fn().mockReturnValue({
                    limit: vi.fn().mockReturnValue({
                      offset: vi.fn().mockResolvedValue([
                        {
                          ...mockReservation,
                          elderInfo: {
                            id: 'elder-1',
                            name: '李老太',
                            age: 75,
                            gender: 2,
                          },
                          room: mockRoom,
                          createdByUser: mockUser,
                        },
                      ]),
                    }),
                  }),
                }),
              }),
            }),
          }),
        }),
      } as any)

      const request = new NextRequest(
        'http://localhost:3000/api/reservations?page=1&limit=10'
      )
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.list).toHaveLength(1)
      expect(data.data.list[0].reservationNumber).toBe(
        mockReservation.reservationNumber
      )
      expect(data.data.pagination).toBeDefined()
    })

    it('should return 401 when user is not authenticated', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/reservations')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.code).toBe('UNAUTHORIZED')
    })

    it('should handle search and filter parameters', async () => {
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            where: vi.fn().mockResolvedValue([{ count: 0 }]),
          }),
        }),
      } as any)

      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              leftJoin: vi.fn().mockReturnValue({
                where: vi.fn().mockReturnValue({
                  orderBy: vi.fn().mockReturnValue({
                    limit: vi.fn().mockReturnValue({
                      offset: vi.fn().mockResolvedValue([]),
                    }),
                  }),
                }),
              }),
            }),
          }),
        }),
      } as any)

      const request = new NextRequest(
        'http://localhost:3000/api/reservations?search=张三&status=1&startDate=2024-12-01&endDate=2024-12-31'
      )
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
    })
  })

  describe('POST /api/reservations', () => {
    it('should create reservation successfully', async () => {
      const createData = {
        roomId: 'room-1',
        expectedCheckInDate: '2024-12-15',
        expectedCheckOutDate: '2024-12-20',
        reservationType: 1,
        contactName: '张三',
        contactPhone: '13800138000',
        depositAmount: 1000,
      }

      // Mock room check
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([mockRoom]),
          }),
        }),
      } as any)

      // Mock reservation creation
      vi.mocked(db.insert).mockReturnValueOnce({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([mockReservation]),
        }),
      } as any)

      const request = new NextRequest(
        'http://localhost:3000/api/reservations',
        {
          method: 'POST',
          body: JSON.stringify(createData),
        }
      )
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.contactName).toBe(createData.contactName)
    })

    it('should return 404 when room not found', async () => {
      const createData = {
        roomId: 'nonexistent-room',
        expectedCheckInDate: '2024-12-15',
        contactName: '张三',
        contactPhone: '13800138000',
      }

      // Mock room not found
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([]),
          }),
        }),
      } as any)

      const request = new NextRequest(
        'http://localhost:3000/api/reservations',
        {
          method: 'POST',
          body: JSON.stringify(createData),
        }
      )
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.code).toBe('ROOM_NOT_FOUND')
    })

    it('should return 400 when room is unavailable', async () => {
      const createData = {
        roomId: 'room-1',
        expectedCheckInDate: '2024-12-15',
        contactName: '张三',
        contactPhone: '13800138000',
      }

      // Mock unavailable room
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([{ ...mockRoom, status: 2 }]),
          }),
        }),
      } as any)

      const request = new NextRequest(
        'http://localhost:3000/api/reservations',
        {
          method: 'POST',
          body: JSON.stringify(createData),
        }
      )
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.code).toBe('ROOM_UNAVAILABLE')
    })

    it('should validate required fields', async () => {
      const invalidData = {
        roomId: '',
        contactName: '',
        contactPhone: 'invalid-phone',
      }

      const request = new NextRequest(
        'http://localhost:3000/api/reservations',
        {
          method: 'POST',
          body: JSON.stringify(invalidData),
        }
      )
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.code).toBe('INVALID_DATA')
      expect(data.details).toBeDefined()
    })
  })

  describe('GET /api/reservations/[id]', () => {
    it('should return reservation details successfully', async () => {
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              leftJoin: vi.fn().mockReturnValue({
                where: vi.fn().mockReturnValue({
                  limit: vi.fn().mockResolvedValue([
                    {
                      ...mockReservation,
                      elderInfo: {
                        id: 'elder-1',
                        name: '李老太',
                        age: 75,
                        gender: 2,
                        careLevel: 1,
                        healthStatus: 1,
                      },
                      room: { ...mockRoom, facilities: '["空调", "电视"]' },
                      createdByUser: mockUser,
                    },
                  ]),
                }),
              }),
            }),
          }),
        }),
      } as any)

      const response = await getById(
        new NextRequest('http://localhost:3000/api/reservations/reservation-1'),
        { params: { id: 'reservation-1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.id).toBe('reservation-1')
      expect(data.data.room.facilities).toEqual(['空调', '电视'])
    })

    it('should return 404 when reservation not found', async () => {
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              leftJoin: vi.fn().mockReturnValue({
                where: vi.fn().mockReturnValue({
                  limit: vi.fn().mockResolvedValue([]),
                }),
              }),
            }),
          }),
        }),
      } as any)

      const response = await getById(
        new NextRequest('http://localhost:3000/api/reservations/nonexistent'),
        { params: { id: 'nonexistent' } }
      )
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.code).toBe('RESERVATION_NOT_FOUND')
    })
  })

  describe('PUT /api/reservations/[id]', () => {
    it('should update reservation successfully', async () => {
      const updateData = {
        contactName: '张三更新',
        status: 2,
      }

      // Mock existing reservation
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([mockReservation]),
          }),
        }),
      } as any)

      // Mock update
      vi.mocked(db.update).mockReturnValueOnce({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi
              .fn()
              .mockResolvedValue([{ ...mockReservation, ...updateData }]),
          }),
        }),
      } as any)

      const response = await PUT(
        new NextRequest(
          'http://localhost:3000/api/reservations/reservation-1',
          {
            method: 'PUT',
            body: JSON.stringify(updateData),
          }
        ),
        { params: { id: 'reservation-1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.contactName).toBe(updateData.contactName)
    })
  })

  describe('DELETE /api/reservations/[id]', () => {
    it('should delete reservation successfully', async () => {
      // Mock existing reservation
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([mockReservation]),
          }),
        }),
      } as any)

      // Mock delete
      vi.mocked(db.delete).mockReturnValueOnce({
        where: vi.fn().mockResolvedValue(undefined),
      } as any)

      const response = await DELETE(
        new NextRequest('http://localhost:3000/api/reservations/reservation-1'),
        { params: { id: 'reservation-1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
    })

    it('should not delete checked-in reservation', async () => {
      // Mock checked-in reservation
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi
              .fn()
              .mockResolvedValue([{ ...mockReservation, status: 3 }]),
          }),
        }),
      } as any)

      const response = await DELETE(
        new NextRequest('http://localhost:3000/api/reservations/reservation-1'),
        { params: { id: 'reservation-1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.code).toBe('CANNOT_DELETE_CHECKED_IN')
    })
  })

  describe('Room Availability API', () => {
    it('should check room availability successfully', async () => {
      const mockAvailableRooms = [
        {
          ...mockRoom,
          isAvailable: true,
          hasReservationConflict: false,
          isRoomFull: false,
          availableBeds: 2,
          facilities: [],
        },
      ]

      // Mock room query
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockResolvedValue([mockRoom]),
        }),
      } as any)

      const request = new NextRequest(
        'http://localhost:3000/api/reservations/availability?checkInDate=2024-12-15&checkOutDate=2024-12-20'
      )
      const response = await getAvailability(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.rooms).toBeDefined()
      expect(data.data.summary).toBeDefined()
    })

    it('should check reservation conflict successfully', async () => {
      const conflictData = {
        roomId: 'room-1',
        checkInDate: '2024-12-15',
        checkOutDate: '2024-12-20',
      }

      // Mock room check
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([mockRoom]),
          }),
        }),
      } as any)

      const request = new NextRequest(
        'http://localhost:3000/api/reservations/availability',
        {
          method: 'POST',
          body: JSON.stringify(conflictData),
        }
      )
      const response = await checkConflict(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.hasConflict).toBeDefined()
      expect(data.data.room).toBeDefined()
    })

    it('should return 404 when room not found for conflict check', async () => {
      const conflictData = {
        roomId: 'nonexistent-room',
        checkInDate: '2024-12-15',
      }

      // Mock room not found
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([]),
          }),
        }),
      } as any)

      const request = new NextRequest(
        'http://localhost:3000/api/reservations/availability',
        {
          method: 'POST',
          body: JSON.stringify(conflictData),
        }
      )
      const response = await checkConflict(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.code).toBe('ROOM_NOT_FOUND')
    })
  })
})
