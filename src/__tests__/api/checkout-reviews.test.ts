import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/checkout-reviews/route'
import { db } from '@/lib/db'
import { checkoutReviews, checkoutApplications } from '@/lib/db/schema'
import { getCurrentUser } from '@/lib/auth-utils'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    transaction: vi.fn(),
  },
}))

vi.mock('@/lib/auth-utils', () => ({
  getCurrentUser: vi.fn(),
}))

const mockUser = {
  id: 'user-1',
  name: 'Test User',
  email: '<EMAIL>',
}

const mockApplication = {
  id: 'app-1',
  applicationNumber: 'CO20240127001',
  elderInfoId: 'elder-1',
  applicantName: '李四',
  applicantPhone: '13800138000',
  applicantRelation: '子女',
  checkoutReason: '家庭原因',
  checkoutType: 1,
  expectedCheckoutDate: '2024-02-01',
  status: 1,
  applicationDate: '2024-01-27',
  createdBy: 'user-1',
}

const mockReview = {
  id: 'review-1',
  applicationId: 'app-1',
  reviewLevel: 1,
  reviewType: 1,
  reviewResult: 1,
  reviewNotes: '审核通过',
  reviewerId: 'user-1',
  reviewDate: '2024-01-27',
  createdAt: new Date(),
  updatedAt: new Date(),
}

describe('/api/checkout-reviews', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(getCurrentUser).mockResolvedValue(mockUser)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('GET /api/checkout-reviews', () => {
    it('should return checkout reviews list successfully', async () => {
      const mockCountResult = [{ count: 1 }]
      const mockReviews = [
        {
          ...mockReview,
          application: mockApplication,
          reviewer: mockUser,
        },
      ]

      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue(mockCountResult),
            }),
          }),
        }),
      } as any)

      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({
                orderBy: vi.fn().mockReturnValue({
                  limit: vi.fn().mockReturnValue({
                    offset: vi.fn().mockReturnValue(mockReviews),
                  }),
                }),
              }),
            }),
          }),
        }),
      } as any)

      const request = new NextRequest('http://localhost:3000/api/checkout-reviews?page=1&limit=10')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.list).toHaveLength(1)
      expect(data.data.list[0].reviewLevel).toBe(1)
      expect(data.data.pagination.total).toBe(1)
    })

    it('should filter reviews by application ID', async () => {
      const mockCountResult = [{ count: 1 }]
      const mockReviews = [
        {
          ...mockReview,
          application: mockApplication,
          reviewer: mockUser,
        },
      ]

      vi.mocked(db.select).mockReturnValue({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue(mockCountResult),
            }),
          }),
        }),
      } as any)

      vi.mocked(db.select).mockReturnValue({
        from: vi.fn().mockReturnValue({
          leftJoin: vi.fn().mockReturnValue({
            leftJoin: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({
                orderBy: vi.fn().mockReturnValue({
                  limit: vi.fn().mockReturnValue({
                    offset: vi.fn().mockReturnValue(mockReviews),
                  }),
                }),
              }),
            }),
          }),
        }),
      } as any)

      const request = new NextRequest('http://localhost:3000/api/checkout-reviews?applicationId=app-1')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
    })

    it('should return 401 when user is not authenticated', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/checkout-reviews')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.code).toBe('UNAUTHORIZED')
    })
  })

  describe('POST /api/checkout-reviews', () => {
    const validReviewData = {
      applicationId: 'app-1',
      reviewLevel: 1,
      reviewType: 1,
      reviewResult: 1,
      reviewNotes: '审核通过',
      requirements: [],
      suggestions: '建议尽快办理',
    }

    it('should create review successfully', async () => {
      // Mock application exists
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([mockApplication]),
          }),
        }),
      } as any)

      // Mock transaction
      vi.mocked(db.transaction).mockImplementation(async (callback) => {
        const mockTx = {
          insert: vi.fn().mockReturnValue({
            values: vi.fn().mockReturnValue({
              returning: vi.fn().mockReturnValue([mockReview]),
            }),
          }),
          update: vi.fn().mockReturnValue({
            set: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({}),
            }),
          }),
        }
        return await callback(mockTx as any)
      })

      const request = new NextRequest('http://localhost:3000/api/checkout-reviews', {
        method: 'POST',
        body: JSON.stringify(validReviewData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.reviewLevel).toBe(1)
      expect(data.message).toBe('审核记录创建成功')
    })

    it('should return 404 when application does not exist', async () => {
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([]),
          }),
        }),
      } as any)

      const request = new NextRequest('http://localhost:3000/api/checkout-reviews', {
        method: 'POST',
        body: JSON.stringify(validReviewData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.code).toBe('APPLICATION_NOT_FOUND')
    })

    it('should handle review result "通过" and update application status', async () => {
      const passReviewData = {
        ...validReviewData,
        reviewResult: 1, // 通过
        reviewLevel: 3, // 终审
      }

      // Mock application exists
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([mockApplication]),
          }),
        }),
      } as any)

      // Mock transaction
      vi.mocked(db.transaction).mockImplementation(async (callback) => {
        const mockTx = {
          insert: vi.fn().mockReturnValue({
            values: vi.fn().mockReturnValue({
              returning: vi.fn().mockReturnValue([mockReview]),
            }),
          }),
          update: vi.fn().mockReturnValue({
            set: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({}),
            }),
          }),
        }
        return await callback(mockTx as any)
      })

      const request = new NextRequest('http://localhost:3000/api/checkout-reviews', {
        method: 'POST',
        body: JSON.stringify(passReviewData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
    })

    it('should handle review result "拒绝" and update application status', async () => {
      const rejectReviewData = {
        ...validReviewData,
        reviewResult: 2, // 拒绝
        reviewNotes: '材料不完整',
      }

      // Mock application exists
      vi.mocked(db.select).mockReturnValueOnce({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockReturnValue([mockApplication]),
          }),
        }),
      } as any)

      // Mock transaction
      vi.mocked(db.transaction).mockImplementation(async (callback) => {
        const mockTx = {
          insert: vi.fn().mockReturnValue({
            values: vi.fn().mockReturnValue({
              returning: vi.fn().mockReturnValue([{ ...mockReview, reviewResult: 2 }]),
            }),
          }),
          update: vi.fn().mockReturnValue({
            set: vi.fn().mockReturnValue({
              where: vi.fn().mockReturnValue({}),
            }),
          }),
        }
        return await callback(mockTx as any)
      })

      const request = new NextRequest('http://localhost:3000/api/checkout-reviews', {
        method: 'POST',
        body: JSON.stringify(rejectReviewData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
    })

    it('should return 400 when validation fails', async () => {
      const invalidData = {
        applicationId: '', // 无效的 applicationId
        reviewLevel: 0, // 无效的 reviewLevel
        reviewType: 0, // 无效的 reviewType
        reviewResult: 0, // 无效的 reviewResult
      }

      const request = new NextRequest('http://localhost:3000/api/checkout-reviews', {
        method: 'POST',
        body: JSON.stringify(invalidData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.code).toBe('VALIDATION_ERROR')
      expect(data.errors).toBeDefined()
    })

    it('should return 401 when user is not authenticated', async () => {
      vi.mocked(getCurrentUser).mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/checkout-reviews', {
        method: 'POST',
        body: JSON.stringify(validReviewData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.code).toBe('UNAUTHORIZED')
    })

    it('should handle database errors gracefully', async () => {
      vi.mocked(db.select).mockImplementation(() => {
        throw new Error('Database connection failed')
      })

      const request = new NextRequest('http://localhost:3000/api/checkout-reviews', {
        method: 'POST',
        body: JSON.stringify(validReviewData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.code).toBe('INTERNAL_ERROR')
    })
  })
})
