import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { GET, POST } from '@/app/api/nursing/visit-registrations/route'
import { NextRequest } from 'next/server'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    innerJoin: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    offset: vi.fn().mockReturnThis(),
    values: vi.fn().mockReturnThis(),
    returning: vi.fn().mockReturnThis(),
  }
}))

vi.mock('@/lib/auth', () => ({
  auth: {
    api: {
      getSession: vi.fn()
    }
  }
}))

describe('/api/nursing/visit-registrations', () => {
  const mockDb = vi.mocked(await import('@/lib/db')).db
  const mockAuth = vi.mocked(await import('@/lib/auth')).auth

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('GET', () => {
    it('should return paginated visit registrations when authenticated', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const mockRegistrations = [
        {
          id: 'visit-1',
          registrationNumber: 'VR20241201001',
          elderName: '赵大爷',
          elderAge: 78,
          elderGender: 1,
          genderLabel: '男',
          elderRoomNumber: '103',
          elderBedNumber: 'A',
          visitorName: '赵小明',
          visitorPhone: '13900139000',
          relationToElder: '儿子',
          visitorCount: 2,
          visitDate: '2024-12-01',
          plannedStartTime: '14:00',
          plannedEndTime: '16:00',
          visitPurpose: '探望父亲',
          visitType: 1,
          visitTypeLabel: '家属探访',
          visitLocation: '老人房间',
          isOutsideVisit: false,
          status: 1,
          statusLabel: '预约',
          approvalRequired: false,
          temperatureCheck: 36.5,
          healthDeclaration: true,
          createdAt: '2024-12-01T10:00:00Z'
        }
      ]

      const mockTotal = [{ count: 1 }]

      mockDb.select
        .mockResolvedValueOnce(mockRegistrations)
        .mockResolvedValueOnce(mockTotal)

      const url = 'http://localhost:3000/api/nursing/visit-registrations?page=1&limit=10'
      const request = new NextRequest(url)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.registrations).toEqual(mockRegistrations)
      expect(data.data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1
      })
    })

    it('should handle filter parameters correctly', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      mockDb.select
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([{ count: 0 }])

      const url = 'http://localhost:3000/api/nursing/visit-registrations?visitType=1&status=1&startDate=2024-12-01'
      const request = new NextRequest(url)
      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(mockDb.select).toHaveBeenCalled()
    })

    it('should return 401 when not authenticated', async () => {
      mockAuth.api.getSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/nursing/visit-registrations')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Unauthorized')
    })
  })

  describe('POST', () => {
    it('should create a new visit registration when authenticated with valid data', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const newRegistration = {
        id: 'visit-new',
        registrationNumber: 'VR20241201002',
        elderInfoId: 'elder-1',
        visitorName: '李小红',
        visitorPhone: '13800138000',
        relationToElder: '女儿',
        visitorCount: 1,
        visitDate: '2024-12-02',
        plannedStartTime: '15:00',
        plannedEndTime: '17:00',
        visitPurpose: '探望母亲',
        visitType: 1,
        visitLocation: '老人房间',
        isOutsideVisit: false,
        approvalRequired: false,
        temperatureCheck: 36.3,
        healthDeclaration: true,
        registeredBy: 'user-1',
        createdAt: new Date()
      }

      mockDb.insert.mockReturnValue(mockDb)
      mockDb.values.mockReturnValue(mockDb)
      mockDb.returning.mockResolvedValue([newRegistration])

      const requestBody = {
        elderInfoId: 'elder-1',
        visitorName: '李小红',
        visitorPhone: '13800138000',
        relationToElder: '女儿',
        visitorCount: 1,
        visitDate: '2024-12-02',
        plannedStartTime: '15:00',
        plannedEndTime: '17:00',
        visitPurpose: '探望母亲',
        visitType: 1,
        visitLocation: '老人房间',
        isOutsideVisit: false,
        temperatureCheck: 36.3,
        healthDeclaration: true
      }

      const request = new NextRequest('http://localhost:3000/api/nursing/visit-registrations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('探访登记创建成功')
      expect(data.data).toEqual(newRegistration)
    })

    it('should handle outside visit registration', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const outsideVisitRegistration = {
        id: 'visit-outside',
        registrationNumber: 'VR20241201003',
        elderInfoId: 'elder-1',
        visitorName: '王小华',
        visitorPhone: '13700137000',
        relationToElder: '侄子',
        visitType: 1,
        isOutsideVisit: true,
        outsideDestination: '附近公园',
        approvalRequired: true,
        createdAt: new Date()
      }

      mockDb.insert.mockReturnValue(mockDb)
      mockDb.values.mockReturnValue(mockDb)
      mockDb.returning.mockResolvedValue([outsideVisitRegistration])

      const requestBody = {
        elderInfoId: 'elder-1',
        visitorName: '王小华',
        visitorPhone: '13700137000',
        relationToElder: '侄子',
        visitDate: '2024-12-02',
        plannedStartTime: '10:00',
        plannedEndTime: '12:00',
        visitPurpose: '陪老人散步',
        visitType: 1,
        isOutsideVisit: true,
        outsideDestination: '附近公园',
        healthDeclaration: true
      }

      const request = new NextRequest('http://localhost:3000/api/nursing/visit-registrations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.isOutsideVisit).toBe(true)
      expect(data.data.approvalRequired).toBe(true)
    })

    it('should return 400 for invalid data', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const invalidRequestBody = {
        elderInfoId: 'elder-1',
        // Missing required fields
        visitorName: '',
        visitorPhone: '',
        visitDate: '',
        visitType: 0, // Invalid type
        healthDeclaration: false // Required to be true
      }

      const request = new NextRequest('http://localhost:3000/api/nursing/visit-registrations', {
        method: 'POST',
        body: JSON.stringify(invalidRequestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Validation error')
      expect(data.details).toBeDefined()
    })

    it('should return 401 when not authenticated', async () => {
      mockAuth.api.getSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/nursing/visit-registrations', {
        method: 'POST',
        body: JSON.stringify({}),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Unauthorized')
    })

    it('should validate temperature check range', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const requestBodyWithHighTemp = {
        elderInfoId: 'elder-1',
        visitorName: '测试访客',
        visitorPhone: '13800138000',
        relationToElder: '朋友',
        visitDate: '2024-12-02',
        plannedStartTime: '15:00',
        plannedEndTime: '17:00',
        visitPurpose: '探访',
        visitType: 2,
        visitLocation: '会客室',
        temperatureCheck: 38.5, // High temperature
        healthDeclaration: true
      }

      const request = new NextRequest('http://localhost:3000/api/nursing/visit-registrations', {
        method: 'POST',
        body: JSON.stringify(requestBodyWithHighTemp),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Validation error')
    })
  })
})
