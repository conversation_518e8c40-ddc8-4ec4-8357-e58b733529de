import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { GET, POST } from '@/app/api/nursing/leave-applications/route'
import { NextRequest } from 'next/server'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    innerJoin: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    offset: vi.fn().mockReturnThis(),
    values: vi.fn().mockReturnThis(),
    returning: vi.fn().mockReturnThis(),
  }
}))

vi.mock('@/lib/auth', () => ({
  auth: {
    api: {
      getSession: vi.fn()
    }
  }
}))

vi.mock('drizzle-orm', () => ({
  eq: vi.fn(),
  and: vi.fn(),
  gte: vi.fn(),
  lte: vi.fn(),
  ilike: vi.fn(),
  desc: vi.fn(),
  asc: vi.fn(),
  sql: vi.fn(),
  count: vi.fn()
}))

describe('/api/nursing/leave-applications', () => {
  const mockDb = vi.mocked(await import('@/lib/db')).db
  const mockAuth = vi.mocked(await import('@/lib/auth')).auth

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('GET', () => {
    it('should return paginated leave applications when authenticated', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const mockApplications = [
        {
          id: 'app-1',
          applicationNumber: 'LA20241201001',
          elderName: '张三',
          elderAge: 75,
          elderGender: 1,
          genderLabel: '男',
          elderRoomNumber: '101',
          elderBedNumber: 'A',
          applicantName: '张小明',
          applicantRelation: '儿子',
          leaveType: 1,
          leaveTypeLabel: '外出',
          leaveReason: '回家探亲',
          startDate: '2024-12-01',
          endDate: '2024-12-02',
          status: 1,
          statusLabel: '待审核',
          createdAt: '2024-12-01T10:00:00Z'
        }
      ]

      const mockTotal = [{ count: 1 }]

      mockDb.select
        .mockResolvedValueOnce(mockApplications)
        .mockResolvedValueOnce(mockTotal)

      const url = 'http://localhost:3000/api/nursing/leave-applications?page=1&limit=10'
      const request = new NextRequest(url)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.applications).toEqual(mockApplications)
      expect(data.data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1
      })
    })

    it('should handle search and filter parameters', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      mockDb.select
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([{ count: 0 }])

      const url = 'http://localhost:3000/api/nursing/leave-applications?search=张三&status=1&leaveType=1'
      const request = new NextRequest(url)
      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(mockDb.select).toHaveBeenCalled()
    })

    it('should return 401 when not authenticated', async () => {
      mockAuth.api.getSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/nursing/leave-applications')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Unauthorized')
    })
  })

  describe('POST', () => {
    it('should create a new leave application when authenticated with valid data', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const newApplication = {
        id: 'app-new',
        applicationNumber: 'LA20241201002',
        elderInfoId: 'elder-1',
        applicantName: '李小红',
        applicantPhone: '13800138000',
        applicantRelation: '女儿',
        leaveType: 2,
        leaveReason: '回家过节',
        startDate: '2024-12-25',
        endDate: '2024-12-27',
        expectedReturnDate: '2024-12-27',
        destination: '家中',
        createdBy: 'user-1',
        createdAt: new Date()
      }

      mockDb.insert.mockReturnValue(mockDb)
      mockDb.values.mockReturnValue(mockDb)
      mockDb.returning.mockResolvedValue([newApplication])

      const requestBody = {
        elderInfoId: 'elder-1',
        applicantName: '李小红',
        applicantPhone: '13800138000',
        applicantRelation: '女儿',
        leaveType: 2,
        leaveReason: '回家过节',
        startDate: '2024-12-25',
        endDate: '2024-12-27',
        expectedReturnDate: '2024-12-27',
        destination: '家中'
      }

      const request = new NextRequest('http://localhost:3000/api/nursing/leave-applications', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('请假申请创建成功')
      expect(data.data).toEqual(newApplication)
    })

    it('should return 400 for invalid data', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const invalidRequestBody = {
        elderInfoId: 'elder-1',
        // Missing required fields
        applicantName: '',
        applicantPhone: '',
        leaveType: 0 // Invalid type
      }

      const request = new NextRequest('http://localhost:3000/api/nursing/leave-applications', {
        method: 'POST',
        body: JSON.stringify(invalidRequestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Validation error')
      expect(data.details).toBeDefined()
    })

    it('should return 401 when not authenticated', async () => {
      mockAuth.api.getSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/nursing/leave-applications', {
        method: 'POST',
        body: JSON.stringify({}),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Unauthorized')
    })

    it('should handle database errors gracefully', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      mockDb.insert.mockReturnValue(mockDb)
      mockDb.values.mockReturnValue(mockDb)
      mockDb.returning.mockRejectedValue(new Error('Database error'))

      const requestBody = {
        elderInfoId: 'elder-1',
        applicantName: '李小红',
        applicantPhone: '13800138000',
        applicantRelation: '女儿',
        leaveType: 2,
        leaveReason: '回家过节',
        startDate: '2024-12-25',
        endDate: '2024-12-27',
        expectedReturnDate: '2024-12-27',
        destination: '家中'
      }

      const request = new NextRequest('http://localhost:3000/api/nursing/leave-applications', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Internal server error')
    })
  })
})
