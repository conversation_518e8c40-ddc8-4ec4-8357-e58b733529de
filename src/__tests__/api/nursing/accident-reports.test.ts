import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { GET, POST } from '@/app/api/nursing/accident-reports/route'
import { NextRequest } from 'next/server'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    innerJoin: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    offset: vi.fn().mockReturnThis(),
    values: vi.fn().mockReturnThis(),
    returning: vi.fn().mockReturnThis(),
  }
}))

vi.mock('@/lib/auth', () => ({
  auth: {
    api: {
      getSession: vi.fn()
    }
  }
}))

describe('/api/nursing/accident-reports', () => {
  const mockDb = vi.mocked(await import('@/lib/db')).db
  const mockAuth = vi.mocked(await import('@/lib/auth')).auth

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('GET', () => {
    it('should return paginated accident reports when authenticated', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const mockReports = [
        {
          id: 'report-1',
          reportNumber: 'AR20241201001',
          elderName: '王老太',
          elderAge: 80,
          elderGender: 2,
          genderLabel: '女',
          elderRoomNumber: '102',
          elderBedNumber: 'B',
          accidentDate: '2024-12-01',
          accidentTime: '14:30',
          location: '走廊',
          accidentType: 1,
          accidentTypeLabel: '跌倒',
          severity: 2,
          severityLabel: '一般',
          description: '老人在走廊行走时不慎跌倒',
          discoveredBy: '护士小李',
          discoveredTime: '14:30',
          hospitalTransfer: false,
          familyNotified: true,
          status: 1,
          statusLabel: '待处理',
          createdAt: '2024-12-01T14:35:00Z'
        }
      ]

      const mockTotal = [{ count: 1 }]

      mockDb.select
        .mockResolvedValueOnce(mockReports)
        .mockResolvedValueOnce(mockTotal)

      const url = 'http://localhost:3000/api/nursing/accident-reports?page=1&limit=10'
      const request = new NextRequest(url)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.reports).toEqual(mockReports)
      expect(data.data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1
      })
    })

    it('should handle filter parameters correctly', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      mockDb.select
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([{ count: 0 }])

      const url = 'http://localhost:3000/api/nursing/accident-reports?accidentType=1&severity=2&status=1'
      const request = new NextRequest(url)
      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(mockDb.select).toHaveBeenCalled()
    })

    it('should return 401 when not authenticated', async () => {
      mockAuth.api.getSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/nursing/accident-reports')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Unauthorized')
    })
  })

  describe('POST', () => {
    it('should create a new accident report when authenticated with valid data', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const newReport = {
        id: 'report-new',
        reportNumber: 'AR20241201002',
        elderInfoId: 'elder-1',
        accidentDate: '2024-12-01',
        accidentTime: '15:00',
        location: '房间内',
        accidentType: 2,
        severity: 1,
        description: '老人在房间内不慎烫伤',
        discoveredBy: '护士小王',
        discoveredTime: '15:00',
        hospitalTransfer: false,
        familyNotified: true,
        reportedBy: 'user-1',
        reportedTime: new Date(),
        createdAt: new Date()
      }

      mockDb.insert.mockReturnValue(mockDb)
      mockDb.values.mockReturnValue(mockDb)
      mockDb.returning.mockResolvedValue([newReport])

      const requestBody = {
        elderInfoId: 'elder-1',
        accidentDate: '2024-12-01',
        accidentTime: '15:00',
        location: '房间内',
        accidentType: 2,
        severity: 1,
        description: '老人在房间内不慎烫伤',
        discoveredBy: '护士小王',
        discoveredTime: '15:00',
        hospitalTransfer: false,
        familyNotified: true
      }

      const request = new NextRequest('http://localhost:3000/api/nursing/accident-reports', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('事故报告创建成功')
      expect(data.data).toEqual(newReport)
    })

    it('should return 400 for invalid data', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const invalidRequestBody = {
        elderInfoId: 'elder-1',
        // Missing required fields
        accidentDate: '',
        location: '',
        accidentType: 0, // Invalid type
        severity: 0, // Invalid severity
        description: ''
      }

      const request = new NextRequest('http://localhost:3000/api/nursing/accident-reports', {
        method: 'POST',
        body: JSON.stringify(invalidRequestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Validation error')
      expect(data.details).toBeDefined()
    })

    it('should return 401 when not authenticated', async () => {
      mockAuth.api.getSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/nursing/accident-reports', {
        method: 'POST',
        body: JSON.stringify({}),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Unauthorized')
    })

    it('should generate unique report numbers', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      const report1 = { id: 'report-1', reportNumber: 'AR20241201001' }
      const report2 = { id: 'report-2', reportNumber: 'AR20241201002' }

      mockDb.insert.mockReturnValue(mockDb)
      mockDb.values.mockReturnValue(mockDb)
      mockDb.returning
        .mockResolvedValueOnce([report1])
        .mockResolvedValueOnce([report2])

      const requestBody = {
        elderInfoId: 'elder-1',
        accidentDate: '2024-12-01',
        accidentTime: '15:00',
        location: '房间内',
        accidentType: 2,
        severity: 1,
        description: '事故描述',
        discoveredBy: '护士',
        discoveredTime: '15:00'
      }

      // First request
      const request1 = new NextRequest('http://localhost:3000/api/nursing/accident-reports', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      })

      const response1 = await POST(request1)
      const data1 = await response1.json()

      // Second request
      const request2 = new NextRequest('http://localhost:3000/api/nursing/accident-reports', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      })

      const response2 = await POST(request2)
      const data2 = await response2.json()

      expect(data1.data.reportNumber).toMatch(/^AR\d{8}\d{3}$/)
      expect(data2.data.reportNumber).toMatch(/^AR\d{8}\d{3}$/)
      // Note: In real implementation, these would be different due to random suffix
    })
  })
})
