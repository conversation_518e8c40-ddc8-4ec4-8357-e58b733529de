import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { GET } from '@/app/api/nursing/overview/route'
import { NextRequest } from 'next/server'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    innerJoin: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    groupBy: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
  }
}))

vi.mock('@/lib/auth', () => ({
  auth: {
    api: {
      getSession: vi.fn()
    }
  }
}))

vi.mock('@/lib/db/schema', () => ({
  elderInfo: {
    id: 'id',
    name: 'name',
    age: 'age',
    gender: 'gender',
    status: 'status',
    careLevel: 'care_level'
  },
  rooms: {
    id: 'id',
    roomNumber: 'room_number',
    totalBeds: 'total_beds',
    occupiedBeds: 'occupied_beds'
  },
  leaveApplications: {
    id: 'id',
    applicationNumber: 'application_number',
    elderInfoId: 'elder_info_id',
    leaveType: 'leave_type',
    startDate: 'start_date',
    endDate: 'end_date',
    status: 'status',
    createdAt: 'created_at'
  },
  accidentReports: {
    id: 'id',
    reportNumber: 'report_number',
    elderInfoId: 'elder_info_id',
    accidentType: 'accident_type',
    severity: 'severity',
    accidentDate: 'accident_date',
    status: 'status',
    createdAt: 'created_at'
  },
  visitRegistrations: {
    id: 'id',
    registrationNumber: 'registration_number',
    elderInfoId: 'elder_info_id',
    visitorName: 'visitor_name',
    visitType: 'visit_type',
    visitDate: 'visit_date',
    plannedStartTime: 'planned_start_time',
    plannedEndTime: 'planned_end_time',
    status: 'status'
  }
}))

describe('/api/nursing/overview', () => {
  const mockDb = vi.mocked(await import('@/lib/db')).db
  const mockAuth = vi.mocked(await import('@/lib/auth')).auth

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('GET', () => {
    it('should return overview data when authenticated', async () => {
      // Mock authentication
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      // Mock database responses
      const mockElderStats = [{ total: 50, inResidence: 45, onLeave: 3, discharged: 2 }]
      const mockCareLevelDistribution = [
        { level: 1, count: 15, label: '自理' },
        { level: 2, count: 20, label: '半护理' },
        { level: 3, count: 10, label: '全护理' }
      ]
      const mockRoomUsage = [{ totalRooms: 25, occupiedRooms: 23, availableRooms: 2, bedUsageRate: 92 }]
      const mockRecentLeaveApplications = [
        {
          id: 'leave-1',
          applicationNumber: 'LA20241201001',
          elderName: '张三',
          leaveType: 1,
          leaveTypeLabel: '外出',
          startDate: '2024-12-01',
          endDate: '2024-12-02',
          status: 1,
          statusLabel: '待审核'
        }
      ]
      const mockRecentAccidentReports = []
      const mockTodayVisits = [
        {
          id: 'visit-1',
          registrationNumber: 'VR20241201001',
          elderName: '李四',
          visitorName: '李小明',
          visitType: 1,
          visitTypeLabel: '家属探访',
          plannedStartTime: '14:00',
          plannedEndTime: '16:00',
          status: 1,
          statusLabel: '预约'
        }
      ]

      // Setup mock chain calls
      mockDb.select.mockReturnValue(mockDb)
      mockDb.from.mockReturnValue(mockDb)
      mockDb.innerJoin.mockReturnValue(mockDb)
      mockDb.where.mockReturnValue(mockDb)
      mockDb.groupBy.mockReturnValue(mockDb)
      mockDb.orderBy.mockReturnValue(mockDb)
      mockDb.limit.mockReturnValue(mockDb)

      // Mock different query results
      mockDb.select
        .mockResolvedValueOnce(mockElderStats) // elderStats query
        .mockResolvedValueOnce(mockCareLevelDistribution) // careLevelDistribution query
        .mockResolvedValueOnce(mockRoomUsage) // roomUsage query
        .mockResolvedValueOnce(mockRecentLeaveApplications) // recentLeaveApplications query
        .mockResolvedValueOnce(mockRecentAccidentReports) // recentAccidentReports query
        .mockResolvedValueOnce(mockTodayVisits) // todayVisits query

      const request = new NextRequest('http://localhost:3000/api/nursing/overview')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toEqual({
        elderStats: mockElderStats[0],
        careLevelDistribution: mockCareLevelDistribution,
        roomUsage: mockRoomUsage[0],
        recentLeaveApplications: mockRecentLeaveApplications,
        recentAccidentReports: mockRecentAccidentReports,
        todayVisits: mockTodayVisits
      })
    })

    it('should return 401 when not authenticated', async () => {
      mockAuth.api.getSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/nursing/overview')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Unauthorized')
    })

    it('should handle database errors gracefully', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      mockDb.select.mockRejectedValue(new Error('Database connection failed'))

      const request = new NextRequest('http://localhost:3000/api/nursing/overview')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Internal server error')
    })

    it('should return empty arrays when no data exists', async () => {
      mockAuth.api.getSession.mockResolvedValue({
        user: { id: 'user-1', name: 'Test User' }
      })

      // Mock empty responses
      const emptyElderStats = [{ total: 0, inResidence: 0, onLeave: 0, discharged: 0 }]
      const emptyCareLevelDistribution = []
      const emptyRoomUsage = [{ totalRooms: 0, occupiedRooms: 0, availableRooms: 0, bedUsageRate: 0 }]
      const emptyRecentLeaveApplications = []
      const emptyRecentAccidentReports = []
      const emptyTodayVisits = []

      mockDb.select
        .mockResolvedValueOnce(emptyElderStats)
        .mockResolvedValueOnce(emptyCareLevelDistribution)
        .mockResolvedValueOnce(emptyRoomUsage)
        .mockResolvedValueOnce(emptyRecentLeaveApplications)
        .mockResolvedValueOnce(emptyRecentAccidentReports)
        .mockResolvedValueOnce(emptyTodayVisits)

      const request = new NextRequest('http://localhost:3000/api/nursing/overview')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.elderStats.total).toBe(0)
      expect(data.data.careLevelDistribution).toEqual([])
      expect(data.data.recentLeaveApplications).toEqual([])
      expect(data.data.recentAccidentReports).toEqual([])
      expect(data.data.todayVisits).toEqual([])
    })
  })
})
