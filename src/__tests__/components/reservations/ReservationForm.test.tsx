import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ReservationForm } from '@/components/reservations/ReservationForm'

// Mock dependencies
vi.mock('@/hooks/use-reservations', () => ({
  useReservations: () => ({
    createReservation: vi.fn(),
    updateReservation: vi.fn(),
    checkRoomAvailability: vi.fn(),
    isLoading: false,
  }),
}))

vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => {
    if (typeof date === 'string') return date
    return '2024-12-15'
  }),
}))

vi.mock('date-fns/locale', () => ({
  zhCN: {},
}))

const mockAvailableRooms = [
  {
    id: 'room-1',
    roomNumber: '101',
    roomType: 1,
    roomTypeName: '单人间',
    floor: 1,
    capacity: 1,
    currentOccupancy: 0,
    monthlyRate: 3000,
    facilities: ['空调', '电视'],
    isAvailable: true,
    hasReservationConflict: false,
    isRoomFull: false,
    availableBeds: 1,
  },
  {
    id: 'room-2',
    roomNumber: '102',
    roomType: 2,
    roomTypeName: '双人间',
    floor: 1,
    capacity: 2,
    currentOccupancy: 1,
    monthlyRate: 4000,
    facilities: ['空调', '电视', '独立卫生间'],
    isAvailable: true,
    hasReservationConflict: false,
    isRoomFull: false,
    availableBeds: 1,
  },
]

describe('ReservationForm', () => {
  const mockProps = {
    onSuccess: vi.fn(),
    onCancel: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should render form fields correctly', () => {
    render(<ReservationForm {...mockProps} />)

    // 检查基本信息字段
    expect(screen.getByLabelText(/联系人姓名/)).toBeInTheDocument()
    expect(screen.getByLabelText(/联系电话/)).toBeInTheDocument()
    expect(screen.getByLabelText(/与老人关系/)).toBeInTheDocument()
    expect(screen.getByLabelText(/预订类型/)).toBeInTheDocument()

    // 检查入住信息字段
    expect(screen.getByLabelText(/预期入住日期/)).toBeInTheDocument()
    expect(screen.getByLabelText(/预期退房日期/)).toBeInTheDocument()

    // 检查其他信息字段
    expect(screen.getByLabelText(/特殊要求/)).toBeInTheDocument()
    expect(screen.getByLabelText(/押金金额/)).toBeInTheDocument()
    expect(screen.getByLabelText(/押金已支付/)).toBeInTheDocument()
    expect(screen.getByLabelText(/备注/)).toBeInTheDocument()

    // 检查操作按钮
    expect(screen.getByRole('button', { name: /取消/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /创建预订/ })).toBeInTheDocument()
  })

  it('should validate required fields', async () => {
    const user = userEvent.setup()
    render(<ReservationForm {...mockProps} />)

    // 尝试提交空表单
    const submitButton = screen.getByRole('button', { name: /创建预订/ })
    await user.click(submitButton)

    // 检查验证错误信息
    await waitFor(() => {
      expect(screen.getByText(/请输入联系人姓名/)).toBeInTheDocument()
      expect(screen.getByText(/请输入联系电话/)).toBeInTheDocument()
      expect(screen.getByText(/请选择预期入住日期/)).toBeInTheDocument()
      expect(screen.getByText(/请选择房间/)).toBeInTheDocument()
    })
  })

  it('should validate phone number format', async () => {
    const user = userEvent.setup()
    render(<ReservationForm {...mockProps} />)

    const phoneInput = screen.getByLabelText(/联系电话/)
    await user.type(phoneInput, '123456')

    const submitButton = screen.getByRole('button', { name: /创建预订/ })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/请输入有效的手机号码/)).toBeInTheDocument()
    })
  })

  it('should fill form with initial data when provided', () => {
    const initialData = {
      contactName: '张三',
      contactPhone: '13800138000',
      contactRelation: '子女',
      reservationType: 2,
      specialRequirements: '需要轮椅',
      depositAmount: 1000,
      depositPaid: true,
      notes: '测试备注',
    }

    render(<ReservationForm {...mockProps} initialData={initialData} />)

    expect(screen.getByDisplayValue('张三')).toBeInTheDocument()
    expect(screen.getByDisplayValue('13800138000')).toBeInTheDocument()
    expect(screen.getByDisplayValue('子女')).toBeInTheDocument()
    expect(screen.getByDisplayValue('需要轮椅')).toBeInTheDocument()
    expect(screen.getByDisplayValue('1000')).toBeInTheDocument()
    expect(screen.getByDisplayValue('测试备注')).toBeInTheDocument()
  })

  it('should check room availability when dates change', async () => {
    const mockCheckRoomAvailability = vi.fn().mockResolvedValue({
      rooms: mockAvailableRooms,
    })

    vi.mocked(require('@/hooks/use-reservations').useReservations).mockReturnValue({
      createReservation: vi.fn(),
      updateReservation: vi.fn(),
      checkRoomAvailability: mockCheckRoomAvailability,
      isLoading: false,
    })

    const user = userEvent.setup()
    render(<ReservationForm {...mockProps} />)

    // 选择入住日期
    const checkInButton = screen.getByRole('button', { name: /选择入住日期/ })
    await user.click(checkInButton)

    // 模拟日期选择（这里简化处理，实际测试中可能需要更复杂的日期选择模拟）
    fireEvent.change(screen.getByLabelText(/预期入住日期/), {
      target: { value: '2024-12-15' },
    })

    await waitFor(() => {
      expect(mockCheckRoomAvailability).toHaveBeenCalled()
    })
  })

  it('should display available rooms after availability check', async () => {
    const mockCheckRoomAvailability = vi.fn().mockResolvedValue({
      rooms: mockAvailableRooms,
    })

    vi.mocked(require('@/hooks/use-reservations').useReservations).mockReturnValue({
      createReservation: vi.fn(),
      updateReservation: vi.fn(),
      checkRoomAvailability: mockCheckRoomAvailability,
      isLoading: false,
    })

    render(<ReservationForm {...mockProps} />)

    // 模拟可用性检查完成
    fireEvent.change(screen.getByLabelText(/预期入住日期/), {
      target: { value: '2024-12-15' },
    })

    await waitFor(() => {
      expect(screen.getByText('房间选择')).toBeInTheDocument()
      expect(screen.getByText('101')).toBeInTheDocument()
      expect(screen.getByText('102')).toBeInTheDocument()
      expect(screen.getByText('单人间')).toBeInTheDocument()
      expect(screen.getByText('双人间')).toBeInTheDocument()
    })
  })

  it('should select room when clicked', async () => {
    const mockCheckRoomAvailability = vi.fn().mockResolvedValue({
      rooms: mockAvailableRooms,
    })

    vi.mocked(require('@/hooks/use-reservations').useReservations).mockReturnValue({
      createReservation: vi.fn(),
      updateReservation: vi.fn(),
      checkRoomAvailability: mockCheckRoomAvailability,
      isLoading: false,
    })

    const user = userEvent.setup()
    render(<ReservationForm {...mockProps} />)

    // 模拟可用性检查完成
    fireEvent.change(screen.getByLabelText(/预期入住日期/), {
      target: { value: '2024-12-15' },
    })

    await waitFor(() => {
      expect(screen.getByText('101')).toBeInTheDocument()
    })

    // 选择房间
    const roomCard = screen.getByText('101').closest('div')
    if (roomCard) {
      await user.click(roomCard)
    }

    // 检查房间是否被选中（通过样式变化）
    await waitFor(() => {
      expect(roomCard).toHaveClass('border-blue-500')
    })
  })

  it('should submit form with valid data', async () => {
    const mockCreateReservation = vi.fn().mockResolvedValue({})
    const mockCheckRoomAvailability = vi.fn().mockResolvedValue({
      rooms: mockAvailableRooms,
    })

    vi.mocked(require('@/hooks/use-reservations').useReservations).mockReturnValue({
      createReservation: mockCreateReservation,
      updateReservation: vi.fn(),
      checkRoomAvailability: mockCheckRoomAvailability,
      isLoading: false,
    })

    const user = userEvent.setup()
    render(<ReservationForm {...mockProps} />)

    // 填写表单
    await user.type(screen.getByLabelText(/联系人姓名/), '张三')
    await user.type(screen.getByLabelText(/联系电话/), '13800138000')

    // 选择日期和房间
    fireEvent.change(screen.getByLabelText(/预期入住日期/), {
      target: { value: '2024-12-15' },
    })

    await waitFor(() => {
      expect(screen.getByText('101')).toBeInTheDocument()
    })

    const roomCard = screen.getByText('101').closest('div')
    if (roomCard) {
      await user.click(roomCard)
    }

    // 提交表单
    const submitButton = screen.getByRole('button', { name: /创建预订/ })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockCreateReservation).toHaveBeenCalledWith(
        expect.objectContaining({
          contactName: '张三',
          contactPhone: '13800138000',
          roomId: 'room-1',
          expectedCheckInDate: '2024-12-15',
        })
      )
      expect(mockProps.onSuccess).toHaveBeenCalled()
    })
  })

  it('should call onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup()
    render(<ReservationForm {...mockProps} />)

    const cancelButton = screen.getByRole('button', { name: /取消/ })
    await user.click(cancelButton)

    expect(mockProps.onCancel).toHaveBeenCalled()
  })

  it('should show update mode when reservationId is provided', () => {
    render(<ReservationForm {...mockProps} reservationId="reservation-1" />)

    expect(screen.getByRole('button', { name: /更新预订/ })).toBeInTheDocument()
  })

  it('should disable submit button when no room is selected', () => {
    render(<ReservationForm {...mockProps} />)

    const submitButton = screen.getByRole('button', { name: /创建预订/ })
    expect(submitButton).toBeDisabled()
  })

  it('should show loading state during form submission', async () => {
    vi.mocked(require('@/hooks/use-reservations').useReservations).mockReturnValue({
      createReservation: vi.fn(),
      updateReservation: vi.fn(),
      checkRoomAvailability: vi.fn(),
      isLoading: true,
    })

    render(<ReservationForm {...mockProps} />)

    const submitButton = screen.getByRole('button', { name: /创建预订/ })
    expect(submitButton).toBeDisabled()
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })
})
