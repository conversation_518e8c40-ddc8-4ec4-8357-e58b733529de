import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { CheckoutApplicationForm } from '@/components/checkout/checkout-application-form'

// Mock dependencies
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => {
    if (formatStr === 'yyyy-MM-dd') {
      return '2024-01-27'
    }
    return '2024-01-27'
  }),
}))

const mockElderOptions = [
  {
    id: 'elder-1',
    name: '张三',
    idCard: '110101194901010001',
    roomNumber: 'A101',
  },
  {
    id: 'elder-2',
    name: '李四',
    idCard: '110101194902020002',
    roomNumber: 'A102',
  },
]

const mockOnSubmit = vi.fn()
const mockOnCancel = vi.fn()

describe('CheckoutApplicationForm', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render form fields correctly', () => {
    render(
      <CheckoutApplicationForm
        elderOptions={mockElderOptions}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    )

    // 检查表单标题
    expect(screen.getByText('退住申请')).toBeInTheDocument()
    expect(screen.getByText('请填写完整的退住申请信息，所有标记为必填的字段都需要填写')).toBeInTheDocument()

    // 检查必填字段
    expect(screen.getByText('选择老人 *')).toBeInTheDocument()
    expect(screen.getByText('退住类型 *')).toBeInTheDocument()
    expect(screen.getByText('申请人姓名 *')).toBeInTheDocument()
    expect(screen.getByText('申请人电话 *')).toBeInTheDocument()
    expect(screen.getByText('与老人关系 *')).toBeInTheDocument()
    expect(screen.getByText('预期退住日期 *')).toBeInTheDocument()
    expect(screen.getByText('退住原因 *')).toBeInTheDocument()

    // 检查可选字段
    expect(screen.getByText('备注')).toBeInTheDocument()
    expect(screen.getByText('相关附件')).toBeInTheDocument()

    // 检查按钮
    expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '提交申请' })).toBeInTheDocument()
  })

  it('should populate elder options correctly', async () => {
    const user = userEvent.setup()
    
    render(
      <CheckoutApplicationForm
        elderOptions={mockElderOptions}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    )

    // 点击老人选择下拉框
    const elderSelect = screen.getByRole('combobox', { name: /选择老人/i })
    await user.click(elderSelect)

    // 检查老人选项
    await waitFor(() => {
      expect(screen.getByText('张三 - 110101194901010001 (A101)')).toBeInTheDocument()
      expect(screen.getByText('李四 - 110101194902020002 (A102)')).toBeInTheDocument()
    })
  })

  it('should validate required fields', async () => {
    const user = userEvent.setup()
    
    render(
      <CheckoutApplicationForm
        elderOptions={mockElderOptions}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    )

    // 直接提交表单而不填写任何字段
    const submitButton = screen.getByRole('button', { name: '提交申请' })
    await user.click(submitButton)

    // 检查验证错误消息
    await waitFor(() => {
      expect(screen.getByText('请选择老人')).toBeInTheDocument()
      expect(screen.getByText('请输入申请人姓名')).toBeInTheDocument()
      expect(screen.getByText('请输入申请人电话')).toBeInTheDocument()
      expect(screen.getByText('请输入与老人关系')).toBeInTheDocument()
      expect(screen.getByText('请选择预期退住日期')).toBeInTheDocument()
      expect(screen.getByText('请输入退住原因')).toBeInTheDocument()
    })

    // 确保 onSubmit 没有被调用
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('should submit form with valid data', async () => {
    const user = userEvent.setup()
    
    render(
      <CheckoutApplicationForm
        elderOptions={mockElderOptions}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    )

    // 填写表单
    // 选择老人
    const elderSelect = screen.getByRole('combobox', { name: /选择老人/i })
    await user.click(elderSelect)
    await user.click(screen.getByText('张三 - 110101194901010001 (A101)'))

    // 填写申请人信息
    await user.type(screen.getByPlaceholderText('请输入申请人姓名'), '王五')
    await user.type(screen.getByPlaceholderText('请输入申请人电话'), '13800138000')
    
    // 选择关系
    const relationSelect = screen.getByRole('combobox', { name: /与老人关系/i })
    await user.click(relationSelect)
    await user.click(screen.getByText('子女'))

    // 填写退住原因
    await user.type(screen.getByPlaceholderText('请详细说明退住原因'), '家庭原因需要退住')

    // 选择预期退住日期
    const dateButton = screen.getByRole('button', { name: /请选择日期/i })
    await user.click(dateButton)
    // 这里简化日期选择的测试，实际测试中可能需要更复杂的日期选择逻辑

    // 提交表单
    const submitButton = screen.getByRole('button', { name: '提交申请' })
    await user.click(submitButton)

    // 由于日期选择比较复杂，这里主要验证表单验证逻辑
    // 在实际项目中，可能需要 mock 日期选择组件
  })

  it('should handle file upload', async () => {
    const user = userEvent.setup()
    
    render(
      <CheckoutApplicationForm
        elderOptions={mockElderOptions}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    )

    // 创建模拟文件
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    
    // 获取文件输入元素
    const fileInput = screen.getByLabelText(/点击上传文件/i)
    
    // 上传文件
    await user.upload(fileInput, file)

    // 检查文件是否显示在已上传列表中
    await waitFor(() => {
      expect(screen.getByText('已上传文件')).toBeInTheDocument()
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })
  })

  it('should remove uploaded file', async () => {
    const user = userEvent.setup()
    
    render(
      <CheckoutApplicationForm
        elderOptions={mockElderOptions}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    )

    // 上传文件
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    const fileInput = screen.getByLabelText(/点击上传文件/i)
    await user.upload(fileInput, file)

    // 等待文件显示
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })

    // 点击删除按钮
    const removeButton = screen.getByRole('button', { name: '' }) // X 按钮
    await user.click(removeButton)

    // 检查文件是否被移除
    await waitFor(() => {
      expect(screen.queryByText('test.pdf')).not.toBeInTheDocument()
      expect(screen.queryByText('已上传文件')).not.toBeInTheDocument()
    })
  })

  it('should call onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <CheckoutApplicationForm
        elderOptions={mockElderOptions}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    )

    const cancelButton = screen.getByRole('button', { name: '取消' })
    await user.click(cancelButton)

    expect(mockOnCancel).toHaveBeenCalledTimes(1)
  })

  it('should populate initial data when provided', () => {
    const initialData = {
      elderInfoId: 'elder-1',
      applicantName: '王五',
      applicantPhone: '13800138000',
      applicantRelation: '子女',
      checkoutReason: '家庭原因',
      checkoutType: 1,
      expectedCheckoutDate: new Date('2024-02-01'),
      notes: '备注信息',
    }

    render(
      <CheckoutApplicationForm
        elderOptions={mockElderOptions}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        initialData={initialData}
      />
    )

    // 检查表单字段是否被正确填充
    expect(screen.getByDisplayValue('王五')).toBeInTheDocument()
    expect(screen.getByDisplayValue('13800138000')).toBeInTheDocument()
    expect(screen.getByDisplayValue('家庭原因')).toBeInTheDocument()
    expect(screen.getByDisplayValue('备注信息')).toBeInTheDocument()
  })

  it('should show loading state when isLoading is true', () => {
    render(
      <CheckoutApplicationForm
        elderOptions={mockElderOptions}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        isLoading={true}
      />
    )

    const submitButton = screen.getByRole('button', { name: '提交中...' })
    expect(submitButton).toBeDisabled()
  })

  it('should handle checkout type selection', async () => {
    const user = userEvent.setup()
    
    render(
      <CheckoutApplicationForm
        elderOptions={mockElderOptions}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    )

    // 点击退住类型下拉框
    const typeSelect = screen.getByRole('combobox', { name: /退住类型/i })
    await user.click(typeSelect)

    // 检查退住类型选项
    await waitFor(() => {
      expect(screen.getByText('正常退住')).toBeInTheDocument()
      expect(screen.getByText('转院退住')).toBeInTheDocument()
      expect(screen.getByText('其他原因')).toBeInTheDocument()
    })

    // 选择转院退住
    await user.click(screen.getByText('转院退住'))

    // 验证选择是否生效（这里可能需要检查表单状态或其他指示器）
  })
})
