import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect } from '@jest/globals'
import { 
  AdmissionSteps, 
  SimpleAdmissionSteps, 
  getStatusDescription,
  STATUS_DESCRIPTIONS 
} from '@/components/admission/admission-steps'

describe('AdmissionSteps', () => {
  describe('AdmissionSteps 组件', () => {
    it('应该正确渲染所有步骤', () => {
      render(<AdmissionSteps currentStatus={1} />)
      
      expect(screen.getByText('提交申请')).toBeInTheDocument()
      expect(screen.getByText('健康评估')).toBeInTheDocument()
      expect(screen.getByText('审核流程')).toBeInTheDocument()
      expect(screen.getByText('签订合同')).toBeInTheDocument()
      expect(screen.getByText('缴费处理')).toBeInTheDocument()
      expect(screen.getByText('入住安排')).toBeInTheDocument()
    })

    it('应该正确显示当前步骤状态', () => {
      render(<AdmissionSteps currentStatus={2} />)
      
      // 第一步应该是完成状态
      const firstStep = screen.getByText('提交申请').closest('div')
      expect(firstStep).toHaveClass('text-green-600')
      
      // 第二步应该是当前状态
      const secondStep = screen.getByText('健康评估').closest('div')
      expect(secondStep).toHaveClass('text-blue-600')
    })

    it('应该正确处理被拒绝的状态', () => {
      render(<AdmissionSteps currentStatus={6} />)
      
      // 前三步应该显示错误状态
      const firstStep = screen.getByText('提交申请').closest('div')
      expect(firstStep).toHaveClass('text-red-600')
    })

    it('应该支持垂直布局', () => {
      const { container } = render(
        <AdmissionSteps currentStatus={1} orientation="vertical" />
      )
      
      // 垂直布局应该有不同的样式类
      expect(container.firstChild).toHaveClass('space-y-4')
    })

    it('应该支持隐藏描述', () => {
      render(<AdmissionSteps currentStatus={1} showDescription={false} />)
      
      // 描述文本不应该出现
      expect(screen.queryByText('填写入住申请表')).not.toBeInTheDocument()
    })

    it('应该正确处理已入住状态', () => {
      render(<AdmissionSteps currentStatus={7} />)
      
      // 所有步骤都应该是完成状态
      const lastStep = screen.getByText('入住安排').closest('div')
      expect(lastStep).toHaveClass('text-green-600')
    })
  })

  describe('SimpleAdmissionSteps 组件', () => {
    it('应该渲染简化的步骤条', () => {
      const { container } = render(<SimpleAdmissionSteps currentStatus={3} />)
      
      // 应该有6个步骤圆圈
      const stepCircles = container.querySelectorAll('[title]')
      expect(stepCircles).toHaveLength(6)
    })

    it('应该正确显示步骤编号', () => {
      render(<SimpleAdmissionSteps currentStatus={1} />)
      
      // 第一步应该显示当前状态，其他显示编号
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
      expect(screen.getByText('4')).toBeInTheDocument()
      expect(screen.getByText('5')).toBeInTheDocument()
      expect(screen.getByText('6')).toBeInTheDocument()
    })

    it('应该正确显示完成状态的图标', () => {
      const { container } = render(<SimpleAdmissionSteps currentStatus={5} />)
      
      // 前面的步骤应该显示完成图标
      const completedSteps = container.querySelectorAll('.text-green-600')
      expect(completedSteps.length).toBeGreaterThan(0)
    })

    it('应该正确显示错误状态', () => {
      const { container } = render(<SimpleAdmissionSteps currentStatus={6} />)
      
      // 应该有错误状态的步骤
      const errorSteps = container.querySelectorAll('.text-red-600')
      expect(errorSteps.length).toBeGreaterThan(0)
    })

    it('应该支持自定义样式类', () => {
      const { container } = render(
        <SimpleAdmissionSteps currentStatus={1} className="custom-class" />
      )
      
      expect(container.firstChild).toHaveClass('custom-class')
    })
  })

  describe('getStatusDescription 函数', () => {
    it('应该返回正确的状态描述', () => {
      expect(getStatusDescription(1)).toBe('申请已提交，等待评估')
      expect(getStatusDescription(2)).toBe('正在进行健康评估')
      expect(getStatusDescription(3)).toBe('等待初审')
      expect(getStatusDescription(4)).toBe('审核中')
      expect(getStatusDescription(5)).toBe('审核通过，准备签约')
      expect(getStatusDescription(6)).toBe('申请被拒绝')
      expect(getStatusDescription(7)).toBe('已入住')
    })

    it('应该处理未知状态', () => {
      expect(getStatusDescription(999)).toBe('未知状态')
      expect(getStatusDescription(0)).toBe('未知状态')
      expect(getStatusDescription(-1)).toBe('未知状态')
    })
  })

  describe('STATUS_DESCRIPTIONS 常量', () => {
    it('应该包含所有必要的状态', () => {
      expect(STATUS_DESCRIPTIONS).toHaveProperty('1')
      expect(STATUS_DESCRIPTIONS).toHaveProperty('2')
      expect(STATUS_DESCRIPTIONS).toHaveProperty('3')
      expect(STATUS_DESCRIPTIONS).toHaveProperty('4')
      expect(STATUS_DESCRIPTIONS).toHaveProperty('5')
      expect(STATUS_DESCRIPTIONS).toHaveProperty('6')
      expect(STATUS_DESCRIPTIONS).toHaveProperty('7')
    })

    it('所有状态描述都应该是字符串', () => {
      Object.values(STATUS_DESCRIPTIONS).forEach(description => {
        expect(typeof description).toBe('string')
        expect(description.length).toBeGreaterThan(0)
      })
    })
  })

  describe('步骤状态逻辑', () => {
    it('应该正确计算步骤状态 - 正常流程', () => {
      // 测试各个状态下的步骤状态
      const testCases = [
        { status: 1, expectedCurrentStep: 0 }, // 提交申请
        { status: 2, expectedCurrentStep: 1 }, // 健康评估
        { status: 3, expectedCurrentStep: 2 }, // 审核流程
        { status: 4, expectedCurrentStep: 2 }, // 审核流程
        { status: 5, expectedCurrentStep: 3 }, // 签订合同/缴费处理
        { status: 7, expectedCurrentStep: 5 }, // 入住安排
      ]

      testCases.forEach(({ status, expectedCurrentStep }) => {
        const { container } = render(<AdmissionSteps currentStatus={status} />)
        
        // 检查当前步骤是否正确高亮
        const currentSteps = container.querySelectorAll('.text-blue-600')
        expect(currentSteps.length).toBeGreaterThan(0)
      })
    })

    it('应该正确处理并行步骤（合同和缴费）', () => {
      render(<AdmissionSteps currentStatus={5} />)
      
      // 状态5时，合同和缴费都应该是当前状态
      expect(screen.getByText('签订合同')).toBeInTheDocument()
      expect(screen.getByText('缴费处理')).toBeInTheDocument()
    })

    it('应该正确处理拒绝状态的错误显示', () => {
      const { container } = render(<AdmissionSteps currentStatus={6} />)
      
      // 前三步应该显示错误状态
      const errorSteps = container.querySelectorAll('.text-red-600')
      expect(errorSteps.length).toBeGreaterThan(0)
    })
  })

  describe('响应式和样式', () => {
    it('应该支持水平和垂直布局切换', () => {
      const { rerender, container } = render(
        <AdmissionSteps currentStatus={1} orientation="horizontal" />
      )
      
      // 水平布局
      expect(container.querySelector('.flex.items-center.justify-between')).toBeInTheDocument()
      
      // 切换到垂直布局
      rerender(<AdmissionSteps currentStatus={1} orientation="vertical" />)
      expect(container.querySelector('.space-y-4')).toBeInTheDocument()
    })

    it('应该正确应用自定义样式类', () => {
      const { container } = render(
        <AdmissionSteps currentStatus={1} className="custom-steps" />
      )
      
      expect(container.firstChild).toHaveClass('custom-steps')
    })

    it('应该在不同状态下应用正确的颜色样式', () => {
      const { container } = render(<AdmissionSteps currentStatus={2} />)
      
      // 完成状态应该是绿色
      const completedElements = container.querySelectorAll('.text-green-600')
      expect(completedElements.length).toBeGreaterThan(0)
      
      // 当前状态应该是蓝色
      const currentElements = container.querySelectorAll('.text-blue-600')
      expect(currentElements.length).toBeGreaterThan(0)
      
      // 待处理状态应该是灰色
      const pendingElements = container.querySelectorAll('.text-gray-400')
      expect(pendingElements.length).toBeGreaterThan(0)
    })
  })

  describe('可访问性', () => {
    it('应该为步骤提供适当的标题属性', () => {
      render(<SimpleAdmissionSteps currentStatus={1} />)
      
      // 检查是否有 title 属性
      expect(screen.getByTitle('提交申请')).toBeInTheDocument()
      expect(screen.getByTitle('健康评估')).toBeInTheDocument()
      expect(screen.getByTitle('审核流程')).toBeInTheDocument()
    })

    it('应该使用语义化的图标', () => {
      const { container } = render(<AdmissionSteps currentStatus={5} />)
      
      // 检查是否使用了适当的图标
      expect(container.querySelector('[data-lucide="check-circle"]')).toBeInTheDocument()
      expect(container.querySelector('[data-lucide="clock"]')).toBeInTheDocument()
    })
  })
})
