import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import NursingOverviewPage from '@/app/(dashboard)/nursing/page'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: vi.fn()
}))

// Mock fetch
global.fetch = vi.fn()

// Mock ECharts
vi.mock('echarts-for-react', () => ({
  default: ({ option, style }: any) => (
    <div data-testid="echarts-chart" style={style}>
      Mock Chart: {JSON.stringify(option)}
    </div>
  )
}))

describe('NursingOverviewPage', () => {
  const mockPush = vi.fn()
  const mockRouter = { push: mockPush }

  beforeEach(() => {
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  it('should render loading state initially', () => {
    vi.mocked(fetch).mockImplementation(() => new Promise(() => {})) // Never resolves

    render(<NursingOverviewPage />)

    expect(screen.getByText('加载中...')).toBeInTheDocument()
  })

  it('should render overview data when loaded successfully', async () => {
    const mockOverviewData = {
      success: true,
      data: {
        elderStats: {
          total: 50,
          inResidence: 45,
          onLeave: 3,
          discharged: 2
        },
        careLevelDistribution: [
          { level: 1, count: 15, label: '自理' },
          { level: 2, count: 20, label: '半护理' },
          { level: 3, count: 10, label: '全护理' }
        ],
        roomUsage: {
          totalRooms: 25,
          occupiedRooms: 23,
          availableRooms: 2,
          bedUsageRate: 92
        },
        recentLeaveApplications: [
          {
            id: 'leave-1',
            applicationNumber: 'LA20241201001',
            elderName: '张三',
            leaveType: 1,
            leaveTypeLabel: '外出',
            startDate: '2024-12-01',
            endDate: '2024-12-02',
            status: 1,
            statusLabel: '待审核'
          }
        ],
        recentAccidentReports: [
          {
            id: 'accident-1',
            reportNumber: 'AR20241201001',
            elderName: '李四',
            accidentType: 1,
            accidentTypeLabel: '跌倒',
            severity: 2,
            severityLabel: '一般',
            accidentDate: '2024-12-01',
            status: 1,
            statusLabel: '待处理'
          }
        ],
        todayVisits: [
          {
            id: 'visit-1',
            registrationNumber: 'VR20241201001',
            elderName: '王五',
            visitorName: '王小明',
            visitType: 1,
            visitTypeLabel: '家属探访',
            plannedStartTime: '14:00',
            plannedEndTime: '16:00',
            status: 1,
            statusLabel: '预约'
          }
        ]
      }
    }

    vi.mocked(fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockOverviewData)
    } as Response)

    render(<NursingOverviewPage />)

    await waitFor(() => {
      expect(screen.getByText('在住服务总览')).toBeInTheDocument()
    })

    // Check statistics cards
    expect(screen.getByText('50')).toBeInTheDocument() // Total elders
    expect(screen.getByText('45')).toBeInTheDocument() // In residence
    expect(screen.getByText('3')).toBeInTheDocument()  // On leave
    expect(screen.getByText('2')).toBeInTheDocument()  // Discharged

    expect(screen.getByText('25')).toBeInTheDocument() // Total rooms
    expect(screen.getByText('23')).toBeInTheDocument() // Occupied rooms
    expect(screen.getByText('2')).toBeInTheDocument()  // Available rooms
    expect(screen.getByText('92%')).toBeInTheDocument() // Bed usage rate

    // Check recent activities
    expect(screen.getByText('张三')).toBeInTheDocument()
    expect(screen.getByText('李四')).toBeInTheDocument()
    expect(screen.getByText('王五')).toBeInTheDocument()
  })

  it('should handle API error gracefully', async () => {
    vi.mocked(fetch).mockResolvedValue({
      ok: false,
      status: 500
    } as Response)

    render(<NursingOverviewPage />)

    await waitFor(() => {
      expect(screen.getByText('加载失败')).toBeInTheDocument()
    })
  })

  it('should navigate to correct pages when clicking navigation buttons', async () => {
    const mockOverviewData = {
      success: true,
      data: {
        elderStats: { total: 0, inResidence: 0, onLeave: 0, discharged: 0 },
        careLevelDistribution: [],
        roomUsage: { totalRooms: 0, occupiedRooms: 0, availableRooms: 0, bedUsageRate: 0 },
        recentLeaveApplications: [],
        recentAccidentReports: [],
        todayVisits: []
      }
    }

    vi.mocked(fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockOverviewData)
    } as Response)

    render(<NursingOverviewPage />)

    await waitFor(() => {
      expect(screen.getByText('在住服务总览')).toBeInTheDocument()
    })

    // Test navigation buttons
    const leaveApplicationsButton = screen.getByText('请假申请')
    leaveApplicationsButton.click()
    expect(mockPush).toHaveBeenCalledWith('/nursing/leave-applications')

    const accidentReportsButton = screen.getByText('事故报告')
    accidentReportsButton.click()
    expect(mockPush).toHaveBeenCalledWith('/nursing/accident-reports')

    const visitRegistrationsButton = screen.getByText('探访登记')
    visitRegistrationsButton.click()
    expect(mockPush).toHaveBeenCalledWith('/nursing/visit-registrations')
  })

  it('should render charts when data is available', async () => {
    const mockOverviewData = {
      success: true,
      data: {
        elderStats: { total: 50, inResidence: 45, onLeave: 3, discharged: 2 },
        careLevelDistribution: [
          { level: 1, count: 15, label: '自理' },
          { level: 2, count: 20, label: '半护理' },
          { level: 3, count: 10, label: '全护理' }
        ],
        roomUsage: { totalRooms: 25, occupiedRooms: 23, availableRooms: 2, bedUsageRate: 92 },
        recentLeaveApplications: [],
        recentAccidentReports: [],
        todayVisits: []
      }
    }

    vi.mocked(fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockOverviewData)
    } as Response)

    render(<NursingOverviewPage />)

    await waitFor(() => {
      expect(screen.getAllByTestId('echarts-chart')).toHaveLength(2) // Care level and room usage charts
    })
  })

  it('should show empty state when no recent activities', async () => {
    const mockOverviewData = {
      success: true,
      data: {
        elderStats: { total: 0, inResidence: 0, onLeave: 0, discharged: 0 },
        careLevelDistribution: [],
        roomUsage: { totalRooms: 0, occupiedRooms: 0, availableRooms: 0, bedUsageRate: 0 },
        recentLeaveApplications: [],
        recentAccidentReports: [],
        todayVisits: []
      }
    }

    vi.mocked(fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockOverviewData)
    } as Response)

    render(<NursingOverviewPage />)

    await waitFor(() => {
      expect(screen.getByText('暂无数据')).toBeInTheDocument()
    })
  })

  it('should refresh data when refresh button is clicked', async () => {
    const mockOverviewData = {
      success: true,
      data: {
        elderStats: { total: 50, inResidence: 45, onLeave: 3, discharged: 2 },
        careLevelDistribution: [],
        roomUsage: { totalRooms: 25, occupiedRooms: 23, availableRooms: 2, bedUsageRate: 92 },
        recentLeaveApplications: [],
        recentAccidentReports: [],
        todayVisits: []
      }
    }

    vi.mocked(fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockOverviewData)
    } as Response)

    render(<NursingOverviewPage />)

    await waitFor(() => {
      expect(screen.getByText('在住服务总览')).toBeInTheDocument()
    })

    // Initial fetch call
    expect(fetch).toHaveBeenCalledTimes(1)

    // Click refresh button (if exists)
    const refreshButton = screen.queryByText('刷新')
    if (refreshButton) {
      refreshButton.click()
      expect(fetch).toHaveBeenCalledTimes(2)
    }
  })
})
