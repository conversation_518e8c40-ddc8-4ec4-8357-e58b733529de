import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, jest, beforeEach } from '@jest/globals'
import { AdmissionApplicationForm } from '@/components/admission/admission-application-form'

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

// Mock date-fns
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => {
    if (formatStr === 'yyyy-MM-dd') {
      return '2024-02-01'
    }
    return '2024-02-01'
  }),
}))

describe('AdmissionApplicationForm', () => {
  const mockOnSubmit = jest.fn()
  const user = userEvent.setup()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('表单渲染', () => {
    it('应该渲染所有必要的表单字段', () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      // 老人基本信息
      expect(screen.getByLabelText(/老人姓名/)).toBeInTheDocument()
      expect(screen.getByLabelText(/年龄/)).toBeInTheDocument()
      expect(screen.getByLabelText(/性别/)).toBeInTheDocument()
      expect(screen.getByLabelText(/身份证号/)).toBeInTheDocument()
      expect(screen.getByLabelText(/联系电话/)).toBeInTheDocument()

      // 申请人信息
      expect(screen.getByLabelText(/申请人姓名/)).toBeInTheDocument()
      expect(screen.getByLabelText(/与老人关系/)).toBeInTheDocument()
      expect(screen.getByLabelText(/联系地址/)).toBeInTheDocument()

      // 紧急联系人
      expect(screen.getByLabelText(/联系人姓名/)).toBeInTheDocument()
      expect(screen.getByLabelText(/紧急联系人.*联系电话/)).toBeInTheDocument()

      // 健康和护理信息
      expect(screen.getByLabelText(/健康状况/)).toBeInTheDocument()
      expect(screen.getByLabelText(/护理需求/)).toBeInTheDocument()

      // 入住信息
      expect(screen.getByLabelText(/预期入住日期/)).toBeInTheDocument()
    })

    it('应该显示正确的卡片标题', () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      expect(screen.getByText('老人基本信息')).toBeInTheDocument()
      expect(screen.getByText('申请人信息')).toBeInTheDocument()
      expect(screen.getByText('紧急联系人')).toBeInTheDocument()
      expect(screen.getByText('健康和护理信息')).toBeInTheDocument()
      expect(screen.getByText('入住信息')).toBeInTheDocument()
      expect(screen.getByText('相关文件')).toBeInTheDocument()
    })

    it('应该显示提交和重置按钮', () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      expect(screen.getByRole('button', { name: /提交申请/ })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /重置/ })).toBeInTheDocument()
    })
  })

  describe('表单验证', () => {
    it('应该验证必填字段', async () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      const submitButton = screen.getByRole('button', { name: /提交申请/ })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('请输入老人姓名')).toBeInTheDocument()
        expect(screen.getByText('请输入有效年龄')).toBeInTheDocument()
        expect(screen.getByText('请输入申请人姓名')).toBeInTheDocument()
        expect(screen.getByText('请选择与老人关系')).toBeInTheDocument()
        expect(screen.getByText('请输入紧急联系人姓名')).toBeInTheDocument()
        expect(screen.getByText('请描述健康状况')).toBeInTheDocument()
        expect(screen.getByText('请描述护理需求')).toBeInTheDocument()
        expect(screen.getByText('请选择预期入住日期')).toBeInTheDocument()
      })

      expect(mockOnSubmit).not.toHaveBeenCalled()
    })

    it('应该验证年龄范围', async () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      const ageInput = screen.getByLabelText(/年龄/)
      await user.clear(ageInput)
      await user.type(ageInput, '150')

      const submitButton = screen.getByRole('button', { name: /提交申请/ })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('年龄不能超过120岁')).toBeInTheDocument()
      })
    })

    it('应该验证身份证号长度', async () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      const idCardInput = screen.getAllByLabelText(/身份证号/)[0]
      await user.clear(idCardInput)
      await user.type(idCardInput, '123')

      const submitButton = screen.getByRole('button', { name: /提交申请/ })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('请输入有效身份证号')).toBeInTheDocument()
      })
    })

    it('应该验证手机号长度', async () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      const phoneInput = screen.getAllByLabelText(/联系电话/)[0]
      await user.clear(phoneInput)
      await user.type(phoneInput, '123')

      const submitButton = screen.getByRole('button', { name: /提交申请/ })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('请输入有效手机号')).toBeInTheDocument()
      })
    })
  })

  describe('表单交互', () => {
    it('应该能够填写所有字段', async () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      // 填写老人基本信息
      await user.type(screen.getByLabelText(/老人姓名/), '张三')
      await user.type(screen.getByLabelText(/年龄/), '75')
      await user.type(screen.getAllByLabelText(/身份证号/)[0], '110101194801011234')
      await user.type(screen.getAllByLabelText(/联系电话/)[0], '13800138001')

      // 填写申请人信息
      await user.type(screen.getByLabelText(/申请人姓名/), '张小明')
      await user.type(screen.getAllByLabelText(/联系电话/)[1], '13800138002')
      await user.type(screen.getAllByLabelText(/身份证号/)[1], '110101197001011234')
      await user.type(screen.getByLabelText(/联系地址/), '北京市朝阳区测试街道1号')

      // 填写紧急联系人
      await user.type(screen.getByLabelText(/联系人姓名/), '李四')
      await user.type(screen.getByLabelText(/紧急联系人.*联系电话/), '13800138003')
      await user.type(screen.getByLabelText(/与老人关系.*紧急/), '女儿')

      // 填写健康信息
      await user.type(screen.getByLabelText(/健康状况/), '身体健康，行动自如')
      await user.type(screen.getByLabelText(/护理需求/), '日常生活照料')

      // 验证字段值
      expect(screen.getByDisplayValue('张三')).toBeInTheDocument()
      expect(screen.getByDisplayValue('75')).toBeInTheDocument()
      expect(screen.getByDisplayValue('张小明')).toBeInTheDocument()
      expect(screen.getByDisplayValue('李四')).toBeInTheDocument()
    })

    it('应该能够选择性别', async () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      const genderSelect = screen.getByRole('combobox', { name: /性别/ })
      await user.click(genderSelect)

      await waitFor(() => {
        expect(screen.getByText('男')).toBeInTheDocument()
        expect(screen.getByText('女')).toBeInTheDocument()
      })

      await user.click(screen.getByText('女'))
      expect(genderSelect).toHaveTextContent('女')
    })

    it('应该能够选择与老人关系', async () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      const relationSelect = screen.getByRole('combobox', { name: /与老人关系/ })
      await user.click(relationSelect)

      await waitFor(() => {
        expect(screen.getByText('儿子')).toBeInTheDocument()
        expect(screen.getByText('女儿')).toBeInTheDocument()
        expect(screen.getByText('配偶')).toBeInTheDocument()
      })

      await user.click(screen.getByText('儿子'))
      expect(relationSelect).toHaveTextContent('儿子')
    })

    it('应该能够重置表单', async () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      // 填写一些字段
      await user.type(screen.getByLabelText(/老人姓名/), '张三')
      await user.type(screen.getByLabelText(/年龄/), '75')

      // 重置表单
      const resetButton = screen.getByRole('button', { name: /重置/ })
      await user.click(resetButton)

      // 验证字段已清空
      expect(screen.getByLabelText(/老人姓名/)).toHaveValue('')
      expect(screen.getByLabelText(/年龄/)).toHaveValue(0)
    })
  })

  describe('文件上传', () => {
    it('应该显示文件上传区域', () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      expect(screen.getByText(/点击上传.*或拖拽文件到此处/)).toBeInTheDocument()
      expect(screen.getByText(/支持 PDF、JPG、PNG 格式/)).toBeInTheDocument()
    })

    it('应该能够上传文件', async () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
      const fileInput = screen.getByRole('textbox', { hidden: true })

      await user.upload(fileInput, file)

      await waitFor(() => {
        expect(screen.getByText('test.pdf')).toBeInTheDocument()
        expect(screen.getByText('已上传文件：')).toBeInTheDocument()
      })
    })

    it('应该能够删除已上传的文件', async () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
      const fileInput = screen.getByRole('textbox', { hidden: true })

      await user.upload(fileInput, file)

      await waitFor(() => {
        expect(screen.getByText('test.pdf')).toBeInTheDocument()
      })

      const deleteButton = screen.getByRole('button', { name: /删除/ })
      await user.click(deleteButton)

      await waitFor(() => {
        expect(screen.queryByText('test.pdf')).not.toBeInTheDocument()
      })
    })
  })

  describe('表单提交', () => {
    const validFormData = {
      elderName: '张三',
      elderAge: 75,
      elderGender: 1,
      elderIdCard: '110101194801011234',
      elderPhone: '13800138001',
      applicantName: '张小明',
      applicantRelation: '儿子',
      applicantPhone: '13800138002',
      applicantIdCard: '110101197001011234',
      applicantAddress: '北京市朝阳区测试街道1号',
      emergencyContact: '李四',
      emergencyPhone: '13800138003',
      emergencyRelation: '女儿',
      healthCondition: '身体健康',
      careNeeds: '日常生活照料',
      expectedAdmissionDate: new Date('2024-02-01'),
    }

    it('应该在表单有效时调用 onSubmit', async () => {
      mockOnSubmit.mockResolvedValue(undefined)
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      // 填写所有必填字段
      await fillValidForm()

      const submitButton = screen.getByRole('button', { name: /提交申请/ })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            elderName: '张三',
            elderAge: 75,
            applicantName: '张小明',
          })
        )
      })
    })

    it('应该在提交时显示加载状态', async () => {
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} isLoading={true} />)

      const submitButton = screen.getByRole('button', { name: /提交申请/ })
      expect(submitButton).toBeDisabled()
      expect(screen.getByRole('button', { name: /提交申请/ })).toHaveTextContent('提交申请')
    })

    it('应该处理提交错误', async () => {
      const { toast } = require('sonner')
      mockOnSubmit.mockRejectedValue(new Error('提交失败'))
      
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      await fillValidForm()

      const submitButton = screen.getByRole('button', { name: /提交申请/ })
      await user.click(submitButton)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('提交失败，请重试')
      })
    })

    it('应该在成功提交后重置表单', async () => {
      const { toast } = require('sonner')
      mockOnSubmit.mockResolvedValue(undefined)
      
      render(<AdmissionApplicationForm onSubmit={mockOnSubmit} />)

      await fillValidForm()

      const submitButton = screen.getByRole('button', { name: /提交申请/ })
      await user.click(submitButton)

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('申请提交成功')
        expect(screen.getByLabelText(/老人姓名/)).toHaveValue('')
      })
    })
  })

  describe('初始数据', () => {
    it('应该使用提供的初始数据', () => {
      const initialData = {
        elderName: '李四',
        elderAge: 80,
        applicantName: '李小红',
      }

      render(
        <AdmissionApplicationForm 
          onSubmit={mockOnSubmit} 
          initialData={initialData}
        />
      )

      expect(screen.getByDisplayValue('李四')).toBeInTheDocument()
      expect(screen.getByDisplayValue('80')).toBeInTheDocument()
      expect(screen.getByDisplayValue('李小红')).toBeInTheDocument()
    })
  })

  // 辅助函数：填写有效表单
  async function fillValidForm() {
    await user.type(screen.getByLabelText(/老人姓名/), '张三')
    await user.type(screen.getByLabelText(/年龄/), '75')
    await user.type(screen.getAllByLabelText(/身份证号/)[0], '110101194801011234')
    await user.type(screen.getAllByLabelText(/联系电话/)[0], '13800138001')
    
    await user.type(screen.getByLabelText(/申请人姓名/), '张小明')
    
    // 选择关系
    const relationSelect = screen.getByRole('combobox', { name: /与老人关系/ })
    await user.click(relationSelect)
    await user.click(screen.getByText('儿子'))
    
    await user.type(screen.getAllByLabelText(/联系电话/)[1], '13800138002')
    await user.type(screen.getAllByLabelText(/身份证号/)[1], '110101197001011234')
    await user.type(screen.getByLabelText(/联系地址/), '北京市朝阳区测试街道1号')
    
    await user.type(screen.getByLabelText(/联系人姓名/), '李四')
    await user.type(screen.getByLabelText(/紧急联系人.*联系电话/), '13800138003')
    await user.type(screen.getByLabelText(/与老人关系.*紧急/), '女儿')
    
    await user.type(screen.getByLabelText(/健康状况/), '身体健康')
    await user.type(screen.getByLabelText(/护理需求/), '日常生活照料')
    
    // 选择日期（这里简化处理）
    const dateButton = screen.getByRole('button', { name: /请选择日期/ })
    await user.click(dateButton)
  }
})
