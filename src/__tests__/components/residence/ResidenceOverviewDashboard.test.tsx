import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ResidenceOverviewDashboard } from '@/components/residence/ResidenceOverviewDashboard'
import type { ResidenceOverviewData } from '@/components/residence/ResidenceOverviewDashboard'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => '01月15日'),
}))

vi.mock('date-fns/locale', () => ({
  zhCN: {},
}))

const mockData: ResidenceOverviewData = {
  overview: {
    totalRooms: 100,
    availableRooms: 80,
    occupiedRooms: 60,
    maintenanceRooms: 5,
    totalBeds: 200,
    occupiedBeds: 150,
    occupancyRate: 75,
    totalResidents: 145,
    newAdmissions: 8,
    pendingReservations: 12,
  },
  roomTypes: [
    {
      roomType: 1,
      roomTypeName: '单人间',
      roomCount: 30,
      totalBeds: 30,
      occupiedBeds: 25,
      occupancyRate: 83.33,
    },
    {
      roomType: 2,
      roomTypeName: '双人间',
      roomCount: 50,
      totalBeds: 100,
      occupiedBeds: 80,
      occupancyRate: 80,
    },
  ],
  reservations: {
    pendingReservations: 12,
    statusDistribution: [
      { status: 1, statusName: '待确认', count: 12 },
      { status: 2, statusName: '已确认', count: 25 },
      { status: 3, statusName: '已入住', count: 18 },
      { status: 4, statusName: '已取消', count: 3 },
      { status: 5, statusName: '已过期', count: 2 },
    ],
  },
  recentAdmissions: [
    {
      id: '1',
      name: '张三',
      age: 75,
      gender: 1,
      genderName: '男',
      roomNumber: '101',
      admissionDate: '2024-01-15',
      careLevel: 2,
      careLevelName: '半自理',
    },
    {
      id: '2',
      name: '李四',
      age: 82,
      gender: 2,
      genderName: '女',
      roomNumber: '102',
      admissionDate: '2024-01-14',
      careLevel: 1,
      careLevelName: '自理',
    },
  ],
  trend: [
    { date: '2024-01-01', newAdmissions: 2, newReservations: 5 },
    { date: '2024-01-02', newAdmissions: 1, newReservations: 3 },
    { date: '2024-01-03', newAdmissions: 3, newReservations: 4 },
  ],
  dateRange: {
    start: '2024-01-01',
    end: '2024-01-31',
  },
}

describe('ResidenceOverviewDashboard', () => {
  it('should render loading skeleton when loading is true', () => {
    render(<ResidenceOverviewDashboard data={mockData} loading={true} />)
    
    // Check for loading skeleton elements
    const skeletons = screen.getAllByRole('generic')
    expect(skeletons.length).toBeGreaterThan(0)
    
    // Should show animate-pulse class
    const animatedElements = document.querySelectorAll('.animate-pulse')
    expect(animatedElements.length).toBeGreaterThan(0)
  })

  it('should render overview metrics correctly', () => {
    render(<ResidenceOverviewDashboard data={mockData} loading={false} />)
    
    // Check core metrics
    expect(screen.getByText('100')).toBeInTheDocument() // Total rooms
    expect(screen.getByText('75%')).toBeInTheDocument() // Occupancy rate
    expect(screen.getByText('145')).toBeInTheDocument() // Total residents
    expect(screen.getByText('12')).toBeInTheDocument() // Pending reservations
    
    // Check labels
    expect(screen.getByText('总房间数')).toBeInTheDocument()
    expect(screen.getByText('床位使用率')).toBeInTheDocument()
    expect(screen.getByText('在院人数')).toBeInTheDocument()
    expect(screen.getByText('待处理预订')).toBeInTheDocument()
  })

  it('should render room type distribution correctly', () => {
    render(<ResidenceOverviewDashboard data={mockData} loading={false} />)
    
    // Check room type names
    expect(screen.getByText('单人间')).toBeInTheDocument()
    expect(screen.getByText('双人间')).toBeInTheDocument()
    
    // Check room counts
    expect(screen.getByText('30 间')).toBeInTheDocument()
    expect(screen.getByText('50 间')).toBeInTheDocument()
    
    // Check occupancy rates
    expect(screen.getByText('83.33%')).toBeInTheDocument()
    expect(screen.getByText('80%')).toBeInTheDocument()
  })

  it('should render reservation status distribution correctly', () => {
    render(<ResidenceOverviewDashboard data={mockData} loading={false} />)
    
    // Check status names
    expect(screen.getByText('待确认')).toBeInTheDocument()
    expect(screen.getByText('已确认')).toBeInTheDocument()
    expect(screen.getByText('已入住')).toBeInTheDocument()
    expect(screen.getByText('已取消')).toBeInTheDocument()
    expect(screen.getByText('已过期')).toBeInTheDocument()
    
    // Check counts
    expect(screen.getByText('25')).toBeInTheDocument() // 已确认
    expect(screen.getByText('18')).toBeInTheDocument() // 已入住
    expect(screen.getByText('3')).toBeInTheDocument() // 已取消
    expect(screen.getByText('2')).toBeInTheDocument() // 已过期
  })

  it('should render recent admissions correctly', () => {
    render(<ResidenceOverviewDashboard data={mockData} loading={false} />)
    
    // Check resident names
    expect(screen.getByText('张三')).toBeInTheDocument()
    expect(screen.getByText('李四')).toBeInTheDocument()
    
    // Check resident details
    expect(screen.getByText('75岁 · 男 · 半自理')).toBeInTheDocument()
    expect(screen.getByText('82岁 · 女 · 自理')).toBeInTheDocument()
    
    // Check room numbers
    expect(screen.getByText('101')).toBeInTheDocument()
    expect(screen.getByText('102')).toBeInTheDocument()
  })

  it('should show empty state when no recent admissions', () => {
    const dataWithoutAdmissions = {
      ...mockData,
      recentAdmissions: [],
    }
    
    render(<ResidenceOverviewDashboard data={dataWithoutAdmissions} loading={false} />)
    
    expect(screen.getByText('暂无最近入住记录')).toBeInTheDocument()
  })

  it('should handle null room numbers gracefully', () => {
    const dataWithNullRoom = {
      ...mockData,
      recentAdmissions: [
        {
          id: '1',
          name: '张三',
          age: 75,
          gender: 1,
          genderName: '男',
          roomNumber: null,
          admissionDate: '2024-01-15',
          careLevel: 2,
          careLevelName: '半自理',
        },
      ],
    }
    
    render(<ResidenceOverviewDashboard data={dataWithNullRoom} loading={false} />)
    
    expect(screen.getByText('未分配房间')).toBeInTheDocument()
  })

  it('should handle null admission dates gracefully', () => {
    const dataWithNullDate = {
      ...mockData,
      recentAdmissions: [
        {
          id: '1',
          name: '张三',
          age: 75,
          gender: 1,
          genderName: '男',
          roomNumber: '101',
          admissionDate: null,
          careLevel: 2,
          careLevelName: '半自理',
        },
      ],
    }
    
    render(<ResidenceOverviewDashboard data={dataWithNullDate} loading={false} />)
    
    expect(screen.getByText('未知日期')).toBeInTheDocument()
  })

  it('should apply custom className', () => {
    const { container } = render(
      <ResidenceOverviewDashboard 
        data={mockData} 
        loading={false} 
        className="custom-class" 
      />
    )
    
    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('should display correct bed utilization details', () => {
    render(<ResidenceOverviewDashboard data={mockData} loading={false} />)
    
    // Check bed utilization details
    expect(screen.getByText('150/200 床位')).toBeInTheDocument()
    expect(screen.getByText('可用 80 | 维修 5')).toBeInTheDocument()
    expect(screen.getByText('新入住 8 人')).toBeInTheDocument()
    expect(screen.getByText('需要及时处理')).toBeInTheDocument()
  })

  it('should format numbers with locale formatting', () => {
    const dataWithLargeNumbers = {
      ...mockData,
      overview: {
        ...mockData.overview,
        totalRooms: 1000,
        totalResidents: 1500,
        pendingReservations: 1200,
      },
    }
    
    render(<ResidenceOverviewDashboard data={dataWithLargeNumbers} loading={false} />)
    
    // Numbers should be formatted with locale (commas)
    expect(screen.getByText('1,000')).toBeInTheDocument()
    expect(screen.getByText('1,500')).toBeInTheDocument()
    expect(screen.getByText('1,200')).toBeInTheDocument()
  })
})
