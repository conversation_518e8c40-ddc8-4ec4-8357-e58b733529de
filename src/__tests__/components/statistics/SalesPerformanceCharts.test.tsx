import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { SalesPerformanceCharts } from '@/components/statistics/SalesPerformanceCharts'
import type { SalesPerformanceData } from '@/components/statistics/SalesPerformanceCharts'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}))

// Mock Chart component
vi.mock('@/components/ui/chart', () => ({
  Chart: ({ option, loading }: { option: any; loading?: boolean }) => (
    <div data-testid="chart" data-loading={loading}>
      {loading ? 'Loading chart...' : JSON.stringify(option.title)}
    </div>
  ),
}))

describe('SalesPerformanceCharts', () => {
  const mockData: SalesPerformanceData = {
    overview: {
      totalConsultations: 150,
      signedConsultations: 45,
      totalFollowUps: 320,
      activeSalesCount: 8,
      conversionRate: 30.0,
    },
    ranking: [
      {
        userId: 'user-1',
        userName: '张三',
        userEmail: '<EMAIL>',
        consultationCount: 25,
        signedCount: 8,
        followUpCount: 45,
        conversionRate: '32.00',
      },
      {
        userId: 'user-2',
        userName: '李四',
        userEmail: '<EMAIL>',
        consultationCount: 20,
        signedCount: 6,
        followUpCount: 38,
        conversionRate: '30.00',
      },
      {
        userId: 'user-3',
        userName: '王五',
        userEmail: '<EMAIL>',
        consultationCount: 18,
        signedCount: 5,
        followUpCount: 32,
        conversionRate: '27.78',
      },
    ],
    trend: [
      {
        date: '2024-01-01',
        consultationCount: 10,
        signedCount: 3,
        conversionRate: '30.00',
      },
      {
        date: '2024-01-02',
        consultationCount: 12,
        signedCount: 4,
        conversionRate: '33.33',
      },
      {
        date: '2024-01-03',
        consultationCount: 8,
        signedCount: 2,
        conversionRate: '25.00',
      },
    ],
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('渲染测试', () => {
    it('应该正确渲染业绩概览卡片', () => {
      render(<SalesPerformanceCharts data={mockData} />)

      // 检查概览卡片
      expect(screen.getByText('总咨询数')).toBeInTheDocument()
      expect(screen.getByText('150')).toBeInTheDocument()
      expect(screen.getByText('签约数量')).toBeInTheDocument()
      expect(screen.getByText('45')).toBeInTheDocument()
      expect(screen.getByText('跟进次数')).toBeInTheDocument()
      expect(screen.getByText('320')).toBeInTheDocument()
      expect(screen.getByText('活跃销售员')).toBeInTheDocument()
      expect(screen.getByText('8')).toBeInTheDocument()
      expect(screen.getByText('转化率 30%')).toBeInTheDocument()
    })

    it('应该正确渲染销售趋势图表', () => {
      render(<SalesPerformanceCharts data={mockData} />)

      // 检查图表标题
      expect(screen.getByText('销售趋势分析')).toBeInTheDocument()
      
      // 检查图表组件
      const charts = screen.getAllByTestId('chart')
      expect(charts.length).toBeGreaterThan(0)
      
      // 检查图表配置
      const trendChart = charts.find(chart => 
        chart.textContent?.includes('销售趋势分析')
      )
      expect(trendChart).toBeInTheDocument()
    })

    it('应该正确渲染销售员排行榜', () => {
      render(<SalesPerformanceCharts data={mockData} />)

      // 检查排行榜标题
      expect(screen.getByText('销售员业绩排行榜')).toBeInTheDocument()
      
      // 检查详细排行
      expect(screen.getByText('详细排行')).toBeInTheDocument()
      
      // 检查销售员信息
      expect(screen.getByText('张三')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      expect(screen.getByText('25 咨询')).toBeInTheDocument()
      expect(screen.getByText('8 签约 (32.00%)')).toBeInTheDocument()
      
      expect(screen.getByText('李四')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      expect(screen.getByText('20 咨询')).toBeInTheDocument()
      expect(screen.getByText('6 签约 (30.00%)')).toBeInTheDocument()
    })

    it('应该正确显示排名徽章', () => {
      render(<SalesPerformanceCharts data={mockData} />)

      // 检查排名徽章
      expect(screen.getByText('#1')).toBeInTheDocument()
      expect(screen.getByText('#2')).toBeInTheDocument()
      expect(screen.getByText('#3')).toBeInTheDocument()
    })
  })

  describe('加载状态测试', () => {
    it('应该显示加载骨架屏', () => {
      render(<SalesPerformanceCharts data={mockData} loading={true} />)

      // 检查骨架屏
      const skeletons = screen.getAllByRole('generic').filter(el => 
        el.className.includes('animate-pulse')
      )
      expect(skeletons.length).toBeGreaterThan(0)
    })

    it('应该在图表中显示加载状态', () => {
      render(<SalesPerformanceCharts data={mockData} loading={true} />)

      // 检查图表加载状态
      const charts = screen.getAllByTestId('chart')
      charts.forEach(chart => {
        expect(chart.getAttribute('data-loading')).toBe('true')
      })
    })
  })

  describe('数据处理测试', () => {
    it('应该正确处理空数据', () => {
      const emptyData: SalesPerformanceData = {
        overview: {
          totalConsultations: 0,
          signedConsultations: 0,
          totalFollowUps: 0,
          activeSalesCount: 0,
          conversionRate: 0,
        },
        ranking: [],
        trend: [],
      }

      render(<SalesPerformanceCharts data={emptyData} />)

      // 检查空数据显示
      expect(screen.getByText('0')).toBeInTheDocument()
      expect(screen.getByText('转化率 0%')).toBeInTheDocument()
    })

    it('应该正确处理大数值', () => {
      const largeData: SalesPerformanceData = {
        overview: {
          totalConsultations: 1500,
          signedConsultations: 450,
          totalFollowUps: 3200,
          activeSalesCount: 25,
          conversionRate: 30.0,
        },
        ranking: mockData.ranking,
        trend: mockData.trend,
      }

      render(<SalesPerformanceCharts data={largeData} />)

      // 检查数值格式化
      expect(screen.getByText('1,500')).toBeInTheDocument()
      expect(screen.getByText('450')).toBeInTheDocument()
      expect(screen.getByText('3,200')).toBeInTheDocument()
    })

    it('应该正确限制排行榜显示数量', () => {
      const manyRankingData: SalesPerformanceData = {
        ...mockData,
        ranking: [
          ...mockData.ranking,
          ...Array.from({ length: 15 }, (_, i) => ({
            userId: `user-${i + 4}`,
            userName: `用户${i + 4}`,
            userEmail: `user${i + 4}@example.com`,
            consultationCount: 10 - i,
            signedCount: 3 - Math.floor(i / 2),
            followUpCount: 20 - i,
            conversionRate: '25.00',
          })),
        ],
      }

      render(<SalesPerformanceCharts data={manyRankingData} />)

      // 应该只显示前10名
      expect(screen.getByText('#1')).toBeInTheDocument()
      expect(screen.getByText('#10')).toBeInTheDocument()
      expect(screen.queryByText('#11')).not.toBeInTheDocument()
    })
  })

  describe('样式和布局测试', () => {
    it('应该应用自定义 className', () => {
      const { container } = render(
        <SalesPerformanceCharts data={mockData} className="custom-class" />
      )

      expect(container.firstChild).toHaveClass('custom-class')
    })

    it('应该使用正确的网格布局', () => {
      render(<SalesPerformanceCharts data={mockData} />)

      // 检查网格布局类
      const gridContainer = screen.getByText('总咨询数').closest('.grid')
      expect(gridContainer).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-4')
    })

    it('应该显示正确的图标', () => {
      render(<SalesPerformanceCharts data={mockData} />)

      // 检查图标是否存在（通过 aria-hidden 属性）
      const icons = document.querySelectorAll('[aria-hidden="true"]')
      expect(icons.length).toBeGreaterThan(0)
    })
  })

  describe('图表配置测试', () => {
    it('应该生成正确的趋势图表配置', () => {
      render(<SalesPerformanceCharts data={mockData} />)

      const charts = screen.getAllByTestId('chart')
      const trendChart = charts.find(chart => 
        chart.textContent?.includes('销售趋势分析')
      )
      
      expect(trendChart).toBeInTheDocument()
    })

    it('应该生成正确的排行榜图表配置', () => {
      render(<SalesPerformanceCharts data={mockData} />)

      const charts = screen.getAllByTestId('chart')
      const rankingChart = charts.find(chart => 
        chart.textContent?.includes('销售员业绩排行榜')
      )
      
      expect(rankingChart).toBeInTheDocument()
    })
  })

  describe('响应式设计测试', () => {
    it('应该在不同屏幕尺寸下正确显示', () => {
      render(<SalesPerformanceCharts data={mockData} />)

      // 检查响应式类
      const gridContainer = screen.getByText('总咨询数').closest('.grid')
      expect(gridContainer).toHaveClass('grid-cols-1')
      expect(gridContainer).toHaveClass('md:grid-cols-2')
      expect(gridContainer).toHaveClass('lg:grid-cols-4')
    })
  })
})
