/**
 * 表单管理 Hooks
 *
 * 提供表单状态管理、验证、提交和错误处理功能
 */

import { useCallback, useEffect, useState } from 'react'
import {
  useForm,
  UseFormReturn,
  FieldValues,
  UseFormProps,
  Path,
  PathValue,
  FieldErrors,
  SubmitHandler,
  SubmitErrorHandler,
  useFieldArray,
} from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useApp, useNotifications, useAsyncOperation } from '@/store'

// 表单配置类型
export interface FormConfig<T extends FieldValues> extends UseFormProps<T> {
  schema?: z.ZodSchema<T>
  onSubmit?: SubmitHandler<T>
  onError?: SubmitErrorHandler<T>
  onSuccess?: (data: T) => void
  submitKey?: string
  autoSave?: boolean
  autoSaveDelay?: number
  resetOnSuccess?: boolean
  showSuccessMessage?: boolean
  showErrorMessage?: boolean
  successMessage?: string
  errorMessage?: string
}

// 表单状态类型
export interface FormState {
  isSubmitting: boolean
  isValidating: boolean
  isDirty: boolean
  isValid: boolean
  submitCount: number
  hasErrors: boolean
  lastSubmitTime?: Date
}

/**
 * 增强的表单 Hook
 */
export function useEnhancedForm<T extends FieldValues>(
  config: FormConfig<T> = {}
) {
  const {
    schema,
    onSubmit,
    onError,
    onSuccess,
    submitKey = 'form-submit',
    autoSave = false,
    autoSaveDelay = 2000,
    resetOnSuccess = false,
    showSuccessMessage = true,
    showErrorMessage = true,
    successMessage = '保存成功',
    errorMessage = '保存失败',
    ...formProps
  } = config

  const { showSuccess, showError } = useNotifications()
  const { execute } = useAsyncOperation()
  const [autoSaveTimer, setAutoSaveTimer] = useState<NodeJS.Timeout>()

  // 创建表单实例
  const form = useForm<T>({
    ...formProps,
    resolver: schema ? zodResolver(schema) : formProps.resolver,
  })

  const {
    handleSubmit,
    formState: {
      isSubmitting,
      isValidating,
      isDirty,
      isValid,
      submitCount,
      errors,
    },
    reset,
    watch,
  } = form

  // 表单状态
  const formState: FormState = {
    isSubmitting,
    isValidating,
    isDirty,
    isValid,
    submitCount,
    hasErrors: Object.keys(errors).length > 0,
    lastSubmitTime: submitCount > 0 ? new Date() : undefined,
  }

  // 提交处理函数
  const handleFormSubmit = useCallback(
    async (data: T) => {
      if (!onSubmit) return

      try {
        await execute(() => onSubmit(data), {
          loadingKey: submitKey,
          successMessage: showSuccessMessage ? successMessage : undefined,
          errorMessage: showErrorMessage ? errorMessage : undefined,
          showSuccessNotification: showSuccessMessage,
          showErrorNotification: showErrorMessage,
          onSuccess: result => {
            onSuccess?.(data)
            if (resetOnSuccess) {
              reset()
            }
          },
        })
      } catch (error) {
        console.error('表单提交失败:', error)
      }
    },
    [
      onSubmit,
      execute,
      submitKey,
      showSuccessMessage,
      showErrorMessage,
      successMessage,
      errorMessage,
      onSuccess,
      resetOnSuccess,
      reset,
    ]
  )

  // 错误处理函数
  const handleFormError = useCallback(
    (errors: FieldErrors<T>) => {
      onError?.(errors)

      if (showErrorMessage) {
        const errorMessages = Object.values(errors)
          .map(error => error?.message)
          .filter(Boolean)
          .join(', ')

        if (errorMessages) {
          showError('表单验证失败', errorMessages)
        }
      }
    },
    [onError, showErrorMessage, showError]
  )

  // 自动保存功能
  useEffect(() => {
    if (!autoSave || !onSubmit || !isDirty) return

    // 清除之前的定时器
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer)
    }

    // 设置新的定时器
    const timer = setTimeout(() => {
      if (isValid) {
        const data = watch()
        handleFormSubmit(data as T)
      }
    }, autoSaveDelay)

    setAutoSaveTimer(timer)

    return () => {
      if (timer) {
        clearTimeout(timer)
      }
    }
  }, [
    autoSave,
    onSubmit,
    isDirty,
    isValid,
    autoSaveDelay,
    watch,
    handleFormSubmit,
  ])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer)
      }
    }
  }, [autoSaveTimer])

  return {
    ...form,
    formState,
    handleSubmit: handleSubmit(handleFormSubmit, handleFormError),
    submitForm: handleFormSubmit,
  }
}

/**
 * 表单字段 Hook
 */
export function useFormField<T extends FieldValues, K extends Path<T>>(
  form: UseFormReturn<T>,
  name: K,
  options: {
    required?: boolean
    validate?: (value: PathValue<T, K>) => boolean | string
    transform?: (value: any) => PathValue<T, K>
  } = {}
) {
  const { required, validate, transform } = options
  const {
    register,
    formState: { errors },
    setValue,
    watch,
    trigger,
  } = form

  const value = watch(name)
  const error = errors[name]
  const hasError = !!error

  // 注册字段
  const fieldProps = register(name, {
    required: required ? '此字段为必填项' : false,
    validate: validate
      ? value => {
          const result = validate(value)
          return result === true ? true : result || '验证失败'
        }
      : undefined,
  })

  // 设置值（带转换）
  const setFieldValue = useCallback(
    (value: any) => {
      const transformedValue = transform ? transform(value) : value
      setValue(name, transformedValue, {
        shouldValidate: true,
        shouldDirty: true,
      })
    },
    [setValue, name, transform]
  )

  // 触发验证
  const validateField = useCallback(() => {
    return trigger(name)
  }, [trigger, name])

  return {
    ...fieldProps,
    value,
    error,
    hasError,
    errorMessage: error?.message,
    setValue: setFieldValue,
    validate: validateField,
  }
}

/**
 * 表单数组 Hook
 */
export function useFormArray<T extends FieldValues, K extends Path<T>>(
  form: UseFormReturn<T>,
  name: K
) {
  const { control, watch } = form
  const { fields, append, prepend, remove, swap, move, insert, replace } =
    useFieldArray({
      control,
      name,
    })

  const values = watch(name)

  return {
    fields,
    values,
    append,
    prepend,
    remove,
    swap,
    move,
    insert,
    replace,
    length: fields.length,
  }
}

/**
 * 表单步骤 Hook
 */
export function useFormSteps<T extends FieldValues>(
  form: UseFormReturn<T>,
  steps: {
    name: string
    title: string
    description?: string
    fields: Path<T>[]
    validate?: (data: Partial<T>) => boolean | string
  }[]
) {
  const [currentStep, setCurrentStep] = useState(0)
  const { trigger, getValues } = form

  const currentStepConfig = steps[currentStep]
  const isFirstStep = currentStep === 0
  const isLastStep = currentStep === steps.length - 1

  // 验证当前步骤
  const validateCurrentStep = useCallback(async () => {
    if (!currentStepConfig) return false

    // 验证字段
    const isFieldsValid = await trigger(currentStepConfig.fields)

    // 自定义验证
    if (currentStepConfig.validate) {
      const data = getValues()
      const customValidation = currentStepConfig.validate(data)
      if (customValidation !== true) {
        return false
      }
    }

    return isFieldsValid
  }, [currentStep, currentStepConfig, trigger, getValues])

  // 下一步
  const nextStep = useCallback(async () => {
    if (isLastStep) return false

    const isValid = await validateCurrentStep()
    if (isValid) {
      setCurrentStep(prev => prev + 1)
      return true
    }
    return false
  }, [isLastStep, validateCurrentStep])

  // 上一步
  const prevStep = useCallback(() => {
    if (isFirstStep) return false
    setCurrentStep(prev => prev - 1)
    return true
  }, [isFirstStep])

  // 跳转到指定步骤
  const goToStep = useCallback(
    async (step: number) => {
      if (step < 0 || step >= steps.length) return false

      // 如果是向前跳转，需要验证中间的步骤
      if (step > currentStep) {
        for (let i = currentStep; i < step; i++) {
          setCurrentStep(i)
          const isValid = await validateCurrentStep()
          if (!isValid) {
            return false
          }
        }
      }

      setCurrentStep(step)
      return true
    },
    [currentStep, steps.length, validateCurrentStep]
  )

  return {
    currentStep,
    currentStepConfig,
    steps,
    isFirstStep,
    isLastStep,
    nextStep,
    prevStep,
    goToStep,
    validateCurrentStep,
    progress: ((currentStep + 1) / steps.length) * 100,
  }
}

/**
 * 表单持久化 Hook
 */
export function useFormPersistence<T extends FieldValues>(
  form: UseFormReturn<T>,
  key: string,
  options: {
    storage?: 'localStorage' | 'sessionStorage'
    debounceDelay?: number
    exclude?: Path<T>[]
  } = {}
) {
  const {
    storage = 'localStorage',
    debounceDelay = 1000,
    exclude = [],
  } = options
  const { watch, reset } = form
  const [saveTimer, setSaveTimer] = useState<NodeJS.Timeout>()

  // 保存到存储
  const saveToStorage = useCallback(
    (data: T) => {
      try {
        const filteredData = { ...data }
        exclude.forEach(field => {
          delete filteredData[field]
        })

        const storageObj =
          storage === 'localStorage' ? localStorage : sessionStorage
        storageObj.setItem(key, JSON.stringify(filteredData))
      } catch (error) {
        console.error('保存表单数据失败:', error)
      }
    },
    [key, storage, exclude]
  )

  // 从存储加载
  const loadFromStorage = useCallback(() => {
    try {
      const storageObj =
        storage === 'localStorage' ? localStorage : sessionStorage
      const saved = storageObj.getItem(key)
      if (saved) {
        const data = JSON.parse(saved)
        reset(data)
        return data
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    }
    return null
  }, [key, storage, reset])

  // 清除存储
  const clearStorage = useCallback(() => {
    try {
      const storageObj =
        storage === 'localStorage' ? localStorage : sessionStorage
      storageObj.removeItem(key)
    } catch (error) {
      console.error('清除表单数据失败:', error)
    }
  }, [key, storage])

  // 监听表单变化并保存
  useEffect(() => {
    const subscription = watch(data => {
      if (saveTimer) {
        clearTimeout(saveTimer)
      }

      const timer = setTimeout(() => {
        saveToStorage(data as T)
      }, debounceDelay)

      setSaveTimer(timer)
    })

    return () => {
      subscription.unsubscribe()
      if (saveTimer) {
        clearTimeout(saveTimer)
      }
    }
  }, [watch, saveToStorage, debounceDelay, saveTimer])

  // 初始化时加载数据
  useEffect(() => {
    loadFromStorage()
  }, [loadFromStorage])

  return {
    saveToStorage,
    loadFromStorage,
    clearStorage,
  }
}
