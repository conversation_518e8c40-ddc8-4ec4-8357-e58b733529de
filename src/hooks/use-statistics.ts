import { useState, useCallback } from 'react'
import { useApi } from '@/hooks/use-api'
import { 
  StatisticsExporter, 
  ExportConfig, 
  SalesPerformanceExportData, 
  ChannelAnalysisExportData 
} from '@/lib/statistics-export'

// 销售业绩统计查询参数
export interface SalesPerformanceParams {
  startDate?: string
  endDate?: string
  userId?: string
  period?: 'week' | 'month' | 'quarter' | 'year'
}

// 渠道分析查询参数
export interface ChannelAnalysisParams {
  startDate?: string
  endDate?: string
  period?: 'week' | 'month' | 'quarter' | 'year'
  groupBy?: 'channel' | 'time' | 'both'
}

// 销售业绩统计数据
export interface SalesPerformanceData {
  overview: {
    totalConsultations: number
    signedConsultations: number
    totalFollowUps: number
    activeSalesCount: number
    conversionRate: number
  }
  ranking: Array<{
    userId: string
    userName: string
    userEmail: string
    consultationCount: number
    signedCount: number
    followUpCount: number
    conversionRate: string
  }>
  trend: Array<{
    date: string
    consultationCount: number
    signedCount: number
    conversionRate: string
  }>
  dateRange: {
    start?: string
    end?: string
  }
}

// 渠道分析数据
export interface ChannelAnalysisData {
  analysis: Array<{
    channel: number
    channelName: string
    totalCount: number
    signedCount: number
    followingCount: number
    pendingCount: number
    abandonedCount: number
    conversionRate: string
    followUpRate: string
  }>
  comparison: Array<{
    channel: number
    channelName: string
    totalCount: number
    signedCount: number
    conversionRate: string
    percentage: string
  }>
  funnel: Array<{
    channel: number
    channelName: string
    stages: Array<{
      name: string
      count: number
      percentage: number | string
    }>
  }>
  channels: Record<number, string>
  dateRange: {
    start?: string
    end?: string
  }
}

/**
 * 销售业绩统计 Hook
 */
export function useSalesPerformance() {
  const { request } = useApi()
  const [isLoading, setIsLoading] = useState(false)
  const [isExporting, setIsExporting] = useState(false)

  // 获取销售业绩统计
  const getSalesPerformance = useCallback(async (params: SalesPerformanceParams = {}) => {
    setIsLoading(true)
    try {
      const queryParams = new URLSearchParams()
      
      if (params.startDate) queryParams.append('startDate', params.startDate)
      if (params.endDate) queryParams.append('endDate', params.endDate)
      if (params.userId) queryParams.append('userId', params.userId)
      if (params.period) queryParams.append('period', params.period)

      const response = await request({
        url: `/api/statistics/sales-performance?${queryParams.toString()}`,
        method: 'GET',
      })

      return response.data as SalesPerformanceData
    } finally {
      setIsLoading(false)
    }
  }, [request])

  // 导出销售业绩数据
  const exportSalesPerformance = useCallback(async (
    data: SalesPerformanceData,
    config: ExportConfig
  ) => {
    setIsExporting(true)
    try {
      // 转换数据格式
      const exportData: SalesPerformanceExportData = {
        overview: data.overview,
        ranking: data.ranking.map(item => ({
          userName: item.userName,
          userEmail: item.userEmail,
          consultationCount: item.consultationCount,
          signedCount: item.signedCount,
          followUpCount: item.followUpCount,
          conversionRate: item.conversionRate,
        })),
        trend: data.trend,
      }

      // 设置导出配置
      const exportConfig: ExportConfig = {
        ...config,
        title: config.title || '销售业绩统计报表',
        dateRange: data.dateRange,
      }

      const result = await StatisticsExporter.exportSalesPerformance(exportData, exportConfig)
      
      if (result.success) {
        StatisticsExporter.downloadFile(result)
      }
      
      return result
    } finally {
      setIsExporting(false)
    }
  }, [])

  return {
    getSalesPerformance,
    exportSalesPerformance,
    isLoading,
    isExporting,
  }
}

/**
 * 渠道分析统计 Hook
 */
export function useChannelAnalysis() {
  const { request } = useApi()
  const [isLoading, setIsLoading] = useState(false)
  const [isExporting, setIsExporting] = useState(false)

  // 获取渠道分析数据
  const getChannelAnalysis = useCallback(async (params: ChannelAnalysisParams = {}) => {
    setIsLoading(true)
    try {
      const queryParams = new URLSearchParams()
      
      if (params.startDate) queryParams.append('startDate', params.startDate)
      if (params.endDate) queryParams.append('endDate', params.endDate)
      if (params.period) queryParams.append('period', params.period)
      if (params.groupBy) queryParams.append('groupBy', params.groupBy)

      const response = await request({
        url: `/api/statistics/channel-analysis?${queryParams.toString()}`,
        method: 'GET',
      })

      return response.data as ChannelAnalysisData
    } finally {
      setIsLoading(false)
    }
  }, [request])

  // 导出渠道分析数据
  const exportChannelAnalysis = useCallback(async (
    data: ChannelAnalysisData,
    config: ExportConfig
  ) => {
    setIsExporting(true)
    try {
      // 转换数据格式
      const exportData: ChannelAnalysisExportData = {
        analysis: data.analysis,
        comparison: data.comparison,
      }

      // 设置导出配置
      const exportConfig: ExportConfig = {
        ...config,
        title: config.title || '媒介渠道分析报表',
        dateRange: data.dateRange,
      }

      const result = await StatisticsExporter.exportChannelAnalysis(exportData, exportConfig)
      
      if (result.success) {
        StatisticsExporter.downloadFile(result)
      }
      
      return result
    } finally {
      setIsExporting(false)
    }
  }, [])

  return {
    getChannelAnalysis,
    exportChannelAnalysis,
    isLoading,
    isExporting,
  }
}

/**
 * 综合统计 Hook
 */
export function useStatistics() {
  const salesPerformance = useSalesPerformance()
  const channelAnalysis = useChannelAnalysis()

  // 获取统计概览
  const getStatisticsOverview = useCallback(async (params: {
    startDate?: string
    endDate?: string
    period?: 'week' | 'month' | 'quarter' | 'year'
  } = {}) => {
    const [salesData, channelData] = await Promise.all([
      salesPerformance.getSalesPerformance(params),
      channelAnalysis.getChannelAnalysis(params),
    ])

    return {
      sales: salesData,
      channels: channelData,
    }
  }, [salesPerformance, channelAnalysis])

  // 批量导出数据
  const exportAllData = useCallback(async (
    salesData: SalesPerformanceData,
    channelData: ChannelAnalysisData,
    format: 'csv' | 'json' = 'csv'
  ) => {
    const timestamp = new Date().toISOString().split('T')[0]
    
    const results = await Promise.all([
      salesPerformance.exportSalesPerformance(salesData, {
        format,
        filename: `销售业绩报表_${timestamp}.${format}`,
      }),
      channelAnalysis.exportChannelAnalysis(channelData, {
        format,
        filename: `渠道分析报表_${timestamp}.${format}`,
      }),
    ])

    return results
  }, [salesPerformance, channelAnalysis])

  return {
    ...salesPerformance,
    ...channelAnalysis,
    getStatisticsOverview,
    exportAllData,
    isLoading: salesPerformance.isLoading || channelAnalysis.isLoading,
    isExporting: salesPerformance.isExporting || channelAnalysis.isExporting,
  }
}
