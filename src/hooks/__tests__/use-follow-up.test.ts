import { renderHook, act } from '@testing-library/react'
import { vi } from 'vitest'
import { useFollowUp, useFollowUpReminder, useFollowUpStatus } from '../use-follow-up'

// Mock the useApi hook
const mockRequest = vi.fn()
vi.mock('@/hooks/use-api', () => ({
  useApi: () => ({
    request: mockRequest,
  }),
}))

describe('useFollowUp', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should get follow-ups list', async () => {
    const mockResponse = {
      data: {
        items: [
          {
            id: '1',
            consultationId: 'consultation-1',
            content: 'Test follow-up',
            nextFollowUpDate: '2024-01-20',
            createdAt: '2024-01-15T10:30:00Z',
            user: { id: 'user-1', name: '张三', email: '<EMAIL>' },
            consultation: { id: 'consultation-1', consultantName: '李四', consultantPhone: '13800138000', purpose: '咨询', status: 1 },
          },
        ],
        pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
      },
    }

    mockRequest.mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useFollowUp())

    let followUps
    await act(async () => {
      followUps = await result.current.getFollowUps({ page: 1, limit: 10 })
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/follow-ups?page=1&limit=10',
      method: 'GET',
    })
    expect(followUps).toEqual(mockResponse.data)
  })

  it('should get single follow-up', async () => {
    const mockResponse = {
      data: {
        id: '1',
        consultationId: 'consultation-1',
        content: 'Test follow-up',
        nextFollowUpDate: '2024-01-20',
        createdAt: '2024-01-15T10:30:00Z',
        user: { id: 'user-1', name: '张三', email: '<EMAIL>' },
        consultation: { id: 'consultation-1', consultantName: '李四', consultantPhone: '13800138000', purpose: '咨询', status: 1 },
      },
    }

    mockRequest.mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useFollowUp())

    let followUp
    await act(async () => {
      followUp = await result.current.getFollowUp('1')
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/follow-ups/1',
      method: 'GET',
    })
    expect(followUp).toEqual(mockResponse.data)
  })

  it('should create follow-up', async () => {
    const mockResponse = {
      data: {
        id: '1',
        consultationId: 'consultation-1',
        content: 'New follow-up',
        nextFollowUpDate: '2024-01-20',
        createdAt: '2024-01-15T10:30:00Z',
        user: { id: 'user-1', name: '张三', email: '<EMAIL>' },
        consultation: { id: 'consultation-1', consultantName: '李四', consultantPhone: '13800138000', purpose: '咨询', status: 1 },
      },
    }

    mockRequest.mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useFollowUp())

    const createData = {
      consultationId: 'consultation-1',
      content: 'New follow-up',
      nextFollowUpDate: '2024-01-20',
    }

    let followUp
    await act(async () => {
      followUp = await result.current.createFollowUp(createData)
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/follow-ups',
      method: 'POST',
      data: createData,
    })
    expect(followUp).toEqual(mockResponse.data)
  })

  it('should update follow-up', async () => {
    const mockResponse = {
      data: {
        id: '1',
        consultationId: 'consultation-1',
        content: 'Updated follow-up',
        nextFollowUpDate: '2024-01-25',
        createdAt: '2024-01-15T10:30:00Z',
        user: { id: 'user-1', name: '张三', email: '<EMAIL>' },
        consultation: { id: 'consultation-1', consultantName: '李四', consultantPhone: '13800138000', purpose: '咨询', status: 1 },
      },
    }

    mockRequest.mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useFollowUp())

    const updateData = {
      content: 'Updated follow-up',
      nextFollowUpDate: '2024-01-25',
    }

    let followUp
    await act(async () => {
      followUp = await result.current.updateFollowUp('1', updateData)
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/follow-ups/1',
      method: 'PUT',
      data: updateData,
    })
    expect(followUp).toEqual(mockResponse.data)
  })

  it('should delete follow-up', async () => {
    mockRequest.mockResolvedValue({})

    const { result } = renderHook(() => useFollowUp())

    await act(async () => {
      await result.current.deleteFollowUp('1')
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/follow-ups/1',
      method: 'DELETE',
    })
  })

  it('should get consultation follow-ups', async () => {
    const mockResponse = {
      data: {
        items: [
          {
            id: '1',
            consultationId: 'consultation-1',
            content: 'Test follow-up',
            nextFollowUpDate: '2024-01-20',
            createdAt: '2024-01-15T10:30:00Z',
            user: { id: 'user-1', name: '张三', email: '<EMAIL>' },
            consultation: { id: 'consultation-1', consultantName: '李四', consultantPhone: '13800138000', purpose: '咨询', status: 1 },
          },
        ],
        pagination: { page: 1, limit: 100, total: 1, totalPages: 1 },
      },
    }

    mockRequest.mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useFollowUp())

    let followUps
    await act(async () => {
      followUps = await result.current.getConsultationFollowUps('consultation-1')
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/follow-ups?consultationId=consultation-1&limit=100',
      method: 'GET',
    })
    expect(followUps).toEqual(mockResponse.data)
  })

  it('should handle loading state', async () => {
    mockRequest.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))

    const { result } = renderHook(() => useFollowUp())

    expect(result.current.isLoading).toBe(false)

    act(() => {
      result.current.getFollowUps()
    })

    expect(result.current.isLoading).toBe(true)

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 150))
    })

    expect(result.current.isLoading).toBe(false)
  })
})

describe('useFollowUpReminder', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should get reminders', async () => {
    const mockResponse = {
      data: {
        items: [
          {
            id: '1',
            consultationId: 'consultation-1',
            consultantName: '李四',
            consultantPhone: '13800138000',
            purpose: '咨询',
            followUpDate: '2024-01-20',
            priority: 'high',
            status: 'pending',
            assignedUser: { id: 'user-1', name: '张三' },
          },
        ],
      },
    }

    mockRequest.mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useFollowUpReminder())

    let reminders
    await act(async () => {
      reminders = await result.current.getReminders({ status: 'pending' })
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/follow-up-reminders?status=pending',
      method: 'GET',
    })
    expect(reminders).toEqual(mockResponse.data.items)
  })

  it('should mark reminder as completed', async () => {
    mockRequest.mockResolvedValue({})

    const { result } = renderHook(() => useFollowUpReminder())

    await act(async () => {
      await result.current.markReminderCompleted('1')
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/follow-up-reminders/1/complete',
      method: 'POST',
    })
  })

  it('should snooze reminder', async () => {
    mockRequest.mockResolvedValue({})

    const { result } = renderHook(() => useFollowUpReminder())

    await act(async () => {
      await result.current.snoozeReminder('1', '2024-01-25')
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/follow-up-reminders/1/snooze',
      method: 'POST',
      data: { newDate: '2024-01-25' },
    })
  })

  it('should get today reminders', async () => {
    const mockResponse = {
      data: {
        items: [],
      },
    }

    mockRequest.mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useFollowUpReminder())

    await act(async () => {
      await result.current.getTodayReminders()
    })

    const today = new Date().toISOString().split('T')[0]
    expect(mockRequest).toHaveBeenCalledWith({
      url: `/api/follow-up-reminders?status=pending&startDate=${today}&endDate=${today}`,
      method: 'GET',
    })
  })

  it('should get reminder stats', async () => {
    const mockResponse = {
      data: {
        total: 10,
        overdue: 2,
        today: 3,
        tomorrow: 1,
        upcoming: 4,
        completed: 5,
      },
    }

    mockRequest.mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useFollowUpReminder())

    let stats
    await act(async () => {
      stats = await result.current.getReminderStats('user-1')
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/follow-up-reminders/stats?userId=user-1',
      method: 'GET',
    })
    expect(stats).toEqual(mockResponse.data)
  })
})

describe('useFollowUpStatus', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should update consultation status', async () => {
    mockRequest.mockResolvedValue({})

    const { result } = renderHook(() => useFollowUpStatus())

    await act(async () => {
      await result.current.updateConsultationStatus('consultation-1', 2, 'Status updated')
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/consultations/consultation-1/status',
      method: 'PUT',
      data: { status: 2, notes: 'Status updated' },
    })
  })

  it('should batch update status', async () => {
    mockRequest.mockResolvedValue({})

    const { result } = renderHook(() => useFollowUpStatus())

    const updates = [
      { consultationId: 'consultation-1', status: 2 },
      { consultationId: 'consultation-2', status: 3 },
    ]

    await act(async () => {
      await result.current.batchUpdateStatus(updates)
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/consultations/batch-status',
      method: 'PUT',
      data: { updates },
    })
  })

  it('should set follow-up priority', async () => {
    mockRequest.mockResolvedValue({})

    const { result } = renderHook(() => useFollowUpStatus())

    await act(async () => {
      await result.current.setFollowUpPriority('consultation-1', 'high')
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/consultations/consultation-1/priority',
      method: 'PUT',
      data: { priority: 'high' },
    })
  })

  it('should assign follow-up user', async () => {
    mockRequest.mockResolvedValue({})

    const { result } = renderHook(() => useFollowUpStatus())

    await act(async () => {
      await result.current.assignFollowUpUser('consultation-1', 'user-2')
    })

    expect(mockRequest).toHaveBeenCalledWith({
      url: '/api/consultations/consultation-1/assign',
      method: 'PUT',
      data: { assignedUserId: 'user-2' },
    })
  })
})
