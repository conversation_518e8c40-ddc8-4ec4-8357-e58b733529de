'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { useEffect } from 'react'

type Theme = 'light' | 'dark' | 'system'

interface UserPreferences {
  sidebarCollapsed: boolean
  compactMode: boolean
  animationsEnabled: boolean
  language: 'zh-CN' | 'en-US'
}

interface ThemeStore {
  theme: Theme
  resolvedTheme: 'light' | 'dark'
  userPreferences: UserPreferences
  setTheme: (theme: Theme) => void
  setUserPreferences: (preferences: Partial<UserPreferences>) => void
  toggleSidebar: () => void
  toggleCompactMode: () => void
  toggleAnimations: () => void
  setLanguage: (language: 'zh-CN' | 'en-US') => void
}

/**
 * 主题管理和用户偏好设置 Hook
 * 支持明亮、暗黑和系统主题，以及用户个性化设置
 */
export const useTheme = create<ThemeStore>()(
  persist(
    (set, _get) => ({
      theme: 'system',
      resolvedTheme: 'light',
      userPreferences: {
        sidebarCollapsed: false,
        compactMode: false,
        animationsEnabled: true,
        language: 'zh-CN',
      },
      setTheme: (theme: Theme) => {
        set({ theme })

        // 更新解析后的主题
        let resolvedTheme: 'light' | 'dark' = 'light'

        if (theme === 'dark') {
          resolvedTheme = 'dark'
        } else if (theme === 'system') {
          resolvedTheme = window.matchMedia('(prefers-color-scheme: dark)')
            .matches
            ? 'dark'
            : 'light'
        }

        set({ resolvedTheme })

        // 更新 HTML 类名
        const root = window.document.documentElement
        root.classList.remove('light', 'dark')
        root.classList.add(resolvedTheme)

        // 添加主题变化动画类
        root.classList.add('theme-transition')
        setTimeout(() => {
          root.classList.remove('theme-transition')
        }, 300)
      },
      setUserPreferences: (preferences: Partial<UserPreferences>) => {
        set(state => ({
          userPreferences: { ...state.userPreferences, ...preferences },
        }))
      },
      toggleSidebar: () => {
        set(state => ({
          userPreferences: {
            ...state.userPreferences,
            sidebarCollapsed: !state.userPreferences.sidebarCollapsed,
          },
        }))
      },
      toggleCompactMode: () => {
        set(state => ({
          userPreferences: {
            ...state.userPreferences,
            compactMode: !state.userPreferences.compactMode,
          },
        }))
      },
      toggleAnimations: () => {
        set(state => ({
          userPreferences: {
            ...state.userPreferences,
            animationsEnabled: !state.userPreferences.animationsEnabled,
          },
        }))
      },
      setLanguage: (language: 'zh-CN' | 'en-US') => {
        set(state => ({
          userPreferences: {
            ...state.userPreferences,
            language,
          },
        }))
      },
    }),
    {
      name: 'theme-and-preferences-storage',
      onRehydrateStorage: () => state => {
        if (state) {
          // 重新计算解析后的主题
          let resolvedTheme: 'light' | 'dark' = 'light'

          if (state.theme === 'dark') {
            resolvedTheme = 'dark'
          } else if (state.theme === 'system') {
            resolvedTheme = window.matchMedia('(prefers-color-scheme: dark)')
              .matches
              ? 'dark'
              : 'light'
          }

          state.resolvedTheme = resolvedTheme

          // 更新 HTML 类名
          const root = window.document.documentElement
          root.classList.remove('light', 'dark')
          root.classList.add(resolvedTheme)

          // 应用用户偏好设置
          if (state.userPreferences.compactMode) {
            root.classList.add('compact-mode')
          }

          if (!state.userPreferences.animationsEnabled) {
            root.classList.add('reduce-motion')
          }
        }
      },
    }
  )
)

/**
 * 系统主题监听 Hook
 * 监听系统主题变化并自动更新
 */
export function useSystemThemeListener() {
  const { theme, setTheme } = useTheme()

  useEffect(() => {
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

      const handleChange = () => {
        // 重新触发主题设置以更新解析后的主题
        setTheme('system')
      }

      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    }
  }, [theme, setTheme])
}
