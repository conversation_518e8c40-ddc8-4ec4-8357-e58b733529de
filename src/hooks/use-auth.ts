'use client'

import { useSession } from '@/lib/auth-client'
import { useEffect, useState, useMemo } from 'react'
import { filterMenuItems, MENU_ITEMS, type MenuItem } from '@/lib/permissions'

// 临时简化版本 - 在生产环境中应该通过 API 获取
async function fetchUserPermissions(userId: string): Promise<string[]> {
  // 临时返回所有权限，在生产环境中应该通过 API 获取
  return [
    'consultation:read',
    'consultation:write',
    'follow-up:read',
    'follow-up:write',
    'statistics:read',
    'reservation:read',
    'reservation:write',
    'admission:read',
    'admission:write',
    'checkout:read',
    'checkout:write',
  ]
}

async function fetchUserRole(userId: string): Promise<any> {
  // 临时返回管理员角色，在生产环境中应该通过 API 获取
  return {
    id: '1',
    name: 'admin',
    displayName: '管理员',
  }
}

/**
 * 用户权限 Hook
 */
export function usePermissions() {
  const { data: session } = useSession()
  const [permissions, setPermissions] = useState<string[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchPermissions() {
      if (!session?.user?.id) {
        setPermissions([])
        setLoading(false)
        return
      }

      try {
        const userPermissions = await fetchUserPermissions(session.user.id)
        setPermissions(userPermissions)
      } catch (error) {
        console.error('获取权限失败:', error)
        setPermissions([])
      } finally {
        setLoading(false)
      }
    }

    fetchPermissions()
  }, [session?.user?.id])

  const hasPermission = (permission: string) => {
    return permissions.includes(permission)
  }

  const hasAnyPermission = (requiredPermissions: string[]) => {
    return requiredPermissions.some(permission =>
      permissions.includes(permission)
    )
  }

  const hasAllPermissions = (requiredPermissions: string[]) => {
    return requiredPermissions.every(permission =>
      permissions.includes(permission)
    )
  }

  return {
    permissions,
    loading,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
  }
}

/**
 * 用户角色 Hook
 */
export function useRole() {
  const { data: session } = useSession()
  const [role, setRole] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchRole() {
      if (!session?.user?.id) {
        setRole(null)
        setLoading(false)
        return
      }

      try {
        const userRole = await fetchUserRole(session.user.id)
        setRole(userRole)
      } catch (error) {
        console.error('获取角色失败:', error)
        setRole(null)
      } finally {
        setLoading(false)
      }
    }

    fetchRole()
  }, [session?.user?.id])

  const hasRole = (roleName: string) => {
    return role?.name === roleName
  }

  const hasAnyRole = (roleNames: string[]) => {
    return role ? roleNames.includes(role.name) : false
  }

  return {
    role,
    loading,
    hasRole,
    hasAnyRole,
  }
}

/**
 * 菜单权限 Hook
 */
export function useMenuPermissions() {
  const { permissions, loading } = usePermissions()

  const accessibleMenuItems = useMemo(() => {
    if (loading) return []
    return filterMenuItems(MENU_ITEMS, permissions)
  }, [permissions, loading])

  const canAccessRoute = (path: string) => {
    if (loading) return false

    const findMenuItem = (
      items: MenuItem[],
      targetPath: string
    ): MenuItem | null => {
      for (const item of items) {
        if (item.path === targetPath) {
          return item
        }
        if (item.children) {
          const found = findMenuItem(item.children, targetPath)
          if (found) return found
        }
      }
      return null
    }

    const menuItem = findMenuItem(MENU_ITEMS, path)
    if (!menuItem) return true // 如果路由不在菜单中，默认允许访问

    if (!menuItem.permissions || menuItem.permissions.length === 0) {
      return true
    }

    return menuItem.permissions.some(permission =>
      permissions.includes(permission)
    )
  }

  return {
    accessibleMenuItems,
    canAccessRoute,
    loading,
  }
}

/**
 * 认证状态 Hook
 */
export function useAuth() {
  const { data: session, isPending, error } = useSession()
  const {
    permissions,
    loading: permissionsLoading,
    ...permissionMethods
  } = usePermissions()
  const { role, loading: roleLoading, ...roleMethods } = useRole()
  const {
    accessibleMenuItems,
    canAccessRoute,
    loading: menuLoading,
  } = useMenuPermissions()

  const isAuthenticated = !!session?.user
  const isLoading =
    isPending || permissionsLoading || roleLoading || menuLoading

  const user = session?.user
    ? {
        ...session.user,
        role,
        permissions,
      }
    : null

  return {
    user,
    session,
    isAuthenticated,
    isLoading,
    error,
    accessibleMenuItems,
    canAccessRoute,
    ...permissionMethods,
    ...roleMethods,
  }
}
