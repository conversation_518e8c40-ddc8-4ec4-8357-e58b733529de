'use client'

import { useAuth as useZustandAuth } from '@/store'
import { useMenuPermissions as useOriginalMenuPermissions } from './use-auth'
import { useMemo } from 'react'

/**
 * 基于 Zustand 的认证 Hook
 *
 * 这个 hook 提供了与原有 useAuth hook 兼容的接口，
 * 但使用 Zustand 进行状态管理
 */
export function useAuth() {
  const zustandAuth = useZustandAuth()
  const {
    accessibleMenuItems,
    canAccessRoute,
    loading: menuLoading,
  } = useOriginalMenuPermissions()

  // 组合用户信息，包含角色和权限
  const user = useMemo(() => {
    if (!zustandAuth.user) return null

    return {
      ...zustandAuth.user,
      role: zustandAuth.role,
      permissions: zustandAuth.permissions,
    }
  }, [zustandAuth.user, zustandAuth.role, zustandAuth.permissions])

  return {
    // 用户信息
    user,
    session: zustandAuth.user ? { user: zustandAuth.user } : null,
    isAuthenticated: zustandAuth.isAuthenticated,
    isLoading: zustandAuth.isLoading || menuLoading,
    error: zustandAuth.error,

    // 权限相关
    permissions: zustandAuth.permissions,
    role: zustandAuth.role,
    hasPermission: zustandAuth.hasPermission,
    hasAnyPermission: zustandAuth.hasAnyPermission,
    hasAllPermissions: zustandAuth.hasAllPermissions,
    hasRole: zustandAuth.hasRole,
    hasAnyRole: zustandAuth.hasAnyRole,

    // 菜单权限
    accessibleMenuItems,
    canAccessRoute,

    // 操作方法
    login: zustandAuth.login,
    logout: zustandAuth.logout,
    refreshAuth: zustandAuth.refreshAuth,
  }
}

/**
 * 权限检查 Hook
 */
export function usePermissions() {
  const {
    permissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isLoading,
  } = useZustandAuth()

  return {
    permissions,
    loading: isLoading,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
  }
}

/**
 * 角色检查 Hook
 */
export function useRole() {
  const { role, hasRole, hasAnyRole, isLoading } = useZustandAuth()

  return {
    role,
    loading: isLoading,
    hasRole,
    hasAnyRole,
  }
}

/**
 * 菜单权限 Hook
 */
export function useMenuPermissions() {
  return useOriginalMenuPermissions()
}
