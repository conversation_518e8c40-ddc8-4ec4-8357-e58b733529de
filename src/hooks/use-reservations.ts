import { useState, useCallback } from 'react'
import { useApi } from './use-api'

export interface ReservationParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  roomId?: string
  startDate?: string
  endDate?: string
  sortBy?: 'reservationDate' | 'expectedCheckInDate' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

export interface CreateReservationData {
  elderInfoId?: string
  roomId: string
  expectedCheckInDate: string
  expectedCheckOutDate?: string
  reservationType?: number
  contactName: string
  contactPhone: string
  contactRelation?: string
  specialRequirements?: string
  depositAmount?: number
  depositPaid?: boolean
  notes?: string
}

export interface UpdateReservationData {
  elderInfoId?: string
  roomId?: string
  expectedCheckInDate?: string
  expectedCheckOutDate?: string
  actualCheckInDate?: string
  actualCheckOutDate?: string
  status?: number
  reservationType?: number
  contactName?: string
  contactPhone?: string
  contactRelation?: string
  specialRequirements?: string
  depositAmount?: number
  depositPaid?: boolean
  notes?: string
}

export interface AvailabilityParams {
  checkInDate: string
  checkOutDate?: string
  roomType?: string
  capacity?: string
  excludeReservationId?: string
}

export interface ConflictCheckData {
  roomId: string
  checkInDate: string
  checkOutDate?: string
  excludeReservationId?: string
}

export function useReservations() {
  const { request } = useApi()
  const [isLoading, setIsLoading] = useState(false)

  const getReservations = useCallback(async (params: ReservationParams = {}) => {
    setIsLoading(true)
    try {
      const queryParams = new URLSearchParams()
      
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.limit) queryParams.append('limit', params.limit.toString())
      if (params.search) queryParams.append('search', params.search)
      if (params.status) queryParams.append('status', params.status)
      if (params.roomId) queryParams.append('roomId', params.roomId)
      if (params.startDate) queryParams.append('startDate', params.startDate)
      if (params.endDate) queryParams.append('endDate', params.endDate)
      if (params.sortBy) queryParams.append('sortBy', params.sortBy)
      if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder)

      const response = await request({
        url: `/api/reservations?${queryParams.toString()}`,
        method: 'GET',
      })

      return response.data
    } finally {
      setIsLoading(false)
    }
  }, [request])

  const getReservation = useCallback(async (id: string) => {
    setIsLoading(true)
    try {
      const response = await request({
        url: `/api/reservations/${id}`,
        method: 'GET',
      })

      return response.data
    } finally {
      setIsLoading(false)
    }
  }, [request])

  const createReservation = useCallback(async (data: CreateReservationData) => {
    setIsLoading(true)
    try {
      const response = await request({
        url: '/api/reservations',
        method: 'POST',
        data,
      })

      return response.data
    } finally {
      setIsLoading(false)
    }
  }, [request])

  const updateReservation = useCallback(async (id: string, data: UpdateReservationData) => {
    setIsLoading(true)
    try {
      const response = await request({
        url: `/api/reservations/${id}`,
        method: 'PUT',
        data,
      })

      return response.data
    } finally {
      setIsLoading(false)
    }
  }, [request])

  const deleteReservation = useCallback(async (id: string) => {
    setIsLoading(true)
    try {
      const response = await request({
        url: `/api/reservations/${id}`,
        method: 'DELETE',
      })

      return response.data
    } finally {
      setIsLoading(false)
    }
  }, [request])

  const checkRoomAvailability = useCallback(async (params: AvailabilityParams) => {
    setIsLoading(true)
    try {
      const queryParams = new URLSearchParams()
      
      queryParams.append('checkInDate', params.checkInDate)
      if (params.checkOutDate) queryParams.append('checkOutDate', params.checkOutDate)
      if (params.roomType) queryParams.append('roomType', params.roomType)
      if (params.capacity) queryParams.append('capacity', params.capacity)
      if (params.excludeReservationId) queryParams.append('excludeReservationId', params.excludeReservationId)

      const response = await request({
        url: `/api/reservations/availability?${queryParams.toString()}`,
        method: 'GET',
      })

      return response.data
    } finally {
      setIsLoading(false)
    }
  }, [request])

  const checkReservationConflict = useCallback(async (data: ConflictCheckData) => {
    setIsLoading(true)
    try {
      const response = await request({
        url: '/api/reservations/availability',
        method: 'POST',
        data,
      })

      return response.data
    } finally {
      setIsLoading(false)
    }
  }, [request])

  return {
    getReservations,
    getReservation,
    createReservation,
    updateReservation,
    deleteReservation,
    checkRoomAvailability,
    checkReservationConflict,
    isLoading,
  }
}
