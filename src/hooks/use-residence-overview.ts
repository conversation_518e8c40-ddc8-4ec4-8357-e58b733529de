import { useState, useCallback } from 'react'
import { useApi } from './use-api'
import type { ResidenceOverviewData } from '@/components/residence/ResidenceOverviewDashboard'

export interface ResidenceOverviewParams {
  period?: 'week' | 'month' | 'quarter' | 'year'
  startDate?: string
  endDate?: string
}

export function useResidenceOverview() {
  const { request } = useApi()
  const [isLoading, setIsLoading] = useState(false)

  const getResidenceOverview = useCallback(async (params: ResidenceOverviewParams = {}) => {
    setIsLoading(true)
    try {
      const queryParams = new URLSearchParams()
      
      if (params.period) {
        queryParams.append('period', params.period)
      }
      if (params.startDate) {
        queryParams.append('startDate', params.startDate)
      }
      if (params.endDate) {
        queryParams.append('endDate', params.endDate)
      }

      const response = await request({
        url: `/api/residence/overview?${queryParams.toString()}`,
        method: 'GET',
      })

      return response.data as ResidenceOverviewData
    } finally {
      setIsLoading(false)
    }
  }, [request])

  return {
    getResidenceOverview,
    isLoading,
  }
}
