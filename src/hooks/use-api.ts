/**
 * API 数据获取 Hooks
 *
 * 提供 SWR 风格的数据获取和缓存功能
 */

import { useEffect, useState, useCallback, useRef } from 'react'
import { z } from 'zod'
import { apiClient, type RequestConfig, ApiError } from '@/lib/api-client'
import { useApiStore, useAppStore } from '@/store'

// SWR 配置类型
export interface SWRConfig<T = any> extends Omit<RequestConfig, 'url'> {
  // 重新验证间隔（毫秒）
  refreshInterval?: number
  // 窗口聚焦时重新验证
  revalidateOnFocus?: boolean
  // 网络重连时重新验证
  revalidateOnReconnect?: boolean
  // 错误时重试
  errorRetryCount?: number
  // 错误重试间隔
  errorRetryInterval?: number
  // 初始数据
  fallbackData?: T
  // 数据转换函数
  transform?: (data: any) => T
  // 条件获取
  shouldFetch?: boolean
  // 依赖项
  deps?: any[]
}

// SWR 返回类型
export interface SWRResponse<T> {
  data: T | undefined
  error: Error | null
  isLoading: boolean
  isValidating: boolean
  mutate: (
    data?: T | Promise<T> | ((current: T | undefined) => T | Promise<T>),
    shouldRevalidate?: boolean
  ) => Promise<T | undefined>
  revalidate: () => Promise<T | undefined>
}

/**
 * SWR Hook - 数据获取和缓存
 */
export function useSWR<T = any>(
  key: string | null,
  config: SWRConfig<T> = {}
): SWRResponse<T> {
  const {
    refreshInterval,
    revalidateOnFocus = true,
    revalidateOnReconnect = true,
    errorRetryCount = 3,
    errorRetryInterval = 5000,
    fallbackData,
    transform,
    shouldFetch = true,
    deps = [],
    ...requestConfig
  } = config

  const [data, setData] = useState<T | undefined>(fallbackData)
  const [error, setError] = useState<Error | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isValidating, setIsValidating] = useState(false)

  const { getCache, setCache } = useApiStore()
  const retryCountRef = useRef(0)
  const refreshIntervalRef = useRef<NodeJS.Timeout>()
  const mountedRef = useRef(true)

  // 清理函数
  useEffect(() => {
    mountedRef.current = true
    return () => {
      mountedRef.current = false
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
      }
    }
  }, [])

  // 获取数据的核心函数
  const fetchData = useCallback(
    async (isRevalidating = false): Promise<T | undefined> => {
      if (!key || !shouldFetch) return

      try {
        if (!isRevalidating) {
          setIsLoading(true)
        }
        setIsValidating(true)
        setError(null)

        // 检查缓存
        const cachedData = getCache(key)
        if (cachedData && !isRevalidating) {
          const transformedData = transform ? transform(cachedData) : cachedData
          setData(transformedData)
          setIsLoading(false)
          return transformedData
        }

        // 发起请求
        const response = await apiClient.get(key, {
          ...requestConfig,
          cache: true,
        })

        if (!mountedRef.current) return

        const transformedData = transform ? transform(response) : response
        setData(transformedData)
        retryCountRef.current = 0

        return transformedData
      } catch (err) {
        if (!mountedRef.current) return

        const apiError = err instanceof Error ? err : new Error('Unknown error')
        setError(apiError)

        // 错误重试
        if (retryCountRef.current < errorRetryCount) {
          retryCountRef.current++
          setTimeout(() => {
            if (mountedRef.current) {
              fetchData(isRevalidating)
            }
          }, errorRetryInterval)
        }

        throw apiError
      } finally {
        if (mountedRef.current) {
          setIsLoading(false)
          setIsValidating(false)
        }
      }
    },
    [
      key,
      shouldFetch,
      transform,
      getCache,
      requestConfig,
      errorRetryCount,
      errorRetryInterval,
    ]
  )

  // 重新验证
  const revalidate = useCallback(() => {
    return fetchData(true)
  }, [fetchData])

  // 数据变更
  const mutate = useCallback(
    async (
      data?: T | Promise<T> | ((current: T | undefined) => T | Promise<T>),
      shouldRevalidate = true
    ): Promise<T | undefined> => {
      if (data === undefined) {
        return shouldRevalidate ? revalidate() : undefined
      }

      let newData: T
      if (typeof data === 'function') {
        newData = await (data as (current: T | undefined) => T | Promise<T>)(
          data
        )
      } else {
        newData = await data
      }

      setData(newData)

      // 更新缓存
      if (key) {
        setCache(key, newData)
      }

      // 重新验证
      if (shouldRevalidate) {
        revalidate()
      }

      return newData
    },
    [key, setCache, revalidate]
  )

  // 初始数据获取
  useEffect(() => {
    fetchData()
  }, [key, shouldFetch, ...deps])

  // 定时刷新
  useEffect(() => {
    if (refreshInterval && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(() => {
        revalidate()
      }, refreshInterval)

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current)
        }
      }
    }
  }, [refreshInterval, revalidate])

  // 窗口聚焦时重新验证
  useEffect(() => {
    if (!revalidateOnFocus) return

    const handleFocus = () => revalidate()
    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [revalidateOnFocus, revalidate])

  // 网络重连时重新验证
  useEffect(() => {
    if (!revalidateOnReconnect) return

    const handleOnline = () => revalidate()
    window.addEventListener('online', handleOnline)
    return () => window.removeEventListener('online', handleOnline)
  }, [revalidateOnReconnect, revalidate])

  return {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    revalidate,
  }
}

/**
 * 分页数据获取 Hook
 */
export interface PaginationConfig {
  page?: number
  pageSize?: number
  total?: number
}

export interface PaginatedSWRConfig<T> extends SWRConfig<T> {
  pagination?: PaginationConfig
}

export interface PaginatedSWRResponse<T> extends SWRResponse<T[]> {
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
  loadMore: () => Promise<void>
  refresh: () => Promise<void>
}

export function usePaginatedSWR<T = any>(
  key: string | null,
  config: PaginatedSWRConfig<T> = {}
): PaginatedSWRResponse<T> {
  const { pagination = {}, ...swrConfig } = config
  const { page = 1, pageSize = 10, total = 0 } = pagination

  const [allData, setAllData] = useState<T[]>([])
  const [paginationState, setPaginationState] = useState({
    page,
    pageSize,
    total,
    totalPages: Math.ceil(total / pageSize),
    hasNextPage: page < Math.ceil(total / pageSize),
    hasPrevPage: page > 1,
  })

  const swrKey = key ? `${key}?page=${page}&pageSize=${pageSize}` : null

  const swr = useSWR<{ data: T[]; total: number }>(swrKey, {
    ...swrConfig,
    transform: response => {
      // 假设 API 返回格式为 { data: T[], total: number }
      return response
    },
  })

  // 更新数据和分页状态
  useEffect(() => {
    if (swr.data) {
      if (page === 1) {
        setAllData(swr.data.data)
      } else {
        setAllData(prev => [...prev, ...swr.data!.data])
      }

      const newTotal = swr.data.total
      setPaginationState({
        page,
        pageSize,
        total: newTotal,
        totalPages: Math.ceil(newTotal / pageSize),
        hasNextPage: page < Math.ceil(newTotal / pageSize),
        hasPrevPage: page > 1,
      })
    }
  }, [swr.data, page, pageSize])

  const loadMore = useCallback(async () => {
    if (paginationState.hasNextPage) {
      const nextPage = page + 1
      const nextKey = key
        ? `${key}?page=${nextPage}&pageSize=${pageSize}`
        : null
      if (nextKey) {
        await swr.mutate()
      }
    }
  }, [key, page, pageSize, paginationState.hasNextPage, swr])

  const refresh = useCallback(async () => {
    setAllData([])
    await swr.revalidate()
  }, [swr])

  return {
    data: allData,
    error: swr.error,
    isLoading: swr.isLoading,
    isValidating: swr.isValidating,
    mutate: swr.mutate as any,
    revalidate: swr.revalidate as any,
    pagination: paginationState,
    loadMore,
    refresh,
  }
}

/**
 * 简化的 API Hook - 兼容现有代码
 */
export function useApi<T = any>(
  url: string | null,
  config: SWRConfig<T> = {}
): SWRResponse<T> {
  return useSWR<T>(url, config)
}

/**
 * 数据变更 Hook
 */
export function useMutation<TData = any, TVariables = any>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  config: {
    onSuccess?: (data: TData, variables: TVariables) => void
    onError?: (error: Error, variables: TVariables) => void
    onSettled?: (
      data: TData | undefined,
      error: Error | null,
      variables: TVariables
    ) => void
  } = {}
) {
  const [data, setData] = useState<TData | undefined>()
  const [error, setError] = useState<Error | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const { onSuccess, onError, onSettled } = config

  const mutate = useCallback(
    async (variables: TVariables): Promise<TData> => {
      try {
        setIsLoading(true)
        setError(null)

        const result = await mutationFn(variables)
        setData(result)

        onSuccess?.(result, variables)
        onSettled?.(result, null, variables)

        return result
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown error')
        setError(error)

        onError?.(error, variables)
        onSettled?.(undefined, error, variables)

        throw error
      } finally {
        setIsLoading(false)
      }
    },
    [mutationFn, onSuccess, onError, onSettled]
  )

  const reset = useCallback(() => {
    setData(undefined)
    setError(null)
    setIsLoading(false)
  }, [])

  return {
    data,
    error,
    isLoading,
    mutate,
    reset,
  }
}
