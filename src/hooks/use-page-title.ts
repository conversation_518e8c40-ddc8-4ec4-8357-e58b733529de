'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { generatePageTitle } from '@/lib/routes'

/**
 * 页面标题管理 Hook
 * 自动根据路由更新页面标题
 */
export function usePageTitle(customTitle?: string) {
  const pathname = usePathname()

  useEffect(() => {
    const title = customTitle || generatePageTitle(pathname)
    const fullTitle =
      title === '养老院管理系统' ? title : `${title} - 养老院管理系统`

    document.title = fullTitle
  }, [pathname, customTitle])

  return {
    setTitle: (title: string) => {
      document.title = `${title} - 养老院管理系统`
    },
  }
}
