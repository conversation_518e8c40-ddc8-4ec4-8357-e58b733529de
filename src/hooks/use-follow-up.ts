'use client'

import { useState, useCallback } from 'react'
import { useApi } from '@/hooks/use-api'
import { FollowUpRecord } from '@/components/follow-up/FollowUpTimeline'
import { FollowUpReminder } from '@/components/follow-up/FollowUpReminder'

// API 响应类型
interface FollowUpListResponse {
  items: FollowUpRecord[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

interface FollowUpQueryParams {
  page?: number
  limit?: number
  consultationId?: string
  search?: string
  startDate?: string
  endDate?: string
}

interface CreateFollowUpData {
  consultationId: string
  content: string
  nextFollowUpDate?: string
}

interface UpdateFollowUpData {
  content?: string
  nextFollowUpDate?: string
}

/**
 * 跟进记录管理 Hook
 */
export function useFollowUp() {
  const { request } = useApi()
  const [isLoading, setIsLoading] = useState(false)

  // 获取跟进记录列表
  const getFollowUps = useCallback(async (params: FollowUpQueryParams = {}) => {
    setIsLoading(true)
    try {
      const searchParams = new URLSearchParams()
      
      if (params.page) searchParams.append('page', params.page.toString())
      if (params.limit) searchParams.append('limit', params.limit.toString())
      if (params.consultationId) searchParams.append('consultationId', params.consultationId)
      if (params.search) searchParams.append('search', params.search)
      if (params.startDate) searchParams.append('startDate', params.startDate)
      if (params.endDate) searchParams.append('endDate', params.endDate)

      const response = await request<FollowUpListResponse>({
        url: `/api/follow-ups?${searchParams.toString()}`,
        method: 'GET',
      })

      return response.data
    } finally {
      setIsLoading(false)
    }
  }, [request])

  // 获取单个跟进记录
  const getFollowUp = useCallback(async (id: string) => {
    const response = await request<FollowUpRecord>({
      url: `/api/follow-ups/${id}`,
      method: 'GET',
    })
    return response.data
  }, [request])

  // 创建跟进记录
  const createFollowUp = useCallback(async (data: CreateFollowUpData) => {
    const response = await request<FollowUpRecord>({
      url: '/api/follow-ups',
      method: 'POST',
      data,
    })
    return response.data
  }, [request])

  // 更新跟进记录
  const updateFollowUp = useCallback(async (id: string, data: UpdateFollowUpData) => {
    const response = await request<FollowUpRecord>({
      url: `/api/follow-ups/${id}`,
      method: 'PUT',
      data,
    })
    return response.data
  }, [request])

  // 删除跟进记录
  const deleteFollowUp = useCallback(async (id: string) => {
    await request({
      url: `/api/follow-ups/${id}`,
      method: 'DELETE',
    })
  }, [request])

  // 获取咨询记录的跟进历史
  const getConsultationFollowUps = useCallback(async (consultationId: string) => {
    return getFollowUps({ consultationId, limit: 100 })
  }, [getFollowUps])

  return {
    isLoading,
    getFollowUps,
    getFollowUp,
    createFollowUp,
    updateFollowUp,
    deleteFollowUp,
    getConsultationFollowUps,
  }
}

/**
 * 跟进提醒管理 Hook
 */
export function useFollowUpReminder() {
  const { request } = useApi()
  const [isLoading, setIsLoading] = useState(false)

  // 获取跟进提醒列表
  const getReminders = useCallback(async (params: {
    status?: 'pending' | 'completed' | 'overdue'
    priority?: 'high' | 'medium' | 'low'
    assignedUserId?: string
    startDate?: string
    endDate?: string
  } = {}) => {
    setIsLoading(true)
    try {
      const searchParams = new URLSearchParams()
      
      Object.entries(params).forEach(([key, value]) => {
        if (value) searchParams.append(key, value)
      })

      const response = await request<{ items: FollowUpReminder[] }>({
        url: `/api/follow-up-reminders?${searchParams.toString()}`,
        method: 'GET',
      })

      return response.data.items
    } finally {
      setIsLoading(false)
    }
  }, [request])

  // 标记提醒为完成
  const markReminderCompleted = useCallback(async (reminderId: string) => {
    await request({
      url: `/api/follow-up-reminders/${reminderId}/complete`,
      method: 'POST',
    })
  }, [request])

  // 延期提醒
  const snoozeReminder = useCallback(async (reminderId: string, newDate: string) => {
    await request({
      url: `/api/follow-up-reminders/${reminderId}/snooze`,
      method: 'POST',
      data: { newDate },
    })
  }, [request])

  // 获取今日提醒
  const getTodayReminders = useCallback(async () => {
    const today = new Date().toISOString().split('T')[0]
    return getReminders({ 
      status: 'pending',
      startDate: today,
      endDate: today,
    })
  }, [getReminders])

  // 获取逾期提醒
  const getOverdueReminders = useCallback(async () => {
    return getReminders({ status: 'overdue' })
  }, [getReminders])

  // 获取用户的提醒统计
  const getReminderStats = useCallback(async (userId?: string) => {
    const searchParams = new URLSearchParams()
    if (userId) searchParams.append('userId', userId)

    const response = await request<{
      total: number
      overdue: number
      today: number
      tomorrow: number
      upcoming: number
      completed: number
    }>({
      url: `/api/follow-up-reminders/stats?${searchParams.toString()}`,
      method: 'GET',
    })

    return response.data
  }, [request])

  return {
    isLoading,
    getReminders,
    markReminderCompleted,
    snoozeReminder,
    getTodayReminders,
    getOverdueReminders,
    getReminderStats,
  }
}

/**
 * 跟进状态管理 Hook
 */
export function useFollowUpStatus() {
  const { request } = useApi()

  // 更新咨询状态
  const updateConsultationStatus = useCallback(async (
    consultationId: string, 
    status: number,
    notes?: string
  ) => {
    await request({
      url: `/api/consultations/${consultationId}/status`,
      method: 'PUT',
      data: { status, notes },
    })
  }, [request])

  // 批量更新咨询状态
  const batchUpdateStatus = useCallback(async (updates: Array<{
    consultationId: string
    status: number
    notes?: string
  }>) => {
    await request({
      url: '/api/consultations/batch-status',
      method: 'PUT',
      data: { updates },
    })
  }, [request])

  // 设置跟进优先级
  const setFollowUpPriority = useCallback(async (
    consultationId: string,
    priority: 'high' | 'medium' | 'low'
  ) => {
    await request({
      url: `/api/consultations/${consultationId}/priority`,
      method: 'PUT',
      data: { priority },
    })
  }, [request])

  // 分配跟进人员
  const assignFollowUpUser = useCallback(async (
    consultationId: string,
    userId: string
  ) => {
    await request({
      url: `/api/consultations/${consultationId}/assign`,
      method: 'PUT',
      data: { assignedUserId: userId },
    })
  }, [request])

  return {
    updateConsultationStatus,
    batchUpdateStatus,
    setFollowUpPriority,
    assignFollowUpUser,
  }
}
