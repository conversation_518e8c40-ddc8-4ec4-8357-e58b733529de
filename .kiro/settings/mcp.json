{"mcpServers": {"context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase", "--access-token", "********************************************"], "type": "stdio"}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "type": "stdio"}, "firecrawl-mcp": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-3209e08f2b34493291de50c28b0aee0d"}}}}