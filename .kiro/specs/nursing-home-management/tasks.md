# 养老院入住管理系统实施计划

## 实施任务

- [x] 1. 项目基础架构搭建

  - 创建 Next.js 14 项目，配置 TypeScript 和 Tailwind CSS
  - 设置 Supabase 数据库连接和 Drizzle ORM 配置
  - 配置 ESLint、Prettier 和代码规范
  - 设置 shadcn/ui 组件库和基础样式
  - _需求: 需求 6.1, 需求 6.4_

- [x] 2. 数据库设计与初始化

  - [x] 2.1 使用 Drizzle ORM 创建数据库模式

    - 定义所有数据表的 Drizzle schema 文件
    - 设置主键、外键约束和索引
    - 创建数据库迁移脚本
    - _需求: 需求 1.1, 需求 2.1, 需求 3.1, 需求 4.1, 需求 5.1_

  - [x] 2.2 插入基础数据和种子数据
    - 创建系统角色和权限基础数据
    - 插入字典数据和系统配置
    - 创建测试用户和示例数据
    - _需求: 需求 6.1, 需求 6.4_

- [x] 3. 认证和权限系统开发

  - [x] 3.1 集成 Better Auth 认证系统

    - 配置 Better Auth 提供商和认证策略
    - 实现用户注册、登录和会话管理
    - 创建认证中间件和路由保护
    - 编写认证相关的单元测试
    - _需求: 需求 6.1, 需求 6.2_

  - [x] 3.2 实现 RBAC 权限控制系统

    - 开发基于角色的权限验证逻辑
    - 创建权限检查 Hook 和组件
    - 实现动态路由和菜单权限控制
    - 编写权限系统的集成测试
    - _需求: 需求 6.1, 需求 6.2_

  - [x] 3.3 开发用户管理界面
    - 创建登录页面使用 shadcn/ui 组件
    - 实现用户信息管理和权限分配界面
    - 开发角色管理和权限配置页面
    - 编写用户界面的 E2E 测试
    - _需求: 需求 6.1, 需求 6.2_

- [x] 4. 核心布局和导航系统

  - [x] 4.1 创建响应式布局组件

    - 实现主布局组件（Header、Sidebar、Main）
    - 使用 Tailwind CSS 创建响应式设计
    - 集成 Framer Motion 实现流畅动画
    - 实现主题切换和用户偏好设置
    - _需求: 需求 7.4, 需求 7.5_

  - [x] 4.2 开发导航和路由系统

    - 配置 Next.js App Router 和动态路由
    - 创建多级导航菜单组件
    - 实现面包屑导航和页面标题管理
    - 开发搜索功能和全局搜索组件
    - _需求: 需求 7.4, 需求 7.5_

  - [x] 4.3 实现通用组件库
    - 基于 shadcn/ui 创建业务组件
    - 开发数据表格、表单和对话框组件
    - 集成 ECharts 创建图表组件
    - 实现文件上传和图片预览组件
    - 编写组件文档和 Storybook 示例
    - _需求: 需求 7.1, 需求 7.2, 需求 7.6_

- [-] 5. 状态管理和数据层

  - [x] 5.1 配置 Zustand 状态管理

    - 设置全局状态 store 和类型定义
    - 实现用户状态和权限状态管理
    - 创建异步状态管理和错误处理
    - 开发状态持久化和同步机制
    - _需求: 需求 6.1, 需求 6.2_

  - [x] 5.2 开发 API 客户端和数据获取

    - 创建基于 fetch 的 API 客户端
    - 实现请求拦截器和错误处理
    - 开发数据缓存和 SWR 策略
    - 集成 Zod 进行数据验证
    - 编写 API 客户端的单元测试
    - _需求: 需求 6.3_

  - [x] 5.3 实现表单管理系统
    - 集成 React Hook Form 和 Zod 验证
    - 创建通用表单组件和验证规则
    - 实现表单状态管理和错误显示
    - 开发表单提交和数据处理逻辑
    - 编写表单组件的测试用例
    - _需求: 需求 7.1, 需求 7.2_

- [ ] 6. 营销管理模块开发

  - [x] 6.1 实现咨询接待管理功能

    - 开发咨询记录的 Next.js API Routes
    - 创建咨询接待列表和详情页面
    - 实现咨询信息录入表单和 Zod 验证
    - 添加文件上传到 Supabase Storage 功能
    - 编写咨询管理的单元测试和集成测试
    - _需求: 需求 1.1, 需求 1.5_

  - [x] 6.2 开发跟进记录功能

    - 实现跟进记录的 API 接口和数据库操作
    - 创建跟进记录时间线展示组件
    - 开发跟进提醒和状态管理功能
    - 使用 Framer Motion 实现交互动画
    - 编写跟进记录的测试用例
    - _需求: 需求 1.2_

  - [x] 6.3 实现业绩统计和报表功能
    - 开发销售员业绩统计 API 和数据聚合
    - 创建媒介渠道效果分析功能
    - 使用 ECharts 实现统计图表和报表展示
    - 添加数据导出和 PDF 生成功能
    - 编写统计功能的测试用例
    - _需求: 需求 1.3, 需求 1.4_

- [ ] 7. 居住管理模块开发

  - [x] 7.1 实现入住总览功能

    - 开发入住统计数据查询 API
    - 创建入住总览仪表板页面
    - 使用 ECharts 实现床位使用率和入住率展示
    - 添加入住趋势图表和数据分析
    - 编写入住总览的测试用例
    - _需求: 需求 2.1_

  - [x] 7.2 开发预订管理功能

    - 实现预订记录的完整 CRUD API
    - 创建预订管理列表和表单页面
    - 开发预订状态跟踪和提醒功能
    - 实现房间可用性查询和预订冲突检测
    - 编写预订管理的单元测试
    - _需求: 需求 2.2_

  - [x] 7.3 实现入住流程管理

    - 开发入住申请、评估、审核的完整 API
    - 创建入住流程步骤条和状态管理
    - 实现合同签订和文件管理功能
    - 开发入住办理和缴费处理功能
    - 添加房间分配和确认功能
    - 编写入住流程的集成测试
    - _需求: 需求 2.3, 需求 2.4, 需求 2.5, 需求 2.6, 需求 2.7, 需求 2.8, 需求 2.9_

  - [x] 7.4 开发退住管理功能
    - 实现退住申请、审核和缴费管理 API
    - 创建退住流程和状态跟踪页面
    - 开发退费计算和处理功能
    - 编写退住管理的测试用例
    - _需求: 需求 2.10_

- [ ] 8. 护理服务模块开发

  - [ ] 8.1 实现在住服务基础功能

    - 开发在住总览和老人信息管理 API
    - 实现请假申请、审核和销假管理
    - 创建床位调整和对调功能
    - 开发事故报告和探访登记功能
    - 编写在住服务的单元测试
    - _需求: 需求 3.1, 需求 3.2, 需求 3.3, 需求 3.4, 需求 3.5_

  - [ ] 8.2 开发护理计划和记录管理

    - 实现护理计划的制定、执行和变更 API
    - 创建护理记录录入和查询功能
    - 开发护理项目管理和核查功能
    - 实现生活记录和护理变更跟踪
    - 编写护理管理的集成测试
    - _需求: 需求 3.6, 需求 3.7_

  - [ ] 8.3 实现护理排班和团队管理

    - 开发护理排班和班次设置 API
    - 创建交接班管理和模板功能
    - 实现护理成员、小组和岗位管理
    - 开发护理知识库和合同跟踪功能
    - 编写排班管理的测试用例
    - _需求: 需求 3.8, 需求 3.10_

  - [ ] 8.4 开发照护看板功能
    - 实现时间维度和长者维度的数据查询 API
    - 创建照护看板的可视化展示
    - 开发多维度数据筛选和分析功能
    - 使用 ECharts 添加护理质量评估和报告功能
    - 编写照护看板的测试用例
    - _需求: 需求 3.9_

- [ ] 9. 财务管理模块开发

  - [ ] 9.1 实现费用账单管理

    - 开发费用账单生成、调整和结算 API
    - 创建账单管理列表和详情页面
    - 实现费用计算和账单状态管理
    - 开发账单打印和 PDF 导出功能
    - 编写账单管理的单元测试
    - _需求: 需求 4.1_

  - [ ] 9.2 开发缴费和预缴管理

    - 实现预缴月费用和缴费通知 API
    - 创建缴费记录和缴费明细管理
    - 开发个人账户和余额管理功能
    - 实现日常消费和消费记录功能
    - 编写缴费管理的集成测试
    - _需求: 需求 4.2, 需求 4.5_

  - [ ] 9.3 实现费用查询和报表功能

    - 开发多维度费用查询和统计 API
    - 创建月费用报表和欠费查询功能
    - 使用 ECharts 实现财务报表生成和数据分析
    - 添加报表导出和打印功能
    - 编写报表功能的测试用例
    - _需求: 需求 4.3, 需求 4.4_

  - [ ] 9.4 开发费用标准和优惠管理
    - 实现各类费用标准设置和管理 API
    - 开发抄表管理和仪表收费功能
    - 创建会员折扣和预缴折扣管理
    - 实现请假规则和费用减免功能
    - 编写费用标准管理的测试用例
    - _需求: 需求 4.6, 需求 4.7, 需求 4.8_

- [ ] 10. 库存管理模块开发

  - [ ] 10.1 实现库存基础管理功能

    - 开发库存查询和多条件筛选 API
    - 创建库存列表和详情展示页面
    - 实现库存状态管理和预警功能
    - 编写库存查询的单元测试
    - _需求: 需求 5.1_

  - [ ] 10.2 开发入库和出库管理

    - 实现入库管理和入库原因记录 API
    - 开发出库管理和领用部门跟踪
    - 创建入库出库表单和审核流程
    - 实现库存数量自动更新功能
    - 编写入库出库的集成测试
    - _需求: 需求 5.2, 需求 5.3_

  - [ ] 10.3 实现物品调拨和库存调整

    - 开发物品调拨申请和审核 API
    - 实现库存调整和盘点记录管理
    - 创建调拨流程和状态跟踪功能
    - 编写调拨管理的测试用例
    - _需求: 需求 5.4, 需求 5.5_

  - [ ] 10.4 开发供应商和仓库管理
    - 实现供应商信息管理和维护 API
    - 开发仓库管理和库存分布功能
    - 创建订单流水和采购记录管理
    - 编写供应商管理的测试用例
    - _需求: 需求 5.6, 需求 5.7_

- [ ] 11. 系统管理模块开发

  - [ ] 11.1 完善用户和权限管理界面

    - 开发用户账号管理的完整界面
    - 实现角色权限的灵活配置界面
    - 创建权限管理界面和操作功能
    - 编写权限管理的安全测试
    - _需求: 需求 6.1, 需求 6.2_

  - [ ] 11.2 实现系统日志和审计功能

    - 开发操作日志记录和查询 API
    - 创建系统日志分析和审计页面
    - 实现日志导出和归档功能
    - 编写日志管理的测试用例
    - _需求: 需求 6.3_

  - [ ] 11.3 开发字典和配置管理

    - 实现系统字典的维护和管理 API
    - 开发系统参数配置功能
    - 创建基础数据管理界面
    - 编写配置管理的测试用例
    - _需求: 需求 6.4_

  - [ ] 11.4 实现设备管理功能
    - 开发设备信息管理和状态监控 API
    - 实现设备报警和处理功能
    - 创建设备管理界面和操作功能
    - 编写设备管理的测试用例
    - _需求: 需求 6.5_

- [ ] 12. 系统集成和优化

  - [ ] 12.1 实现模块间数据集成

    - 开发模块间数据同步和一致性保证
    - 实现跨模块业务流程集成
    - 创建数据导入导出功能
    - 编写数据集成的测试用例
    - _需求: 需求 1.1, 需求 2.1, 需求 3.1, 需求 4.1, 需求 5.1_

  - [ ] 12.2 性能优化和缓存实现

    - 实现 Next.js 静态生成和增量静态再生
    - 开发客户端缓存和 SWR 数据获取策略
    - 优化 Supabase 查询和数据库性能
    - 进行 Web Vitals 性能测试和调优
    - _需求: 需求 7.1, 需求 7.2_

  - [ ] 12.3 安全加固和测试
    - 实现数据加密和脱敏功能
    - 开发 API 安全和访问控制
    - 进行安全漏洞扫描和修复
    - 编写安全测试用例
    - _需求: 需求 6.1, 需求 6.2, 需求 6.3_

- [ ] 13. 系统测试和部署

  - [ ] 13.1 完整功能测试

    - 使用 Playwright 进行端到端功能测试
    - 执行用户验收测试
    - 修复测试发现的问题
    - 编写测试报告和文档
    - _需求: 需求 1.1-需求 7.6_

  - [ ] 13.2 系统部署和上线

    - 配置 Vercel 生产环境和部署流程
    - 进行 Supabase 数据迁移和系统初始化
    - 实施系统监控和错误追踪
    - 编写部署文档和运维手册
    - _需求: 需求 6.4_

  - [ ] 13.3 用户培训和文档编写
    - 编写用户操作手册和帮助文档
    - 制作系统使用培训材料
    - 进行用户培训和系统交付
    - 建立技术支持和维护机制
    - _需求: 需求 7.1-需求 7.6_
