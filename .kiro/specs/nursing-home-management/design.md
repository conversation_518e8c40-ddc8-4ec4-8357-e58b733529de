# 养老院入住管理系统设计文档

## 概述

养老院入住管理系统是一个基于 Web 的综合管理平台，采用前后端分离架构，为养老机构提供完整的数字化管理解决方案。系统设计遵循模块化、可扩展和用户友好的原则，确保系统的稳定性、安全性和易用性。

## 架构设计

### 系统架构

```mermaid
graph TB
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据访问层]
    C --> D[数据库层]

    A1[Web前端] --> A
    A2[移动端] --> A

    B1[营销管理服务] --> B
    B2[居住管理服务] --> B
    B3[护理服务] --> B
    B4[财务服务] --> B
    B5[库存服务] --> B
    B6[系统服务] --> B

    C1[数据访问接口] --> C
    C2[缓存层] --> C

    D1[PostgreSQL数据库] --> D
    D2[Supabase存储] --> D
    D3[Vercel文件存储] --> D
```

### 技术栈选择

**前端技术栈：**

- React 18 + Next.js 14：现代化的 React 全栈框架，提供 SSR 和优秀的开发体验
- TypeScript：类型安全的 JavaScript 超集
- Tailwind CSS：实用优先的 CSS 框架
- shadcn/ui：基于 Radix UI 的高质量组件库
- ECharts：强大的数据可视化图表库
- Framer Motion：流畅的动画库
- Zustand：轻量级状态管理
- React Hook Form：高性能表单库
- Zod：TypeScript 优先的模式验证

**后端技术栈：**

- Next.js API Routes：全栈开发，API 和前端统一
- PostgreSQL：强大的关系型数据库
- Supabase：开源的 Firebase 替代方案，提供数据库、认证、存储
- Drizzle ORM：TypeScript 优先的 ORM
- Better Auth：现代化的认证解决方案
- Vercel：部署和托管平台

## 组件和接口设计

### 前端组件架构

```mermaid
graph TD
    A[App.tsx] --> B[Layout组件]
    B --> C[Header组件]
    B --> D[Sidebar组件]
    B --> E[Main组件]

    E --> F[营销管理模块]
    E --> G[居住管理模块]
    E --> H[护理服务模块]
    E --> I[财务管理模块]
    E --> J[库存管理模块]
    E --> K[系统管理模块]

    F --> F1[咨询接待]
    F --> F2[跟进记录]
    F --> F3[业绩统计]

    G --> G1[入住总览]
    G --> G2[预订管理]
    G --> G3[入住流程]
    G --> G4[退住管理]
```

### 核心页面设计

#### 1. 首页设计

- **左侧导航栏**：多级菜单结构，支持折叠展开
- **顶部导航**：搜索框、通知中心、用户头像和下拉菜单
- **主内容区**：
  - 数据概览卡片：入住率、床位使用率、本月收入等关键指标
  - 趋势图表：入住趋势、收入趋势等可视化图表
  - 待办事项：待审核申请、即将到期合同等
  - 快速操作：常用功能的快捷入口

#### 2. 营销管理页面

- **咨询接待列表**：表格展示咨询记录，支持筛选和搜索
- **咨询详情表单**：记录咨询人信息、长者情况、咨询目的
- **跟进记录**：时间线形式展示跟进历史
- **统计报表**：销售员业绩统计、渠道效果分析

#### 3. 居住管理页面

- **入住总览**：仪表板形式展示入住统计信息
- **预订管理**：预订列表和预订表单
- **入住流程**：步骤条形式展示入住各个环节
- **房间管理**：房间状态可视化展示

#### 4. 护理服务页面

- **护理计划**：甘特图形式展示护理计划
- **护理记录**：表单录入和历史记录查看
- **照护看板**：多维度数据展示和筛选
- **排班管理**：日历形式的排班界面

#### 5. 财务管理页面

- **账单管理**：账单列表和详情查看
- **费用统计**：图表形式展示收入分析
- **缴费管理**：缴费记录和提醒功能
- **报表中心**：各类财务报表生成和导出

### API 接口设计

#### Next.js API Routes 规范

```
GET    /api/consultations          # 获取咨询记录列表
POST   /api/consultations          # 创建咨询记录
GET    /api/consultations/[id]     # 获取咨询记录详情
PUT    /api/consultations/[id]     # 更新咨询记录
DELETE /api/consultations/[id]     # 删除咨询记录

GET    /api/elders                 # 获取老人列表
POST   /api/elders                 # 创建老人信息
GET    /api/elders/[id]            # 获取老人详情
PUT    /api/elders/[id]            # 更新老人信息

GET    /api/rooms                  # 获取房间列表
GET    /api/rooms/available        # 获取可用房间
POST   /api/reservations           # 创建预订
PUT    /api/reservations/[id]      # 更新预订状态

GET    /api/contracts              # 获取合同列表
POST   /api/contracts              # 创建合同
GET    /api/contracts/[id]         # 获取合同详情

GET    /api/nursing/plans          # 获取护理计划
POST   /api/nursing/records        # 创建护理记录
GET    /api/nursing/schedules      # 获取排班信息

GET    /api/bills                  # 获取账单列表
POST   /api/bills                  # 创建账单
PUT    /api/bills/[id]/pay         # 缴费操作

GET    /api/inventory              # 获取库存列表
POST   /api/inventory/in           # 入库操作
POST   /api/inventory/out          # 出库操作
```

#### TypeScript 类型定义

```typescript
// 咨询记录类型
interface Consultation {
  id: string
  elderInfo: ElderInfo
  consultantName: string
  consultantPhone: string
  purpose: string
  expectedCheckInDate: Date
  mediaChannel: number
  status: number
  notes: string
  createdAt: Date
  updatedAt: Date
}

// 老人信息类型
interface ElderInfo {
  id: string
  name: string
  age: number
  phone: string
  gender: number
  idCard: string
  emergencyContact: EmergencyContact
  healthInfo: HealthInfo
}

// API响应格式
interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}
```

## 数据模型设计

### 核心实体关系

```mermaid
erDiagram
    ELDER_INFO ||--o{ ELDER_HEALTH_INFO : has
    ELDER_INFO ||--o{ ELDER_ASSESSMENT : has
    ELDER_INFO ||--o{ ELDER_CONTRACT : signs
    ELDER_INFO ||--o{ ELDER_CHECK_IN : performs
    ELDER_INFO ||--o{ ELDER_BILL : generates
    ELDER_INFO ||--o{ SURVEY_CONSULTATION : involves

    ELDER_ROOM ||--o{ ELDER_INFO : accommodates
    ELDER_ROOM ||--o{ ELDER_RESERVATION : reserves

    ELDER_CONTRACT ||--o{ ELDER_CHECK_IN : enables
    ELDER_CONTRACT ||--o{ ELDER_CHECK_OUT : terminates

    APP_USER ||--o{ ELDER_INFO : manages
    APP_USER ||--o{ APP_DEVICE : owns

    SYS_MGR_USER ||--o{ SYS_USER_ROLE : has
    SYS_ROLE ||--o{ SYS_ROLE_PERMISSION : includes
    SYS_PERMISSION ||--o{ SYS_ROLE_PERMISSION : grants
```

### 数据库设计优化

- 主键自动索引
- 外键字段建立索引
- 常用查询字段建立复合索引
- 时间字段建立索引用于范围查询

## 错误处理设计

1. **业务错误处理**：

   - 表单验证错误：实时显示错误信息
   - 操作失败：Toast 提示具体错误原因
   - 网络错误：显示重试按钮

2. **用户体验优化**：

   - Loading 状态显示
   - 骨架屏加载效果
   - 错误边界组件捕获异常

## 安全设计

### 认证和授权

1. **JWT Token 认证**：

   - 登录成功后返回 JWT Token
   - Token 包含用户信息和权限
   - 设置合理的过期时间

2. **RBAC 权限模型**：

   - 用户-角色-权限三层模型
   - 支持数据权限控制
   - 动态权限验证

3. **接口安全**：
   - 所有接口需要 Token 验证
   - 敏感操作需要二次验证
   - 接口访问频率限制

### 数据安全

1. **数据加密**：

   - 密码使用 BCrypt 加密
   - 敏感信息 AES 加密存储
   - 传输过程 HTTPS 加密

2. **数据脱敏**：

   - 身份证号部分隐藏
   - 手机号中间位隐藏
   - 日志中敏感信息脱敏

3. **操作审计**：
   - 记录所有关键操作
   - 包含操作人、时间、内容
   - 支持审计日志查询
