# 养老院入住管理系统

专业的养老院入住管理系统，提供从营销咨询、入住管理、在住服务、费用结算到库存管理的全流程数字化解决方案。

## 技术栈

### 前端技术

- **Next.js 14** - React 全栈框架，支持 SSR 和 App Router
- **TypeScript** - 类型安全的 JavaScript 超集
- **Tailwind CSS** - 实用优先的 CSS 框架
- **shadcn/ui** - 基于 Radix UI 的高质量组件库
- **Framer Motion** - 流畅的动画库
- **ECharts** - 强大的数据可视化图表库
- **Zustand** - 轻量级状态管理
- **React Hook Form** - 高性能表单库
- **Zod** - TypeScript 优先的模式验证

### 后端技术

- **Next.js API Routes** - 全栈开发，API 和前端统一
- **PostgreSQL** - 强大的关系型数据库
- **Supabase** - 开源的 Firebase 替代方案
- **Drizzle ORM** - TypeScript 优先的 ORM
- **Better Auth** - 现代化的认证解决方案

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API 路由
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # React 组件
│   └── ui/               # shadcn/ui 组件
├── hooks/                # 自定义 Hooks
├── lib/                  # 工具库
│   ├── auth.ts           # Better Auth 配置
│   ├── auth-client.ts    # 客户端认证
│   ├── db/               # 数据库配置
│   ├── supabase.ts       # Supabase 客户端
│   └── utils.ts          # 工具函数
```

## 开发环境设置

### 1. 安装依赖

```bash
npm install
```

### 2. 环境变量配置

复制 `.env.local` 文件并配置以下环境变量：

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Database Configuration
DATABASE_URL=your_database_connection_string

# Better Auth Configuration
BETTER_AUTH_SECRET=your_auth_secret_key
BETTER_AUTH_URL=http://localhost:3000
```

### 3. 数据库设置

```bash
# 生成数据库迁移文件
npm run db:generate

# 执行数据库迁移
npm run db:migrate

# 或者直接推送到数据库（开发环境）
npm run db:push
```

### 4. 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 可用脚本

- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run start` - 启动生产服务器
- `npm run lint` - 运行 ESLint 检查
- `npm run lint:fix` - 自动修复 ESLint 问题
- `npm run format` - 格式化代码
- `npm run format:check` - 检查代码格式
- `npm run type-check` - TypeScript 类型检查
- `npm run db:generate` - 生成数据库迁移
- `npm run db:migrate` - 执行数据库迁移
- `npm run db:push` - 推送 schema 到数据库
- `npm run db:studio` - 打开 Drizzle Studio

## 功能模块

### 1. 营销管理

- 咨询接待记录
- 跟进记录管理
- 业绩统计分析
- 媒介渠道效果分析

### 2. 居住管理

- 入住总览
- 预订管理
- 入住流程管理
- 退住管理

### 3. 护理服务

- 在住服务管理
- 护理计划制定
- 护理记录管理
- 照护看板

### 4. 财务管理

- 费用账单管理
- 缴费管理
- 费用查询统计
- 财务报表

### 5. 库存管理

- 库存查询
- 出入库管理
- 物品调拨
- 供应商管理

### 6. 系统管理

- 用户权限管理
- 系统日志审计
- 字典配置管理
- 设备管理

## 代码规范

项目使用 ESLint 和 Prettier 进行代码规范管理：

- 使用 TypeScript 严格模式
- 遵循 Next.js 最佳实践
- 使用 Prettier 进行代码格式化
- 组件使用 PascalCase 命名
- 文件使用 kebab-case 命名

## 部署

推荐使用 Vercel 进行部署：

1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 自动部署

## 许可证

本项目采用 MIT 许可证。
